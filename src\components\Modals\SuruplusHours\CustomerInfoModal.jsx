import React, { useEffect, useState, useCallback } from "react";

import {
    Form,
    Modal,
    List,
    Button,
    message
} from "antd";

import * as controller from '../../../controllers/reports/surplus-hours-controller'
export const CustomerInfoModal = ({ isOpen, toggle, customerID }) => {
    const [form] = Form.useForm();
    const [customer, setCustomer] = useState([])
    const [loading, setLoading] = useState(false)

    const getCustomer = useCallback(async () => {
        try {
            setLoading(true)
            if (customerID) {
                const costumerInfo = await controller.getCustomerInfo(customerID)
                setCustomer(costumerInfo)
                if (costumerInfo.length === 0)
                    throw new Error('Não foi possivel buscar dados do cliente')
            }
        } catch (error) {
            message.error(error.message)
        } finally {
            setLoading(false)
        }
    }, [customerID])

    useEffect(() => {
        getCustomer()
    }, [getCustomer])

    return (
        <Modal
            title="Informações do Cliente"
            open={isOpen}
            onCancel={toggle}
            footer={[<Button onClick={toggle}>Fechar</Button>]}
        >
            <Form
                layout="vertical"
                onFinish={toggle}
                requiredMark={false}
                form={form}
            >
                <List
                    itemLayout="horizontal"
                    pagination={{ pageSize: 10 }}
                    dataSource={customer}
                    loading={loading}
                    renderItem={(item) => (
                        <List.Item>
                            <List.Item.Meta title={item.name} description={item.value} />
                        </List.Item>
                    )}
                />

            </Form>
        </Modal>
    );
};