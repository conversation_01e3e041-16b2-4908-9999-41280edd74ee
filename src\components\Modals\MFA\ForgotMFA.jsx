import { useState } from "react";
import Modal from "antd/lib/modal/Modal";
import { Button, Form, Input, message } from "antd";
import { otrsPost, ticketPost } from "../../../service/apiOtrs";

export const ForgotMFA = (props) => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const { email } = props;
  const [form] = Form.useForm();

  const resetSecret = async (data) => {
    setLoading(true);

    // ticketPost("/create/ticket", {
    //   title: "DSM - Reset de MFA",
    //   from: email,
    //   queue: 234,
    //   description: data.description,
    // })
    //   .then((res) => {
    //     message.success("Em breve você receberá uma notificação no e-mail.");
    //     form.resetFields();
    //     setIsModalVisible(false);
    //     setLoading(false);
    //   })
    //   .catch((err) => {
    //     message.error("Erro ao tentar desativar ticket");
    //     setLoading(false);
    //   });
    message.success("Em breve você receberá uma notificação no e-mail.");
    form.resetFields();
    setIsModalVisible(false);
    setLoading(false);
  };

  const showModal = () => {
    setIsModalVisible(true);
  };

  const handleOk = () => {
    form.submit();
    // setIsModalVisible(false);
  };

  const handleCancel = () => {
    setIsModalVisible(false);
    form.resetFields();
  };

  return (
    <>
      <Button type="link" onClick={showModal}>
        Perdi meu código
      </Button>
      <Modal
        title="Cadastre um ticket para recuperar seu código MFA"
        open={isModalVisible}
        confirmLoading={loading}
        onCancel={handleCancel}
        cancelText="Cancelar"
        onOk={handleOk}
        okText="Enviar"
      >
        <Form form={form} onFinish={resetSecret}>
          <Form.Item name="description">
            <Input.TextArea placeholder="Descreva o que aconteceu" />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};
