import React, { useEffect, useState } from "react";
import {
  Layout,
  Form,
  Card,
  Row,
  Col,
  Input,
  Select,
  Button,
  Typography,
  Affix,
  Tooltip,
  message,
  Popconfirm,
} from "antd";
import {
  DeleteOutlined,
  CloseCircleOutlined,
  PlusOutlined,
  ArrowLeftOutlined,
  CheckCircleOutlined,
  ArrowUpOutlined,
  CheckOutlined,
} from "@ant-design/icons";
import { HeaderMenu } from "../../components/HeaderMenu";
import { SideMenu } from "../../components/SideMenu";
import { useLocation, useNavigate } from "react-router-dom";
import { v4 } from "uuid";
import { dynamoPost } from "../../service/apiDsmDynamo";
import { logNewAuditAction } from "../../controllers/audit/logNewAuditAction";
import { TextColor } from "../../hooks/ColorInverter";
const { Content } = Layout;

export const ManagmentServices = () => {
  let fullTime = 0;
  const navigate = useNavigate();
  const { state } = useLocation();
  const { Option } = Select;
  const { Title, Text } = Typography;
  const { TextArea } = Input;
  const [toTopVisible, setToTopVisible] = useState(false);
  const [tagName, setTagName] = useState("");
  const [serviceName, setServiceName] = useState("");
  const [serviceDescription, setServiceDescription] = useState("");
  const [collapsed, setCollapsed] = useState(false);
  const [disabled, setDisabled] = useState(true);
  const [proposalData, setProposalData] = useState([]);
  const [activities, setActivities] = useState([
    {
      id: 0,
      list: [{ index: v4(), value: "" }],
      name: "",
      tag_color: "",
      activity_description: "",
      estimated_time: "",
      estimated_type: "",
      main_estimated_time: 0,
      main_estimated_time_type: "hour",
    },
  ]);

  const [sumHoursForType, setSumHoursForType] = useState([
    { main_estimated_time: 0 },
  ]);

  const [mainTagColorInput, setMainTagColorInput] = useState("#0f9347");
  const [service_form] = Form.useForm();
  const [activities_form] = Form.useForm();
  const [mainPercentage, setMainPercentage] = useState(0);
  const [loading, setLoading] = useState(false);
  const [selectTagToggle, setSelectTagToggle] = useState(false);

  function uniqByKeepLast(data, key) {
    return [...new Map(data.map((x) => [key(x), x]).values())];
  }

  let arr = [];

  const newArr = uniqByKeepLast(state, (e) => e.tag.name);

  newArr.forEach((e) => {
    return arr.push(e[1]);
  });

  const handleAddSubActivity = (activityKey) => {
    setActivities((prevState) => prevState.map((item) => {
        if (item.id === activityKey) {
          return {
            ...item,
            list: [...item.list, { index: v4(), value: "" }],
          };
        }
        return item;
      }));
  };

  const handleRemoveSubActivity = (activityKey, id) => {
    setActivities((prevState) => prevState.map((item) => {
        if (item.id === activityKey) {
          return {
            ...item,
            list: item.list.filter((item) => item.index !== id),
          };
        }
        return item;
      }));
    message.warning("Subatividade removida!");
  };

  const handleSubActivityChange = (value, id, activityKey) => {
    setActivities((prevState) =>
      prevState.map((item) => {
        if (item.id === activityKey) {
          return {
            ...item,
            list: item.list.map((item) => {
              if (item.index === id) {
                return {
                  ...item,
                  value: value,
                };
              }

              return item;
            }),
          };
        }

        return item;
      })
    );
  };

  const estimatedActivities = [
    {
      placeholder: "0",
      form_title: "main_estimated_time",
    },
  ];

  const handleAddActivity = () => {
    setActivities((prev) => [
      ...prev,
      {
        id: v4(),
        list: [{ index: v4(), value: "" }],
        name: "",
        tag_color: "",
        tag_name: "",
        estimated_time: "",
        estimated_type: "",
        main_estimated_time: 0,
        main_estimated_time_type: "hour",
      },
    ]);
  };

  const handleRemoveActivity = (index) => {
    setActivities((prev) => prev.filter(({ id }) => index !== id));
    message.warning("Atividade removida!");
  };

  const handleActivityChange = (value, id, field) => {
    value === "percentage" &&
      state === "catalog" &&
      message.warning(
        "O valor de porcentagem será calculado apenas após atrelar o serviço a uma proposta"
      );
    setActivities((prev) => prev.map((item) => {
        if (item.id === id) {
          return {
            ...item,
            [field]: value,
          };
        }
        return item;
      }));
  };

  const handleNullSubtask = () => {
    activities.map((item) => {
      return item.list.map((subItem, subItemKey) => {
        if (subItem.value === "" && item.list.length > 1) {
          handleRemoveSubActivity(item.id, subItem.index, true);
        }
      });
    });
  };

  const handleSubmit = async (data) => {
    setLoading(true);
    if (!serviceDescription || serviceDescription === "") {
      message.warning("Preencha a descrição do serviço.");
    } else {
      data["tag_name"] = tagName;
      data["tag_color"] = mainTagColorInput;
      handleNullSubtask();
      localStorage.setItem(
        "technical-proposal/services",
        JSON.stringify([
          ...proposalData.map((item) => item),
          {
            id: v4(),
            name: serviceName,
            description: serviceDescription,
            management: true,
            active: true,
            tag: {
              name: tagName,
              rgb: data.tag_color ? data.tag_color : mainTagColorInput,
            },
            tasks: activities.map((item, key) => ({
              estimates: {
                "8x5setup": key === 0 ? Number(fullTime) : 0,
                "24x7setup": 0,
                "24x7sust": 0,
                "8x5sust": 0,
              },
              name: item.name,
              description: item.activity_description,
              taskTime: {
                time: item.main_estimated_time,
                type: item.main_estimated_time_type
                  ? item.main_estimated_time_type
                  : "percentage",
              },
              subtasks: item.list
                .map((currentSubtask, id) => {
                  if (id === 0) {
                    if (currentSubtask.value !== "") {
                      return { description: currentSubtask.value };
                    } else if (
                      item?.list?.length > 1 &&
                      currentSubtask.value === ""
                    ) {
                      return { description: "" };
                    } else {
                      return {
                        description: "Atividade sem Sub Atividades definidas",
                      };
                    }
                  } else {
                    return { description: currentSubtask.value };
                  }
                })
                .filter((item) => item.description !== "")
                .map((item) =>
                  item.description === "Atividade sem Sub Atividades definidas"
                    ? { description: "" }
                    : { description: item.description }
                ),
            })),
          },
        ])
      );
      try {
        await dynamoPost(`${process.env.REACT_APP_STAGE}-services`, {
          id: v4(),
          name: serviceName,
          description: serviceDescription,
          management: true,
          active: true,
          tag: {
            name: tagName,
            rgb: data.tag_color ? data.tag_color : mainTagColorInput,
          },
          tasks: activities.map((item, key) => ({
            estimates: {
              "8x5setup": key === 0 ? Number(fullTime) : 0,
              "24x7setup": 0,
              "24x7sust": 0,
              "8x5sust": 0,
            },
            name: item.name,
            description: item.activity_description,
            taskTime: {
              time: item.main_estimated_time,
              type: item.main_estimated_time_type
                ? item.main_estimated_time_type
                : "percentage",
            },
            subtasks: item.list
              .map((currentSubtask, id) => {
                if (id === 0) {
                  if (currentSubtask.value !== "") {
                    return { description: currentSubtask.value };
                  } else if (
                    item?.list?.length > 1 &&
                    currentSubtask.value === ""
                  ) {
                    return { description: "" };
                  } else {
                    return {
                      description: "Atividade sem Sub Atividades definidas",
                    };
                  }
                } else {
                  return { description: currentSubtask.value };
                }
              })
              .filter((item) => item.description !== "")
              .map((item) =>
                item.description === "Atividade sem Sub Atividades definidas"
                  ? { description: "" }
                  : { description: item.description }
              ),
          })),
        });
      } catch {
        message.error("Erro ao salvar serviço");
      }

      const username = localStorage.getItem("@dsm/username");
      const title = "Adição de Serviço de Gerenciamento";
      const description = `${username} adicionou o serviço de gerenciamento: ${serviceName}`;
      logNewAuditAction(username, title, description);
      message.success("Serviço de gerenciamento adicionado com sucesso!");
      navigate(-1);
    }
    setLoading(false);
  };

  const screenScroll = () => {
    document.documentElement.scrollTop > 360
      ? setToTopVisible(true)
      : setToTopVisible(false);
  };

  useEffect(() => {
    window.addEventListener("scroll", screenScroll);
    if (localStorage.getItem("technical-proposal/services")) {
      setProposalData(
        JSON.parse(localStorage.getItem("technical-proposal/services"))
      );
    }
  }, []);

  useEffect(() => {
    if (state && state !== "catalog") {
      setMainPercentage(
        state.reduce((acc, curr) => acc + (curr.content || 0), 0)
      );
    }
  }, [state]);

  useEffect(() => {
    setSumHoursForType([
      {
        main_estimated_time: activities.reduce((acc, cur) => {
          if (cur.main_estimated_time_type !== "hour") {
            if (cur.main_estimated_time) {
              let sumPercentage =
                Number(acc) +
                Number(
                  (mainPercentage * Number(cur.main_estimated_time)) / 100
                );
              return Math.ceil(sumPercentage);
            }
            return Math.ceil(acc);
          } else {
            if (cur.main_estimated_time) {
              let sum = Number(acc) + Number(cur.main_estimated_time);
              return Math.ceil(sum);
            }
            return Math.ceil(acc);
          }
        }, 0),
      },
    ]);
    activities.map((item) => {
      if (
        item.main_estimated_time &&
        item.main_estimated_time !== 0 &&
        item.main_estimated_time !== "" &&
        item.name !== "" &&
        item.activity_description !== "" &&
        tagName !== "" &&
        serviceName &&
        serviceName !== "" &&
        serviceDescription &&
        serviceDescription !== ""
      ) {
        setDisabled(false);
      } else {
        setDisabled(true);
      }
    });
  }, [activities, mainPercentage, tagName, serviceName, serviceDescription]);
  return (
    <Layout style={{ minHeight: "100vh" }}>
      <HeaderMenu collapsed={collapsed} setCollapsed={setCollapsed} />
      <Layout>
        <SideMenu collapsed={collapsed} />
        <Content style={{ padding: "2em" }}>
          <Card
            style={{
              boxShadow: "0 0 10px rgba(0,0,0,0.1)",
              borderRadius: "20px",
            }}
          >
            <Popconfirm
              title="Ao voltar você perderá os dados do formulário, tem certeza?"
              onConfirm={() => {
                navigate(-1);
              }}
              okText="Sim"
              cancelText="Não"
            >
              <Button
                type="text"
                style={{ marginBottom: "15px", padding: "2px 5px 2px 0" }}
              >
                <ArrowLeftOutlined />
                Adicionar Serviço
              </Button>
            </Popconfirm>
            <Card style={{ backgroundColor: "#f6f6f6", padding: "10px 0" }}>
              <Form
                form={service_form}
                layout="vertical"
                requiredMark={false}
                onFinish={handleSubmit}
              >
                <Row>
                  <Col lg={8} sm={24}>
                    <Form.Item name="name_servico">
                      <Text>
                        Nome{" "}
                        <span
                          style={{
                            color: "#ff4d4f",
                            fontWeight: "bold",
                          }}
                        >
                          *
                        </span>
                      </Text>
                      <Input
                        placeholder="Nome do serviço"
                        style={{
                          borderColor: !serviceName ? "#ff4d4f" : "",
                          marginTop: 8,
                        }}
                        onChange={(e) => {
                          setServiceName(e.target.value);
                        }}
                      />
                      {!serviceName && (
                        <span
                          style={{
                            color: "#ff4d4f",
                            fontSize: 13,
                          }}
                        >
                          * valor não informado, campo obrigatório.
                        </span>
                      )}
                    </Form.Item>
                  </Col>
                  <Col lg={8} sm={24} offset={1}>
                    <Tooltip
                      placement="top"
                      title={
                        <>
                          {selectTagToggle === false
                            ? "Deseja utilizar uma tag existente?"
                            : "Deseja criar uma nova tag"}
                          <Row justify="end">
                            <Col>
                              <Button
                                type="primary"
                                icon={<CheckOutlined />}
                                onClick={() =>
                                  setSelectTagToggle(!selectTagToggle)
                                }
                              ></Button>
                            </Col>
                          </Row>
                        </>
                      }
                    >
                      {selectTagToggle === false ? (
                        <Form.Item name="tag_color">
                          <Row>
                            <Col span={22}>
                              <Form.Item name="tag_name">
                                <Text>
                                  Nome da Tag{" "}
                                  <span
                                    style={{
                                      color: "#ff4d4f",
                                      fontWeight: "bold",
                                    }}
                                  >
                                    *
                                  </span>
                                </Text>
                                <Input
                                  placeholder="Nome da tag"
                                  style={{
                                    borderColor: !serviceName ? "#ff4d4f" : "",
                                    marginTop: 8,
                                  }}
                                  onChange={(event) => {
                                    setTagName(event.target.value);
                                  }}
                                />
                              </Form.Item>
                            </Col>
                            <Col span={2}>
                              <Row>
                                <Form.Item name="tag_color">
                                  <div
                                    style={{
                                      display: "flex",
                                      flexDirection: "row-reverse",
                                      borderRadius: "3px",
                                      marginTop: 30,
                                    }}
                                  >
                                    <Input
                                      type="color"
                                      value={mainTagColorInput}
                                      onChange={(event) => {
                                        setMainTagColorInput(
                                          event.target.value
                                        );
                                      }}
                                      style={{
                                        width: "2rem",
                                        padding: 0,
                                        clipPath: "circle(32% at center)",
                                        border: "none",
                                        outline: "none",
                                        boxShadow: "none",
                                      }}
                                    />
                                  </div>
                                </Form.Item>
                              </Row>
                            </Col>
                          </Row>
                        </Form.Item>
                      ) : (
                        <Form.Item name="tag_color">
                          <Row>
                            <Col span={24}>
                              <Form.Item name="tag_name">
                                <Text>
                                  Nome da Tag{" "}
                                  <span
                                    style={{
                                      color: "#ff4d4f",
                                      fontWeight: "bold",
                                    }}
                                  >
                                    *
                                  </span>
                                </Text>
                                <Select
                                  placeholder="Nome da tag"
                                  style={{
                                    marginTop: 8,
                                  }}
                                  onChange={(e, i) => {
                                    setTagName(e);
                                    state?.map((i) =>
                                      i.tag.name === e
                                        ? setMainTagColorInput(i.tag.rgb)
                                        : null
                                    );
                                  }}
                                >
                                  {arr.map((item) => {
                                    return (
                                      <Option value={item.tag.name}>
                                        {TextColor(
                                          item?.tag.rgb,
                                          item?.tag.name
                                        )}
                                      </Option>
                                    );
                                  })}
                                </Select>
                              </Form.Item>
                            </Col>
                          </Row>
                        </Form.Item>
                      )}
                    </Tooltip>
                  </Col>
                  <Col lg={6} sm={24} offset={1}>
                    {estimatedActivities.map(
                      (mainEstimatedActivity, mainEstimatedActivityKey) => {
                        return (
                          <Form.Item label="Estimativa">
                            {sumHoursForType.map(
                              (sumHoursForTypeItem, sumHoursForTypeKey) => {
                                return Object.values(sumHoursForTypeItem).map(
                                  (
                                    sumHoursForTypeItemValue,
                                    sumHoursForTypeValueKey
                                  ) => {
                                    fullTime = sumHoursForTypeItemValue;
                                    return (
                                      sumHoursForTypeValueKey ===
                                        mainEstimatedActivityKey && (
                                        <Input
                                          style={{
                                            backgroundColor: "#fff",
                                          }}
                                          disabled
                                          key={sumHoursForTypeValueKey}
                                          placeholder={sumHoursForTypeItemValue}
                                        />
                                      )
                                    );
                                  }
                                );
                              }
                            )}
                          </Form.Item>
                        );
                      }
                    )}
                  </Col>
                </Row>
                <Row>
                  <Col span={24}>
                    <Form.Item name="description">
                      <Text>
                        Descrição de serviço{" "}
                        <span
                          style={{
                            color: "#ff4d4f",
                            fontWeight: "bold",
                          }}
                        >
                          *
                        </span>
                      </Text>
                      <TextArea
                        rows={1}
                        placeholder="Insira a descrição do serviço"
                        value={serviceDescription}
                        onChange={(e) => {
                          setServiceDescription(e.target.value);
                        }}
                        style={{
                          marginTop: "8px",
                        }}
                      />
                      {!serviceDescription && (
                        <span
                          style={{
                            color: "#ff4d4f",
                            fontSize: 13,
                          }}
                        >
                          * valor não informado, campo obrigatório.
                        </span>
                      )}
                    </Form.Item>
                  </Col>
                </Row>
              </Form>
            </Card>
            <Title level={3} style={{ fontWeight: "400", marginTop: "20px" }}>
              Atividades
            </Title>
            <Form layout="vertical" requiredMark={false} form={activities_form}>
              {activities.map((container, containerKey) => (
                <Card
                  key={containerKey}
                  style={{
                    backgroundColor: "#f4f4f4",
                    padding: "20px 5px",
                    margin: "25px 0",
                  }}
                >
                  <Row gutter={[16, 16]}>
                    <Col lg={12} sm={24}>
                      <Form.Item>
                        <Text>
                          Nome{" "}
                          <span
                            style={{
                              color: "#ff4d4f",
                              fontWeight: "bold",
                            }}
                          >
                            *
                          </span>
                        </Text>
                        <Input
                          placeholder="Nome"
                          style={{
                            borderColor: !activities[containerKey].name
                              ? "#ff4d4f"
                              : "",
                            marginTop: 8,
                          }}
                          value={activities[containerKey].name}
                          onChange={(e) =>
                            handleActivityChange(
                              e.target.value,
                              container.id,
                              "name"
                            )
                          }
                        />
                        {!activities[containerKey].name && (
                          <span
                            style={{
                              color: "#ff4d4f",
                              fontSize: 13,
                            }}
                          >
                            * valor não informado, campo obrigatório.
                          </span>
                        )}
                      </Form.Item>
                    </Col>

                    <Col lg={12} sm={24}>
                      {estimatedActivities.map(
                        (estimatedActivity, estimatedActivityKey) => {
                          return (
                            <Form.Item label="Estimativa">
                              <Input
                                addonAfter={
                                  <Select
                                    defaultValue="hour"
                                    className="select-after"
                                    onSelect={(e) => {
                                      handleActivityChange(
                                        e,
                                        container.id,
                                        "main_estimated_time_type"
                                      );
                                    }}
                                  >
                                    <Option value="percentage">
                                      Porcentagem
                                    </Option>
                                    <Option value="hour">Horas</Option>
                                  </Select>
                                }
                                type="number"
                                min="0"
                                max={
                                  container.main_estimated_time_type ===
                                  "percentage"
                                    ? 100
                                    : ""
                                }
                                placeholder={estimatedActivity.placeholder}
                                onChange={(e) =>
                                  handleActivityChange(
                                    parseInt(e.target.value),
                                    container.id,
                                    estimatedActivity.form_title
                                  )
                                }
                              ></Input>
                            </Form.Item>
                          );
                        }
                      )}
                    </Col>
                  </Row>
                  <Row>
                    <Col span={24}>
                      <Form.Item>
                        <Text>
                          Descrição{" "}
                          <span
                            style={{
                              color: "#ff4d4f",
                              fontWeight: "bold",
                            }}
                          >
                            *
                          </span>
                        </Text>
                        <TextArea
                          value={activities[containerKey].activity_description}
                          style={{
                            borderColor: !activities[containerKey]
                              .activity_description
                              ? "#ff4d4f"
                              : "",
                          }}
                          onChange={(e) =>
                            handleActivityChange(
                              e.target.value,
                              container.id,
                              "activity_description"
                            )
                          }
                          placeholder="Digite a descrição da atividade"
                        />
                        {!activities[containerKey].activity_description && (
                          <span
                            style={{
                              color: "#ff4d4f",
                              fontSize: 13,
                            }}
                          >
                            * valor não informado, campo obrigatório.
                          </span>
                        )}
                      </Form.Item>
                    </Col>
                  </Row>
                  <Form.Item label="Sub Atividades">
                    {container.list.map((item, index) => (
                      <Row
                        justify="space-between"
                        style={{ marginBottom: "20px" }}
                      >
                        <Col span={22}>
                          <Input
                            rows={1}
                            disabled={
                              !activities[containerKey].activity_description ||
                              !activities[containerKey].name
                            }
                            placeholder="Descrição"
                            key={index}
                            value={item.value}
                            onChange={(e) =>
                              handleSubActivityChange(
                                e.target.value,
                                item.index,
                                container.id
                              )
                            }
                          />
                        </Col>
                        <Col span={1}>
                          <Button
                            disabled={container.list.some(
                              (item) => item.value === ""
                            )}
                            icon={<PlusOutlined />}
                            type="text"
                            onClick={() => {
                              handleAddSubActivity(container.id);
                            }}
                          ></Button>
                        </Col>
                        {container.list.length > 1 && (
                          <Col span={1}>
                            <Button
                              danger
                              type="text"
                              onClick={() => {
                                handleRemoveSubActivity(
                                  container.id,
                                  item.index
                                );
                              }}
                            >
                              <DeleteOutlined />
                            </Button>
                          </Col>
                        )}
                      </Row>
                    ))}
                  </Form.Item>

                  <Row style={{ width: "100%" }} justify="space-between">
                    <Button
                      type="link"
                      style={{ margin: "15px 0" }}
                      onClick={() => {
                        handleAddActivity(container.id);
                      }}
                    >
                      Adicionar Atividade
                      <PlusOutlined />
                    </Button>
                    {activities.length > 1 && (
                      <Button
                        danger
                        type="text"
                        style={{ margin: "15px 0" }}
                        onClick={() => {
                          handleRemoveActivity(container.id);
                        }}
                      >
                        Remover Atividade
                        <DeleteOutlined />
                      </Button>
                    )}
                  </Row>
                </Card>
              ))}
            </Form>
            <Row justify="space-between">
              <Button
                danger
                type="primary"
                onClick={() => navigate(-1)}
                icon={<CloseCircleOutlined />}
              >
                Cancelar
              </Button>
              {disabled === true ? (
                <Tooltip
                  placement="top"
                  title={"Verifique os dados das atividades"}
                >
                  <Button
                    type="primary"
                    onClick={() => service_form.submit()}
                    disabled={disabled}
                  >
                    Concluir <CheckCircleOutlined />
                  </Button>
                </Tooltip>
              ) : (
                <Button
                  type="primary"
                  onClick={() => service_form.submit()}
                  disabled={disabled}
                  loading={loading}
                >
                  Concluir <CheckCircleOutlined />
                </Button>
              )}
            </Row>
          </Card>
        </Content>
        {toTopVisible === true && (
          <Affix
            style={{
              clipPath: "circle()",
              position: "fixed",
              right: 5,
              bottom: 5,
              width: "50px",
            }}
          >
            <Button
              style={{ width: "100%" }}
              type="primary"
              icon={<ArrowUpOutlined />}
              onClick={() => window.scrollTo({ top: 0, behavior: "smooth" })}
            ></Button>
          </Affix>
        )}
      </Layout>
    </Layout>
  );
};
