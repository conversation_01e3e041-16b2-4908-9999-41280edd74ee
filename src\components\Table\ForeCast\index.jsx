import React, { useState, useEffect } from 'react'
import { ForeCast } from '../../Graphics/ForeCast'
import { Table, Select, Modal, Typography, Row, Col, Card} from 'antd'
import '../../../styles/table.css'

const columns = [
  {
    title: 'Profissional',
    dataIndex: 'professional',
    key: 'professional',
    render: (professional) => <p className="table-font">{professional}</p>,
  },
  {
    title: 'Máxi<PERSON>',
    dataIndex: 'max',
    key: 'max',
    render: (max) => <p className="table-font">{max}</p>,
  },
  {
    title: 'Alocadas',
    dataIndex: 'allocated',
    key: 'allocated',
    render: (allocated) => <p className="table-font">{allocated}</p>,
  },
  {
    title: 'Horas registradas',
    dataIndex: 'recordedHours',
    key: 'recordedHours',
    render: (recordedHours) => <p className='table-font'>{recordedHours}</p>,
  },
  {
    title: 'Restantes',
    dataIndex: 'remaining',
    key: 'remaining',
    render: (remaining) => <p className='table-font'>{remaining}</p>,
  }
]

const data = [
  {
    key: '1',
    professional: '<PERSON> Silveira',
    max: '5h',
    allocated: '3h 30min',
    recordedHours: '4h',
    remaining: '2h'
  },
  {
    key: '2',
    professional: 'José',
    max: '4h',
    allocated: '3h 30min',
    recordedHours: '2h',
    remaining: '10h'
  },
  {
    key: '3',
    professional: 'Silveira',
    max: '6h',
    allocated: '2h 30min',
    recordedHours: '8h',
    remaining: '8h'
  },
  {
    key: '4',
    professional: 'Douglas',
    max: '8h',
    allocated: '3h 00min',
    recordedHours: '4h',
    remaining: '4h'
  },
  {
    key: '5',
    professional: 'Gilberto',
    max: '16h',
    allocated: '8h 00min',
    recordedHours: '2h',
    remaining: '9h'
  },
]

const ForeCastTable = (props) => {
  const { Title, Text } = Typography;
  const [tablePage, setTablePage] = useState(true)
  const [data, setData] = useState([])
  const [filteredProfessional, setFilteredProfessional] = useState([])
  const [professionalData, setProfessionalData] = useState([])
  const { Option } = Select;

  useEffect(() => {
    setData(
      [
        {
          key: '1',
          professional: 'José Silveira',
          max: '5h',
          allocated: '3h 30min',
          recordedHours: '4h',
          remaining: '2h'
        },
        {
          key: '2',
          professional: 'José',
          max: '4h',
          allocated: '3h 30min',
          recordedHours: '2h',
          remaining: '10h'
        },
        {
          key: '3',
          professional: 'Silveira',
          max: '6h',
          allocated: '2h 30min',
          recordedHours: '8h',
          remaining: '8h'
        },
        {
          key: '4',
          professional: 'Douglas',
          max: '8h',
          allocated: '3h 00min',
          recordedHours: '4h',
          remaining: '4h'
        },
        {
          key: '5',
          professional: 'Gilberto',
          max: '16h',
          allocated: '8h 00min',
          recordedHours: '2h',
          remaining: '9h'
        },
      ]  
    )
  }, [])

  useEffect(() => {
    setFilteredProfessional(data)
  }, [data])

  function showModal() {
    setTablePage(!tablePage)
  }

  function getFilteredProfessional(name) {
    if(name === "Todos"){
      return setFilteredProfessional(data)
    }
    let professionals = []
    data.forEach((professional) => {
      if(professional.professional === name) {
        professionals.push(professional)
      }
    })
    setFilteredProfessional(professionals)
  }

  return (
    <Card bordered={false} 
      style={{
          borderRadius: '20px', 
          height: '100%',
          boxShadow: "0 0 10px rgba(0,0,0,0.1)",
      }}
    >
      {tablePage === true ? (
          <Row justify='space-between'>
            <Col>
              <Title level={3} style={{fontWeight: 400}}>Gestão de forecast</Title>
            </Col>
            <Col>
              <Select
                mode="default"
                placeholder="Todos os Profissionais"
                onChange={(value) => getFilteredProfessional(value)}  
              >
                <Option value={'Todos'}>{'Todos'}</Option>
                {data.map((val) => (
                  <Option value={val.professional}>{val.professional}</Option>
                ))}
              </Select>
            </Col>
            <Col span={24}>
              <Table
                scroll={{ x: '100%' }}
                onRow={(record, rowIndex) => {
                  return {
                    onClick: event => {
                      setProfessionalData(record)
                      showModal()
                    }
                  }
                }}            
                pagination={false}
                columns={columns}
                dataSource={filteredProfessional}
              >
              </Table>
            </Col>
          </Row>
        ) : (
        <ForeCast professional={professionalData} showModal={showModal}/>
        )} 
    </Card> 
  )
}

export { ForeCastTable }
