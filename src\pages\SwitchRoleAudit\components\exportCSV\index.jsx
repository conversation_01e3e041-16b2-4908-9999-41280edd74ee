import { But<PERSON>, Col } from "antd";
import { TableExcelSwitcRoleAudit } from "./tableExcel";
import { logNewAuditAction } from "../../../../controllers/audit/logNewAuditAction";

export const ExportCSVButton = ({ exportData, columns }) => {
  return (
    <Col>
      <TableExcelSwitcRoleAudit data={exportData} allColumns={columns}>
        <Button
          type="primary"
          onClick={() => {
            const username = localStorage.getItem("@dsm/username");
            const title = "Histórico de Acesso - Exportação de CSV";
            const description = `${username} exportou o CSV de histórico de acessos de switch role`;
            logNewAuditAction(username, title, description);
          }}
        >
          Exportar CSV
        </Button>
      </TableExcelSwitcRoleAudit>
    </Col>
  );
};
