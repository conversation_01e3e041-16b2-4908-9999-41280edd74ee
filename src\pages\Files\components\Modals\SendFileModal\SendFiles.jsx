import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Spin,
  Row,
  Image,
  message,
  Col,
  Typography,
} from "antd";
import { SendOutlined } from "@ant-design/icons";
import { useToggle } from "../../../../../hooks/useToggle";
import { useState } from "react";
import { shallowEqual, useSelector } from "react-redux";
import { dynamoPut } from "../../../../../service/apiDsmDynamo";
import { v4 } from "uuid";
import imageSrc from "../../../../../assets/images/emailSent.png";
import {
  cleanFilesState,
  setUploadFileFilesState,
  setUploadReadyFilesState,
} from "../../../../../store/actions/files-action";
import { uploadFileS3 } from "../../../controllers/uploadFileS3";
import * as controller from "../../../../../controllers/files/filesController";

export const SendFilesModal = () => {
  const { Text } = Typography;
  const [toggleModalValue, toggle] = useToggle();
  const [modalName, setModalName] = useState("Carregando arquivo");
  const [loading, setLoading] = useState(false);
  const uploadReady = useSelector(
    (state) => state.files.uploadReady,
    shallowEqual
  );
  const uploadFile = useSelector(
    (state) => state.files.uploadFile,
    shallowEqual
  );
  const fileContent = useSelector(
    (state) => state.files.fileContent,
    shallowEqual
  );

  const handleSubmit = async () => {
    setLoading(true);
    toggle();
    const tableName = `${process.env.REACT_APP_STAGE}-files-archive`;

    const dynamoObj = {
      ...uploadFile,
      id: v4(),
    };

    try {
      await uploadFileS3(uploadFile, fileContent, dynamoObj.id);
      await dynamoPut(tableName, dynamoObj.id, dynamoObj);
    } catch (err) {
      message.error("Erro no upload do arquivo!");
      console.log({ err });
    }

    setUploadFileFilesState({
      field: "uploadFile",
      value: {
        name: "",
        type: "",
        category: "",
        createdAt: "",
        updatedAt: "",
        customerId: "",
        customerName: "",
        customerCNPJ: "",
        tags: [],
        emails: [],
        userName: "",
        userEmail: "",
      },
    });
    await controller.dispatchEmail(uploadFile);

    setUploadReadyFilesState(false);
    cleanFilesState();
    setLoading(false);
    setModalName("E-mail enviado");
  };

  return (
    <>
      <Button
        type="primary"
        onClick={() => handleSubmit()}
        icon={<SendOutlined />}
        disabled={!uploadReady}
      >
        Enviar
      </Button>
      <Modal
        title={modalName}
        open={toggleModalValue}
        width={"40%"}
        centered
        closable={false}
        footer={
          <Row justify={"end"}>
            <Button type="primary" onClick={toggle}>
              Fechar
            </Button>
          </Row>
        }
      >
        {loading ? (
          <Row justify="center" align={"middle"} style={{ height: "100%" }}>
            <Col span={24}>
              <Row justify="center">
                <Spin size="large" />
              </Row>
              <Row justify="center">
                <Text>Subindo o arquivo em nosso servidor</Text>
              </Row>
            </Col>
          </Row>
        ) : (
          <Row justify="center" align={"middle"} style={{ height: "100%" }}>
            <Col span={24}>
              <Row justify="center">
                <Image src={imageSrc} preview={false} />
              </Row>
              <Row justify="center">
                <Text>E-mail enviado com sucesso</Text>
              </Row>
            </Col>
          </Row>
        )}
      </Modal>
    </>
  );
};
