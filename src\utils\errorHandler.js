/**
 * Sistema de tratamento de erros centralizado
 * Especialmente para erros 500 e problemas de API
 */

import { logger } from './logger';
import { message } from 'antd';

class ErrorHandler {
  constructor() {
    this.errorQueue = [];
    this.maxQueueSize = 50;
    this.setupGlobalErrorHandlers();
  }

  /**
   * Configura handlers globais de erro
   */
  setupGlobalErrorHandlers() {
    // Handler para erros não capturados
    window.addEventListener('unhandledrejection', (event) => {
      this.handleUnhandledRejection(event);
    });

    // Handler para erros JavaScript
    window.addEventListener('error', (event) => {
      this.handleJavaScriptError(event);
    });
  }

  /**
   * Trata erros de Promise não capturadas
   */
  handleUnhandledRejection(event) {
    const error = event.reason;
    
    // Verificar se é um erro de Axios
    if (error?.isAxiosError) {
      this.handleAxiosError(error);
      event.preventDefault(); // Prevenir que apareça no console
    } else {
      logger.error('Promise rejeitada não tratada:', error);
    }
  }

  /**
   * Trata erros JavaScript gerais
   */
  handleJavaScriptError(event) {
    logger.error('Erro JavaScript:', {
      message: event.message,
      filename: event.filename,
      lineno: event.lineno,
      colno: event.colno,
      error: event.error
    });
  }

  /**
   * Trata erros específicos do Axios
   */
  handleAxiosError(error) {
    const status = error.response?.status;
    const url = error.config?.url;
    const method = error.config?.method?.toUpperCase();

    // Log detalhado do erro
    const errorInfo = {
      status,
      url,
      method,
      message: error.message,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      currentPage: window.location.href
    };

    // Adicionar à fila de erros
    this.addToErrorQueue(errorInfo);

    switch (status) {
      case 500:
        this.handle500Error(error, errorInfo);
        break;
      case 502:
      case 503:
      case 504:
        this.handleServerError(error, errorInfo);
        break;
      case 429:
        this.handleRateLimitError(error, errorInfo);
        break;
      default:
        this.handleGenericError(error, errorInfo);
    }
  }

  /**
   * Trata erros 500 especificamente
   */
  handle500Error(error, errorInfo) {
    logger.error('🚨 Erro 500 - Erro interno do servidor:', errorInfo);

    // Verificar se é um erro recorrente
    const recentErrors = this.getRecentErrors(error.config?.url, 300000); // 5 minutos
    
    if (recentErrors.length > 3) {
      logger.error('🔥 Múltiplos erros 500 detectados para a mesma URL:', {
        url: error.config?.url,
        count: recentErrors.length,
        timeframe: '5 minutos'
      });
      
      // Mostrar notificação para o usuário apenas uma vez
      if (!this.hasShownServerErrorNotification) {
        message.error({
          content: 'Detectamos problemas no servidor. Nossa equipe foi notificada.',
          duration: 10,
          key: 'server-error'
        });
        this.hasShownServerErrorNotification = true;
        
        // Reset após 10 minutos
        setTimeout(() => {
          this.hasShownServerErrorNotification = false;
        }, 600000);
      }
    }

    // Tentar identificar padrões
    this.analyzeErrorPatterns();
  }

  /**
   * Trata outros erros de servidor (502, 503, 504)
   */
  handleServerError(error, errorInfo) {
    logger.warn('⚠️ Erro de servidor:', errorInfo);
    
    // Mostrar mensagem mais amigável para o usuário
    message.warning({
      content: 'Serviço temporariamente indisponível. Tentando novamente...',
      duration: 5,
      key: 'server-unavailable'
    });
  }

  /**
   * Trata erros de rate limit (429)
   */
  handleRateLimitError(error, errorInfo) {
    logger.warn('🚦 Rate limit atingido:', errorInfo);
    
    message.warning({
      content: 'Muitas requisições. Aguarde um momento antes de tentar novamente.',
      duration: 8,
      key: 'rate-limit'
    });
  }

  /**
   * Trata erros genéricos
   */
  handleGenericError(error, errorInfo) {
    logger.debug('Erro de requisição:', errorInfo);
  }

  /**
   * Adiciona erro à fila para análise
   */
  addToErrorQueue(errorInfo) {
    this.errorQueue.push(errorInfo);
    
    // Manter apenas os últimos erros
    if (this.errorQueue.length > this.maxQueueSize) {
      this.errorQueue.shift();
    }
  }

  /**
   * Obtém erros recentes para uma URL específica
   */
  getRecentErrors(url, timeframe = 300000) {
    const now = Date.now();
    return this.errorQueue.filter(error => {
      const errorTime = new Date(error.timestamp).getTime();
      return error.url === url && (now - errorTime) < timeframe;
    });
  }

  /**
   * Analisa padrões de erro para identificar problemas sistêmicos
   */
  analyzeErrorPatterns() {
    const now = Date.now();
    const last5Minutes = this.errorQueue.filter(error => {
      const errorTime = new Date(error.timestamp).getTime();
      return (now - errorTime) < 300000; // 5 minutos
    });

    if (last5Minutes.length > 10) {
      logger.error('🔥 Alto volume de erros detectado:', {
        count: last5Minutes.length,
        timeframe: '5 minutos',
        urls: [...new Set(last5Minutes.map(e => e.url))],
        statuses: [...new Set(last5Minutes.map(e => e.status))]
      });
    }
  }

  /**
   * Obtém estatísticas de erro
   */
  getErrorStats() {
    const now = Date.now();
    const last24Hours = this.errorQueue.filter(error => {
      const errorTime = new Date(error.timestamp).getTime();
      return (now - errorTime) < 86400000; // 24 horas
    });

    const statusCounts = {};
    const urlCounts = {};

    last24Hours.forEach(error => {
      statusCounts[error.status] = (statusCounts[error.status] || 0) + 1;
      urlCounts[error.url] = (urlCounts[error.url] || 0) + 1;
    });

    return {
      total: last24Hours.length,
      statusCounts,
      urlCounts,
      timeframe: '24 horas'
    };
  }

  /**
   * Limpa a fila de erros
   */
  clearErrorQueue() {
    this.errorQueue = [];
    logger.info('Fila de erros limpa');
  }
}

// Instância singleton
const errorHandler = new ErrorHandler();

export default errorHandler;
export { ErrorHandler };
