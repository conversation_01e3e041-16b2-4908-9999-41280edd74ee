import { Row, Col, Space, Button, message } from "antd";
import { RangePickerStandard } from "../../../../components/RangePicker";
import moment from "moment";
import { useState } from "react";
import { SearchInput } from "../../../../components/SearchInput";
import { differenceInCalendarDays } from "date-fns";
import { ExportCSVButton } from "../exportCSV";
import { SearchOutlined } from "@ant-design/icons";
export const SwitchRoleAuditHeader = ({
  setSelectedTable,
  setSearch,
  setSearchDate,
  exportData,
  exportColumns,
  setSearchTrigger
}) => {
  const [displayDate, setDisplayDate] = useState([]);
  const [isSearchDisabled, setIsSearchDisabled] = useState(false)
  const onChangeSearch = (value) => {
    setSearch(value);
  };

  const onChangeTable = (value) => {
    setSelectedTable(value);
  };

  const onDateChange = (value) => {
    let startDate = moment(value[0]).subtract(1, "days");
    let endDate = moment(value[1]).add(1, "days");
    let diff = differenceInCalendarDays(new Date(value[1]), new Date(value[0]));
    if (diff > moment().daysInMonth()) {
      message.warning("O período selecionado não pode ser maior do que um mês");
      setSearchDate(false);
      setIsSearchDisabled(true)
    } else {
      setIsSearchDisabled(false)
      setSearchDate([
        startDate.format("MM-DD-YYYY"),
        endDate.format("MM-DD-YYYY"),
      ]);
      setDisplayDate([
        startDate.add(1, "days").format("DD/MM/YYYY"),
        endDate.subtract(1, "days").format("DD/MM/YYYY"),
      ]);
    }
  };

  const handleSearchTrigger = () => {
    setSearchTrigger()
  }

  return (
    <Row
      justify="space-between"
      style={{ marginBottom: "1em" }}
      gutter={[16, 16]}
    >
      <Col xl={6} lg={12}>
        <SearchInput onChange={onChangeSearch} placeholder="Pesquisar" />
      </Col>
      <Col>
        <Space wrap>
          <Button
            onClick={() => onChangeTable("requested")}
            style={{
              borderColor: "#f5bd05",
              color: "#f5bd05",
            }}
          >
            Solicitação de acesso
          </Button>
          <Button onClick={() => onChangeTable("conceeded")} type="primary">
            Concessão de acesso
          </Button>
        </Space>
      </Col>
      <Col xl={8} lg={8}>
      <Space size={5}>
        <RangePickerStandard onChange={onDateChange} value={displayDate} widthSize={80}/>
        <Button type="primary" onClick={handleSearchTrigger}><SearchOutlined />Pesquisar</Button>
        </Space>
      </Col>
      <ExportCSVButton exportData={exportData} columns={exportColumns} />
    </Row>
  );
};
