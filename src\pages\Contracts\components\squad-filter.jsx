import { useState, useMemo, useEffect } from "react";
import { shallowEqual, useSelector } from "react-redux";
import { Col, Typography } from "antd";
import { MultiSelect } from "../../../components/MultiSelect";
import { setContractFieldState } from "../../../store/actions/contract-action";
import { getSquads } from "../../../controllers/reports/surplus-hours-controller";

const allOption = { value: 0, label: "Todos" };

export const SquadSelectorField = ({ span }) => {
  const squads = useSelector(
    (state) => state.contract.squadOptions,
    shallowEqual
  );
  const selectedSquads = useSelector(
    (state) => state.contract.selectedSquads,
    shallowEqual
  );
  const [allSquadsSelect, setAllSquadsSelect] = useState(true);
  useEffect(() => {
    getSquads();
  }, []);
  const { Text } = Typography;

  const squadOptions = useMemo(() => {
    let options = [];

    options = squads.map((squad) => {
      return {
        value: squad.id,
        label: squad.name,
      };
    });

    options.unshift(allOption);
    return options;
  }, [squads]);

  const handleSelectSquad = (options) => {
    if (options.length > 0) {
      if (allSquadsSelect) {
        const exist = options.findIndex((option) => option.label === "Todos");
        if (exist >= 0) {
          const newOptions = options.filter((opt) => opt.label !== "Todos");
          setContractFieldState({ field: "selectedSquads", value: newOptions });
          setAllSquadsSelect(false);
          return;
        }
      } else {
        const isAll = options.find((option) => option.label === "Todos");
        if (isAll) {
          setContractFieldState({
            field: "selectedSquads",
            value: [allOption],
          });
          setAllSquadsSelect(true);
          return;
        }
      }
    }

    setContractFieldState({ field: "selectedSquads", value: options });
  };

  return (
    <Col span={span} style={{ zIndex: 100 }}>
      <Text>Squad:</Text>
      <MultiSelect
        options={squadOptions}
        onChange={handleSelectSquad}
        value={selectedSquads}
      />
    </Col>
  );
};
