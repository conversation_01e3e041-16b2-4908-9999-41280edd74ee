import { React, useCallback, useEffect, useState } from "react";
import { <PERSON>, <PERSON>, Pie<PERSON>hart, Sector } from "recharts";
import { Card, Col, Row, Select, Spin, Typography } from "antd";
import { LeftCircleOutlined } from "@ant-design/icons";
import "./ContractsPerWallet.css";
import { useMemo } from "react";
import { CockpitsHeader } from "../../CockpitsHeader";
import { getUpdatedContractHours } from "../../../controllers/cockpit/cockpitController";

function ContractsPerWallet(props) {
  const { Title, Text } = Typography;
  const [activeIndex, setActiveIndex] = useState(0);
  const [isFavorite, setIsFavorite] = useState(false);
  const [colors, setColors] = useState();
  const [allWallets, setAllWallets] = useState([]);
  const [allContracts, setAllContracts] = useState();
  const [filteredContracts, setFilteredContracts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedContract, setSelectedContract] = useState([]);
  const { Option } = Select;
  const [data, setData] = useState([
    {
      name: "Não selecionado.",
      value: 0,
      valueShow: 1,
      fill: colors ? colors[0].color : "#ccc",
    },
  ]);

  let totalHours = useMemo(() => {
    if (selectedContract.length > 0) {
      return Number(selectedContract[0].value);
    }
    let sum = data.reduce((prev, curr) => {
      return prev + Number(curr.value);
    }, 0);
    return sum;
  }, [selectedContract, data]);

  const renderActiveShape = (props) => {
    const { cx, cy, innerRadius, outerRadius, startAngle, endAngle, fill } =
      props;

    return (
      <g>
        <text x={cx} y={cy} dy={-150} textAnchor="middle" fill={"black"}></text>
        <text x={cx} y={cy} dy={-5} textAnchor="middle" fill={"black"}>
          Horas totais
        </text>
        <text x={cx} y={cy} dy={20} textAnchor="middle" fill={"black"}>
          {totalHours}
        </text>

        <Sector
          cx={cx}
          cy={cy}
          innerRadius={innerRadius}
          outerRadius={outerRadius}
          startAngle={startAngle}
          endAngle={endAngle}
          fill={fill}
        />
      </g>
    );
  };

  const GRAPH_NAME = "ContractsPerWallet";
  useEffect(() => {
    setIsFavorite(checkIsVisible());
    getGraphColor();
    getAllInfo();
  }, [props.favorites, props.wallets, props.contracts]);

  const removeDuplicates = (array) => {
    return array.filter((a, b) => {
      return array.findIndex((element) => element.name === a.name) === b;
    });
  };

  function getAllInfo() {
    if (props.wallets && props.contracts) {
      let uniqueWallets = removeDuplicates(props.wallets);
      setAllWallets(uniqueWallets);
      setAllContracts(props.contracts);
      setLoading(false);
    }
  }

  function clearSelectedContract() {
    setSelectedContract([]);
  }

  async function filterData(name) {
    let filteredContractsArray = [];
    Object.entries(allContracts).forEach((val) => {
      if (val[1].document_id === name) {
        val[1].total_hours =
          typeof val[1].total_hours !== "number" ? 0 : val[1].total_hours;
        filteredContractsArray.push(val[1]);
      }
    });

    let contractsWithChangedHours = filteredContractsArray.filter(
      (contract) => contract.hasChangedHours === 1
    );

    if (contractsWithChangedHours.length > 0) {
      let updatedHours = await getUpdatedContractHours(
        contractsWithChangedHours
      );

      updatedHours.forEach((contract) => {
        let index = filteredContractsArray.findIndex(
          (c) => c.id === contract.contract_id
        );
        if (index !== -1) {
          filteredContractsArray[index].total_hours = parseInt(
            contract.new_hours
          );
        }
      });
    }

    setFilteredContracts(filteredContractsArray);
    setFilterData(filteredContractsArray);
  }

  function setFilterData(filteredContractsArray) {
    const newData = [];
    if (filteredContractsArray.length > 0) {
      filteredContractsArray.forEach((val) => {
        newData.push({
          name: val.name,
          valueShow:
            Number(val.total_hours) === 0 && Number(val.total_hours) === 0
              ? 1
              : val.total_hours,
          value: val.total_hours,
          fill: colors ? colors[0].color : generateRandonColor(),
        });
      });
    } else {
      newData.push({
        name: "Sem dados",
        valueShow: 1,
        value: 0,
        fill: "#ccc",
      });
    }

    setSelectedContract([]);
    setData(newData);
  }

  function generateRandonColor() {
    return (
      "#" +
      Math.floor(Math.random() * 0x1000000)
        .toString(16)
        .padStart(6, "0")
    );
  }

  function getGraphColor() {
    let storage_color = JSON.parse(localStorage.getItem("colors"));
    if (storage_color) {
      setColors(storage_color[GRAPH_NAME]);
    }
  }

  function checkIsVisible() {
    let localStorageData = JSON.parse(localStorage.getItem("favorites"));
    if (localStorageData) {
      var index = localStorageData.findIndex(
        (value) => value.component === GRAPH_NAME
      );

      return index !== -1;
    } else {
      return false;
    }
  }

  async function updateFavorite() {
    await props?.setVisionFavorite(GRAPH_NAME);
    setIsFavorite(checkIsVisible());
  }

  const onPieEnter = useCallback(
    (_, index) => {
      setActiveIndex(index);
    },
    [setActiveIndex]
  );

  const onPieClick = (index) => {
    if (index.name !== "Não selecionado." || index.name !== "Sem dados") {
      if (selectedContract.length === 0) {
        let obj = {};
        obj.value = index.name;
        handleClick(obj);
      }
    }
  };

  const sortAlpha = (array) => {
    // Verificar se array é válido e é realmente um array
    if (!array || !Array.isArray(array)) {
      return [];
    }

    const sortedArray = array.sort((a, b) => {
      if (a.name > b.name) {
        return 1;
      }
      if (a.name < b.name) {
        return -1;
      }
      return 0;
    });

    return sortedArray;
  };

  const handleClick = (e) => {
    if (e.value === "Não selecionado." || e.value === "Sem dados") {
      return;
    }

    if (selectedContract.length === 0) {
      let selectedContractData = data?.find((c) => c.name === e.value);

      if (typeof selectedContractData.valueShow !== "number") {
        selectedContractData.valueShow = Number(selectedContractData.valueShow);
      }
      setSelectedContract([selectedContractData]);
    }
  };

  return (
    <Card
      bordered="false"
      style={{
        borderRadius: "20px",
        boxShadow: "0 0 10px rgba(0,0,0,0.1)",
        minWidth: "450px",
        minHeight: "418px",
        alignItems: loading ? "center" : "",
        display: loading ? "flex" : "",
        justifyContent: loading ? "center" : "",
      }}
    >
      {loading ? (
        <Row
          style={{
            height: "100%",
            width: "100%",
            display: "flex",
            justifyContent: "center",
            flexDirection: "column",
          }}
          align="middle"
        >
          <Spin />
          <Text>Carregando...</Text>
        </Row>
      ) : (
        <>
          <CockpitsHeader
            title={
              <Col span={22}>
                <Title level={4} style={{ fontWeight: 400 }}>
                  Contratos por carteira
                </Title>
              </Col>
            }
            isFavorite={isFavorite}
            updateFavorite={updateFavorite}
          />
          <Row>
            <Select
              showSearch
              style={{ width: "100%" }}
              placeholder="Selecionar carteira"
              loading={allWallets.length === 0}
              optionFilterProp="children"
              onChange={(value) => filterData(value)}
              filterOption={(input, option) =>
                option.props.children
                  .toLowerCase()
                  .indexOf(input.toLowerCase()) >= 0 ||
                option.props.value.toLowerCase().indexOf(input.toLowerCase()) >=
                  0
              }
            >
              {sortAlpha(allWallets).map((value, index) => {
                return (
                  <Option key={index} value={value.name}>
                    {value.name}
                  </Option>
                );
              })}
            </Select>
          </Row>
          <div
            className="cardsContainer-per-wallet"
            style={{ position: "relative" }}
          >
            <LeftCircleOutlined
              style={{
                zIndex: "2",
                display: selectedContract.length > 0 ? "" : "none",
                position: "absolute",
                top: "30px",
                left: "10px",
              }}
              onClick={() => clearSelectedContract()}
            />
            <PieChart
              className="graph"
              width={450}
              height={300}
              style={{ zIndex: "1" }}
            >
              <Pie
                activeIndex={activeIndex}
                activeShape={renderActiveShape}
                data={selectedContract.length > 0 ? selectedContract : data}
                startAngle={360}
                endAngle={0}
                cx={120}
                cy={120}
                innerRadius={60}
                outerRadius={100}
                fill="#43914F"
                dataKey="valueShow"
                onClick={(e) => onPieClick(e)}
              ></Pie>
              <Legend
                onClick={(e) => handleClick(e)}
                layout="vetical"
                verticalAlign="bottom"
                align="right"
                style={{ right: "0px" }}
                iconType="circle"
                iconSize={8}
              />
            </PieChart>
          </div>
        </>
      )}
    </Card>
  );
}

export default ContractsPerWallet;
