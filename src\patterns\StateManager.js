import { useState, useEffect, useCallback } from 'react';

/**
 * Observer Pattern Implementation for State Management
 * Follows Observer Pattern and Single Responsibility Principle
 */

/**
 * Subject Interface
 * Defines the contract for observable objects
 */
class Subject {
  constructor() {
    this.observers = new Set();
  }

  /**
   * Add an observer
   */
  subscribe(observer) {
    if (typeof observer !== 'function') {
      throw new Error('Observer must be a function');
    }
    this.observers.add(observer);
    
    // Return unsubscribe function
    return () => this.unsubscribe(observer);
  }

  /**
   * Remove an observer
   */
  unsubscribe(observer) {
    this.observers.delete(observer);
  }

  /**
   * Notify all observers
   */
  notify(data) {
    this.observers.forEach(observer => {
      try {
        observer(data);
      } catch (error) {
        console.error('Error in observer:', error);
      }
    });
  }

  /**
   * Get number of observers
   */
  getObserverCount() {
    return this.observers.size;
  }

  /**
   * Clear all observers
   */
  clearObservers() {
    this.observers.clear();
  }
}

/**
 * State Store Implementation
 * Manages application state with observer pattern
 */
class StateStore extends Subject {
  constructor(initialState = {}) {
    super();
    this.state = { ...initialState };
    this.history = [{ ...initialState }];
    this.maxHistorySize = 50;
  }

  /**
   * Get current state
   */
  getState() {
    return { ...this.state };
  }

  /**
   * Set state and notify observers
   */
  setState(newState) {
    const prevState = { ...this.state };
    
    if (typeof newState === 'function') {
      this.state = { ...this.state, ...newState(prevState) };
    } else {
      this.state = { ...this.state, ...newState };
    }

    // Add to history
    this.addToHistory(this.state);

    // Notify observers
    this.notify({
      type: 'STATE_CHANGE',
      payload: {
        prevState,
        newState: this.state,
        timestamp: new Date().toISOString(),
      }
    });
  }

  /**
   * Update a specific property
   */
  updateProperty(key, value) {
    this.setState({ [key]: value });
  }

  /**
   * Reset state to initial state
   */
  reset(initialState = {}) {
    this.state = { ...initialState };
    this.history = [{ ...initialState }];
    this.notify({
      type: 'STATE_RESET',
      payload: {
        newState: this.state,
        timestamp: new Date().toISOString(),
      }
    });
  }

  /**
   * Add state to history
   */
  addToHistory(state) {
    this.history.push({ ...state });
    
    // Limit history size
    if (this.history.length > this.maxHistorySize) {
      this.history.shift();
    }
  }

  /**
   * Get state history
   */
  getHistory() {
    return [...this.history];
  }

  /**
   * Undo last state change
   */
  undo() {
    if (this.history.length > 1) {
      this.history.pop(); // Remove current state
      const prevState = this.history[this.history.length - 1];
      this.state = { ...prevState };
      
      this.notify({
        type: 'STATE_UNDO',
        payload: {
          newState: this.state,
          timestamp: new Date().toISOString(),
        }
      });
    }
  }

  /**
   * Check if undo is available
   */
  canUndo() {
    return this.history.length > 1;
  }
}

/**
 * Event Bus Implementation
 * Global event system using Observer pattern
 */
class EventBus extends Subject {
  constructor() {
    super();
    this.eventListeners = new Map();
  }

  /**
   * Subscribe to specific event type
   */
  on(eventType, callback) {
    if (!this.eventListeners.has(eventType)) {
      this.eventListeners.set(eventType, new Set());
    }
    
    this.eventListeners.get(eventType).add(callback);
    
    // Return unsubscribe function
    return () => this.off(eventType, callback);
  }

  /**
   * Unsubscribe from specific event type
   */
  off(eventType, callback) {
    if (this.eventListeners.has(eventType)) {
      this.eventListeners.get(eventType).delete(callback);
      
      // Clean up empty event types
      if (this.eventListeners.get(eventType).size === 0) {
        this.eventListeners.delete(eventType);
      }
    }
  }

  /**
   * Emit an event
   */
  emit(eventType, data) {
    // Notify specific event listeners
    if (this.eventListeners.has(eventType)) {
      this.eventListeners.get(eventType).forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`Error in event listener for ${eventType}:`, error);
        }
      });
    }

    // Notify global observers
    this.notify({
      type: eventType,
      payload: data,
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * Get all event types
   */
  getEventTypes() {
    return Array.from(this.eventListeners.keys());
  }

  /**
   * Clear all listeners for an event type
   */
  clearEventListeners(eventType) {
    if (eventType) {
      this.eventListeners.delete(eventType);
    } else {
      this.eventListeners.clear();
    }
  }
}

/**
 * Singleton State Manager
 * Combines StateStore and EventBus
 */
class StateManager {
  constructor() {
    if (StateManager.instance) {
      return StateManager.instance;
    }

    this.stores = new Map();
    this.eventBus = new EventBus();
    StateManager.instance = this;
  }

  static getInstance() {
    if (!StateManager.instance) {
      StateManager.instance = new StateManager();
    }
    return StateManager.instance;
  }

  /**
   * Create or get a store
   */
  createStore(name, initialState = {}) {
    if (!this.stores.has(name)) {
      this.stores.set(name, new StateStore(initialState));
    }
    return this.stores.get(name);
  }

  /**
   * Get a store
   */
  getStore(name) {
    return this.stores.get(name);
  }

  /**
   * Remove a store
   */
  removeStore(name) {
    const store = this.stores.get(name);
    if (store) {
      store.clearObservers();
      this.stores.delete(name);
    }
  }

  /**
   * Get event bus
   */
  getEventBus() {
    return this.eventBus;
  }

  /**
   * Get all store names
   */
  getStoreNames() {
    return Array.from(this.stores.keys());
  }
}

/**
 * React Hook for using state store
 */
export const useStateStore = (storeName, initialState = {}) => {
  const stateManager = StateManager.getInstance();
  const store = stateManager.createStore(storeName, initialState);
  
  const [state, setState] = useState(store.getState());

  useEffect(() => {
    const unsubscribe = store.subscribe((event) => {
      if (event.type === 'STATE_CHANGE' || event.type === 'STATE_RESET' || event.type === 'STATE_UNDO') {
        setState(event.payload.newState);
      }
    });

    return unsubscribe;
  }, [store]);

  const updateState = useCallback((newState) => {
    store.setState(newState);
  }, [store]);

  const updateProperty = useCallback((key, value) => {
    store.updateProperty(key, value);
  }, [store]);

  const resetState = useCallback((newInitialState) => {
    store.reset(newInitialState);
  }, [store]);

  const undo = useCallback(() => {
    store.undo();
  }, [store]);

  return {
    state,
    setState: updateState,
    updateProperty,
    resetState,
    undo,
    canUndo: store.canUndo(),
    getHistory: () => store.getHistory(),
  };
};

/**
 * React Hook for using event bus
 */
export const useEventBus = () => {
  const stateManager = StateManager.getInstance();
  const eventBus = stateManager.getEventBus();

  const emit = useCallback((eventType, data) => {
    eventBus.emit(eventType, data);
  }, [eventBus]);

  const on = useCallback((eventType, callback) => {
    return eventBus.on(eventType, callback);
  }, [eventBus]);

  useEffect(() => {
    // Cleanup function to remove listeners on unmount
    return () => {
      // Note: Individual listeners should be cleaned up by components
    };
  }, []);

  return { emit, on };
};

/**
 * React Hook for listening to specific events
 */
export const useEventListener = (eventType, callback, dependencies = []) => {
  const { on } = useEventBus();

  useEffect(() => {
    const unsubscribe = on(eventType, callback);
    return unsubscribe;
  }, [eventType, on, ...dependencies]);
};

// Export singleton instance
export const stateManager = StateManager.getInstance();

export default StateManager;
