import "../TotalProjects/index.css"
import {useState} from "react";
import {ProjectPerStatus} from "../ProjectPerStatus";
import { Typography, Card, Row, Col } from "antd";

export const TotalProjects = () => {
    const {Title, Text } = Typography;
    const [projectVision, setProjectVision] = useState(false);
    const [projectSelected, setProjectSelected] = useState({});
    
    const projectsInfo = [
        {
            status: 'Novo',
            amount: 6
        },
        {
            status: 'Encerramento',
            amount: 6
        },
        {
            status: 'Paralizado',
            amount: 6
        },
        {
            status: 'Planejamento e inicialização',
            amount: 6
        },
        {
            status: 'Entrega',
            amount: 6
        },
        {
            status: 'Atendimento',
            amount: 6
        },
    ]

    function toggleVision() {
        setProjectVision(!projectVision)
    }

    function selectStatusProject(projects) {
        setProjectSelected(projects)
        toggleVision()
    }

    return (
        <Card bordered={false} style={{borderRadius: '20px', height: '100%', boxShadow: "0 0 10px rgba(0,0,0,0.1)"}}>
                {!projectVision ? (
                    <>
                        <Title level={4} style={{fontWeight: 400}}>Total projetos</Title>
                        <Row>
                            {projectsInfo.map((projects) => {
                                return (
                                    <Col lg={12} sm={24} onClick={() => selectStatusProject(projects)}>
                                        <Card hoverable style={{borderRadius: '8px', margin: '5px 5px'}}>
                                        <Row justify="space-evenly" align="middle">
                                            <Col span={10}>
                                                <p className="circle">{projects.amount}</p>
                                            </Col>
                                            <Col span={12}>
                                                <Text>{projects.status}</Text>
                                            </Col>
                                        </Row>
                                        </Card>
                                    </Col>
                                )
                            })}
                        </Row>
                    </>
                ) : (
                    <ProjectPerStatus data={projectSelected} toggleVision={toggleVision}/>
                )}
        </Card>
    )
}