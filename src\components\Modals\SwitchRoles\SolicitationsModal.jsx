import {
  Row,
  Tag,
  Modal,
  Table,
  Button,
  message,
  Typography,
  Tooltip,
} from "antd";
import { useMemo, useState, useCallback } from "react";
import { TeamOutlined } from "@ant-design/icons";
import Axios from "axios";
import { otrsPut } from "../../../service/apiOtrs";
import { logNewAuditAction } from "../../../controllers/audit/logNewAuditAction";
import { filterTableData } from "../../../utils/filterTableData";
import { SolicitationsModalHeader } from "./components/modalHeader";
import { filterTableColumns } from "../../../utils/filterTableColumns";
import { revokeSolicitationFromPreviousApi } from "../../../service/previousModelSwitchRoleAPI";
import {
  formatInvokeStateMachineBody,
  getPermissionSetFromUser,
} from "./controller/AllowAccess";
import { shallowEqual, useSelector } from "react-redux";
import * as switchRoleController from "../../../pages/SwitchRoles/controllers/index";
import { sleep } from "../../../utils/sleep";
import { OTRSTicketNumberRedirector } from "../../../pages/SwitchRoles/components";
import {
  ActionsSwitchRoleCell,
  SolicitationTimeTag,
} from "../../../components/Modals/SwitchRoles/components";
import { formatSwitchRoleData } from "./controller/SolicitationsModal";
export const SolicitationsModal = (props) => {
  const permissionSets = useSelector(
    (state) => state.switchRole.permissionSets,
    shallowEqual
  );
  const { solicitations, client, clients, permissions, origin = "" } = props;
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [isMultipleAccounts, setIsMultipleAccounts] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [loading, setLoading] = useState(false);
  const [search, setSearch] = useState("");
  const [state, setState] = useState("");
  const [currentSolicitation, setCurrentSolicitation] = useState();
  const { Text } = Typography;

  const confirm = useCallback(
    async (account, actionType) => {
      setLoading(true);
      if (permissionSets.find((x) => x.user === account.username)) {
        try {
          await revokeSolicitationFromPreviousApi(account);
          await switchRoleController.getAllSwitchRoleData(permissionSets);
          setShowModal(false);
        } catch (e) {
          message.error("Houve um erro ao revogar o acesso");
        }
      } else {
        let permissionSetAttachedToUser = getPermissionSetFromUser(
          permissionSets,
          account
        );
        console.log(permissionSetAttachedToUser);
        let formattedRevokeBody = formatInvokeStateMachineBody({
          formData: account,
          data: account,
          permissionSetAttachedToUser,
        });
        await Axios.post(
          process.env.REACT_APP_API_PERMISSION + "state-machine",
          formattedRevokeBody,
          {
            headers: {
              authorization: localStorage.getItem("jwt"),
            },
          }
        )
          .then(async ({ data }) => {
            try {
              otrsPut("close/ticket", {
                title: "Acesso removido em Switch Role",
                from: "DSM",
                description: `Acesso do usuário ${account.username} na conta ${account.account_id} removido.`,
                tktNumber: parseInt(account.client_ticket),
              });
            } catch (error) {
              console.log(error);
            }
            setLoading(false);

            await sleep(2000);

            await switchRoleController.getAllSwitchRoleData(permissionSets);
          })
          .catch((error) => {
            setLoading(false);
            message.error("Erro ao remover acesso...");
          });
      }
      message.success("Acesso removido com sucesso!");
      const username = localStorage.getItem("@dsm/username");
      const title = "Revogação de Acesso";
      const description = `${username} revogou o acesso do usuário ${account.username} à conta ${account.account_id}`;
      logNewAuditAction(username, title, description);
    },
    [permissionSets]
  );

  const columns = useMemo(() => {
    return [
      {
        code: "view_actions_ticket",
        key: "ticket_id_soc",
        dataIndex: "ticket_id",
        title: "Ticket SOC",
        render: (ticket) => <OTRSTicketNumberRedirector ticket={ticket} />,
      },
      {
        code: "view_customer_from_account",
        dataIndex: "client_name",
        key: "client_name",
        title: "Cliente",
        render: (client_name) => {
          client_name =
            client_name?.charAt(0).toUpperCase() + client_name?.slice(1);
          return <Text>{client_name}</Text>;
        },
      },
      {
        code: "view_actions_account",
        key: "account_id",
        dataIndex: "account_id",
        title: "Conta",
      },
      {
        code: "view_actions_user",
        key: "username",
        dataIndex: "username",
        title: "Usuário",
      },
      {
        code: "view_actions_time",
        key: "time",
        dataIndex: "time",
        title: "Tempo",
        width: "1%",
        render: (time, item) => {
          return (
            <Row justify="center">
              {time
                ? parseInt(time) / 60 + "h"
                : parseInt(item.requestedTime) / 60 + "h"}
            </Row>
          );
        },
      },
      {
        code: "view_actions_allowed",
        dataIndex: "allowed",
        key: "allowed",
        title: "Permitido",
        align: "center",
        width: "1%",
        render: (_, item) => (
          <Tag color={item.allowed ? "green" : "red"}>
            {item.allowed ? "Permitido" : "Não Permitido"}
          </Tag>
        ),
      },
      {
        code: "view_actions_active",
        dataIndex: "active",
        key: "active",
        title: "Ativo",
        width: "1%",
        render: (a, item) => <SolicitationTimeTag active={a} item={item} />,
      },
      {
        code: "view_actions_accept_deny",
        dataIndex: "id",
        title: "Ações",
        key: "actions",
        width: "1%",
        render: (user, item) => (
          <ActionsSwitchRoleCell
            item={item}
            confirm={confirm}
            setCurrentSolicitation={setCurrentSolicitation}
            permissionSets={permissionSets}
            origin={origin}
          />
        ),
      },
    ];
  }, [permissionSets]);

  const tableData = useMemo(() => {
    let data =
      origin === "client"
        ? formatSwitchRoleData(solicitations, clients)
        : solicitations || [];
    let filteredData = solicitations || [];

    if (state) {
      if (state === "active")
        return (filteredData = filteredData.filter(
          (item) => item.active === "Aprovado"
        ));
      else if (state === "inactive")
        return (filteredData = filteredData.filter(
          (item) => item.active === "Solicitado"
        ));
      else return filteredData;
    }
    filteredData = filterTableData({
      searchFields: ["account_id", "username", "client_name"],
      search,
      data,
    });
    return filteredData;
  }, [search, solicitations, showModal, state]);

  const tableColumns = useMemo(() => {
    if (origin === "client") return columns;
    else return filterTableColumns(columns, permissions);
  }, [permissions, isMultipleAccounts, columns]);

  const rowSelection = {
    type: "checkbox",
    selectedRowKeys,
    onChange: (selectedRowKeys, selectedRows) => {
      setSelectedRowKeys(selectedRowKeys);
    },

    getCheckboxProps: (record) => ({
      account_id: record.account_id,
    }),
  };

  return (
    <>
      <Tooltip
        title="Visualizar acessos de contas solicitadas"
        placement="left"
      >
        <Button
          type="primary"
          onClick={async () => {
            setShowModal(true);
          }}
        >
          <TeamOutlined />
        </Button>
      </Tooltip>
      <Modal
        title={
          client.client_name
            ? "Solicitações para acessar - " + client.client_name
            : "Seus acessos"
        }
        style={{ minWidth: "65vw" }}
        open={showModal}
        onCancel={() => {
          setShowModal(false);
        }}
        closable={false}
        footer={[
          <Button type="primary" onClick={() => setShowModal(false)}>
            Fechar
          </Button>,
        ]}
      >
        <SolicitationsModalHeader
          setSearch={setSearch}
          isMultipleAccounts={isMultipleAccounts}
          setState={setState}
          setIsMultipleAccounts={setIsMultipleAccounts}
          origin={origin}
          selectedRowKeys={selectedRowKeys}
          revokeFunction={confirm}
          tableData={tableData}
          setSelectedRowKeys={(keys) => setSelectedRowKeys(keys)}
        />
        <Table
          scroll={{ x: "100%" }}
          rowSelection={isMultipleAccounts ? rowSelection : null}
          dataSource={tableData}
          rowKey={(record) => record}
          columns={tableColumns}
        />
      </Modal>
    </>
  );
};
