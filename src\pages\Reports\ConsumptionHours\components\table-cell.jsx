import React, { useEffect } from "react";

import { Typography } from "antd";

import * as controller from "../../../../controllers/reports/surplus-hours-controller";
import { useSelector, shallowEqual } from "react-redux";

export const TableCell = ({ total, data }) => {
  const contractsWithChangedHours = useSelector(
    (state) => state.consumption.updatedHoursContracts,
    shallowEqual
  );
  const { Text } = Typography;

  useEffect(() => {
    async function fetch() {
      await controller.getUpdatedHours([data], contractsWithChangedHours);
    }

    if (data?.hasChangedHours) {
      fetch();
    }
  }, [data]);

  const renderCellContent = (total, data, contractsWithChangedHours) => {
    if (data?.hasChangedHours) {
      let currentContractChangedHoursData = contractsWithChangedHours.filter(
        (contract) => contract.contract_id === data.id
      );

      if (currentContractChangedHoursData.length === 0) {
        return <Text>{controller.formactMinutosToHours(total)}</Text>;
      }

      let [periodYear, periodMonth] = data.period.split("-");
      let currentDataPeriod = new Date(periodYear, periodMonth - 1).getTime();

      currentContractChangedHoursData.sort((a, b) => {
        return (
          new Date(a.updated_consumption_hours).getTime() -
          new Date(b.updated_consumption_hours).getTime()
        );
      });

      let applicableTotal = total;

      for (let i = 0; i < currentContractChangedHoursData.length; i++) {
        let update = currentContractChangedHoursData[i];

        let [updateMonth, updateYear] = update.full_date.split("-");
        let formattedDate = `${updateYear}-${updateMonth}-01`;
        let updateTime = new Date(formattedDate).getTime();

        if (currentDataPeriod >= updateTime) {
          applicableTotal = update.new_hours * 60;
        }
      }

      return <Text>{controller.formactMinutosToHours(applicableTotal)}</Text>;
    } else {
      return <Text>{controller.formactMinutosToHours(total)}</Text>;
    }
  };

  return (
    <p style={{ width: "80px", fontSize: "14px" }}>
      {renderCellContent(total, data, contractsWithChangedHours)}
    </p>
  );
};
