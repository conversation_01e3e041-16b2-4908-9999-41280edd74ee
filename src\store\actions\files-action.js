import {
  setSelectedFileFilesReduce,
  setSelectedFileFieldReduce,
  setUploadFileFilesReduce,
  setFilterStateFilesReduce,
  setUploadReadyFilesReduce,
  setReloadFilesDataFilesReduce,
  setDateRangeFilesReduce,
  setAllTagsFilesReduce,
  setAllFilesReduce,
  setFileContentFilesReduce,
  setEnableFileUpdateTagsReduce,
  setEnableFileUpdateNameReduce,
  setCategoryFilesReduce,
  setTagsFilesReduce,
  setEmailsFilesReduce,
  setCustomerFilesReduce,
  cleanSelectedFileReduce,
  cleanUploadFileReduce,
  cleanFilesReduce,
  setUploadFilesStateReduce,
  cleanCloseModalReduce,
  cleanAllReduce,
} from "../reducers/files-reducer";

import { store } from "../store";

export const setAllFilesState = (payload) => {
  store.dispatch(setAllFilesReduce(payload));
};

export const setFilterStateFilesState = (payload) => {
  store.dispatch(setFilterStateFilesReduce(payload));
};

export const setDateRangeFilesState = (payload) => {
  store.dispatch(setDateRangeFilesReduce(payload));
};

export const setUploadReadyFilesState = (payload) => {
  store.dispatch(setUploadReadyFilesReduce(payload));
};

export const setReloadFilesDataFilesState = (payload) => {
  store.dispatch(setReloadFilesDataFilesReduce(payload));
};

export const setAllTagsFilesState = (payload) => {
  store.dispatch(setAllTagsFilesReduce(payload));
};

export const setSelectedFileFilesState = (payload) => {
  store.dispatch(setSelectedFileFilesReduce(payload));
};

export const setSelectedFileFieldState = (payload) => {
  store.dispatch(setSelectedFileFieldReduce(payload));
};

export const setUploadFileFilesState = (payload) => {
  store.dispatch(setUploadFileFilesReduce(payload));
};

export const setFileContentFilesState = (payload) => {
  store.dispatch(setFileContentFilesReduce(payload));
};

export const setEnableFileUpdateTagsState = (payload) => {
  store.dispatch(setEnableFileUpdateTagsReduce(payload));
};

export const setEnableFileUpdateNameState = (payload) => {
  store.dispatch(setEnableFileUpdateNameReduce(payload));
};

export const setCategoryState = (payload) => {
  store.dispatch(setCategoryFilesReduce(payload));
};

export const setTagsState = (payload) => {
  store.dispatch(setTagsFilesReduce(payload));
};

export const setEmailsState = (payload) => {
  store.dispatch(setEmailsFilesReduce(payload));
};

export const setCustomerState = (payload) => {
  store.dispatch(setCustomerFilesReduce(payload));
};

export const cleanSelectedFileState = () => {
  store.dispatch(cleanSelectedFileReduce());
};
export const cleanUploadFileState = () => {
  store.dispatch(cleanUploadFileReduce());
};
export const cleanFilesState = () => {
  store.dispatch(cleanFilesReduce());
};

export const setUploadFilesState = (payload) => {
  store.dispatch(setUploadFilesStateReduce(payload));
};

export const cleanCloseModalState = (payload) => {
  store.dispatch(cleanCloseModalReduce(payload));
};

export const cleanAllState = (payload) => {
  store.dispatch(cleanAllReduce(payload));
};
