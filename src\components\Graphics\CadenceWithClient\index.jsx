import { FilePdfOutlined, FilterOutlined, StarFilled, StarOutlined } from "@ant-design/icons";
import {React, useState,useEffect} from 'react';
import { Popover } from "antd";
import './CadenceWithClient.css';
import CadenceGraphFilter from "../../Modals/Filters/CadenceGraphFilter";

function CadenceCard(){
  return(
    <div className="cadence-card-global">
      <p className="cadence-date">Terça 15/10</p>
      <div className="cadence-card">
        <div className="cadence-left-infos">
          <div className="cadence-left-single">
            <p>Inicio</p>
            <p className="cadence-left cadence-left-green">15:00</p>
          </div>
          <div className="cadence-left-single">
            <p>Término</p>
            <p className="cadence-left cadence-left-gray">15:00</p>
          </div>
        </div>
        <div className="center">
          <h3>Reunião com SDMs da Darede</h3>
        </div>
        <div className="right-icons">
          <FilePdfOutlined className="right-icons-single"/>
          <FilePdfOutlined  className="right-icons-single"/>
          <FilePdfOutlined  className="right-icons-single" />
        </div>
      </div>
    </div>
  )
}

export default function CadenceWithClient(props){
  const [isFavorite,setIsFavorite] = useState(false);
  const [popoverVisibility, setPopoverVisibility] = useState(false)
  const GRAPH_NAME = "CadenceWithClient";

  function checkIsVisible(){
    let localStorageData = JSON.parse(localStorage.getItem('favorites'));

    if(localStorageData){
      var index = localStorageData.findIndex(value => value.component ===GRAPH_NAME)
    
     return index !== -1
    }else {
      return false;
    }
  }

  useEffect(()=>{
    setIsFavorite(checkIsVisible());
  },[props.favorites])

  async function updateFavorite(){
    await props?.setVisionFavorite(GRAPH_NAME);
    setIsFavorite(checkIsVisible());
  }

  function handlePopoverVisibility() {
    setPopoverVisibility(!popoverVisibility)
  }

  return(
    <div style={{padding:"0px 10px"}} className="card">
      <div className="card-header">
        <h3 className="card-title">Cadência com clientes</h3>
        <div className="cadence-icons">
        <Popover
          visible={popoverVisibility}
          onClick={handlePopoverVisibility}
                    placement="left"
                    title={"Cadência com clientes"}
                    content={() => {
                      return (
                        <CadenceGraphFilter
                          handlePopoverVisibility={handlePopoverVisibility}
                        />
                      );
                    }}
                    trigger="click"
                    >
                    <div
                      style={{
                        display: "flex",
                        textAlign: "center",
                        cursor: "pointer",
                        gap: "5px",
                      }}
                      className="filter"
                    >
                      <FilterOutlined className="filter-icon" />
                    </div>

                  </Popover>
            {
              (!isFavorite)?
              <StarOutlined  onClick={updateFavorite}  className="star-icon"/>
              :
              <StarFilled  onClick={updateFavorite} className="star-icon"/>
            }
        </div>
      </div>
      <div className="cadence-cards">
        <CadenceCard/>
        <CadenceCard/>
        <CadenceCard/>  
      </div>
    </div>
  )
}