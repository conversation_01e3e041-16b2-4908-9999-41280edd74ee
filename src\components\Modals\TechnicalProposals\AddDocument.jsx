import { useEffect, useState, useContext } from "react";
import axios from "axios";
import {
  <PERSON><PERSON>,
  Modal,
  Row,
  Col,
  Table,
  message,
  Popconfirm,
  Tooltip,
  Typography,
  Alert,
  Badge,
} from "antd";
import {
  PaperClipOutlined,
  DeleteOutlined,
  CheckOutlined,
  DownloadOutlined,
} from "@ant-design/icons";
import { s3UploadObj } from "../../../service/apiDsmS3";
import { ProposalContext } from "../../../contexts/totalValuesProposals";
import { TotalValuesProposalType } from "../../../contexts/reducers/totalValuesProposal";
import { v4 } from "uuid";
import { getFilesToBeDeleted } from "../../../controllers/Proposals/getFilesToBeDeleted";

const FileDownload = require("js-file-download");

async function getBase64(file) {
  const base64string = await new Promise((resolve) => {
    let reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = function () {
      resolve(reader.result);
    };
    reader.onerror = function (error) {
      resolve("");
    };
  });
  return base64string;
}

export const AddDocument = () => {
  const [showModal, setShowModal] = useState();
  const [loading, setLoading] = useState(false);
  const { proposalState, setProposalState } = useContext(ProposalContext);
  const [supportArray, setSupportArray] = useState([]);
  const [fileList, setFileList] = useState([]);
  const [fileId, setFileId] = useState(1);
  const [files, setFiles] = useState([]);
  const [notUploadedFilesId, setNotUploadedFilesId] = useState([]);

  const { Text } = Typography;

  const idFixed = proposalState.idFixed === "" ? v4() : proposalState.idFixed;

  useEffect(() => {
    setSupportArray(proposalState.fileNames);

    if (proposalState?.fileNames.length > 0) {
      const nextId =
        proposalState?.fileNames[proposalState.fileNames?.length - 1]?.id + 1;
      setFileId(nextId);
    }
  }, [proposalState]);

  const handleRemoveItem = (index, item) => {
    const prevList = proposalState.fileNames;
    const arr = [];
    for (let i = 0; i < prevList.length; i++) {
      if (prevList[i].id !== item.id) {
        arr.push(prevList[i]);
      }
    }
    setProposalState({
      type: TotalValuesProposalType.SET_FILE_NAMES,
      value: arr,
    });
  };

  const handleFileSelect = () => {
    const file = document.getElementById("file").files[0];
    if (file) {
      setFiles((state) => [...state, file]);

      let exists = false;
      const addedFile = {
        id: fileId,
        lastModified: file.lastModified,
        name: file.name,
        size: file.size,
        type: file.type,
      };

      setNotUploadedFilesId((state) => [...state, fileId]);

      const newId = fileId + 1;
      setFileId(newId);

      for (let i = 0; i < proposalState.fileNames.length; i++) {
        if (proposalState.fileNames[i].name === addedFile.name) {
          exists = true;
        }
      }

      if (exists === false) {
        const arr = supportArray;
        arr.push(addedFile);
        setProposalState({
          type: TotalValuesProposalType.SET_FILE_NAMES,
          value: arr,
        });
      }
    } else {
      message.error("Selecione um arquivo");
    }
  };

  const handleOk = async (file) => {
    setLoading(true);

    setNotUploadedFilesId([]);

    setProposalState({
      type: TotalValuesProposalType.SET_ID_FIXED,
      value: idFixed,
    });

    files?.map(async (currentFile) => {
      let file_format = currentFile.name.split(".")[1];
      let name = `${idFixed}/documents/${currentFile.name}`;

      const fileToTransform = files.find(
        (file) => file.name === currentFile.name
      );

      if (fileToTransform) {
        const base64obj = await getBase64(fileToTransform);

        const payload = {
          bucketName: `${process.env.REACT_APP_STAGE}-proposals-documents`,
          obj: base64obj,
          fileName: name,
          contentType: `${currentFile.type}`,
        };

        try {
          await s3UploadObj(payload);
        } catch (error) {
          console.log({ error });
          message.error("Houve um erro ao fazer o upload dos arquivos!");
        }
      }
    });

    message.success("Upload dos arquivos feito com sucesso!");

    setLoading(false);
    setShowModal(false);
  };

  const handleGet = async (file) => {
    setLoading(true);
    setShowModal(true);
    let name = `${proposalState.idFixed}/documents/${file}`;

    const {
      data: { data },
    } = await axios.post(
      `${process.env.REACT_APP_API_PERMISSION}s3/link`,
      {
        operation: "get",
        key: encodeURI(name),
        bucket: `${process.env.REACT_APP_STAGE}-proposals-documents`,
      },
      {
        headers: {
          authorization: localStorage.getItem("jwt"),
        },
      }
    );

    axios
      .get(data, {
        responseType: "blob",
      })
      .then((response) => {
        FileDownload(response.data, `${file}`);
        setLoading(false);
      })
      .catch((err) => {
        setLoading(false);
        message.error("Erro ao tentar fazer download! ", { err });
      });

    setShowModal(false);
  };

  const handleOpen = () => {
    setShowModal(true);
  };

  const handleCancel = async () => {
    setLoading(true);

    const filesToBeDeleted = await getFilesToBeDeleted(
      proposalState.idFixed,
      proposalState.fileNames,
      false,
      "/documents"
    );

    const newFiles = proposalState.fileNames.filter(
      (f) => !filesToBeDeleted.includes(f)
    );

    setProposalState({
      type: TotalValuesProposalType.SET_FILE_NAMES,
      value: newFiles,
    });

    setShowModal(false);

    setLoading(false);
  };

  const columns = [
    {
      title: "ID",
      dataIndex: "id",
      key: "id",
      render: (id, item) => {
        return <Text>{item.id}</Text>;
      },
    },
    {
      title: "Nome do arquivo",
      dataIndex: "file_name",
      key: "file_name",
      render: (id, item) => {
        return <Text>{item.name}</Text>;
      },
    },
    {
      title: "Tipo do Arquivo",
      dataIndex: "file_type",
      key: "file_type",
      align: "center",
      render: (id, item) => {
        return <Text>{item.type}</Text>;
      },
    },
    {
      title: "Download",
      dataIndex: "download",
      key: "download",
      align: "center",
      render: (id, item) => {
        return (
          <Button
            type="text"
            onClick={() => {
              handleGet(item.name);
            }}
            disabled={notUploadedFilesId.find((id) => id === item.id)}
          >
            <DownloadOutlined />
          </Button>
        );
      },
    },
    {
      title: "Remover",
      dataIndex: "remove",
      key: "remove",
      render: (id, item, index) => {
        return (
          <Button
            danger
            type="text"
            onClick={() => {
              handleRemoveItem(index, item);
            }}
          >
            <DeleteOutlined />
          </Button>
        );
      },
    },
  ];

  return (
    <>
      <Badge count={proposalState?.fileNames?.length} title="Arquivos salvos">
        <Button type="text-color" onClick={handleOpen}>
          Anexos
          <PaperClipOutlined />
        </Button>
      </Badge>
      <Modal
        title="Anexos"
        open={showModal}
        closable={false}
        width={"60%"}
        footer={[
          <Row justify="space-between">
            <Popconfirm
              title="Ao cancelar, você perderá os arquivos não salvos, tem certeza?"
              okText="Sim"
              cancelText="Não"
              onConfirm={() => handleCancel()}
            >
              <Button type="text-color">
                Cancelar <DeleteOutlined />
              </Button>
            </Popconfirm>
            <Tooltip
              title={
                proposalState?.fileNames?.length > 0
                  ? ""
                  : "Adicione arquivos para habilitar"
              }
            >
              <Button
                disabled={proposalState?.fileNames?.length > 0 ? false : true}
                type="primary"
                onClick={() => handleOk(proposalState?.fileNames)}
              >
                Salvar <CheckOutlined />
              </Button>
            </Tooltip>
          </Row>,
        ]}
      >
        <Row
          style={{ display: "flex", flexDirection: "column" }}
          justify="center"
        >
          <Table
            dataSource={proposalState?.fileNames?.map((e) => {
              return e;
            })}
            columns={columns}
            scroll={{ x: 100 }}
            pagination={true}
            loading={loading}
          ></Table>

          <Row justify="center">
            <Button type="dashed" style={{ marginTop: "20px" }}>
              <label htmlFor="file">Selecionar anexo</label>
            </Button>
          </Row>
          <Col>
            <input
              type="file"
              name="file"
              id="file"
              onChange={handleFileSelect}
              style={{ display: "none" }}
            />
          </Col>

          <Row justify="center" style={{ marginTop: "1rem" }}>
            <Col>
              <Alert
                type="warning"
                showIcon
                message={"O tamanho máximo permitido do arquivo é de 2MB"}
              />
            </Col>
          </Row>
        </Row>
      </Modal>
    </>
  );
};
