import {
  setInvoiceFieldReduce,
  cleanInvoiceReduce,
  setInvoicesDateRangeReduce,
} from "../reducers/invoices-reducer";

import { store } from "../store";

export const setInvoiceFieldState = (payload) => {
  store.dispatch(setInvoiceFieldReduce(payload));
};

export const setInvoicesDateRange = (payload) => {
  store.dispatch(setInvoicesDateRangeReduce(payload));
};

export const cleanInvoiceState = (payload) => {
  store.dispatch(cleanInvoiceReduce(payload));
};
