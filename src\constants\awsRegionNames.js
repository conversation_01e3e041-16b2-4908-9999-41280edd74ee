export const awsRegionNames = {
  "us-east-1": "US East (N. Virginia)",
  "us-east-2": "US East (Ohio)",
  "us-west-1": "US West (N. California)",
  "us-west-2": "US West (Oregon)",
  "ca-central-1": "Canada (Central)",
  "eu-west-1": "EU (Ireland)",
  "eu-central-1": "EU (Frankfurt)",
  "eu-west-2": "EU (London)",
  "eu-west-3": "EU (Paris)",
  "eu-north-1": "EU (Stockholm)",
  "ap-northeast-1": "Asia Pacific (Tokyo)",
  "ap-northeast-2": "Asia Pacific (Seoul)",
  "ap-southeast-1": "Asia Pacific (Singapore)",
  "ap-southeast-2": "Asia Pacific (Sydney)",
  "ap-south-1": "Asia Pacific (Mumbai)",
  "sa-east-1": "South America (São Paulo)",
  "us-gov-west-1": "US Gov West 1",
  "us-gov-east-1": "US Gov East 1",
};

export const awsRegionsArr = [
  { code: "us-east-1", name: "US East (N. Virginia)" },
  { code: "us-east-2", name: "US East (Ohio)" },
  { code: "us-west-1", name: "US West (N. California)" },
  { code: "us-west-2", name: "US West (Oregon)" },
  { code: "ca-central-1", name: "Canada (Central)" },
  { code: "eu-west-1", name: "EU (Ireland)" },
  { code: "eu-central-1", name: "EU (Frankfurt)" },
  { code: "eu-west-2", name: "EU (London)" },
  { code: "eu-west-3", name: "EU (Paris)" },
  { code: "eu-north-1", name: "EU (Stockholm)" },
  { code: "ap-northeast-1", name: "Asia Pacific (Tokyo)" },
  { code: "ap-northeast-2", name: "Asia Pacific (Seoul)" },
  { code: "ap-southeast-1", name: "Asia Pacific (Singapore)" },
  { code: "ap-southeast-2", name: "Asia Pacific (Sydney)" },
  { code: "ap-south-1", name: "Asia Pacific (Mumbai)" },
  { code: "sa-east-1", name: "South America (São Paulo)" },
  { code: "us-gov-west-1", name: "US Gov West 1" },
  { code: "us-gov-east-1", name: "US Gov East 1" },
  { code: "global", name: "Global" },
];
