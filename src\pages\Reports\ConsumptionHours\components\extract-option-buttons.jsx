import { Col, But<PERSON> } from "antd";

import { FileExcelOutlined, FilePdfOutlined } from "@ant-design/icons";

import { TableExcel } from "../table-excel";

import * as controller from "../../../../controllers/reports/surplus-hours-controller";
import { shallowEqual, useSelector } from "react-redux";
import { logNewAuditAction } from "../../../../controllers/audit/logNewAuditAction";

export const ExtractOptionButtons = ({ loading }) => {
  const filteredContracts = useSelector(
    (state) => state.consumption.filteredContracts,
    shallowEqual
  );

  return (
    <>
      <ButtonContainer>
        <Button
          type="primary"
          danger
          style={{ width: "100%" }}
          icon={<FilePdfOutlined />}
          onClick={() => {
            controller.generatePDF(filteredContracts);
            const username = localStorage.getItem("@dsm/username");
            const title = "Download de Consumo de Horas (PDF)";
            const description = `${username} fez o download do consumo de horas em PDF`;
            logNewAuditAction(username, title, description);
          }}
          disabled={loading}
        >
          PDF
        </Button>
      </ButtonContainer>

      <ButtonContainer>
        <TableExcel data={filteredContracts}>
          <Button
            type="primary"
            style={{ width: "100%" }}
            icon={<FileExcelOutlined />}
            onClick={() => {
              const username = localStorage.getItem("@dsm/username");
              const title = "Download de Consumo de Horas (EXCEL)";
              const description = `${username} fez o download do consumo de horas em Excel`;
              logNewAuditAction(username, title, description);
            }}
            disabled={loading}
          >
            Excel
          </Button>
        </TableExcel>
      </ButtonContainer>
    </>
  );
};

const ButtonContainer = ({ children }) => {
  return (
    <Col
      xl={{ span: 3, offset: 1 }}
      lg={{ span: 3, offset: 1 }}
      md={{ span: 3 }}
      sm={{ span: 6 }}
      style={{
        alignItems: "flex-end",
        display: "flex",
        minWidth: 80,
        marginBottom: 10,
      }}
    >
      {children}
    </Col>
  );
};
