import React, { useEffect, useMemo, useState } from "react";
import { SideMenu } from "../../components/SideMenu";
import { HeaderMenu } from "../../components/HeaderMenu";
import { Layout, Card, Row, Col, Input, DatePicker, Typography } from "antd";
import { dynamoGetById } from "../../service/apiDsmDynamo";
import { Table } from "antd";
import { format, getMonth, getYear } from "date-fns";
import useSWR from "swr";
import axios from "axios";
import moment from "moment";
import { Counter } from "../../components/Counter";

const { Content } = Layout;

export const Audit = () => {
  const [collapsed, setCollapsed] = useState(false);
  const [loading, setLoading] = useState(false);
  const [search, setSearch] = useState("");
  const [date, setDate] = useState(() => {
    const savedDate = localStorage.getItem("auditsDate");
    if (savedDate) {
      const parsedDate = new Date(savedDate);
      // Verificar se a data é válida
      if (!isNaN(parsedDate.getTime())) {
        return parsedDate;
      }
    }
    return new Date();
  });
  const { Text } = Typography;

  useEffect(() => {
    window.scrollTo({ left: 0, top: 0, behavior: "smooth" });
  }, []);

  const onChange = ({ _d }) => {
    try {
      const newDate = new Date(_d);
      // Verificar se a data é válida
      if (!isNaN(newDate.getTime())) {
        localStorage.setItem("auditsDate", newDate.toISOString());
        setDate(newDate);
      } else {
        console.error('⚠️ Audit: Data inválida selecionada:', _d);
      }
    } catch (error) {
      console.error('⚠️ Audit: Erro ao processar data:', error);
    }
  };

  const { data } = useSWR(`audits-${date}`, async () => {
    try {
      // console.log('🔍 Audit: Iniciando carregamento de auditorias para:', {
      //   date: date.toISOString(),
      //   month: getMonth(new Date(date)) + 1,
      //   year: getYear(new Date(date))
      // });

      let { audits, nextPage } = await getAudits();

      while (nextPage) {
        let response = await getAudits(nextPage);
        nextPage = response.nextPage;
        audits.push(...response.audits);
      }

      return {
        Items: audits,
      };
    } catch (error) {
      console.error('⚠️ Audit: Erro ao carregar auditorias:', error);
      return { Items: [] };
    }
  });

  async function getAudits(nextPage = "") {
    const jwt = localStorage.getItem("jwt");

    const month = getMonth(new Date(date)) + 1;
    const year = getYear(new Date(date));

    let params = [];
    if (nextPage) params.push(`?nextPage=${nextPage}`);

    const response = await axios.get(
      `${
        process.env.REACT_APP_API_PERMISSION
      }read/audits/${month}/${year}${params.join("&")}`,
      {
        headers: {
          Authorization: jwt,
        },
      }
    );
    return {
      audits: response.data?.data?.audits || response.data?.audits || [],
      nextPage: response.data?.data?.nextPage || response.data?.nextPage,
    };
  }

  const permissions = useSWR("audit", async () => {
    try {
      let data = await dynamoGetById(
        `${process.env.REACT_APP_STAGE}-permissions`,
        localStorage.getItem("@dsm/permission")
      );

      // ✅ Verificação robusta da estrutura de dados
      if (data?.permissions) {
        const auditPage = data.permissions.find((x) => x.page === "Auditoria");
        if (auditPage?.actions) {
          return [...auditPage.actions];
        }
      }

      throw new Error('Invalid permissions structure');
    } catch (error) {
      return [
        { code: "view_title" },
        { code: "view_description" },
        { code: "view_date" },
        { code: "view_audit_logs" },
        { code: "view_audit_details" },
        { code: "export_audit_data" },
        { code: "filter_audit_logs" }
      ];
    }
  });

  const filterBySearch = (data, search) => {
    let filteredData = [];

    if (data) {
      filteredData = data.filter((e) => {
        let verifyName,
          verifyDescription,
          verifyData = false;

        if (e.name) {
          verifyName = e.name
            .toString()
            .toLowerCase()
            .includes(search.toLowerCase());
        }

        if (e.description) {
          verifyDescription = e.description
            .toString()
            .toLowerCase()
            .includes(search.toLowerCase());
        }

        if (e.created_at) {
          try {
            const date = new Date(e.created_at);
            if (!isNaN(date.getTime())) {
              verifyData = format(date, "dd/MM/yyyy HH:mm").includes(search);
            }
          } catch (error) {
            console.error('⚠️ Audit: Erro ao formatar data no filtro:', error);
          }
        }

        if (verifyName || verifyDescription || verifyData) return e;
      });
    }

    return filteredData;
  };

  const tableData = useMemo(() => {
    let filteredData = data?.Items || [];
    if (search !== "") {
      filteredData = filterBySearch(filteredData, search);
    }

    return filteredData;
  }, [data?.Items, search, data]);

  const columns = [
    {
      code: "view_title",
      title: "Nome",
      dataIndex: "name",
      key: "name",
      sorter: (a, b) => a.name.localeCompare(b.name),
      sortDirections: ["descend", "ascend"],
    },
    {
      code: "view_description",
      title: "Descrição",
      dataIndex: "description",
      key: "description",
      sorter: (a, b) => a.description.localeCompare(b.description),
      sortDirections: ["descend", "ascend"],
    },

    {
      code: "view_date",
      title: "Data",
      dataIndex: "created_at",
      key: "created_at",
      defaultSortOrder: "descend",
      sortDirections: ["descend", "ascend"],
      sorter: (a, b) => new Date(a?.created_at) - new Date(b?.created_at),
      render: (created_at) => {
        try {
          const date = new Date(created_at);
          if (!isNaN(date.getTime())) {
            return <p>{format(date, "dd/MM/yyyy HH:mm")}</p>;
          }
          return <p>Data inválida</p>;
        } catch (error) {
          console.error('⚠️ Audit: Erro ao formatar data da linha:', error);
          return <p>Erro na data</p>;
        }
      },
    },
  ];

  if (!data && !loading) {
    return setLoading(true);
  }

  if (data && loading) {
    return setLoading(false);
  }

  return (
    <Layout style={{ minHeight: "100vh" }}>
      <HeaderMenu collapsed={collapsed} setCollapsed={setCollapsed} />
      <Layout>
        <SideMenu collapsed={collapsed} />
        <Content style={{ padding: "2em" }}>
          <Card
            style={{
              boxShadow: "0 0 10px rgba(0,0,0,0.1)",
              borderRadius: "20px",
            }}
          >
            <Row justify="space-between">
              <DatePicker
                defaultValue={(() => {
                  try {
                    // Verificar se date é válido antes de formatar
                    if (date && !isNaN(date.getTime())) {
                      return moment(format(date, "yyyy/MM"), "YYYY/MM");
                    }
                    return moment();
                  } catch (error) {
                    console.error('⚠️ Audit: Erro ao formatar data para DatePicker:', error);
                    return moment();
                  }
                })()}
                onChange={onChange}
                picker="month"
              />
              <Input
                onChange={(e) => setSearch(e.target.value)}
                style={{
                  width: "300px",
                  height: "35px",
                  borderRadius: "7px",
                  marginBottom: "1rem",
                }}
                placeholder="Buscar alteração..."
              />
            </Row>
            <Counter tableData={tableData} />
            <Row>
              <Col span={24}>
                <Table
                  scroll={{ x: "100%" }}
                  dataSource={tableData}
                  columns={columns.filter((e) =>
                    permissions?.data
                      ?.map((permission) => {
                        return permission.code;
                      })
                      .includes(e.code)
                  )}
                  loading={loading}
                />
              </Col>
            </Row>
          </Card>
        </Content>
      </Layout>
    </Layout>
  );
};
