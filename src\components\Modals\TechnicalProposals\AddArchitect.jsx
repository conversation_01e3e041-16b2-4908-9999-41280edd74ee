import {
  DeleteOutlined,
  PlusOutlined,
  IdcardOutlined,
  CheckOutlined
} from '@ant-design/icons'
import { useEffect, useState } from 'react'
import {
  Button,
  Modal,
  Row,
  Col,
  Table,
  Input,
  Typography,
  Form,
  Popconfirm,
  message
} from 'antd'

export const AddArchitect = architectsParent => {
  const { Title } = Typography
  const [architectForm] = Form.useForm()
  const [showModal, setShowModal] = useState()
  const { architects, setArchitects } = architectsParent
  const [childArchitects, setChildArchitects] = useState([])
  const [currentArchitect, setCurrentArchitect] = useState({
    email: ''
  })

  const handleSubmit = e => {
    e.preventDefault()
    setArchitects(childArchitects)
    setShowModal(false)
  }

  const handleRemoveItem = props => {
    const prev = childArchitects
    setChildArchitects(
      prev.filter(architect => architect.email !== props.email)
    )
    message.success(`Arquiteto "${props.name}" removido com sucesso!`)
  }

  const handleCloseModal = () => {
    setShowModal(false)
  }

  const handleAddItem = () => {
    if (
      childArchitects.find(architect => architect === currentArchitect.email)
    ) {
      message.error(`Arquiteto "${currentArchitect.email}" já existe!`)
    } else {
      setChildArchitects(prev => [...prev, currentArchitect])
      message.success(
        `Arquiteto "${currentArchitect.email}" adicionado com sucesso!`
      )
    }
  }

  const columns = [
    {
      title: 'Email',
      dataIndex: 'email',
      key: 'email',
      sorter: {
        compare: (a, b) =>
          a?.email?.toString().localeCompare(b?.email?.toString()),
        multiple: 2
      }
    },
    {
      title: 'Remover',
      dataIndex: 'remove',
      key: 'remove',
      align: 'center',
      render: (id, props) => {
        return (
          <Popconfirm
            title={'Tem certeza que deseja deletar este arquiteto'}
            okText={'Sim'}
            cancelText={'Não'}
            onConfirm={e => {
              handleRemoveItem(props)
            }}
          >
            <Button danger type="text" icon={<DeleteOutlined />}></Button>
          </Popconfirm>
        )
      }
    }
  ]

  useEffect(() => {
    architectForm.resetFields(['architect'])
    setChildArchitects(architectsParent.architectsParent)
  }, [architectsParent.architectsParent])

  return (
    <>
      <Button
        type="text"
        onClick={() => {
          setChildArchitects(architectsParent.architectsParent)
          setShowModal(true)
        }}
      >
        <PlusOutlined />
      </Button>
      <Modal
        title="Adicionar Arquiteto"
        open={showModal}
        width={'80%'}
        closable={false}
        destroyOnClose={true}
        footer={null}
        onCancel={() => {
          setShowModal(false)
          setChildArchitects(childArchitects)
        }}
      >
        <Form layout="horizontal" form={architectForm} onFinish={handleSubmit}>
          <Row justify="center" gutter={[16, 0]}>
            <Col span={24}>
              <Title level={5} style={{ fontWeight: '400' }}>
                Email
              </Title>
              <Form.Item>
                <Input
                  type="text"
                  required
                  onChange={e => {
                    setCurrentArchitect({
                      ...currentArchitect,
                      email: e.target.value
                    })
                  }}
                />
              </Form.Item>
            </Col>
            <Row justify="center" style={{ marginBottom: '1rem' }}>
              <Col span={24}>
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={e => {
                    handleAddItem(e.target)
                  }}
                  disabled={
                    !currentArchitect.displayName || !currentArchitect.email
                  }
                >
                  Adicionar
                </Button>
              </Col>
            </Row>
          </Row>
        </Form>

        <Row style={{ marginBottom: '1rem' }}>
          <Col span={24}>
            {childArchitects.length > 0 ? (
              <Table
                dataSource={childArchitects.map((item, index) => {
                  return {
                    key: index,
                    email: item.email
                  }
                })}
                columns={columns}
                scroll={{ x: 100 }}
                pagination={true}
              ></Table>
            ) : (
              <Row justify="center" gutter={[24, 16]}>
                <Col span={24}>
                  <Row justify="center">
                    <IdcardOutlined style={{ fontSize: '5rem' }} />
                  </Row>
                  <Row>
                    <Title level={5} style={{ fontWeight: '400' }}>
                      Não existem outros arquitetos selecionados
                    </Title>
                  </Row>
                </Col>
              </Row>
            )}
          </Col>
        </Row>
        <Row gutter={[16, 16]} justify={'space-between'}>
          <Button type="text-color" onClick={handleCloseModal}>
            Cancelar <DeleteOutlined />
          </Button>
          <Button type="primary" onClick={handleSubmit}>
            Salvar <CheckOutlined />
          </Button>
        </Row>
      </Modal>
    </>
  )
}
