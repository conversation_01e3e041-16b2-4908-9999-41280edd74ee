import { Row, Col, Typo<PERSON>, <PERSON>, But<PERSON> } from "antd";
import { useContext, useState } from "react";
import { ProposalContext } from "../../contexts/totalValuesProposals";
import { TotalValuesProposalType } from "../../contexts/reducers/totalValuesProposal";
import { realMask } from "../../utils/masks";
import { moneyMask } from "../../utils/money-maks";

export const TotalCalculatorCards = ({ month }) => {
  const { Title, Text } = Typography;
  const { proposalState, setProposalState } = useContext(ProposalContext);

  const filteredTotalValues = proposalState?.totalValues?.filter(
    (v) => v.hourClass === "hourValue"
  )[0].values;

  const sumTotalOfHours = (array) => {
    let result = array.reduce((acc, curr) => {
      return acc + curr.totalValue;
    }, 0);
    return Number(result.toFixed(2));
  };

  const formatedFormName = (str) => {
    if (str.includes("8x5")) return "8x5";

    if (str.includes("24x7")) return "24x7";
  };

  const handleSpreadSetup = (setupHours, sustHours, month) => {
    const spreadSetupValue = proposalState.spreadSetup.find(
      (s) => s.month === month
    ).value;
    const sumSetupValue = sumTotalOfHours(setupHours);
    const sumSustValue = sumTotalOfHours(sustHours);
    let newSpread = [...proposalState?.spreadSetup];

    if (spreadSetupValue > 0) {
      newSpread.forEach((s) => {
        if (s.month === month) {
          s.value = 0;
        }
      });
      setProposalState({
        type: TotalValuesProposalType.SET_SPREAD_SETUP,
        value: newSpread,
      });
      return;
    }

    newSpread.forEach((s) => {
      if (s.month === month) {
        s.value = sumSustValue + sumSetupValue / Number(month);
      }
    });

    setProposalState({
      type: TotalValuesProposalType.SET_SPREAD_SETUP,
      value: newSpread,
    });
  };

  const handleTotalSustValue = (allSustHours) => {
    let value = "0,00";
    if (proposalState?.spreadSetup.find((s) => s.month === month).value > 0) {
      let spread = Number(
        proposalState?.spreadSetup.find((s) => s.month === month).value
      ).toFixed(2);

      spread = moneyMask(spread);
      value = spread;
    } else {
      let sum = sumTotalOfHours(allSustHours);
      sum = sum.toFixed(2);
      value = moneyMask(sum);
    }

    return value;
  };

  const handleTotalSetupValue = (allSetupHours) => {
    let value = 0;

    if (proposalState?.spreadSetup.find((s) => s.month === month).value > 0) {
      value = value.toFixed(2);
      value = moneyMask(value);
    } else {
      let sum = sumTotalOfHours(allSetupHours);
      sum = sum.toFixed(2);
      value = moneyMask(sum);
    }

    return value;
  };

  return (
    <Col span={24}>
      <Row
        gutter={[16, 16]}
        justify="space-between"
        style={{ marginTop: "1rem" }}
      >
        {filteredTotalValues
          .filter((i) => i.month === month)
          ?.map((item, key) => {
            let allSetupHours = item.values.filter((i) => i.type === "SETUP");
            let allSustHours = item.values.filter((i) => i.type === "SUST");
            return (
              item.values.type !== "SEC" && (
                <>
                  <Col
                    xxl={8}
                    xl={24}
                    sm={24}
                    style={{ height: "100%" }}
                    key={key}
                  >
                    <Card style={{ display: "flex" }} key={key}>
                      <Title level={4}>SETUP {`(${month} meses)`}</Title>
                      <ul>
                        {allSetupHours.map((currentHour, key) => {
                          return (
                            <li style={{ textAlign: "justify" }} key={key}>
                              <Text>
                                {`
                            ${currentHour.totalHours} horas, em regime
                            ${formatedFormName(currentHour.formName)} - R$
                            ${moneyMask(currentHour.totalValue.toFixed(2))}
                          `}
                              </Text>
                            </li>
                          );
                        })}
                      </ul>
                      <Title level={5} style={{ fontWeight: 400 }}>
                        Total: R$
                        {handleTotalSetupValue(allSetupHours)}
                      </Title>
                    </Card>
                  </Col>
                  <Col xxl={8} xl={24} sm={24}>
                    <Card>
                      <Title level={4}>SUST {`(${month} meses)`}</Title>
                      <ul>
                        {allSustHours.map((currentHour, key) => {
                          return (
                            <li style={{ textAlign: "justify" }} key={key}>
                              <Text>
                                {`
                           ${currentHour.totalHours} horas mensais, em regime
                           ${formatedFormName(currentHour.formName)} - R$
                           ${moneyMask(currentHour.totalValue.toFixed(2))}
                          `}
                              </Text>
                            </li>
                          );
                        })}
                      </ul>
                      <Title level={5} style={{ fontWeight: 400 }}>
                        Total: R$
                        {handleTotalSustValue(allSustHours)}
                      </Title>
                    </Card>
                  </Col>
                  <Col
                    xxl={8}
                    xl={24}
                    sm={24}
                    style={{
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                    }}
                  >
                    <Row justify="center">
                      <Button
                        type={
                          proposalState.spreadSetup.find(
                            (s) => s.month === month
                          ).value > 0
                            ? "default"
                            : "primary"
                        }
                        onClick={() => {
                          handleSpreadSetup(
                            allSetupHours,
                            allSustHours,
                            item.month
                          );
                        }}
                      >
                        Diluir Valor Setup {`(${month} meses)`}
                      </Button>
                    </Row>
                  </Col>
                </>
              )
            );
          })}
      </Row>
    </Col>
  );
};
