import { createSlice } from "@reduxjs/toolkit";

const REDUCER_NAME = "files";

export const UPLOAD_STATUS = {
  IN_PROGRESS: "IN_PROGRESS",
  SUCCESS: "SUCCESS",
  ERROR: "ERROR",
};

export const INITIAL_STATE = {
  files: [],
  selectedFile: {
    name: "",
    type: "",
    category: "",
    createdAt: "",
    updatedAt: "",
    customerId: "",
    customerName: "",
    customerCNPJ: "",
    userName: "",
    userEmail: "",
    tags: [],
    emails: [],
  },
  category: "",
  tags: [],
  emails: [],
  customerId: "",
  customerName: "",
  customerCNPJ: "",
  customerITSM: "",
  customers: [],
  customersSelectOptions: [],
  uploadReady: false,
  realoadFilesData: false,
  dateRange: {
    start: "",
    end: "",
  },
  filterState: "Todas",
  allTags: [],
  fileContent: null,
  enableFileUpdateTags: true,
  enableFileUpdateName: true,
  stepModal: 0,
  filesToUpload: [],
  uploadStatus: UPLOAD_STATUS.IN_PROGRESS,
};

const filesSlice = createSlice({
  name: REDUCER_NAME,
  initialState: INITIAL_STATE,
  reducers: {
    setSelectedFileFilesReduce(state, action) {
      state.selectedFile = action.payload.value;
    },

    setSelectedFileFieldReduce(state, action) {
      state.selectedFile[action.payload.field] = action.payload.value;
    },

    setUploadFileFilesReduce(state, action) {
      state.uploadFile = action.payload.value;
    },

    setFilterStateFilesReduce(state, action) {
      state.filterState = action.payload.value;
    },

    setUploadReadyFilesReduce(state, action) {
      state.uploadReady = action.payload;
    },

    setReloadFilesDataFilesReduce(state, action) {
      state.realoadFilesData = action.payload;
    },

    setDateRangeFilesReduce(state, action) {
      state.dateRange = action.payload.value;
    },

    setAllTagsFilesReduce(state, action) {
      state.allTags = action.payload;
    },

    setAllFilesReduce(state, action) {
      state.files = action.payload;
    },

    setFileContentFilesReduce(state, action) {
      state.fileContent = action.payload;
    },

    setEnableFileUpdateTagsReduce(state, action) {
      state.enableFileUpdateTags = action.payload;
    },

    setEnableFileUpdateNameReduce(state, action) {
      state.enableFileUpdateName = action.payload;
    },

    setCategoryFilesReduce(state, action) {
      state.category = action.payload;
    },

    setTagsFilesReduce(state, action) {
      state.tags = action.payload.value;
    },

    setEmailsFilesReduce(state, action) {
      state.tags = action.payload.value;
    },

    setCustomerFilesReduce(state, action) {
      state.tags = action.payload.value;
    },

    cleanSelectedFileReduce(state) {
      state.selectedFile = {
        name: "",
        type: "",
        category: "",
        createdAt: "",
        customerId: "",
        customerName: "",
        customerCNPJ: "",
        userName: "",
        userEmail: "",
        tags: [],
        emails: [],
      };
      state.enableFileUpdateTags = true;
      state.enableFileUpdateName = true;
    },

    cleanUploadFileReduce(state) {
      state.enableFileUpdateTags = true;
      state.enableFileUpdateName = true;
    },

    cleanFilesReduce(state) {
      state.selectedFile = {
        name: "",
        type: "",
        category: "",
        createdAt: "",
        customerId: "",
        customerName: "",
        customerCNPJ: "",
        userName: "",
        userEmail: "",
        tags: [],
        emails: [],
      };
      state.uploadFile = {
        name: "",
        type: "",
        category: "",
        createdAt: "",
        customerId: "",
        customerName: "",
        customerCNPJ: "",
        userName: "",
        userEmail: "",
        emails: [],
        tags: [],
      };
      state.uploadReady = false;
      state.realoadFilesData = false;
      state.dateRange = {
        start: "",
        end: "",
      };
      state.filterState = "Todos";
      state.allTags = [];
      state.enableFileUpdateTags = true;
      state.enableFileUpdateName = true;
      state.category = "";
      state.tags = [];
      state.emails = [];
      state.customerId = "";
      state.customerName = "";
      state.customerCNPJ = "";
      state.filesToUpload = [];
    },

    cleanAllReduce(state) {
      state = {
        ...INITIAL_STATE,
        files: state.files,
      };
    },

    cleanCloseModalReduce(state) {
      state.category = INITIAL_STATE.category;
      state.tags = INITIAL_STATE.tags;
      state.emails = INITIAL_STATE.emails;
      state.customerId = INITIAL_STATE.customerId;
      state.customerCNPJ = INITIAL_STATE.customerCNPJ;
      state.customerName = INITIAL_STATE.customerName;
      state.filesToUpload = INITIAL_STATE.filesToUpload;
      state.stepModal = INITIAL_STATE.stepModal;
      state.uploadStatus = INITIAL_STATE.uploadStatus;
    },

    setUploadFilesStateReduce(state, action) {
      state[action.payload.field] = action.payload.value;
    },
  },
});

export const {
  setSelectedFileFilesReduce,
  setSelectedFileFieldReduce,
  setUploadFileFilesReduce,
  setFilterStateFilesReduce,
  setUploadReadyFilesReduce,
  setReloadFilesDataFilesReduce,
  setDateRangeFilesReduce,
  setAllTagsFilesReduce,
  setAllFilesReduce,
  setFileContentFilesReduce,
  setEnableFileUpdateTagsReduce,
  setEnableFileUpdateNameReduce,
  setCategoryFilesReduce,
  setTagsFilesReduce,
  setEmailsFilesReduce,
  setCustomerFilesReduce,
  cleanSelectedFileReduce,
  cleanUploadFileReduce,
  cleanFilesReduce,
  setUploadFilesStateReduce,
  cleanAllReduce,
  cleanCloseModalReduce,
} = filesSlice.actions;

export default filesSlice.reducer;
