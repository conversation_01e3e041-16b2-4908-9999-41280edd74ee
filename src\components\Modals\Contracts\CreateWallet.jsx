import React, { useState } from "react";
import { useSelector, shallowEqual } from "react-redux";

import { format } from "date-fns";
import { Modal, Button, Form, message, Row, Col, Input } from "antd";

import { dynamoPost } from "../../../service/apiDsmDynamo";
import { setContractFieldState } from "../../../store/actions/contract-action";

export const CreateWallet = (props) => {
  const { wallets } = useSelector((state) => state.contract, shallowEqual);

  const { form, show, setShow } = props;
  const [loading, setLoading] = useState(false);
  const [createForm] = Form.useForm();

  const handleSubmit = async (data) => {
    data.name = data.name.trim();
    if (
      wallets.find(
        (wallet) => wallet.name.trim().toLowerCase() === data.name.toLowerCase()
      )
    )
      return message.error("Já existe uma carteira com este nome!");
    setLoading(true);
    try {
      await dynamoPost(`${process.env.REACT_APP_STAGE}-wallets`, {
        ...data,
        created_at: format(new Date(), "yyyy-MM-dd HH:mm:ss"),
        updated_at: format(new Date(), "yyyy-MM-dd HH:mm:ss"),
      });

      setContractFieldState({ field: "wallets", value: [...wallets, data] });

      form.setFieldsValue({
        squad: data.name,
      });
      createForm.resetFields();

      message.success("Carteira adicionada com sucesso!");

      setShow(false);
    } catch (error) {
      console.log(error);
      message.error("Ocorreu um erro ao tentar adicionar esta carteira...");
    }

    setLoading(false);
  };

  return (
    <Modal
      title="Inserir uma nova carteira"
      onCancel={() => {
        createForm.resetFields();
        form.setFieldsValue({
          squad: undefined,
        });
        setShow(false);
      }}
      closable={false}
      open={show}
      footer={[
        <Button
          onClick={() => {
            createForm.resetFields();
            setShow(false);
          }}
        >
          Cancelar
        </Button>,
        <Button
          type="primary"
          onClick={() => createForm.submit()}
          loading={loading}
        >
          Adicionar
        </Button>,
      ]}
    >
      <Row align="middle" justify="center">
        <Col span={24}>
          <Form
            onFinish={handleSubmit}
            name="control-hooks"
            form={createForm}
            layout="vertical"
          >
            <Form.Item name="name">
              <Input placeholder="Nome da Carteira" />
            </Form.Item>
          </Form>
        </Col>
      </Row>
    </Modal>
  );
};
