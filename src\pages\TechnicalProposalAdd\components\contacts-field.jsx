import React, { useEffect, useMemo, useState } from "react";
import { Row, Col, Select, Typography } from "antd";
import { shallowEqual, useSelector } from "react-redux";
import { setTechnicalProposalState } from "../../../store/actions/technical-proposal-action";

import { AddNewContact } from "../../../components/Modals/TechnicalProposals/AddNewContact";
import { dsmApiProvider } from "../../../provider/dsm-api-provider";
const { Title } = Typography;
const { Option } = Select;

export const ContactsField = () => {
  const path = window.location.pathname.split("/").pop();
  const selectedCustomer = useSelector(
    (state) => state.technicalProposal.selectedCustomer,
    shallowEqual
  );
  const contacts = useSelector(
    (state) => state.technicalProposal.contacts,
    shallowEqual
  );
  const selectedContact = useSelector(
    (state) => state.technicalProposal.selectedContact,
    shallowEqual
  );

  const [loading, setLoading] = useState(false);
  const getMainContact = async () => {
    const provider = dsmApiProvider();
    const { data } = await provider.get(`read/id/${path}`, {
      headers: {
        "Content-Type": "application/json",
        dynamodb: `${process.env.REACT_APP_STAGE}-proposals`,
      },
    });
    setTechnicalProposalState({
      field: "selectedContact",
      value: parseInt(data.data.Item?.customer.mainContact) || 0,
    });

    return data;
  };

  useEffect(() => {
    const loadContactsData = async () => {
      if (!selectedCustomer.mainContact) {
        await getMainContact();
        return;
      }
      if (selectedCustomer) {
        if (selectedCustomer?.contacts?.length === 0) {
          setTechnicalProposalState({
            field: "selectedContact",
            value: 0,
          });
        } else if (
          typeof selectedContact !== "number" &&
          selectedCustomer?.contacts?.length
        ) {
          setTechnicalProposalState({
            field: "selectedContact",
            value: parseInt(selectedCustomer?.mainContact) || 0,
          });
        }
        setLoading(false);
      } else setLoading(true);
      setTechnicalProposalState({
        field: "contacts",
        value: contacts,
      });
    };

    loadContactsData();
  }, [selectedCustomer]);

  return (
    <Col sm={24} md={12} lg={6}>
      <Row>
        <Title level={5} style={{ fontWeight: "400" }}>
          Contatos
        </Title>
      </Row>

      <Row>
        <Col span={20}>
          <Select
            id="selectedContact"
            loading={loading}
            value={parseInt(selectedCustomer?.mainContact) || selectedContact}
            showSearch
            placeholder={"Nome do contato"}
            optionFilterProp="children"
            filterOption={(input, option) =>
              option?.children?.toLowerCase().includes(input?.toLowerCase())
            }
            filterSort={(optionA, optionB) =>
              optionA.children.localeCompare(optionB.children)
            }
            style={{ width: "100%" }}
            onSelect={(e) => {
              setTechnicalProposalState({
                field: "selectedCustomer",
                value: {
                  ...selectedCustomer,
                  mainContact: e,
                },
              });
              setTechnicalProposalState({
                field: "selectedContact",
                value: e,
              });
            }}
          >
            {selectedCustomer
              ? selectedCustomer.contacts?.map((c, index) => {
                  return (
                    <Option value={index} key={index}>
                      {c.names?.first_name}
                    </Option>
                  );
                })
              : null}
          </Select>
        </Col>
        <Col span={4}>
          <AddNewContact />
        </Col>
      </Row>
    </Col>
  );
};
