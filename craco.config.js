// Don't open the browser during development
process.env.BROWSER = "none";

// Configurações para React 18 compatibilidade
process.env.SKIP_PREFLIGHT_CHECK = "true";

module.exports = {
  webpack: {
    plugins: [],
    configure: (webpackConfig) => {
      // Configurações específicas para React 18
      webpackConfig.ignoreWarnings = [
        /Failed to parse source map/,
        /Critical dependency: the request of a dependency is an expression/,
      ];
      return webpackConfig;
    },
  },
  eslint: {
    enable: false, // Manter desabilitado para evitar conflitos
  },
  plugins: [],
};
