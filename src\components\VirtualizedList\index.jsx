import React, { useState, useEffect, useRef, useMemo } from 'react';
import { Spin } from 'antd';

/**
 * VirtualizedList Component
 * Renders only visible items for better performance with large datasets
 */
const VirtualizedList = ({
  items = [],
  itemHeight = 50,
  containerHeight = 400,
  renderItem,
  overscan = 5,
  className,
  style,
  loading = false,
  emptyText = 'Nenhum item encontrado',
  onScroll,
  ...props
}) => {
  const [scrollTop, setScrollTop] = useState(0);
  const containerRef = useRef(null);

  // Calculate visible range
  const visibleRange = useMemo(() => {
    const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan);
    const endIndex = Math.min(
      items.length - 1,
      Math.ceil((scrollTop + containerHeight) / itemHeight) + overscan
    );
    
    return { startIndex, endIndex };
  }, [scrollTop, itemHeight, containerHeight, items.length, overscan]);

  // Calculate visible items
  const visibleItems = useMemo(() => {
    const { startIndex, endIndex } = visibleRange;
    return items.slice(startIndex, endIndex + 1).map((item, index) => ({
      item,
      index: startIndex + index,
      key: item.id || item.key || startIndex + index,
    }));
  }, [items, visibleRange]);

  // Handle scroll
  const handleScroll = (event) => {
    const newScrollTop = event.target.scrollTop;
    setScrollTop(newScrollTop);
    
    if (onScroll) {
      onScroll(event, {
        scrollTop: newScrollTop,
        visibleRange,
        visibleItems: visibleItems.length,
      });
    }
  };

  // Total height of all items
  const totalHeight = items.length * itemHeight;

  // Offset for visible items
  const offsetY = visibleRange.startIndex * itemHeight;

  if (loading) {
    return (
      <div
        className={className}
        style={{
          height: containerHeight,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          ...style,
        }}
      >
        <Spin size="large" />
      </div>
    );
  }

  if (items.length === 0) {
    return (
      <div
        className={className}
        style={{
          height: containerHeight,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: '#999',
          ...style,
        }}
      >
        {emptyText}
      </div>
    );
  }

  return (
    <div
      ref={containerRef}
      className={className}
      style={{
        height: containerHeight,
        overflow: 'auto',
        ...style,
      }}
      onScroll={handleScroll}
      {...props}
    >
      <div style={{ height: totalHeight, position: 'relative' }}>
        <div
          style={{
            transform: `translateY(${offsetY}px)`,
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
          }}
        >
          {visibleItems.map(({ item, index, key }) => (
            <div
              key={key}
              style={{
                height: itemHeight,
                overflow: 'hidden',
              }}
            >
              {renderItem(item, index)}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

/**
 * VirtualizedTable Component
 * Virtualized table for large datasets
 */
export const VirtualizedTable = ({
  columns = [],
  dataSource = [],
  rowHeight = 54,
  headerHeight = 40,
  containerHeight = 400,
  className,
  style,
  loading = false,
  emptyText = 'Nenhum dado encontrado',
  onRowClick,
  ...props
}) => {
  const renderRow = (record, index) => (
    <div
      style={{
        display: 'flex',
        alignItems: 'center',
        height: '100%',
        borderBottom: '1px solid #f0f0f0',
        cursor: onRowClick ? 'pointer' : 'default',
      }}
      onClick={() => onRowClick && onRowClick(record, index)}
    >
      {columns.map((column, colIndex) => (
        <div
          key={column.key || column.dataIndex || colIndex}
          style={{
            flex: column.width ? `0 0 ${column.width}px` : 1,
            padding: '0 16px',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap',
          }}
        >
          {column.render
            ? column.render(record[column.dataIndex], record, index)
            : record[column.dataIndex]}
        </div>
      ))}
    </div>
  );

  return (
    <div className={className} style={style}>
      {/* Header */}
      <div
        style={{
          display: 'flex',
          height: headerHeight,
          backgroundColor: '#fafafa',
          borderBottom: '1px solid #f0f0f0',
          fontWeight: 'bold',
        }}
      >
        {columns.map((column, index) => (
          <div
            key={column.key || column.dataIndex || index}
            style={{
              flex: column.width ? `0 0 ${column.width}px` : 1,
              padding: '0 16px',
              display: 'flex',
              alignItems: 'center',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
            }}
          >
            {column.title}
          </div>
        ))}
      </div>

      {/* Body */}
      <VirtualizedList
        items={dataSource}
        itemHeight={rowHeight}
        containerHeight={containerHeight - headerHeight}
        renderItem={renderRow}
        loading={loading}
        emptyText={emptyText}
        {...props}
      />
    </div>
  );
};

/**
 * InfiniteScrollList Component
 * Combines virtualization with infinite scrolling
 */
export const InfiniteScrollList = ({
  items = [],
  hasMore = false,
  loadMore,
  loading = false,
  threshold = 200,
  ...props
}) => {
  const handleScroll = (event, { scrollTop, visibleRange }) => {
    const { scrollHeight, clientHeight } = event.target;
    const scrollBottom = scrollHeight - scrollTop - clientHeight;

    // Load more when near bottom
    if (scrollBottom < threshold && hasMore && !loading && loadMore) {
      loadMore();
    }

    // Call original onScroll if provided
    if (props.onScroll) {
      props.onScroll(event, { scrollTop, visibleRange });
    }
  };

  return (
    <VirtualizedList
      {...props}
      items={items}
      onScroll={handleScroll}
      loading={loading}
    />
  );
};

/**
 * Hook for virtualized list state management
 */
export const useVirtualizedList = (initialItems = []) => {
  const [items, setItems] = useState(initialItems);
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);

  const addItems = (newItems) => {
    setItems(prev => [...prev, ...newItems]);
  };

  const prependItems = (newItems) => {
    setItems(prev => [...newItems, ...prev]);
  };

  const updateItem = (index, updatedItem) => {
    setItems(prev => {
      const newItems = [...prev];
      newItems[index] = updatedItem;
      return newItems;
    });
  };

  const removeItem = (index) => {
    setItems(prev => prev.filter((_, i) => i !== index));
  };

  const clearItems = () => {
    setItems([]);
  };

  return {
    items,
    setItems,
    addItems,
    prependItems,
    updateItem,
    removeItem,
    clearItems,
    loading,
    setLoading,
    hasMore,
    setHasMore,
  };
};

export default VirtualizedList;
