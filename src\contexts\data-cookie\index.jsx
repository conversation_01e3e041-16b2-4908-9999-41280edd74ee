import React, { createContext, useReducer } from "react";
import { <PERSON><PERSON>FieldsReducer } from "../reducers/data-cookie-reducer";

const initialValue = {
  project_name: null,
  client_name: null,
  project_type: null,
  project_status: null,
  project_contacts: null,
  project_architects: [],
  project_bu: [],
  project_opportunity: null,
};

export const DataCookieContext = createContext({
  cookieState: initialValue,
  setCookieState: () => {},
});

export const DataCookieProvider = ({ children }) => {
  const [state, setCookieState] = useReducer(CookieFieldsReducer, initialValue);

  return (
    <DataCookieContext.Provider
      value={{
        cookieState: state,
        setCookieState: setCookieState,
      }}
    >
      {children}
    </DataCookieContext.Provider>
  );
};
