import { Typo<PERSON>, Select, <PERSON>, Col } from "antd";
import { useSelector, shallowEqual } from "react-redux";
import { setUploadFileFilesState } from "../../../store/actions/files-action";
import { AddEmailsModal } from "./Modals/AddEmailsModal/AddEmailsModal";

export const UploadEmailSelect = () => {
  const { Option } = Select;
  const { Text } = Typography;
  const uploadFile = useSelector(
    (state) => state.files.uploadFile,
    shallowEqual
  );

  const handleEmailsSelect = (value) => {
    let newEmails = [];
    value.forEach((t) => {
      newEmails.push(t.value);
    });
    setUploadFileFilesState({
      field: "uploadFile",
      value: { ...uploadFile, emails: newEmails },
    });
  };

  return (
    <>
      <Row>
        <Text>Emails</Text>
      </Row>
      <Row gutter={[8, 8]}>
        <Col span={20}>
          <Select
            style={{ width: "100%" }}
            mode="multiple"
            placeholder={"Emails"}
            labelInValue
            showSearch
            value={uploadFile?.emails?.map((e) => e)}
            optionFilterProp="children"
            filterOption={(input, option) =>
              option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
            }
            onChange={(value) => handleEmailsSelect(value)}
          >
            {uploadFile?.emails?.map((email, key) => (
              <Option value={email} key={key}>
                {email}
              </Option>
            ))}
          </Select>
        </Col>
        <Col span={4}>
          <AddEmailsModal />
        </Col>
      </Row>
    </>
  );
};
