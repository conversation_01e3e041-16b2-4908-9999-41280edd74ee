import { Axios } from "axios";
import { switchRoleProvider } from "../provider/switch-role-api-provider";

const provider = switchRoleProvider();

const attachPermission = async (data) => {
  try {
    const bodyParams = {
      account: data.account_id,
      level: "darede-full",
      permission: data.arn,
      action: "add",
    };
    await provider.post("sso-attach", bodyParams);
  } catch (error) {
    console.log(error);
  }
};

export const allowSolicitationFromPreviousApi = async (formData, data) => {
  try {
    const bodyParams = {
      id: data.id,
      method: "allow",
      username: data.username,
      stage: process.env.REACT_APP_STAGE,
      time: formData.time !== undefined ? formData.time * 60 : data.time,
      role: "darede-full",
      approver: localStorage.getItem("@dsm/username"),
    };

    await provider.post("attach", bodyParams);
    await attachPermission(data);
  } catch (error) {
    console.log(error);
  }
};

export const revokeSolicitationFromPreviousApi = async (account) => {
  try {
    const bodyParams = {
      stage: process.env.REACT_APP_STAGE,
      account_id: account.account_id,
      ticket_id: account.ticket_id,
      username: account.username,
      arn: account.arn,
      role: "darede-full",
      time: account.time,
      id: account.id,
      method: "revoke",
      revoker: localStorage.getItem("@dsm/username"),
      active: "Revogado",
    };

    await provider.post("attach", bodyParams);
  } catch (error) {
    console.log(error);
  }
};
