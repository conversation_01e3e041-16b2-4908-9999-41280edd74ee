.donut-icons {
  display: flex;
  gap: 10px;
}

.card-top {
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.title {
  display: flex;
  gap: 60px;
}

.title h2 {
  color: rgba(39, 45, 62, 1);
  font-family: sans-serif;
  font-weight: 700;
  font-size: 24px;
}

.filter h2 {
  color: rgba(64, 72, 79, 1);
  font-family: sans-serif;
  font-weight: 700;
  font-size: 16px;
}

.container-graphic {
  display: flex;
  flex-direction: row-reverse;
  justify-content: space-evenly;
  align-items: center;
  line-height: normal;
}

.graphic-expiration {
  width: 40%;
  max-width: 350px;
  min-width: 180px;
}

.filter-title {
  display: flex;
  justify-items: space-between;
}

.graphic-data-card:nth-child(1) {
  border: 2px solid rgba(65, 142, 77, 1);
}

.graphic-data-card:nth-child(2) {
  border: 2px solid rgba(112, 211, 63, 1);
}

.graphic-data-card:nth-child(3) {
  border: 2px solid rgba(17, 53, 22, 1);
}

.graphic-data-bar {
  margin-top: 8px;
  margin-bottom: -8px;
  margin-left: -1px;
}
.bar {
  width: 15px;
  height: 8px;
  margin: -15px 0 0 -28px;
  border-top-right-radius: 50px;
  border-bottom-right-radius: 50px;
}
