import { <PERSON><PERSON>, Popover, Row, Table } from "antd";
import React, { useEffect, useState } from "react";
import { FilterFilled } from "@ant-design/icons";
import "../../../styles/table.css";
import "../../../App.css";
import "./ProfessinalInVacation.css";
import ProfessionalInVacationFilter from "../../Modals/Filters/ProfissionalInVacationFilter";

function TableProfessionalInVacation(props) {
  const { professional } = props;
  const [data, setData] = useState([]);
  const [popoverVisibility, setPopoverVisibility] = useState(false);

  const columns = [
    {
      title: "",
      dataIndex: "ticket_priority_id",
      key: "ticket_priority_id",
      render: (ticket_priority_id) => (
        <div className="priority-container">
          <div
            className="priority"
            style={{ background: getPriorityColor(ticket_priority_id) }}
          ></div>
        </div>
      ),
    },
    {
      title: "Cliente",
      dataIndex: "client_name",
      key: "client_name",
      render: (client_name) => <p className="table-font">{client_name}</p>,
    },
    {
      title: "Estado",
      dataIndex: "ticket_state",
      key: "ticket_state",
      render: (ticket_state) => <p className="state">{ticket_state}</p>,
    },
    {
      title: "Tipo",
      dataIndex: "type",
      key: "type",
      render: (wallet) => <p className="table-font">{wallet}</p>,
    },
    {
      title: "Título",
      dataIndex: "subject",
      key: "subject",
      render: (subject) => <p className="table-font">{subject}</p>,
    },
    {
      title: "Proprietario",
      dataIndex: "owner_ticket",
      key: "owner_ticket",
      render: (owner_ticket) => <p className="table-font">{owner_ticket}</p>,
    },
  ];

  function getPriorityColor(priorityId) {
    if (priorityId === 5) {
      return "rgba(255,0,0,0.77)";
    } else if (priorityId === 4) {
      return "#F1F43F";
    } else if (priorityId === (1 || 0)) {
      return "#74CF49";
    }
  }

  function handlePopoverVisibility() {
    setPopoverVisibility(!popoverVisibility);
  }

  useEffect(() => {
    setData(professional.ticket);
  }, [props.professional]);

  return (
    <div>
      <Row justify="end">
        {/* <div className="table-filter">
          <Popover
            visible={popoverVisibility}
            onClick={handlePopoverVisibility}
            placement="left"
            content={() => {
              return (
                <ProfessionalInVacationFilter
                  handlePopoverVisibility={handlePopoverVisibility}
                  professional={professional}
                />
              );
            }}
            trigger="click"
          >
            <div className="table-filter-content">
              <FilterFilled className="table-filter-icon" />
            </div>
          </Popover>
        </div> */}
        <Button
          type="text"
          onClick={() =>
            props.showTableTicketModal ? props.showModal() : props.showModal()
          }
        >
          X
        </Button>
      </Row>
      <Table
        scroll={{ y: 400 }}
        columns={columns}
        dataSource={data}
        pagination
      />
    </div>
  );
}

export default TableProfessionalInVacation;
