import { <PERSON>, <PERSON>, <PERSON>, Typo<PERSON>, Button, Space, Upload } from "antd";
import { UploadFilesFields } from "./UploadFilesFields";
import { CloudUploadOutlined } from "@ant-design/icons";
import { SendFilesModal } from "./Modals/SendFileModal/SendFiles";
import {
  setFileContentFilesState,
  setUploadFileFilesState,
  setUploadReadyFilesState,
} from "../../../store/actions/files-action";
import { shallowEqual, useSelector } from "react-redux";
import { useEffect } from "react";
import { UploadCategorySelect } from "./UploadCategorySelect";

export const UploadFilesCard = () => {
  const { Title } = Typography;

  const uploadFile = useSelector(
    (state) => state.files.uploadFile,
    shallowEqual
  );

  return (
    <Card
      style={{
        boxShadow: "0 0 10px rgba(0,0,0,0.1)",
        borderRadius: "20px",
        minHeight: "200px",
      }}
    >
      <Row justify={"center"} align={"bottom"} style={{ minHeight: "100px" }}>
        <Col span={24}>
          <Row justify={"center"}>
            <Title level={3}>Carregue um novo arquivo</Title>
          </Row>
        </Col>
      </Row>
      <Row justify={"center"} align={"top"} style={{ minHeight: "100px" }}>
        {/* <UploadCategorySelect /> */}
        {/* <Upload
          onChange={(info) => handleFileUpload(info)}
          showUploadList={false}
          maxCount={1}
          data={(data) => setFileContentFilesState(data)}
        >
          <Button type="default" icon={<CloudUploadOutlined />}>
            Upload de arquivo
          </Button>
        </Upload> */}
      </Row>
    </Card>
  );
};
