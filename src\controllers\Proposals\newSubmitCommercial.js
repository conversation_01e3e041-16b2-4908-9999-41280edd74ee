import { message } from "antd";
import { generatePDF } from "./generatePDF";
import { dynamoPut, dynamoGet } from "../../service/apiDsmDynamo";
import { pipeUpdate } from "../../service/apiPipedrive";
import { verifyEmptyCostsFields } from "../../utils/verifyEmptyCostsFields";
import { verifySustMinHours } from "../../utils/verifySustMinHours";
import { logNewAuditAction } from "../audit/logNewAuditAction";

export const submitCommercialController = async (
  edit_proposal,
  draftItems,
  currentContacts,
  services,
  proposalState,
  customerBrand,
  mainSearchId,
  user,
  state
) => {
  const fileNames = proposalState.fileNames;
  const architectureFileNames = proposalState.architectureFileNames;
  const scenarioFileNames = proposalState.scenarioFileNames;
  const pipedriveDealOwner = proposalState?.pipedriveDealOwner;

  const formData = edit_proposal.getFieldsValue();

  const awsCostsArr = formData["main_aws_investments"];
  const addCosts = formData["aditional_costs"];

  const emptyRequiredFields = await verifyEmptyCostsFields(
    awsCostsArr,
    addCosts,
    formData
  );

  if (emptyRequiredFields.length > 0) {
    return false;
  }

  let month =
    proposalState.month === "" || proposalState.month === []
      ? ["12"]
      : proposalState.month;

  let greatestMonth = month[0];

  for (let i = 0; i < month.length; i++) {
    if (greatestMonth < month[i]) {
      greatestMonth = month[i];
    }
  }
  month = greatestMonth;

  const totalValues = proposalState.totalValues.find(
    (v) => v.hourClass === "hourValue"
  ).values;
  const additionalTotalValues = proposalState.totalValues.find(
    (v) => v.hourClass === "additionalHourValue"
  ).values;
  const totalValuesFilteredByMonth = totalValues.find((v) => v.month === month);
  const additionalTotalValuesFilteredByMonth = additionalTotalValues.find(
    (v) => v.month === month
  );

  let sust24x7 = 0;
  let sust8x5 = 0;
  let setup24x7 = 0;
  let setup8x5 = 0;
  let addSetup8x5 = 0;
  let addSetup24x7 = 0;

  sust24x7 = totalValuesFilteredByMonth.values.find(
    (d) => d.formName === "sust24x7"
  ).totalHours;

  sust8x5 = totalValuesFilteredByMonth.values.find(
    (d) => d.formName === "sust8x5"
  ).totalHours;

  setup24x7 = totalValuesFilteredByMonth.values.find(
    (d) => d.formName === "setup24x7"
  ).totalHours;

  setup8x5 = totalValuesFilteredByMonth.values.find(
    (d) => d.formName === "setup8x5"
  ).totalHours;

  addSetup24x7 = additionalTotalValuesFilteredByMonth.values.find(
    (d) => d.formName === "setup24x7"
  ).totalValue;

  addSetup8x5 = additionalTotalValuesFilteredByMonth.values.find(
    (d) => d.formName === "setup8x5"
  ).totalValue;

  const consideredMonth =
    proposalState.month === "" || proposalState.month === []
      ? ["12"]
      : proposalState.month;

  const sustMinHoursVerified = await verifySustMinHours(
    totalValues,
    consideredMonth
  );

  if (!sustMinHoursVerified) {
    message.error("Verifique o valor das horas recorrentes");
    return false;
  }

  let scope = draftItems.challenges;
  scope = scope?.replace(/<[^>]+>/g, "");

  const pipePaymentMethod =
    proposalState.paymentMethod !== ""
      ? proposalState.paymentMethod
      : state.paymentMethod;

  const res = await dynamoGet(`${process.env.REACT_APP_STAGE}-cost-management`);

  const paymentSelected = res[0].paymentMethods.find(
    (p) => p.id === pipePaymentMethod
  );

  try {
    const deal = state.opportunity ? state.opportunity : [];

    const pipedriveBody = {
      id: deal,
      "Pool de Horas 24x7": sust24x7,
      "Pool de Horas 8x5": sust8x5,
      "Pool de Horas Setup 24x7": setup24x7,
      "Pool de Horas Setup 8x5": setup8x5,
      "Custo Excedente 24x7": addSetup24x7,
      "Custo Excedente 8x5": addSetup8x5,
      "Custos Terceiro": 0,
      "Escopo ": scope,
      "Forma de Pagamento": paymentSelected?.name ? paymentSelected?.name : "",
      "Prazo Contratual (meses)": Number(proposalState.month[0]),
      "Numero Chamado Pré Venda": state?.ticketData?.TicketNumber
        ? state?.ticketData?.TicketNumber
        : "",
    };

    const status = proposalState.status
      ? proposalState.status
      : state.commercialStatus;

    if (status === "concluída") {
      await pipeUpdate(pipedriveBody);
    }

    const customerCNPJ = proposalState.customer?.cnpj
      ? proposalState.customer?.cnpj
      : state.customer?.cnpj
      ? state.customer?.cnpj
      : currentContacts?.cnpj
      ? currentContacts?.cnpj
      : "";

    const customerNamesName = proposalState?.customer?.names?.name
      ? proposalState?.customer?.names?.name
      : state?.customer?.names?.name
      ? state?.customer?.names?.name
      : "";

    const customerNamesFantasyName = proposalState?.customer?.names
      ?.fantasy_name
      ? proposalState?.customer?.names?.fantasy_name
      : state?.customer?.names?.fantasy_name
      ? state?.customer?.names?.fantasy_name
      : "";

    const customerData = {
      names: {
        name: customerNamesName,
        fantasy_name: customerNamesFantasyName,
      },
      cnpj: customerCNPJ,
      contacts: proposalState.customer.contacts
        ? proposalState.customer.contacts
        : currentContacts?.contacts,
      mainContact: formData?.project_contacts,
    };

    const proposalData = {
      searchId: mainSearchId,
      name: formData?.project_name ? formData?.project_name : state.name,
      type: formData?.project_type ? formData?.project_type : state.type,
      status: formData?.project_status
        ? formData?.project_status === "revisão técnica"
          ? "revisão técnica"
          : "concluída"
        : "concluída",
      commercialStatus: formData?.project_status
        ? formData?.project_status
        : state.commercialStatus,
      active: state.active ? state.active : true,
      mainOportunity: proposalState.selectedOportunity || state.mainOportunity,
      opportunity: proposalState.oportunities,
      architects: formData?.project_architects
        ? formData?.project_architects
        : state.architects,
      customer: customerData,
      bus: formData?.project_bu,
      services: services,
      specialFields: [
        { name: "Desafios", value: draftItems.challenges },
        { name: "Cenário apresentado", value: draftItems.scenarios },
        { name: "Premissas", value: draftItems.premisses },
        {
          name: "Pontos não contemplados",
          value: draftItems.extra_points,
        },
        { name: "Arquitetura", value: draftItems.architecture },
        {
          name: "Fatores influenciadores",
          value: draftItems.main_factors,
        },
        {
          name: "Custos adicionais",
          value: formData.aditional_costs,
        },
        {
          name: "Resultados esperados",
          value: draftItems.expected_results,
        },
        { name: "Observações", value: draftItems.observations },
        { name: "Notas Internas", value: draftItems.internal_notes },
      ],
      awsCosts: awsCostsArr,
      totalValues: proposalState.totalValues,
      monthSelected: proposalState.month,
      paymentMethod: pipePaymentMethod,
      paymentSelected: paymentSelected,
      pipedriveDealOwner: pipedriveDealOwner,
      fileNames: [...fileNames],
      architectureFileNames: [...architectureFileNames],
      scenarioFileNames: [...scenarioFileNames],
      spreadSetup: proposalState.spreadSetup,
    };

    let customerName = proposalState?.customer?.names?.fantasy_name;
    if (!customerName) customerName = state?.customer?.names?.fantasy_name;

    await dynamoPut(
      `${process.env.REACT_APP_STAGE}-proposals`,
      state.id,
      proposalData
    );

    const cosideredUser = pipedriveDealOwner
      ? { name: pipedriveDealOwner.name, mail: pipedriveDealOwner.email }
      : user;

    await generatePDF(
      proposalData,
      customerName,
      customerBrand,
      customerData,
      cosideredUser
    );

    const username = localStorage.getItem("@dsm/username");
    const title = "Edição de Proposta Comercial";
    const description = `${username} editou a proposta: ${proposalData.name}`;
    logNewAuditAction(username, title, description);

    message.success("Proposta editada com sucesso!");
  } catch (err) {

    message.error("Erro ao editar a proposta");
  }

  return true;
};
