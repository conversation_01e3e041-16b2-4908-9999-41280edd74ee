/**
 * Previne erros de rede de chegarem ao React Error Boundary
 */

import axios from 'axios';

// Interceptor global para capturar e tratar erros de rede
axios.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    // Verificar se é um erro de rede que deve ser tratado silenciosamente
    const is404Error = error.response?.status === 404;
    const is500Error = error.response?.status === 500;

    const isMalformedUrl =
      error.config?.url?.includes('/devread/') ||
      error.config?.url?.includes('/devswitch-role');

    // Tratar erros 500 de forma mais robusta
    if (is500Error) {
      console.error('🚨 Erro 500 interceptado:', {
        url: error.config?.url,
        method: error.config?.method,
        status: error.response?.status,
        message: error.message,
        data: error.response?.data
      });

      return Promise.reject(error);
    }

    // Apenas suprimir URLs malformadas, não 404s de APIs válidas
    if (isMalformedUrl) {
      // Retornar uma resposta mock para evitar quebrar a aplicação
      return Promise.resolve({
        data: { data: { Items: [] } },
        status: 200,
        statusText: 'OK (Mock Response - URL Issue)',
        headers: {},
        config: error.config
      });
    }

    if (is404Error) {

    }

    return Promise.reject(error);
  }
);

// Interceptor para requisições - garantir URLs corretas
axios.interceptors.request.use(
  (config) => {
    if (config.url) {
      const originalUrl = config.url;
      
      const fixes = [
        { from: '/devread/', to: '/dev/read/' },
        { from: '/devswitch-role', to: '/dev/read/switch-role' },
        { from: '/devcustomers/', to: '/dev/customers/' },
        { from: '/devauth/', to: '/dev/auth/' },
        { from: '/devcognito/', to: '/dev/cognito/' },
        { from: '/devhealth', to: '/dev/health' },
        { from: 'devread/', to: 'dev/read/' },
        { from: 'devswitch-role', to: 'dev/read/switch-role' },
        { from: 'devcustomers/', to: 'dev/customers/' },
        { from: 'devauth/', to: 'dev/auth/' },
        { from: 'devcognito/', to: 'dev/cognito/' },
        { from: 'devhealth', to: 'dev/health' },
        // Correções adicionais para URLs malformadas
        { from: 'devread/audits', to: 'dev/read/audits' },
        { from: 'devread/paginate', to: 'dev/read/paginate' }
      ];

      for (const fix of fixes) {
        if (config.url.includes(fix.from)) {
          config.url = config.url.replace(fix.from, fix.to);

        }
      }
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Sobrescrever console.error temporariamente para suprimir erros específicos
const originalConsoleError = console.error;
console.error = (...args) => {
  const message = args.join(' ');
  
  if (
    message.includes('Network Error') ||
    message.includes('ERR_FAILED') ||
    message.includes('CORS policy') ||
    message.includes('XMLHttpRequest') ||
    message.includes('devread') ||
    message.includes('devswitch-role')
  ) {
    return;
  }
  
  originalConsoleError.apply(console, args);
};

// Interceptar XMLHttpRequest para corrigir URLs malformadas
const originalXHROpen = XMLHttpRequest.prototype.open;
XMLHttpRequest.prototype.open = function(method, url, ...args) {
  // Corrigir URL se necessário
  if (typeof url === 'string') {
    const fixes = [
      { from: '/devread/', to: '/dev/read/' },
      { from: '/devswitch-role', to: '/dev/read/switch-role' },
      { from: '/devcognito/', to: '/dev/cognito/' },
      { from: '/devhealth', to: '/dev/health' },
      { from: 'devread/', to: 'dev/read/' },
      { from: 'devswitch-role', to: 'dev/read/switch-role' },
      { from: 'devcognito/', to: 'dev/cognito/' },
      { from: 'devhealth', to: 'dev/health' },
      // Correções adicionais para URLs malformadas
      { from: 'devread/audits', to: 'dev/read/audits' },
      { from: 'devread/paginate', to: 'dev/read/paginate' }
    ];

    for (const fix of fixes) {
      if (url.includes(fix.from)) {
        const oldUrl = url;
        url = url.replace(fix.from, fix.to);

      }
    }
  }

  return originalXHROpen.call(this, method, url, ...args);
};

// Interceptar erros do XMLHttpRequest
const originalXHRSend = XMLHttpRequest.prototype.send;
XMLHttpRequest.prototype.send = function(...args) {
  this.addEventListener('error', (event) => {

    event.stopPropagation();
  });

  this.addEventListener('loadend', () => {
    if (this.status === 0 || this.status >= 400) {

    }
  });

  return originalXHRSend.apply(this, args);
};



export default {
  restore: () => {
    console.error = originalConsoleError;

  }
};
