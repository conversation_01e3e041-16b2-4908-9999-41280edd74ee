export const chartPayload = [
  {
    name: "<PERSON> andamento",
    value: 0,
    fill: "#77D22C",
    status: "Em andamento",
    tickets: [],
  },
  {
    name: "<PERSON><PERSON><PERSON><PERSON>",
    value: 0,
    fill: "#628D55",
    status: "reopened",
    tickets: [],
  },
  {
    name: "Pendente Darede",
    value: 0,
    fill: "#113516",
    status: "Pendente Darede",
    tickets: [],
  },
  {
    name: "Pendente Cliente",
    value: 0,
    fill: "#5E6715",
    status: "Pendente Cliente",
    tickets: [],
  },
  {
    name: "Backlog",
    value: 0,
    fill: "#EA2424",
    status: "backlog",
    tickets: [],
  },
  {
    name: "Pendente fechamento",
    value: 0,
    fill: "#FF9632",
    status: "pending auto close+",
    tickets: [],
  },
  {
    name: "Fechado com êxito",
    value: 0,
    fill: "#36BC09",
    status: "closed successful",
    tickets: [],
  },
  {
    name: "<PERSON><PERSON><PERSON> sem êxito",
    value: 0,
    fill: "#C83232",
    status: "closed unsuccessful",
    tickets: [],
  },
];

export const defaultColors = [
  "rgba(65, 142, 77, 1)",
  "rgba(112, 211, 63, 1)",
  "rgba(17, 53, 22, 1)",
  "rgba(94, 103, 21, 1)",
  "rgba(234, 36, 36, 1)",
  "rgba(255, 150, 50, 1)",
  "rgba(54, 188, 9, 1)",
  "rgba(150, 150, 10, 1)",
];

export const data = {
  labels: [
    " Backlog",
    " Em andamento",
    " Reabertos",
    " Pendente cliente",
    " Pendente Darede",
    " Pendente auto fechamento",
    " Fechado com êxito",
    " Fechado sem êxito",
  ],
  datasets: [
    {
      label: "# of Votes",
      data: [30.8, 13.7, 55.4, 13.7, 2.7, 2.7, 2.7, 2.7],
      backgroundColor: defaultColors,
      borderColor: defaultColors,
      borderWidth: 1,
    },
  ],
};
