import Axios from "axios";
import { logger } from "../utils/logger";

/**
 * Configuração da API Jira com validação de ambiente
 */
const getJiraBaseUrl = () => {
    const jiraUrl = process.env.REACT_APP_API_JIRA_BASE_URL;

    if (!jiraUrl) {
        logger.warn('REACT_APP_API_JIRA_BASE_URL não está definida. Funcionalidades do Jira serão desabilitadas.');
        return null;
    }

    // Garantir que a URL termine com /
    return jiraUrl.endsWith('/') ? jiraUrl : `${jiraUrl}/`;
};

/**
 * Verifica se a API Jira está disponível
 */
export const isJiraAvailable = () => {
    return getJiraBaseUrl() !== null;
};

/**
 * Gera token para autenticação Jira
 */
export const generateToken = async () => {
    const baseUrl = getJiraBaseUrl();

    if (!baseUrl) {
        throw new Error('API Jira não está configurada. Verifique REACT_APP_API_JIRA_BASE_URL.');
    }

    try {
        const { data } = await Axios.get(`${baseUrl}get/token`);
        return data.data;
    } catch (error) {
        logger.error('Erro ao gerar token Jira:', error);
        throw new Error('Falha na autenticação com Jira');
    }
};

/**
 * Faz requisições GET para a API Jira
 */
export const jiraGet = async (route) => {
    const baseUrl = getJiraBaseUrl();

    if (!baseUrl) {
        logger.warn(`Tentativa de chamada Jira bloqueada: ${route}`);
        // Retornar dados mock para evitar quebrar a aplicação
        return {
            data: {
                values: [],
                issues: [],
                worklogs: []
            }
        };
    }

    try {
        const { data } = await Axios.get(`${baseUrl}${route}`);
        return data;
    } catch (error) {
        logger.error(`Erro na requisição Jira [${route}]:`, error);

        // Retornar dados mock em caso de erro
        return {
            data: {
                values: [],
                issues: [],
                worklogs: []
            }
        };
    }
};