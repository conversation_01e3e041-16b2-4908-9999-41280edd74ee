import { Col, Row } from "antd";
import { useEffect, useState } from "react";

export default function ClientTab(props) {
  const [update, setUpdate] = useState(0);
  useEffect(() => {}, [props.favorites]);

  /* Checking if the array is empty. */
  if (props.clientsComponents.length === 0) {
    return <h1 className="font-bold shadow-lg">Nenhuma visão encontrada!</h1>;
  }

  return (
    <Row justify="space-between" gutter={[48, 20]}>
      {
        /* Mapping the array of objects. */
        props.clientsComponents.map((val, index) => {
          return (
            <Col update={update} xl={12} lg={24} key={index}>
              {props.components[val.component]?.object}
            </Col>
          );
        })
      }
    </Row>
  );
}
