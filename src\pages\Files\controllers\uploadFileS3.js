import axios from "axios";

export const uploadFileS3 = async (file, customerId, customerName, fileId) => {
  const backUrl = `${process.env.REACT_APP_API_PERMISSION}s3/link`;
  const extension = file.name.split(".").pop();
  const body = {
    bucket: `${process.env.REACT_APP_STAGE}-files-archive`,
    key: `${customerId}-${encodeURI(customerName)}/${fileId}.${extension}`,
    operation: "put",
    content: file.type,
  };
  const headers = {
    headers: {
      authorization: localStorage.getItem("jwt"),
    },
  };

  const {
    data: { data },
  } = await axios.post(backUrl, body, headers);

  await axios
    .put(data, file.originFileObj, {
      headers: {
        "Content-Type": file.type,
      },
    })
    .then(() => {
      return true;
    });
};
