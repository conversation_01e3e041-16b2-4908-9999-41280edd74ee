import { Input, Select } from "antd";
import { useState } from "react";
import { realMask } from "../../utils/masks";

export const PriceInput = ({
  form,
  formListName,
  fieldName,
  initialValue,
  index,
}) => {
  const { Option } = Select;
  const [value, setValue] = useState(initialValue || "");

  const handleUnitDeafultValue = () => {
    if (formListName === "aditional_costs") {
      if (fieldName.includes("month")) {
        const data = form.getFieldValue(formListName);
        if (data[index]?.month_unit) {
          return data[index]["month_unit"];
        } else {
          return "dolar";
        }
      } else {
        const data = form.getFieldValue(formListName);
        if (data[index]?.year_unit) {
          return data[index]["year_unit"];
        } else {
          return "dolar";
        }
      }
    } else if (formListName === "main_aws_investments") {
      if (fieldName.includes("month")) {
        const data = form.getFieldValue(formListName);
        if (data[index]?.month_unit) {
          return data[index]["month_unit"];
        } else {
          return "dolar";
        }
      } else if (fieldName.includes("year")) {
        const data = form.getFieldValue(formListName);
        if (data[index]?.year_unit) {
          return data[index]["year_unit"];
        } else {
          return "dolar";
        }
      } else {
        const data = form.getFieldValue(formListName);
        if (data[index]?.upfront_unit) {
          return data[index]["upfront_unit"];
        } else {
          return "dolar";
        }
      }
    } else {
      return "dolar";
    }
  };

  const handleUnitSelect = (e) => {
    if (formListName === "aditional_costs") {
      if (fieldName.includes("month")) {
        let data = form.getFieldValue(formListName);
        data[index] = { ...data[index], month_unit: e };
        form.setFieldsValue({ formListName, data });
      } else {
        let data = form.getFieldValue(formListName);
        data[index] = { ...data[index], year_unit: e };
        form.setFieldsValue({ formListName, data });
      }
    } else if (formListName === "main_aws_investments") {
      if (fieldName.includes("month")) {
        let data = form.getFieldValue(formListName);
        data[index] = { ...data[index], month_unit: e };
        form.setFieldsValue({ formListName, data });
      } else if (fieldName.includes("year")) {
        let data = form.getFieldValue(formListName);
        data[index] = { ...data[index], year_unit: e };
        form.setFieldsValue({ formListName, data });
      } else {
        let data = form.getFieldValue(formListName);
        data[index] = { ...data[index], upfront_unit: e };
        form.setFieldsValue({ formListName, data });
      }
    } else {
      return;
    }
  };

  const handleOnChange = (e) => {
    let data = form.getFieldValue(formListName);
    if (data[index]) {
      data[index][fieldName] = realMask(e.currentTarget.value);
    } else {
      data[index] = { [fieldName]: realMask(e.currentTarget.value) };
    }
    form.setFieldsValue({ formListName, data });
    setValue(realMask(e.currentTarget.value));
  };

  const selectAfterCosts = (
    <Select
      defaultValue={handleUnitDeafultValue}
      onSelect={(e) => {
        handleUnitSelect(e);
      }}
    >
      <Option value="dolar">$</Option>
      <Option value="reais">R$</Option>
    </Select>
  );
  return (
    <Input
      type="text"
      value={value}
      addonAfter={selectAfterCosts}
      maxLength={14}
      onChange={(e) => handleOnChange(e)}
    />
  );
};
