import {
  MenuUnfoldOutlined,
  MenuFoldOutlined,
  PoweroffOutlined,
  BellOutlined,
  BellFilled,
  ExclamationCircleTwoTone,
  CheckCircleTwoTone,
} from "@ant-design/icons";
import { Link, NavLink } from "react-router-dom";
import logo from "../../assets/images/logo_full.png";
import { Row, Col, Button, notification, Space, Typography } from "antd";
import { Layout, Image } from "antd";
import React from "react";
import { LogoffButton } from "./components/logoffButton";
import useComplianceData from "./controllers/index";

export const HeaderMenu = (props) => {
  const { collapsed, setCollapsed } = props;
  const { Header } = Layout;
  const { Text } = Typography;

  const { existsNotification } = useComplianceData();

  const openNotification = () => {
    if (existsNotification) {
      notification.open({
        message: "Notificações",
        description: `<PERSON>á Client<PERSON> que contém itens como “Não Compliance”.
                      Verifique a página de Billing.`,
        icon: <ExclamationCircleTwoTone twoToneColor="#F44336" />,
        onClick: () => {
          console.log("Notification clicked");
        },
      });
    } else {
      notification.open({
        message: "Notificações",
        description: `Não há pendências.`,
        icon: <CheckCircleTwoTone twoToneColor="#00B050" />,
        onClick: () => {
          console.log("Notification clicked");
        },
      });
    }
  };

  return (
    <Header style={{ backgroundColor: "white" }}>
      <Row align="end" justify="space-between">
        <Col>
          <Button
            style={{ border: "none" }}
            onClick={() => setCollapsed(!collapsed)}
          >
            {collapsed === true ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
          </Button>
        </Col>
        <Col style={{ height: "64px" }}>
          <Link to="/">
            <Image src={logo} height={59} width={120} preview={false} />
          </Link>
        </Col>

        {window.innerWidth <= 500 ? (
          <Col>
            <NavLink to={"/logoff"}>
              <Button
                style={{ marginTop: "15px", marginLeft: "15px" }}
                onClick={() => {
                  localStorage.clear();
                }}
              >
                <PoweroffOutlined />
              </Button>
            </NavLink>
          </Col>
        ) : (
          <Row style={{ height: "64px" }}>
            <Space wrap>
              <Button type="text" onClick={openNotification}>
                {existsNotification ? (
                  <BellFilled
                    style={{ fontSize: "1.2rem", color: "#F44336" }}
                  />
                ) : (
                  <BellOutlined style={{ fontSize: "1.2rem" }} />
                )}
              </Button>
              <Text>{localStorage.getItem("@dsm/username")}</Text>
              <LogoffButton />
            </Space>
          </Row>
        )}
      </Row>
    </Header>
  );
};
