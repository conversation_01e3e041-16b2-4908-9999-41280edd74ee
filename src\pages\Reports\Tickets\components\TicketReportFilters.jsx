import { useEffect, useState, useMemo, useRef } from "react";
import { shallowEqual, useSelector } from "react-redux";
import { CSVLink } from "react-csv";
import {
  ticketsReportColumns,
  ticketsReportDetailedColumns,
} from "../../constants/exportTicketReportsConstants.js";
import { Row, Col, Typography, Button, Space } from "antd";
import {
  SearchOutlined,
  FileExcelOutlined,
  DownloadOutlined,
} from "@ant-design/icons";

import { Select } from "../../../../components/Select";
import { DateFilter } from "../../../../components/DateFilter";
import { SearchInput } from "../../../../components/SearchInput";

import { setTicketReportState } from "../../../../store/actions/ticket-report-action";
import { selectTicketReportMonth } from "../../../../store/reducers/tickets-report-reduce";
import { getActiveContractsV2 } from "../../../../controllers/contracts/contract-controller";

import * as controller from "../../../../controllers/reports/ticket-reports-controller";
import { filterTableData } from "../../../../utils/filterTableData";
import moment from "moment";
import { getMonth } from "date-fns";

const searchFields = [
  "ticket_number",
  "queue_name",
  "customer_representative",
  "created_user",
  "ticket_priority",
];

export const TicketReportFilters = ({ tickets }) => {
  return (
    <>
      <Row>
        <CustomersSelect />
        <ContractsSelect />
        <MonthFilter />
        <SearchButton />
      </Row>
      <Row justify="space-between" style={{ marginBottom: "10px" }}>
        <Col>
          <SearchInput
            onChange={(e) => {
              setTicketReportState({
                field: "search",
                value: e,
              });
              setTicketReportState({
                field: "filteredTickets",
                value: filterTableData({
                  data: tickets,
                  search: e,
                  searchFields,
                }),
              });
            }}
            placeholder="Filtar por . . ."
            style={{ borderRadius: 2, marginBottom: "8px" }}
          />
        </Col>
        <Col>
          <ExportReportButtons />
        </Col>
      </Row>
    </>
  );
};

const CustomersSelect = () => {
  const [loading, setLoading] = useState(false);

  const customers = useSelector(
    (state) => state.ticketReport.customers,
    shallowEqual
  );

  const customerSelected = useSelector(
    (state) => state.ticketReport.customerSelected,
    shallowEqual
  );

  useEffect(() => {
    init();
  }, []);

  async function init() {
    try {
      setLoading(true);
      const customerList = await controller.getCustomerList();
      setTicketReportState({ field: "customers", value: customerList });
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  }

  function handleChange(value) {
    const customer = controller.getCustomerByName(value);

    setTicketReportState({
      field: "customerSelected",
      value: customer,
    });
    setTicketReportState({
      field: "contractSelected",
      value: "Todos",
    });
  }

  return (
    <Col
      xl={{ span: 7 }}
      lg={{ span: 10 }}
      md={{ span: 6 }}
      sm={{ span: 10 }}
      style={{ marginBottom: 10 }}
    >
      <Typography.Text>
        Clientes <strong style={{ color: "red" }}>*</strong>
      </Typography.Text>
      <Select
        onChange={handleChange}
        options={customers}
        value={customerSelected?.value}
        style={{ width: "100%" }}
        loading={loading}
        showSearch
      />
    </Col>
  );
};

const ContractsSelect = () => {
  const [loading, setLoading] = useState(false);

  const tickets = useSelector((state) => state.ticketReport.tickets);

  const contracts = useSelector(
    (state) => state.ticketReport.contracts,
    shallowEqual
  );

  const customerSelected = useSelector(
    (state) => state.ticketReport.customerSelected,
    shallowEqual
  );

  const contractSelected = useSelector(
    (state) => state.ticketReport.contractSelected,
    shallowEqual
  );

  useEffect(() => {
    init();
  }, []);

  async function init() {
    try {
      setLoading(true);
      const contracts = await getActiveContractsV2();
      setTicketReportState({ field: "contracts", value: contracts });
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  }

  const listValues = useMemo(() => {
    return controller.filteredContractsByCustomer(contracts, customerSelected);
  }, [contracts, customerSelected]);

  return (
    <Col
      xl={{ span: 7, offset: 1 }}
      lg={{ span: 10, offset: 1 }}
      md={{ span: 6, offset: 1 }}
      sm={{ span: 10, offset: 1 }}
      style={{ marginBottom: 10 }}
    >
      <Typography.Text>Contratos</Typography.Text>
      <Select
        onChange={(value) => {
          console.log(value);
          setTicketReportState({ field: "contractSelected", value });
          setTicketReportState({
            field: "filteredTickets",
            value: filterTableData({
              data: tickets,
              search: value.toString(),
              searchFields: ["contract_id"],
            }),
          });
        }}
        filterOption={(input, option) =>
          option?.children?.toLowerCase().startsWith(input?.toLowerCase()) ||
          option?.children?.toLowerCase().includes(input?.toLowerCase())
        }
        options={listValues}
        value={contractSelected}
        style={{ width: "100%" }}
        loading={loading}
        showSearch
        disabled={customerSelected === null}
      />
    </Col>
  );
};

const SearchButton = () => {
  const customerSelected = useSelector(
    (state) => state.ticketReport.customerSelected,
    shallowEqual
  );
  const contractSelected = useSelector(
    (state) => state.ticketReport.contractSelected,
    shallowEqual
  );
  const month = useSelector(selectTicketReportMonth, shallowEqual);

  return (
    <Col
      xl={{ span: 3, offset: 1 }}
      md={{ span: 4, offset: 1 }}
      lg={{ span: 6, offset: 1 }}
      sm={{ span: 7 }}
      style={{
        alignItems: "flex-end",
        display: "flex",
        marginBottom: 10,
        minWidth: 105,
      }}
    >
      <Button
        type="primary"
        style={{ width: "100%" }}
        icon={<SearchOutlined />}
        onClick={() => {
          controller.searchTicketsByDate(
            customerSelected,
            month,
            contractSelected
          );
        }}
        disabled={customerSelected === null}
      >
        Pesquisar
      </Button>
    </Col>
  );
};

const MonthFilter = () => {
  const month = useSelector(selectTicketReportMonth, shallowEqual);

  return (
    <Col
      xl={{ span: 4, offset: 1 }}
      lg={{ span: 6, offset: 1 }}
      md={{ span: 3, offset: 1 }}
      sm={{ span: 10, offset: 1 }}
      style={{ marginBottom: 10 }}
    >
      <DateFilter
        label={"Mês:"}
        onChange={(e) => {
          setTicketReportState({ field: "month", value: e });
        }}
        defaultValue={month}
        limit={true}
      />
    </Col>
  );
};

const ExportReportButtons = () => {
  const customerSelected = useSelector(
    (state) => state.ticketReport.customerSelected,
    shallowEqual
  );
  const month = useSelector((state) => state.ticketReport.month, shallowEqual);
  const search = useSelector(
    (state) => state.ticketReport.search,
    shallowEqual
  );

  return (
    <Space wrap>
      <ExportSummarizedReportButton
        customerSelected={customerSelected}
        month={month}
        search={search}
      />
      <ExportDetailedReportButton
        customerSelected={customerSelected}
        month={month}
        search={search}
      />
    </Space>
  );
};

const ExportSummarizedReportButton = ({ customerSelected, month, search }) => {
  const tickets = useSelector(
    (state) => state.ticketReport.tickets,
    shallowEqual
  );
  const contractSelected = useSelector(
    (state) => state.ticketReport.contractSelected,
    shallowEqual
  );
  const filteredTickets = useSelector(
    (state) => state.ticketReport.filteredTickets,
    shallowEqual
  );

  const formatTickets = () => {
    if (search === "" && contractSelected === "Todos")
      return tickets.map((ticket) => {
        return {
          ...ticket,
          ticket_age: controller.formatTicketAge(ticket).age,
        };
      });
    return filteredTickets.map((ticket) => {
      return {
        ...ticket,
        ticket_age: controller.formatTicketAge(ticket).age,
      };
    });
  };

  const exportData = useMemo(() => {
    let formattedTickets = formatTickets();
    console.log(formattedTickets);
    return formattedTickets;
  }, [tickets, filteredTickets]);

  return (
    <CSVLink
      separator={";"}
      headers={ticketsReportColumns}
      data={exportData}
      filename={`tickets_${customerSelected?.label}_${month}.csv`}
    >
      <Button
        type="primary"
        style={{ width: "100%" }}
        icon={<FileExcelOutlined />}
        disabled={customerSelected === null}
      >
        Exportar Sumarizado
      </Button>
    </CSVLink>
  );
};

const ExportDetailedReportButton = ({ customerSelected, month }) => {
  const contractSelected = useSelector(
    (state) => state.ticketReport.contractSelected,
    shallowEqual
  );
  const [loading, setLoading] = useState(false);
  const [initiateDownload, setInitiateDownload] = useState(false);
  const excelRef = useRef();
  const [detailedTickets, setDetailedTickets] = useState([]);

  const removeBreakRow = (data) => {
    return data.map((ticket) => {
      return {
        ...ticket,
        article_body: ticket.article_body.replace(/\n/g, " "),
      };
    });
  };

  const handleGetDetailedTickets = async () => {
    setLoading(true);
    let data = await controller.getDetailedTicketsReport(
      customerSelected,
      month
    );

    let formmatedData = data.map((ticket) => {
      return {
        ...ticket,
        ticket_age: controller.formatTicketAge(ticket).age,
      };
    });

    if (contractSelected !== "Todos")
      formmatedData = formmatedData.filter(
        (ticket) => parseInt(ticket.contract_id) === parseInt(contractSelected)
      );

    setDetailedTickets(
      controller.removeSignatureFromArticleBody(removeBreakRow(formmatedData))
    );
    setInitiateDownload(true);
    setLoading(false);
  };

  useEffect(() => {
    if (initiateDownload) {
      setInitiateDownload(false);
    }
  }, [customerSelected]);

  return (
    <Row justify="space-between" align="middle" gutter={8}>
      <Col>
        {initiateDownload && (
          <CSVLink
            ref={excelRef}
            separator={";"}
            headers={ticketsReportDetailedColumns}
            data={detailedTickets}
            filename={`tickets_detalhado_${customerSelected?.label}_${month}.csv`}
            target="_blank"
          >
            <Button icon={<FileExcelOutlined />} type="ghost">
              Exportar Detalhado
            </Button>
          </CSVLink>
        )}
      </Col>
      <Col>
        <Button
          type="link"
          icon={<DownloadOutlined />}
          onClick={handleGetDetailedTickets}
          disabled={customerSelected === null}
          loading={loading}
        >
          Download de detalhes
        </Button>
      </Col>
    </Row>
  );
};
