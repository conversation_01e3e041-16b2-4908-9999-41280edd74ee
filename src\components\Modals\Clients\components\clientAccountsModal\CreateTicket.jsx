import {
  Button,
  DatePicker,
  Form,
  Input,
  Modal,
  Select,
  TreeSelect,
  message,
} from "antd";
import { useEffect, useState } from "react";
import {
  createTicketOtrs,
  formatQueues,
  getPersonsOtrs,
  getUserOtrs,
  listQueuesOTRS,
} from "../../controllers/clientAccountsModal";
import moment from "moment";
import { dynamoPost } from "../../../../../service/apiDsmDynamo";

export const CreateTicket = (props) => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [form] = Form.useForm();
  const [queues, setQueues] = useState([]);
  const [queuesLoaded, setQueuesLoaded] = useState(false);
  const [userId, setUserId] = useState(0);
  const [loading, setLoading] = useState(false);
  const [queuesLoading, setQueuesLoading] = useState(false);
  const defaultDate = moment().add(1, "day");
  const { client: customer, accounts, setPopoverZIndex } = props;
  const [contactsList, setContactsList] = useState([]);
  const [contactsLoading, setContactsLoading] = useState(false);

  const showModal = async () => {
    setIsModalVisible(true);
    setPopoverZIndex(900);
    setContactsLoading(true);
    if (!queuesLoaded) {
      setQueuesLoading(true);
      const otrsUserId = await getUserOtrs();
      setUserId(otrsUserId);
      const queuesOTRS = await listQueuesOTRS(otrsUserId);
      setQueues(formatQueues(queuesOTRS));
      setQueuesLoading(false);
      setQueuesLoaded(true);
    }
    const persons = await getPersonsOtrs(customer.identifications.itsm_id);

    setContactsList(persons);
    setContactsLoading(false);
    form.setFieldsValue({
      pendingTime: defaultDate,
    });
    handleTitle();
  };

  const handleOk = () => {
    form.submit();
  };

  const handleCancel = () => {
    form.resetFields();
    setPopoverZIndex(2000);
    setIsModalVisible(false);
  };

  const handleSubmit = async (form) => {
    setLoading(true);

    try {
      let formData = form.getFieldsValue();
      formData = { ...formData, from: formData.to };

      formData.pendingTime = {
        year: formData.pendingTime.year(),
        month: formData.pendingTime.month() + 1,
        day: formData.pendingTime.date(),
        hour: formData.pendingTime.hour(),
        minute: formData.pendingTime.minute(),
      };

      const response = await createTicketOtrs(formData, userId);

      if (response.Error || response?.response?.status === 500) {
        message["error"]({
          content:
            "Fila indisponível no momento, escolha outra fila ou converse com seu gestor para obter as permissões necessárias.",
          style: { zIndex: 3000 },
        });

        setLoading(false);
        return;
      }

      props.setTicketNumber(response.TicketNumber);

      const username = localStorage.getItem("@dsm/username");
      const title = "Criação de ticket";
      const description = `${username} criou o ticket ${
        response.TicketNumber
      }, para o cliente ${
        customer?.names?.fantasy_name || customer?.names?.name
      }`;

      dynamoPost(`${process.env.REACT_APP_STAGE}-audits`, {
        username: username,
        name: title,
        description: description,
        created_at: new Date(),
        updated_at: new Date(),
      });

      message.success("Ticket criado com sucesso!");
      setLoading(false);
      handleCancel();
    } catch (error) {
      console.log(error);
      message.error("Ocorreu um erro ao tentar criar ticket.");
      setLoading(false);
    }
  };

  const handleTitle = () => {
    const accountNames = accounts?.map(
      (account) =>
        customer.accounts.find((a) => a.account_id === account)?.profile
    );

    const title =
      accountNames.length > 1
        ? `Switch Role - Contas ${accountNames
            .slice(0, -1)
            .join(", ")} e ${accountNames.slice(-1)}`
        : accountNames.length === 1
        ? `Switch Role - Conta ${accountNames[0]}`
        : "Switch Role";

    form.setFieldsValue({ title });
  };

  useEffect(() => {
    handleTitle();
  }, [customer, accounts]);

  return (
    <>
      <Button type="primary" onClick={showModal}>
        Criar ticket
      </Button>
      <Modal
        title="Criar ticket"
        open={isModalVisible}
        onOk={() => {
          handleOk();
        }}
        confirmLoading={loading}
        okText="Criar ticket"
        onCancel={handleCancel}
        cancelText="Cancelar"
      >
        <Form
          layout="vertical"
          onFinish={async () => {
            handleSubmit(form);
          }}
          requiredMark={false}
          form={form}
        >
          <Form.Item
            name="title"
            label="Título"
            rules={[{ required: true, message: "Preencha este campo." }]}
          >
            <Input placeholder="Título do ticket" />
          </Form.Item>
          <Form.Item
            name="description"
            label="Descrição"
            rules={[{ required: true, message: "Preencha este campo." }]}
          >
            <Input.TextArea placeholder="Descrição do ticket" />
          </Form.Item>
          <Form.Item
            name="queue"
            label="Fila"
            rules={[{ required: true, message: "Preencha este campo." }]}
          >
            <TreeSelect
              showSearch
              style={{ width: "100%" }}
              dropdownStyle={{ maxHeight: 400, overflow: "auto", zIndex: 1500 }}
              placeholder="Selecione uma fila"
              allowClear
              treeData={queues}
              loading={queuesLoading}
            />
          </Form.Item>
          <Form.Item
            name="to"
            label="Para"
            rules={[{ required: true, message: "Preencha este campo." }]}
          >
            <Select
              options={contactsList.map((contact) => ({
                label: contact.email,
                value: contact.email,
              }))}
              getPopupContainer={(trigger) => trigger.parentElement}
              showSearch
              placeholder="Selecione um contato"
              optionFilterProp="label"
              filterOption={(input, option) =>
                option.label.toLowerCase().includes(input.toLowerCase())
              }
              loading={contactsLoading}
            />
          </Form.Item>
          <Form.Item
            name="pendingTime"
            label="Data do fechamento"
            rules={[{ required: true, message: "Preencha este campo." }]}
          >
            <DatePicker
              defaultValue={defaultDate}
              showTime
              format="DD/MM/YYYY HH:mm"
              onChange={(value) => {
                form.setFieldsValue({ pendingTime: value });
              }}
              getPopupContainer={(trigger) => trigger.parentElement}
              style={{ width: "100%" }}
            />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};
