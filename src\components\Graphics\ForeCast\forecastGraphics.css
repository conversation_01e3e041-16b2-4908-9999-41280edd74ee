.header-test {
     display: flex;
     justify-content: space-around;
     align-items: center;
}

.back-button {
     display: flex;
     justify-content: center;
     align-items: center;
     border-radius: 20px;
     width: 40px;
     margin-right: 20px;
}

.back-button span {
     font-size: 20px;
     font-weight: bold;
}

.card-sub-header{
     display: flex;
     justify-content: space-between;
     align-items: center;
     width: 370px;
}

.title-name {
     font-size: 24px;
     font-weight: 400;
     margin: 0;
}

.header-icon {
     display: flex;
     align-items: center;
     padding: 10px 16px;
     border-radius: 6px;
     box-shadow: 0px 0px 4px 4px #eaecee;
     padding: 10px;
     color: #B4BAC2;
}

.infosContainer1{
     display: flex;
     justify-content: flex-start;
     margin-left: 90px;
     gap: 24px;
}

.infoSingle{
     display: flex;
     align-items: center;
}