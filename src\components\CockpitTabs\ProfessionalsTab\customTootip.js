export const CustomTooltip = ({ active, payload, label }) => {
  return (
    <div
      style={{
        backgroundColor: "white",
        padding: "10px",
        borderRadius: "20px",
        boxShadow: "2px 2px 10px black",
      }}
      className="tooltip"
    >
      <p className="tooltipLabel">{`Não billada: ${payload[0].payload?.notBillable}h`}</p>
      <p className="tooltipDesc">{`Billada: ${payload[0].payload?.billable}h`}</p>
    </div>
  );
};
