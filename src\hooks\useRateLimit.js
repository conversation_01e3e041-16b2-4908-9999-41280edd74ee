import { useState, useEffect, useCallback, useRef } from 'react';

/**
 * Hook para gerenciar rate limiting no frontend
 * Controla a frequência de chamadas de API e fornece feedback visual
 */
export const useRateLimit = (options = {}) => {
  const {
    maxRequests = 10,
    windowMs = 60000, // 1 minuto
    onLimitReached = null,
    autoReset = true
  } = options;

  const [requestCount, setRequestCount] = useState(0);
  const [isLimited, setIsLimited] = useState(false);
  const [resetTime, setResetTime] = useState(null);
  const [timeRemaining, setTimeRemaining] = useState(0);
  
  const requestsRef = useRef([]);
  const timerRef = useRef(null);

  /**
   * Limpa requisições antigas da janela de tempo
   */
  const cleanupOldRequests = useCallback(() => {
    const now = Date.now();
    requestsRef.current = requestsRef.current.filter(
      timestamp => now - timestamp < windowMs
    );
    setRequestCount(requestsRef.current.length);
  }, [windowMs]);

  /**
   * Verifica se uma nova requisição pode ser feita
   */
  const canMakeRequest = useCallback(() => {
    cleanupOldRequests();
    return requestsRef.current.length < maxRequests;
  }, [cleanupOldRequests, maxRequests]);

  /**
   * Registra uma nova requisição
   */
  const recordRequest = useCallback(() => {
    const now = Date.now();
    
    if (!canMakeRequest()) {
      setIsLimited(true);
      const oldestRequest = Math.min(...requestsRef.current);
      const resetTime = oldestRequest + windowMs;
      setResetTime(resetTime);
      
      if (onLimitReached) {
        onLimitReached({
          requestCount: requestsRef.current.length,
          maxRequests,
          resetTime: new Date(resetTime)
        });
      }
      
      return false;
    }

    requestsRef.current.push(now);
    setRequestCount(requestsRef.current.length);
    return true;
  }, [canMakeRequest, maxRequests, windowMs, onLimitReached]);

  /**
   * Reset manual do rate limiter
   */
  const reset = useCallback(() => {
    requestsRef.current = [];
    setRequestCount(0);
    setIsLimited(false);
    setResetTime(null);
    setTimeRemaining(0);
    
    if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }
  }, []);

  /**
   * Wrapper para funções que fazem requisições
   */
  const withRateLimit = useCallback((fn) => {
    return async (...args) => {
      if (!recordRequest()) {
        throw new Error('Rate limit exceeded. Please try again later.');
      }
      
      try {
        return await fn(...args);
      } catch (error) {
        // Em caso de erro, podemos optar por não contar a requisição
        // requestsRef.current.pop();
        // setRequestCount(requestsRef.current.length);
        throw error;
      }
    };
  }, [recordRequest]);

  /**
   * Atualiza o tempo restante até o reset
   */
  useEffect(() => {
    if (isLimited && resetTime && autoReset) {
      const updateTimer = () => {
        const now = Date.now();
        const remaining = Math.max(0, resetTime - now);
        setTimeRemaining(remaining);
        
        if (remaining <= 0) {
          setIsLimited(false);
          setResetTime(null);
          cleanupOldRequests();
        }
      };

      updateTimer();
      timerRef.current = setInterval(updateTimer, 1000);

      return () => {
        if (timerRef.current) {
          clearInterval(timerRef.current);
          timerRef.current = null;
        }
      };
    }
  }, [isLimited, resetTime, autoReset, cleanupOldRequests]);

  /**
   * Limpeza periódica de requisições antigas
   */
  useEffect(() => {
    const cleanupInterval = setInterval(cleanupOldRequests, 10000); // A cada 10 segundos
    
    return () => clearInterval(cleanupInterval);
  }, [cleanupOldRequests]);

  /**
   * Formata o tempo restante em formato legível
   */
  const formatTimeRemaining = useCallback(() => {
    if (timeRemaining <= 0) return '0s';
    
    const seconds = Math.ceil(timeRemaining / 1000);
    if (seconds < 60) return `${seconds}s`;
    
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds}s`;
  }, [timeRemaining]);

  /**
   * Calcula a porcentagem de uso do limite
   */
  const usagePercentage = Math.min(100, (requestCount / maxRequests) * 100);

  return {
    // Estado
    requestCount,
    maxRequests,
    isLimited,
    canMakeRequest: canMakeRequest(),
    usagePercentage,
    timeRemaining,
    resetTime,
    
    // Ações
    recordRequest,
    reset,
    withRateLimit,
    
    // Utilitários
    formatTimeRemaining,
    
    // Informações
    remainingRequests: Math.max(0, maxRequests - requestCount),
    windowMs
  };
};

/**
 * Hook específico para rate limiting de APIs
 */
export const useApiRateLimit = (apiName, options = {}) => {
  const storageKey = `rateLimit_${apiName}`;
  
  // Carregar estado do localStorage
  const loadState = useCallback(() => {
    try {
      const saved = localStorage.getItem(storageKey);
      if (saved) {
        const { requests, timestamp } = JSON.parse(saved);
        const now = Date.now();
        
        // Filtrar requisições dentro da janela de tempo
        const validRequests = requests.filter(
          req => now - req < (options.windowMs || 60000)
        );
        
        return validRequests;
      }
    } catch (error) {
      console.warn('Failed to load rate limit state:', error);
    }
    return [];
  }, [storageKey, options.windowMs]);

  // Salvar estado no localStorage
  const saveState = useCallback((requests) => {
    try {
      localStorage.setItem(storageKey, JSON.stringify({
        requests,
        timestamp: Date.now()
      }));
    } catch (error) {
      console.warn('Failed to save rate limit state:', error);
    }
  }, [storageKey]);

  const rateLimiter = useRateLimit({
    ...options,
    onLimitReached: (info) => {
      console.warn(`Rate limit reached for ${apiName}:`, info);
      if (options.onLimitReached) {
        options.onLimitReached(info);
      }
    }
  });

  // Carregar estado inicial
  useEffect(() => {
    const savedRequests = loadState();
    if (savedRequests.length > 0) {
      // Restaurar estado (implementação simplificada)
      savedRequests.forEach(() => rateLimiter.recordRequest());
    }
  }, []);

  // Salvar estado quando houver mudanças
  useEffect(() => {
    if (rateLimiter.requestCount > 0) {
      const requests = Array(rateLimiter.requestCount).fill(Date.now());
      saveState(requests);
    }
  }, [rateLimiter.requestCount, saveState]);

  return {
    ...rateLimiter,
    apiName
  };
};

/**
 * Hook para rate limiting de formulários
 */
export const useFormRateLimit = (formName, options = {}) => {
  const defaultOptions = {
    maxRequests: 5,
    windowMs: 300000, // 5 minutos
    ...options
  };

  return useApiRateLimit(`form_${formName}`, defaultOptions);
};

export default useRateLimit;
