export const contractsViewMock = [
  {
    fieldName: "Data de início da vigência",
    key: "expected_start_date",
    content: "",
    permissionCode: "info_modal_view_start_date",
  },
  {
    fieldName: "Data de fim da vigência",
    key: "expected_close_date",
    content: "",
    permissionCode: "info_modal_view_end_date",
  },
  {
    fieldName: "Escopo",
    content: "",
    permissionCode: "info_modal_view_scope",
    key: "scope",
  },
  {
    fieldName: "Nome do contrato",
    key: "name",
    content: "",
    permissionCode: "info_modal_view_contract_name",
  },
  {
    fieldName: "ID do contrato",
    key: "itsm_id",
    content: "",
    permissionCode: "info_modal_view_contract_id",
  },
  {
    fieldName: "Total de horas",
    key: "total_hours",
    content: "",
    permissionCode: "info_modal_view_total_hours",
  },
  {
    fieldName: "Squad",
    key: "squad",
    content: "",
    permissionCode: "info_modal_view_squad",
  },
  {
    fieldName: "Porcentagem de bloqueio",
    key: "block",
    content: "",
    permissionCode: "info_modal_view_block_percentage",
  },
  {
    fieldName: "Porcentagem de notificação",
    key: "freezing",
    content: "",
    permissionCode: "info_modal_view_freezing_percentage",
  },
  {
    fieldName: "Tipo de consumo",
    key: "type_hours",
    content: "",
    permissionCode: "info_modal_view_type_hours",
  },
  {
    fieldName: "Data de Criação",
    key: "created_at",
    content: "",
    permissionCode: "info_modal_view_creation_date",
  },
];

export const permissionsMock = [
  { code: "info_modal_view_start_date" },
  { code: "info_modal_view_end_date" },
  { code: "info_modal_view_scope" },
  { code: "info_modal_view_contract_name" },
  { code: "info_modal_view_contract_id" },
  { code: "info_modal_view_total_hours" },
  { code: "info_modal_view_squad" },
  { code: "info_modal_view_block_percentage" },
  { code: "info_modal_view_freezing_percentage" },
  { code: "info_modal_view_type_hours" },
  { code: "info_modal_view_creation_date" },
];

export const filteredContractsMock = [
  { id: 1 },
  { id: 3 },
  { id: 4 },
  { id: 5 },
];

export const allContractsMock = [
  {
    active: 0,
    created_at: "2022-10-31 14:26:22",
    customer: { itsm_id: 44, id: 9 },
    customer_id: 9,
    customer_itsm: 44,
    duration: 12,
    emails: [],
    excess_cost: "0",

    id: 1,
    identifications: { itsm_id: 55, crm_id: 64 },
    name: "Contract1",
    percentage: { freezing: null, block: null },
    pool_type: "pool_8_5",
    scope: "Scope Contract1",
    squad: "SUST-E2",
    target_dates: {
      expected_start_date: "2022/07/07 00:00:00",
      expected_close_date: "2023/07/07 00:00:00",
    },
    total_hours: "0",
    type_hours: "Billing",
    updated_at: "2022-10-31 14:26:22",
    values: { value: "0" },
    verticals: { vertical_aws: null, vertical_pre_sale: null },
  },
  {
    active: 1,
    created_at: "2022-10-31 14:26:22",
    customer: { itsm_id: 45, id: 10 },
    customer_id: 10,
    customer_itsm: 45,
    duration: 12,
    emails: [],
    excess_cost: "0",
    id: 2,
    identifications: { itsm_id: 56, crm_id: 65 },
    name: "Contract2",
    percentage: { freezing: null, block: null },
    pool_type: "pool_8_5",
    scope: "Scope Contract2",
    squad: "SUST-E2",
    target_dates: {
      expected_start_date: "2022/07/07 00:00:00",
      expected_close_date: "2023/07/07 00:00:00",
    },
    total_hours: "0",
    type_hours: "Billing",
    updated_at: "2022-10-31 14:26:22",
    values: { value: "0" },
    verticals: { vertical_aws: null, vertical_pre_sale: null },
  },
  {
    active: 1,
    created_at: "2022-10-31 14:26:22",
    customer: { itsm_id: 46, id: 11 },
    customer_id: 11,
    customer_itsm: 46,
    duration: 12,
    emails: [],
    excess_cost: "0",
    id: 3,
    identifications: { itsm_id: 57, crm_id: 66 },
    name: "Contract3",
    percentage: { freezing: null, block: null },
    pool_type: "pool_8_5",
    scope: "Scope Contract3",
    squad: "SUST-E2",
    target_dates: {
      expected_start_date: "2022/07/07 00:00:00",
      expected_close_date: "2023/07/07 00:00:00",
    },
    total_hours: "0",
    type_hours: "Billing",
    updated_at: "2022-10-31 14:26:22",
    values: { value: "0" },
    verticals: { vertical_aws: null, vertical_pre_sale: null },
  },
  {
    active: 0,
    created_at: "2022-10-31 14:26:22",
    customer: { itsm_id: 47, id: 12 },
    customer_id: 12,
    customer_itsm: 47,
    duration: 12,
    emails: [],
    excess_cost: "0",
    id: 4,
    identifications: { itsm_id: 58, crm_id: 67 },
    name: "Contract4",
    percentage: { freezing: null, block: null },
    pool_type: "pool_8_5",
    scope: "Scope Contract4",
    squad: "SUST-E2",
    target_dates: {
      expected_start_date: "2022/07/07 00:00:00",
      expected_close_date: "2023/07/07 00:00:00",
    },
    total_hours: "0",
    type_hours: "Billing",
    updated_at: "2022-10-31 14:26:22",
    values: { value: "0" },
    verticals: { vertical_aws: null, vertical_pre_sale: null },
  },
  {
    active: 0,
    created_at: "2022-10-31 14:26:22",
    customer: { itsm_id: 48, id: 13 },
    customer_id: 13,
    customer_itsm: 48,
    duration: 12,
    emails: [],
    excess_cost: "0",
    id: 5,
    identifications: { itsm_id: 59, crm_id: 68 },
    name: "Contract5",
    percentage: { freezing: null, block: null },
    pool_type: "pool_8_5",
    scope: "Scope Contract5",
    squad: "SUST-E2",
    target_dates: {
      expected_start_date: "2022/07/07 00:00:00",
      expected_close_date: "2023/07/07 00:00:00",
    },
    total_hours: "0",
    type_hours: "Billing",
    updated_at: "2022-10-31 14:26:22",
    values: { value: "0" },
    verticals: { vertical_aws: null, vertical_pre_sale: null },
  },
];

export const contractInfoByPermissionMock = [
  [
    {
      fieldName: "ID do contrato",
      content: 55,
      permissionCode: "info_modal_view_contract_id",
      key: "itsm_id",
    },

    {
      fieldName: "Data de início da vigência",
      content: "07/07/2022",
      permissionCode: "info_modal_view_start_date",
      key: "expected_start_date",
    },

    {
      fieldName: "Data de fim da vigência",
      content: "07/07/2023",
      permissionCode: "info_modal_view_end_date",
      key: "expected_close_date",
    },

    {
      fieldName: "Data de Criação",
      content: "31/10/2022",
      permissionCode: "info_modal_view_creation_date",
      key: "created_at",
    },

    {
      fieldName: "Nome do contrato",
      content: "Contract1",
      permissionCode: "info_modal_view_contract_name",
      key: "name",
    },

    {
      fieldName: "Escopo",
      content: "Scope Contract1",
      permissionCode: "info_modal_view_scope",
      key: "scope",
    },

    {
      fieldName: "Squad",
      content: "SUST-E2",
      permissionCode: "info_modal_view_squad",
      key: "squad",
    },

    {
      fieldName: "Total de horas",
      content: "0",
      permissionCode: "info_modal_view_total_hours",
      key: "total_hours",
    },

    {
      fieldName: "Tipo de consumo",
      content: "Billing",
      permissionCode: "info_modal_view_type_hours",
      key: "type_hours",
    },
  ],
  [
    {
      fieldName: "ID do contrato",
      content: 57,
      permissionCode: "info_modal_view_contract_id",
      key: "itsm_id",
    },
    {
      fieldName: "Data de início da vigência",
      content: "07/07/2022",
      permissionCode: "info_modal_view_start_date",
      key: "expected_start_date",
    },
    {
      fieldName: "Data de fim da vigência",
      content: "07/07/2023",
      permissionCode: "info_modal_view_end_date",
      key: "expected_close_date",
    },
    {
      fieldName: "Data de Criação",
      content: "31/10/2022",
      permissionCode: "info_modal_view_creation_date",
      key: "created_at",
    },
    {
      fieldName: "Nome do contrato",
      content: "Contract3",
      permissionCode: "info_modal_view_contract_name",
      key: "name",
    },
    {
      fieldName: "Escopo",
      content: "Scope Contract3",
      permissionCode: "info_modal_view_scope",
      key: "scope",
    },
    {
      fieldName: "Squad",
      content: "SUST-E2",
      permissionCode: "info_modal_view_squad",
      key: "squad",
    },
    {
      fieldName: "Total de horas",
      content: "0",
      permissionCode: "info_modal_view_total_hours",
      key: "total_hours",
    },
    {
      fieldName: "Tipo de consumo",
      content: "Billing",
      permissionCode: "info_modal_view_type_hours",
      key: "type_hours",
    },
  ],
  [
    {
      fieldName: "ID do contrato",
      content: 58,
      permissionCode: "info_modal_view_contract_id",
      key: "itsm_id",
    },
    {
      fieldName: "Data de início da vigência",
      content: "07/07/2022",
      permissionCode: "info_modal_view_start_date",
      key: "expected_start_date",
    },
    {
      fieldName: "Data de fim da vigência",
      content: "07/07/2023",
      permissionCode: "info_modal_view_end_date",
      key: "expected_close_date",
    },
    {
      fieldName: "Data de Criação",
      content: "31/10/2022",
      permissionCode: "info_modal_view_creation_date",
      key: "created_at",
    },
    {
      fieldName: "Nome do contrato",
      content: "Contract4",
      permissionCode: "info_modal_view_contract_name",
      key: "name",
    },
    {
      fieldName: "Escopo",
      content: "Scope Contract4",
      permissionCode: "info_modal_view_scope",
      key: "scope",
    },
    {
      fieldName: "Squad",
      content: "SUST-E2",
      permissionCode: "info_modal_view_squad",
      key: "squad",
    },
    {
      fieldName: "Total de horas",
      content: "0",
      permissionCode: "info_modal_view_total_hours",
      key: "total_hours",
    },
    {
      fieldName: "Tipo de consumo",
      content: "Billing",
      permissionCode: "info_modal_view_type_hours",
      key: "type_hours",
    },
  ],
  [
    {
      fieldName: "ID do contrato",
      content: 59,
      permissionCode: "info_modal_view_contract_id",
      key: "itsm_id",
    },
    {
      fieldName: "Data de início da vigência",
      content: "07/07/2022",
      permissionCode: "info_modal_view_start_date",
      key: "expected_start_date",
    },
    {
      fieldName: "Data de fim da vigência",
      content: "07/07/2023",
      permissionCode: "info_modal_view_end_date",
      key: "expected_close_date",
    },
    {
      fieldName: "Data de Criação",
      content: "31/10/2022",
      permissionCode: "info_modal_view_creation_date",
      key: "created_at",
    },
    {
      fieldName: "Nome do contrato",
      content: "Contract5",
      permissionCode: "info_modal_view_contract_name",
      key: "name",
    },
    {
      fieldName: "Escopo",
      content: "Scope Contract5",
      permissionCode: "info_modal_view_scope",
      key: "scope",
    },
    {
      fieldName: "Squad",
      content: "SUST-E2",
      permissionCode: "info_modal_view_squad",
      key: "squad",
    },
    {
      fieldName: "Total de horas",
      content: "0",
      permissionCode: "info_modal_view_total_hours",
      key: "total_hours",
    },
    {
      fieldName: "Tipo de consumo",
      content: "Billing",
      permissionCode: "info_modal_view_type_hours",
      key: "type_hours",
    },
  ],
];
