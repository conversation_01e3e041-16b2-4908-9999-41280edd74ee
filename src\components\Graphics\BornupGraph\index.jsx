import {
  Radio,
  Typography,
  Row,
  Col,
  Card,
  Popover,
  Slider
} from "antd";
import React, { useState, useEffect, useMemo } from "react";
import { Line<PERSON>hart, CartesianGrid, XAxis, YAxis, Line, Tooltip } from "recharts";
import { FilterFilled } from "@ant-design/icons";
import BornupFilter from "../../Modals/Filters/BornupFilter";
import { otrsPost } from "../../../service/apiOtrs";
import moment from "moment";
import "./index.css";
export const BornupGraph = (props) => {
  const { Title, Text } = Typography;
  const [ticketTypeFilter, setTicketTypeFilter] = useState("Todos");
  const [popoverVisible, setPopoverVisible] = useState(false);
  const [filterData, setFilterData] = useState();
  const [dataChart, setDataChart] = useState([]);
  const [formatedData, setFormatedData] = useState([])
  const [isFilterEmpty, setIsFilterEmpty] = useState(true)
  const [allTickets, setAllTickets] = useState([]);
  const [skeletonChartData, setSkeletonChartData] = useState([]);
  const [rangeChartValue, setRangeChartValue] = useState(5);
  const [loading, setLoading] = useState(false);
  const { allContracts, allWallets } = props;


  function handlePopover() {
    setPopoverVisible(!popoverVisible);
  }

  async function getallTimeLogged(contractId, wallet, date, skeleton) {
    const formateDate = date
      ? moment(date).format("YYYY-MM")
      : moment().format("YYYY-MM");

    let response;

    if (contractId && !wallet) {
      response = await otrsPost(
        "read/tickets/allTimeLoggedByContractAndWalletInTimeRange",
        {
          params: {
            start: formateDate + "-01T00:00:00.000Z",
            end: formateDate + "-31T00:00:00.000Z",
            contractId,
          },
        }
      );
    } else if (wallet && !contractId) {
      response = await otrsPost(
        "read/tickets/allTimeLoggedByContractAndWalletInTimeRange",
        {
          params: {
            start: formateDate + "-01T00:00:00.000Z",
            end: formateDate + "-31T00:00:00.000Z",
            wallet,
          },
        }
      );
    } else {
      response = await otrsPost(
        "read/tickets/allTimeLoggedByContractAndWalletInTimeRange",
        {
          params: {
            start: formateDate + "-01T00:00:00.000Z",
            end: formateDate + "-31T00:00:00.000Z",
            contractId,
            wallet,
          },
        }
      );
    }

    const formatedDataChart = await formatDataChart(response.data, skeleton)
    setAllTickets(response.data)
    setFormatedData(formatedDataChart)
    setDataChart(formatedDataChart)
    setLoading(false)
  }

  useEffect(() => {
    const loadData = async () => {
      if (filterData?.contract || filterData?.wallet || filterData?.date) {
        setLoading(true)
        await generateSkeletonChartData(filterData)
        setIsFilterEmpty(false)
      } else {
        setIsFilterEmpty(true)
      }
    };

    loadData();
  }, [filterData])

  async function generateSkeletonChartData(filter) {
    const year = moment(filter.date).format("YYYY")
    const month = moment(filter.date).format("MM")
    const daysOnMonth = new Date(year, month, 0).getDate()
    const skeleton = []

    for (let i = 1; i <= daysOnMonth; i++) {
      skeleton.push({
        date: moment(`${year}-${month}-${i}`).format("DD-MM"),
        contractId: '',
        contractName: '',
        customerName: '',
        dailyConsumeRDS: 0,
        dailyConsumeIncidente: 0,
        ticketId: '',
        ticketPriority: '',
        ticketNumber: '',
        wallet: '',
      })
    }

    setSkeletonChartData(skeleton)

    await getallTimeLogged(
      filterData?.contract,
      filterData?.wallet,
      filterData?.date, skeleton
    );

  }

  async function formatDataChart(tickets, skeleton) {
    skeleton?.forEach((item) => {
      item.dailyConsumeIncidente = 0
      item.dailyConsumeRDS = 0
      tickets.forEach((t) => {

        if (item.date === moment(t.ticket_time_logged_create_time).format("DD-MM")) {
          let isRDS = t.ticket_priority.toLowerCase().includes("rds")

          const consumeNum = isRDS ? item.dailyConsumeRDS : item.dailyConsumeIncidente
          const currentConsume = parseInt(t.daily_total_consumed_min)

          if (isRDS) {
            item.dailyConsumeRDS =  consumeNum + currentConsume < 0 ? 0 : consumeNum + currentConsume
          } else {
            item.dailyConsumeIncidente = consumeNum + currentConsume < 0 ? 0 : consumeNum + currentConsume
          }
        }

      })

    })
    return skeleton
  }

  async function checkTicketType(event) {
    setLoading(true)
    setTicketTypeFilter(event.target.value)
    let value = event.target.value.toLowerCase()
    if (value === "todos") {
      const newData = await formatDataChart(allTickets, skeletonChartData)
      setDataChart(newData)
      setLoading(false)
      return true
    }
    if(value.includes("incident")){
      value = "incident"
    }
    const tickets = allTickets.filter((t) => t.ticket_priority.toLowerCase().includes(value))
    const newData = await formatDataChart(tickets, skeletonChartData)
    setDataChart(newData)
    setLoading(false)

  }

  return (
    <Card
      bordered={false}
      style={{
        borderRadius: "20px",
        height: "100%",
        boxShadow: "0 0 10px rgba(0,0,0,0.1)",
      }}
    >
      <Row justify={'space-between'} style={{ display: "flex", alignContent: "center", width: "100%" }}>
        <Title level={4} style={{ fontWeight: 400 }}>
          Gráfico de Burnup
        </Title>

        <Popover
          open={popoverVisible}
          placement="bottomLeft"
          onClick={() => handlePopover()}
          content={() => {
            return (
              <BornupFilter
                allContracts={allContracts}
                allWallets={allWallets}
                handlePopover={handlePopover}
                setFilterData={setFilterData}
              />
            );
          }}
        >
          <div
            style={{
              display: "flex",
              textAlign: "center",
              cursor: "pointer",
              gap: "5px",
              marginRight: "10px",
            }}
            className="filter"
          >
            <FilterFilled
              className="filter-icon"
            />
          </div>
        </Popover>
      </Row>
      <Row>

        <Col span={24} style={{ marginTop: "10px" }}>
          <Row justify="space-evenly">
            <Radio.Group defaultValue="Todos" onChange={checkTicketType} disabled={isFilterEmpty}>
              <Radio value="Todos">Todos</Radio>
              <Radio value="RDS">RDS</Radio>
              <Radio value="Inc">Incidente</Radio>
            </Radio.Group>
          </Row>
          <hr color="#00b050"></hr>
          {loading ? (
            <Row style={{ width: "100%", height: "280px", display: "flex", justifyContent: "center", alignContent: "center" }}>
              <Col>
                <Text>Carregando...</Text>
              </Col>
            </Row>
          ) : (

            !isFilterEmpty ? (
              dataChart.length > 0 ? (
                <Row>
                  <Col span={24}>
                    <LineChart
                      width={450}
                      height={250}
                      data={dataChart}
                    >
                      <Tooltip formatter={(value, name) => [`${value}`, `${name.slice(12)}`]} />
                      <CartesianGrid strokeDasharray="3" />
                      <XAxis dataKey="date" interval={rangeChartValue} />
                      <YAxis />
                      {ticketTypeFilter === "Inc" ? (
                        <Line type="monotone" dataKey="dailyConsumeIncidente" stroke="#FF0000" />
                      ) : ticketTypeFilter === "RDS" ? (
                        <Line type="monotone" dataKey="dailyConsumeRDS" stroke="#70D33F" />
                      ) : (
                        <>
                          <Line type="monotone" dataKey="dailyConsumeRDS" stroke="#70D33F" />
                          <Line type="monotone" dataKey="dailyConsumeIncidente" stroke="#FF0000" />
                        </>
                      )}
                    </LineChart>
                  </Col>
                    <Col span={24}>
                      <Text style={{marginLeft:"5px"}}>
                        Intervalo de dias:
                      </Text>
                    <Slider max={10} min={3} defaultValue={5} onChange={(e) => setRangeChartValue(e)} style={{ marginTop: "10px" }} />
                    </Col>
                </Row>
              ) : (
                <Row style={{ width: "100%", height: "280px", display: "flex", justifyContent: "center", alignContent: "center" }}>
                <Col>
                  <Text>Sem dados*</Text>
                </Col>
              </Row>
              )
            ) : (
              <Row style={{ width: "100%", height: "280px", display: "flex", justifyContent: "center", alignContent: "center" }}>
                <Col>
                  <Text>Selecione um cliente*</Text>
                </Col>
              </Row>
            )
          )}

        </Col>
      </Row>

    </Card>
  );
};
