import { message } from "antd";

export const handleSubmitClick = (form) => {
  let data = form.getFieldsValue();
  Object.values(data).forEach((item, key) => {
    if (
      item === undefined ||
      item === null ||
      item === "" ||
      item?.length === 0
    ) {
      switch (Object.keys(data)[key]) {
        case "project_name":
          return message.error("Preencha o campo Nome do projeto");
        case "project_type":
          return message.error("Preencha o campo Tipo do projeto");
        case "project_status":
          return message.error("Preencha o campo Status do projeto");
        case "project_opportunity":
          return message.error("Preencha o campo Oportunidade");
        case "project_architects":
          return message.error("Preencha o campo Arquitetos");
        case "project_contacts":
          return message.error("Preencha o campo Contatos");
        case "project_bu":
          const busArray = Object.keys(data)["project_bu"];
          if (!busArray || busArray.length === 0) {
            return message.error("Preencha o campo BUs");
          }
      }
    } else {
      form.submit();
    }
  });
};
