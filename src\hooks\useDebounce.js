import { useState, useEffect, useCallback, useRef } from 'react';

/**
 * Debounce hook - delays execution until after delay has passed since last call
 */
export const useDebounce = (value, delay) => {
  const [debouncedValue, setDebouncedValue] = useState(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
};

/**
 * Debounced callback hook
 */
export const useDebouncedCallback = (callback, delay, deps = []) => {
  const timeoutRef = useRef(null);

  const debouncedCallback = useCallback((...args) => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    timeoutRef.current = setTimeout(() => {
      callback(...args);
    }, delay);
  }, [callback, delay, ...deps]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  // Cancel function
  const cancel = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
  }, []);

  return [debouncedCallback, cancel];
};

/**
 * Throttle hook - limits execution to once per delay period
 */
export const useThrottle = (value, delay) => {
  const [throttledValue, setThrottledValue] = useState(value);
  const lastExecuted = useRef(Date.now());

  useEffect(() => {
    const handler = setTimeout(() => {
      if (Date.now() >= lastExecuted.current + delay) {
        setThrottledValue(value);
        lastExecuted.current = Date.now();
      }
    }, delay - (Date.now() - lastExecuted.current));

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return throttledValue;
};

/**
 * Throttled callback hook
 */
export const useThrottledCallback = (callback, delay, deps = []) => {
  const lastExecuted = useRef(0);
  const timeoutRef = useRef(null);

  const throttledCallback = useCallback((...args) => {
    const now = Date.now();
    
    if (now - lastExecuted.current >= delay) {
      // Execute immediately if enough time has passed
      callback(...args);
      lastExecuted.current = now;
    } else {
      // Schedule execution for later
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      
      const remainingTime = delay - (now - lastExecuted.current);
      timeoutRef.current = setTimeout(() => {
        callback(...args);
        lastExecuted.current = Date.now();
      }, remainingTime);
    }
  }, [callback, delay, ...deps]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return throttledCallback;
};

/**
 * Search hook with debouncing
 */
export const useSearch = (searchFunction, delay = 300) => {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  
  const debouncedQuery = useDebounce(query, delay);

  useEffect(() => {
    if (!debouncedQuery.trim()) {
      setResults([]);
      setIsLoading(false);
      return;
    }

    const performSearch = async () => {
      setIsLoading(true);
      setError(null);
      
      try {
        const searchResults = await searchFunction(debouncedQuery);
        setResults(searchResults);
      } catch (err) {
        setError(err);
        setResults([]);
      } finally {
        setIsLoading(false);
      }
    };

    performSearch();
  }, [debouncedQuery, searchFunction]);

  const clearSearch = useCallback(() => {
    setQuery('');
    setResults([]);
    setError(null);
  }, []);

  return {
    query,
    setQuery,
    results,
    isLoading,
    error,
    clearSearch,
  };
};

/**
 * Auto-save hook with debouncing
 */
export const useAutoSave = (data, saveFunction, delay = 2000) => {
  const [isSaving, setIsSaving] = useState(false);
  const [lastSaved, setLastSaved] = useState(null);
  const [error, setError] = useState(null);

  const [debouncedSave] = useDebouncedCallback(async (dataToSave) => {
    setIsSaving(true);
    setError(null);
    
    try {
      await saveFunction(dataToSave);
      setLastSaved(new Date());
    } catch (err) {
      setError(err);
    } finally {
      setIsSaving(false);
    }
  }, delay, [saveFunction]);

  useEffect(() => {
    if (data && Object.keys(data).length > 0) {
      debouncedSave(data);
    }
  }, [data, debouncedSave]);

  return {
    isSaving,
    lastSaved,
    error,
  };
};

/**
 * Scroll throttling hook
 */
export const useScrollThrottle = (callback, delay = 100) => {
  const throttledCallback = useThrottledCallback(callback, delay);

  useEffect(() => {
    const handleScroll = (event) => {
      throttledCallback({
        scrollY: window.scrollY,
        scrollX: window.scrollX,
        event,
      });
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, [throttledCallback]);
};

/**
 * Resize throttling hook
 */
export const useResizeThrottle = (callback, delay = 250) => {
  const throttledCallback = useThrottledCallback(callback, delay);

  useEffect(() => {
    const handleResize = (event) => {
      throttledCallback({
        width: window.innerWidth,
        height: window.innerHeight,
        event,
      });
    };

    window.addEventListener('resize', handleResize);
    
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [throttledCallback]);
};

export default useDebounce;
