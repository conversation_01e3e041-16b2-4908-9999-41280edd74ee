import React, {useEffect, useState} from 'react'
import {Select, Button, Form, Row, Typography, Col} from 'antd'
import {jiraGet} from "../../../../service/apiJira";

export const TechnicianFilters = (props) => {
    const {Title} = Typography;
    const [allProjects, setAllProjects] = useState([]);
    const [allUsers, setAllUsers] = useState([]);
    const [technician, setTechnician] = useState(false)

    const {handlePopoverVisibility, filter, formatName} = props;

    const {Option} = Select;
    const [form] = Form.useForm();

    function onFilterFinish(values) {
        filter(values);
        handlePopoverVisibility();
    }

    function onFilterFailed() {
    }

    function clear() {
        form.resetFields();
        setTechnician(false)
    }

    async function getAllProjects() {
        const {data} = await jiraGet('read/projects');

        setAllProjects(data.values);
    }

    async function getAllUsers() {
        const {data} = await jiraGet('read/users');

        setAllUsers(data);
    }

    useEffect(() => {
        getAllProjects();
        getAllUsers();
    }, [])

    return (
        <Form
            form={form}
            style={{display: 'flex', flexDirection: 'column'}}
            name="basic"
            initialValues={{remember: true}}
            onFinish={onFilterFinish}
            onFinishFailed={onFilterFailed}
            autoComplete="off"
        >
            <Row justify='space-between' align='middle'>
                <Col span={12}>
                    <Title level={5} style={{fontWeight: 400}}>Técnicos</Title>
                </Col>
                <Col span={6}>
                    <Button type='text' onClick={handlePopoverVisibility}>X</Button>
                </Col>
            </Row>
            <Form.Item name="professional">
                <Select
                    showSearch
                    style={{width: '100%'}}
                    placeholder="Profissional"
                    optionFilterProp="children"
                    onChange={() => setTechnician(true)}
                    filterOption={(input, option) => option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0 || option.props.value.toLowerCase().indexOf(input.toLowerCase()) >= 0}
                >
                    {allUsers.map((user) => (
                        <Option key={user.accountId} value={user.accountId}>{formatName(user.displayName)}</Option>
                    ))}
                </Select>
            </Form.Item>

            <Form.Item name="project">
                <Select
                    showSearch
                    style={{width: '100%'}}
                    placeholder="Projeto"
                    optionFilterProp="children"
                    disabled={!technician ? true : false}
                    filterOption={(input, option) => option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0 || option.props.value.toLowerCase().indexOf(input.toLowerCase()) >= 0}
                >
                    {allProjects.map((project) => (
                        <Option key={project.id} value={project.name}>{project.name}</Option>
                    ))}
                </Select>
            </Form.Item>
            <Row>
                <Button onClick={clear} type='text'>Limpar</Button>
                <Button
                    type='primary'
                    htmlType="submit"
                    className="filter-submit-btn">
                    Aplicar
                </Button>
            </Row>
        </Form>
    )
}
