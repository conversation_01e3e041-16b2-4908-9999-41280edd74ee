import { use<PERSON>emo, useCallback } from "react";
import { useSelector, shallowEqual } from "react-redux";

import { Row, Button, Modal, message } from "antd";
import { CloudUploadOutlined } from "@ant-design/icons";
import { v4 } from "uuid";

import {
  cleanCloseModalState,
  setUploadReadyFilesState,
} from "../../../../../store/actions/files-action";
import { setUploadFilesState } from "../../../../../store/actions/files-action";
import { useToggle } from "../../../../../hooks/useToggle";

import { UPLOAD_STATUS } from "../../../../../store/reducers/files-reducer";

import { TagStep } from "./TagStep";
import { MailStep } from "./MailStep";
import { InfoStep } from "./InfoStep";
import { FilesStep } from "./FilesStep";
import { CustomerStep } from "./CustomerStep";
import { CategoriesStep } from "./CategoriesStep";
import { uploadFileS3 } from "../../../controllers/uploadFileS3";
import { dynamoPut } from "../../../../../service/apiDsmDynamo";

import * as controller from "../../../../../controllers/files/filesController";
import { logNewAuditAction } from "../../../../../controllers/audit/logNewAuditAction";

const steps = [
  {
    title: "Upload de arquivo",
    component: <CategoriesStep />,
  },
  {
    title: "Upload de arquivo",
    component: <TagStep />,
  },
  {
    title: "Upload de arquivo",
    component: <MailStep />,
  },
  {
    title: "Upload de arquivo",
    component: <CustomerStep />,
  },
  {
    title: "Confirme o envio",
    component: <FilesStep />,
  },
  {
    title: "Carregando arquivo",
    component: <InfoStep />,
  },
];

export const UploadFilesModal = () => {
  const stepModal = useSelector((state) => state.files.stepModal, shallowEqual);

  const [toggleModalValue, toggle] = useToggle();

  return (
    <>
      <Button
        type="default"
        icon={<CloudUploadOutlined />}
        onClick={() => {
          toggle();
          setUploadReadyFilesState(false);
        }}
      >
        Upload de arquivo
      </Button>
      <Modal
        title={steps[stepModal].title}
        open={toggleModalValue}
        width={"40%"}
        closable={false}
        centered
        footer={<Footer toggle={toggle} />}
      >
        {steps[stepModal].component}
      </Modal>
    </>
  );
};

const Footer = ({ toggle }) => {
  const tags = useSelector((state) => state.files.tags, shallowEqual);
  const emails = useSelector((state) => state.files.emails, shallowEqual);
  const category = useSelector((state) => state.files.category, shallowEqual);
  const stepModal = useSelector((state) => state.files.stepModal, shallowEqual);
  const uploadStatus = useSelector(
    (state) => state.files.uploadStatus,
    shallowEqual
  );

  const customerId = useSelector(
    (state) => state.files.customerId,
    shallowEqual
  );
  const customerName = useSelector(
    (state) => state.files.customerName,
    shallowEqual
  );
  const customerCNPJ = useSelector(
    (state) => state.files.customerCNPJ,
    shallowEqual
  );
  const filesToUpload = useSelector(
    (state) => state.files.filesToUpload,
    shallowEqual
  );
  const customerITSM = useSelector(
    (state) => state.files.customerITSM,
    shallowEqual
  );

  const uploadFiles = useCallback(async () => {
    nextStep();
    const tableName = `${process.env.REACT_APP_STAGE}-files-archive`;

    const successFiles = [];
    const errorFiles = [];

    let fileListStr = "";

    for (let i = 0; i < filesToUpload.length; i++) {
      const file = filesToUpload[i];
      const extension = file.name.split(".").pop();

      const fileDynamoObj = {
        id: v4(),
        name: file.name,
        type: file.type,
        extension: extension,
        category: category,
        createdAt: new Date(),
        updatedAt: new Date(),
        customerId: customerId,
        customerName: customerName,
        customerCNPJ: customerCNPJ,
        customerITSM: customerITSM,
        tags: tags,
        emails: emails,
        userName: localStorage.getItem("@dsm/username"),
        userEmail: localStorage.getItem("@dsm/mail"),
      };
      try {
        await uploadFileS3(file, customerId, customerName, fileDynamoObj.id);
        await dynamoPut(tableName, fileDynamoObj.id, fileDynamoObj);

        successFiles.push(fileDynamoObj);
      } catch (err) {
        errorFiles.push(fileDynamoObj);
        console.log({ err });
      }

      if (i + 1 < filesToUpload.length) {
        fileListStr += file.name + ", ";
      } else {
        fileListStr += file.name;
      }
    }

    const username = localStorage.getItem("@dsm/username");
    const title = "Upload de Arquivo";
    const descriptionSingleFile = `${username} realizou o upload do seguinte arquivo: ${fileListStr}`;
    const descriptionMultipleFiles = `${username} realizou o upload dos seguintes arquivos: ${fileListStr}`;
    const description =
      filesToUpload.length > 1
        ? descriptionMultipleFiles
        : descriptionSingleFile;

    logNewAuditAction(username, title, description);

    let emailSent = true;
    if (emails.length > 0) {
      try {
        await controller.dispatchEmail(successFiles);
      } catch (error) {
        console.log(error);
        emailSent = false;
      }
    }

    if (errorFiles.length > 0 || !emailSent) {
      setUploadFilesState({
        field: "uploadStatus",
        value: UPLOAD_STATUS.ERROR,
      });
    } else {
      setUploadFilesState({
        field: "uploadStatus",
        value: UPLOAD_STATUS.SUCCESS,
      });
    }

    controller.getFiles();
  }, [
    filesToUpload,
    tags,
    emails,
    category,
    customerId,
    customerName,
    customerCNPJ,
    customerITSM,
  ]);

  function nextStep() {
    setUploadFilesState({
      field: "stepModal",
      value: stepModal + 1,
    });
  }

  function previusStep() {
    setUploadFilesState({
      field: "stepModal",
      value: stepModal - 1,
    });
  }

  const isDisabled = useMemo(() => {
    let disabled = false;

    switch (stepModal) {
      case 0:
        disabled = category.length === 0;
        break;
      case 1:
        disabled = tags.length === 0;
        break;
      case 3:
        disabled = customerId.length === 0;
        break;
      case 4:
        disabled = filesToUpload.length === 0;
        break;
      default:
        break;
    }

    return disabled;
  }, [stepModal, customerId, category, emails, tags, filesToUpload]);

  const content = useMemo(() => {
    if (stepModal === 4) {
      return (
        <>
          <Button type="default" onClick={previusStep}>
            Voltar
          </Button>
          <Button type="primary" onClick={uploadFiles} disabled={isDisabled}>
            Confirmar
          </Button>
        </>
      );
    } else if (stepModal === 5) {
      return (
        <>
          {uploadStatus !== UPLOAD_STATUS.SUCCESS && (
            <Button
              type="default"
              onClick={previusStep}
              disabled={uploadStatus === UPLOAD_STATUS.IN_PROGRESS}
            >
              Voltar
            </Button>
          )}

          <Button
            type="primary"
            onClick={() => {
              cleanCloseModalState();
              toggle();
            }}
            disabled={uploadStatus === UPLOAD_STATUS.IN_PROGRESS}
          >
            Fechar
          </Button>
        </>
      );
    } else if (stepModal > 0) {
      return (
        <>
          <Button type="default" onClick={previusStep}>
            Voltar
          </Button>
          <Button type="primary" onClick={nextStep} disabled={isDisabled}>
            Próximo
          </Button>
        </>
      );
    } else {
      return (
        <Button type="primary" onClick={nextStep} disabled={isDisabled}>
          Próximo
        </Button>
      );
    }
  }, [stepModal, isDisabled, uploadStatus, uploadFiles]);

  return (
    <Row justify={"end"}>
      {uploadStatus !== UPLOAD_STATUS.SUCCESS && (
        <Button
          type="default"
          onClick={() => {
            toggle();
            cleanCloseModalState();
          }}
          disabled={
            stepModal === 5 && uploadStatus === UPLOAD_STATUS.IN_PROGRESS
          }
        >
          Cancelar
        </Button>
      )}
      {content}
    </Row>
  );
};
