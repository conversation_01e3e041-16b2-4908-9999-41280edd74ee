import { format, isValid } from "date-fns";

export const filterSignatureDateIfContractIsNotSigned = (contract) => {
  let signedState = contract?.find(
    (item) => item.fieldName === "Assinado"
  )?.content;
  if (signedState !== "Sim") {
    return contract.filter((item) => item.fieldName !== "Data de assinatura");
  } else {
    return contract;
  }
};

const formatTypeHours = (data) => {
  data = data.split("_").filter((item) => !isNaN(parseInt(item)));
  const firstNumber = data[0];
  const secondNumber = data[1];
  const formattedData = firstNumber + "X" + secondNumber;
  return formattedData;
};

export const formatHourPoolData = (data) => {
  let formattedData = data?.split("_").join(" ").charAt(0).toUpperCase();
  formattedData += data
    ?.split("_")
    .filter((item) => isNaN(parseInt(item)))
    .join(" ")
    .slice(1);
  let hourRegime = formatTypeHours(data);
  formattedData += " " + hourRegime;
  return formattedData;
};

export const formatViewDateFields = (date, field) => {
  const checkValidDate = isValid(new Date(date));
  const fieldsWithNoNeedToFormat = [
    "Data de assinatura",
    "Data de cancelamento/rescisão",
    "Data inicial de serviço",
  ];
  if (fieldsWithNoNeedToFormat.find((item) => item === field)) {
    return date;
  } else if (checkValidDate) {
    return format(new Date(date), "dd/MM/yyyy");
  } else {
    return date.split(" ").shift();
  }
};
