import { HeaderMenu } from "../../components/HeaderMenu";
import { SideMenu } from "../../components/SideMenu";
import { Content } from "antd/lib/layout/layout";
import { ForeCastTable } from "../../components/Table/ForeCast"; 
import { ApproveModule } from "../../components/Table/ApproveModule"
import { Card, Layout } from "antd";
import { Col, Row } from 'antd'
import { CustomerCategory } from "../../components/Graphics/CustomerCategory";

const Activities = (props) => {
    const { collapsed, setCollapsed } = props;

    return (
      <Layout style={{ minHeight: "100vh" }}>
        <HeaderMenu collapsed={collapsed} setCollapsed={setCollapsed} />
        <Layout>
          <SideMenu collapsed={collapsed} />
          <Content style={{ padding: "2em" }}>
              <Row gutter={[12, 12]}>
                <Col span={12}>
                  <ForeCastTable />
                </Col>
                <Col span={12}>
                  <ApproveModule />
                </Col>
                <Col span={12}>
                  <CustomerCategory />
                </Col>
              </Row>
          </Content>
        </Layout>
      </Layout>
    );
}

export { Activities }