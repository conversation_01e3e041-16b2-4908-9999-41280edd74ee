import { useState, useContext } from "react";
import axios from "axios";
import {
  Button,
  Modal,
  Row,
  Col,
  Table,
  message,
  Popconfirm,
  Tooltip,
  Typography,
  <PERSON><PERSON>,
  Badge,
} from "antd";
import {
  PaperClipOutlined,
  DeleteOutlined,
  CheckOutlined,
  DownloadOutlined,
} from "@ant-design/icons";
import { ProposalContext } from "../../../contexts/totalValuesProposals";
import { v4 } from "uuid";
import { setTechnicalProposalState } from "../../../store/actions/technical-proposal-action";
import { shallowEqual, useSelector } from "react-redux";
import { useToggle } from "../../../hooks/useToggle";
import {
  s3UploadObj,
  s3ListObjects,
  s3DeleteMultipleObjects,
} from "../../../service/apiDsmS3";
import { getFilesToBeDeleted } from "../../../controllers/Proposals/getFilesToBeDeleted";
import { getBase64 } from "../../../controllers/Proposals/getBase64";

const FileDownload = require("js-file-download");

export const ArchitectureAttachModal = () => {
  const fileNames = useSelector(
    (state) => state.technicalProposal.architectureFileNames,
    shallowEqual
  );

  const id = useSelector(
    (state) => state.technicalProposal.persistID,
    shallowEqual
  );

  const [loading, setLoading] = useState(false);
  const { proposalState, setProposalState } = useContext(ProposalContext);
  const [supportArchitectArray, setSupportArchitectArray] = useState([]);
  const [firstOpenVerify, setFirstOpenVerify] = useState(true);
  const { Text } = Typography;
  const [fileId, setFileId] = useState(0);

  const idFixed = proposalState.idFixed === "" ? v4() : proposalState.idFixed;

  const [toggleModalValue, toggle] = useToggle();
  const [files, setFiles] = useState([]);
  const [notUploadedFilesId, setNotUploadedFilesId] = useState([]);

  const handleRemoveItem = (index, item) => {
    const prevList = fileNames;
    const arr = [];
    for (let i = 0; i < prevList.length; i++) {
      if (prevList[i].id !== item.id) {
        arr.push(prevList[i]);
      }
    }

    setTechnicalProposalState({
      field: "architectureFileNames",
      value: arr,
    });
  };

  const handleFileSelect = () => {
    const fileInput = document.getElementById("fileArchitecture");
    const architectureFile = fileInput.files[0];
    fileInput.value = "";

    const allowedTypes = ["image/png", "image/jpeg", "image/jpg"];
    if (!allowedTypes.includes(architectureFile.type)) {
      message.error(
        "Tipo de arquivo não suportado. Por favor, selecione um arquivo .png, .jpeg ou .jpg."
      );
      return;
    }

    const filesStr = JSON.stringify(fileNames);
    const filesRedux = JSON.parse(filesStr);

    let ids = [0];

    filesRedux.map((f) => {
      const id = f.id;
      ids.push(id);
    });

    if (architectureFile) {
      const fileSize = architectureFile.size;
      const fileSizeMb = fileSize / 1024 ** 2;
      if (fileSizeMb > 2) {
        message.warning("Esta imagem possue o tamanho maior que 2MB");
        return;
      }
      setFiles((state) => [...state, architectureFile]);

      let exists = false;
      const addedFile = {
        id: Math.max.apply(null, ids) + 1,
        lastModified: architectureFile.lastModified,
        name: architectureFile.name,
        size: architectureFile.size,
        type: architectureFile.type,
        isSaved: true,
      };

      setNotUploadedFilesId((state) => [
        ...state,
        Math.max.apply(null, ids) + 1,
      ]);

      const newId = fileId + 1;
      setFileId(newId);

      for (let i = 0; i < fileNames.length; i++) {
        if (fileNames[i].name === addedFile.name) {
          exists = true;
        }
      }

      if (exists === false) {
        setTechnicalProposalState({
          field: "architectureFileNames",
          value: [...fileNames, addedFile],
        });
      }
    } else {
      message.error("Selecione um arquivo");
    }
  };

  const handleOk = async () => {
    setLoading(true);
    setFirstOpenVerify(false);

    setNotUploadedFilesId([]);

    let filesToBeDeleted = await getFilesToBeDeleted(
      id,
      fileNames,
      true,
      "/documents/architecture"
    );
    let filesToBeDeletedKeys = [];

    const prefix = `${id}/documents/architecture/`;
    filesToBeDeleted.forEach((file) => {
      const key = prefix + file;
      filesToBeDeletedKeys.push(key);
    });

    const filesStr = JSON.stringify(fileNames);
    const filesRedux = JSON.parse(filesStr);

    for (let i = 0; i < filesRedux.length; i++) {
      let name = `${id}/documents/architecture/${filesRedux[i].name}`;

      const fileToTransform = files.find(
        (file) => file.name === filesRedux[i].name
      );

      if (fileToTransform) {
        const base64obj = await getBase64(fileToTransform);
        const payload = {
          bucketName: `${process.env.REACT_APP_STAGE}-proposals-documents`,
          obj: base64obj,
          fileName: name,
          contentType: `${filesRedux[i].type}`,
        };

        try {
          await s3UploadObj(payload);
        } catch (error) {
          console.log({ error });
          message.error("Houve um erro ao fazer o upload dos arquivos!");
        }
      }
    }

    const payloadDelete = {
      bucketName: `${process.env.REACT_APP_STAGE}-proposals-documents`,
      fileKeys: filesToBeDeletedKeys,
    };

    try {
      await s3DeleteMultipleObjects(payloadDelete);
    } catch (error) {
      console.log({ error });
      message.error(
        "Houve um erro ao fazer a deleção dos arquivos de arquitetura!"
      );
    }

    setLoading(false);
    toggle();
  };

  const handleGet = async (file) => {
    setLoading(true);
    toggle();
    let name = `${id}/documents/architecture/${file}`;

    const {
      data: { data },
    } = await axios.post(
      `${process.env.REACT_APP_API_PERMISSION}s3/link`,
      {
        operation: "get",
        key: encodeURI(name),
        bucket: `${process.env.REACT_APP_STAGE}-proposals-documents`,
      },
      {
        headers: {
          authorization: localStorage.getItem("jwt"),
        },
      }
    );

    axios
      .get(data, {
        responseType: "blob",
      })
      .then((response) => {
        FileDownload(response.data, `${file}`);
        setLoading(false);
      })
      .catch((err) => {
        setLoading(false);
        message.error("Erro ao tentar fazer download! ", { err });
      });

    toggle();
  };

  const handleCancel = async () => {
    setLoading(true);
    const filesToBeDeleted = await getFilesToBeDeleted(
      id,
      fileNames,
      false,
      "/documents/architecture"
    );

    const newFiles = fileNames.filter((f) => !filesToBeDeleted.includes(f));

    setTechnicalProposalState({
      field: "architectureFileNames",
      value: newFiles,
    });

    toggle();

    setLoading(false);
  };

  const columns = [
    {
      title: "ID",
      dataIndex: "id",
      key: "id",
      render: (id, item) => {
        return <Text>{item.id}</Text>;
      },
    },
    {
      title: "Nome do arquivo",
      dataIndex: "file_name",
      key: "file_name",
      render: (id, item) => {
        return <Text>{item.name}</Text>;
      },
    },
    {
      title: "Tipo do Arquivo",
      dataIndex: "file_type",
      key: "file_type",
      align: "center",
      render: (id, item) => {
        return <Text>{item.type}</Text>;
      },
    },
    {
      title: "Download",
      dataIndex: "download",
      key: "download",
      align: "center",
      render: (id, item) => {
        return (
          <Button
            type="text"
            onClick={() => {
              handleGet(item.name);
            }}
            disabled={notUploadedFilesId.find((id) => id === item.id)}
          >
            <DownloadOutlined />
          </Button>
        );
      },
    },
    {
      title: "Remover",
      dataIndex: "remove",
      key: "remove",
      render: (id, item, index) => {
        return (
          <Button
            danger
            type="text"
            onClick={() => {
              handleRemoveItem(index, item);
            }}
          >
            <DeleteOutlined />
          </Button>
        );
      },
    },
  ];

  return (
    <>
      <Badge count={fileNames.length} title="Arquivos salvos">
        <Button type="text-color" onClick={toggle}>
          Anexos
          <PaperClipOutlined />
        </Button>
      </Badge>
      <Modal
        title="Anexos"
        open={toggleModalValue}
        closable={false}
        width={"60%"}
        footer={[
          <Row justify="space-between">
            <Popconfirm
              title="Ao cancelar, você perderá os arquivos não salvos, tem certeza?"
              okText="Sim"
              cancelText="Não"
              onConfirm={handleCancel}
            >
              <Button type="text-color">
                Cancelar <DeleteOutlined />
              </Button>
            </Popconfirm>
            <Tooltip
              title={
                fileNames.length > 0 ? "" : "Adicione arquivos para habilitar"
              }
            >
              <Button type="primary" onClick={handleOk}>
                Salvar <CheckOutlined />
              </Button>
            </Tooltip>
          </Row>,
        ]}
      >
        <Row
          style={{ display: "flex", flexDirection: "column" }}
          justify="center"
        >
          <Table
            dataSource={fileNames}
            columns={columns}
            scroll={{ x: 100 }}
            pagination={true}
            loading={loading}
          />

          <Row justify="center">
            <Button type="dashed" style={{ margin: "20px 0 20px 0" }}>
              <label htmlFor="fileArchitecture">Selecionar anexo</label>
            </Button>
          </Row>
          <Row
            justify="center"
            align="bottom"
            style={{ marginBottom: "-20px" }}
            gutter={[16, 16]}
          >
            <Alert
              type="warning"
              showIcon
              message={`São aceitos apenas arquivos do tipo imagem(.png, .jpeg, .jpg)`}
              description={`O tamanho máximo permitido para cada arquivo é de 2MB`}
            />
          </Row>
          <Col>
            <input
              type="file"
              name="fileArchitecture"
              id="fileArchitecture"
              accept="image/png, image/jpeg"
              onChange={handleFileSelect}
              style={{ display: "none" }}
            />
          </Col>

          <Row justify="center" style={{ marginTop: "1rem" }}>
            <Col></Col>
          </Row>
        </Row>
      </Modal>
    </>
  );
};
