const speakeasy = require("speakeasy");

export const secret = speakeasy.generateSecret({
  name: `${process.env.NODE_ENV === 'development' ? 'LOCALHOST - ' : (process.env.REACT_APP_STAGE !== 'prod' ? process.env.REACT_APP_STAGE.toUpperCase() + " - " : "")}DSM`,
});

export function verified(token, secret) {
  return speakeasy.totp.verify({
    secret: secret,
    encoding: "ascii",
    token: token,
  });
}
