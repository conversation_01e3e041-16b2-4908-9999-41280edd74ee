import { capitalizeFirstLetterFromEachWord } from "../../utils/capitalizeFirstLetter";

export const formatTableData = (data) => {
  let formattedData = [];
  data.forEach((e) => {
    formattedData.push({
      id: e.id,
      role: e.role,
      time: e.time ? e.time : e.requestedTime,
      arn: e.arn,
      active: e.active,
      allowed: e.allowed,
      username: e.username,
      ticket_id: e.ticket_id,
      account_id: e.account_id,
      updated_at: e.updated_at,
      client_name: capitalizeFirstLetterFromEachWord(e?.client_name),
      client_ticket: e.client_ticket,
      client_user: e?.client_ticket?.toString() + e.username,
    });
  });
  return formattedData;
};
