import React, { useState } from "react";
import { useSelector, shallowEqual } from "react-redux";

import { Form, Modal, Button, Checkbox, Row, Col } from "antd";

import { SettingOutlined } from "@ant-design/icons";

import { setInvoiceFieldState } from "../../../store/actions/invoice-action";

export const ConfigModal = () => {
  const [showModal, setShowModal] = useState();
  const collumnsToShow = useSelector(
    (state) => state.invoice.collumnsToShow,
    shallowEqual
  );
  const collumnsConsumptionHours = useSelector(
    (state) => state.invoice.collumnsInvoices,
    shallowEqual
  );

  const [form] = Form.useForm();
  const [selectedFields, setSelectedFields] = useState(collumnsToShow);

  function handleSelectField(field, isChecked) {
    const includes = selectedFields.includes(field);

    let newSelectedFields = [];
    if (isChecked && !includes) newSelectedFields = [...selectedFields, field];

    if (!isChecked && includes)
      newSelectedFields = selectedFields.filter((f) => f !== field);

    setSelectedFields(newSelectedFields);
  }

  function handleSave() {
    setInvoiceFieldState({ field: "collumnsToShow", value: selectedFields });
    setShowModal(false);
  }

  return (
    <Row style={{ justifyContent: "flex-end" }}>
      <Col
        xl={{ span: 1 }}
        lg={{ span: 1 }}
        md={{ span: 1 }}
        sm={{ span: 2 }}
        style={{ marginBottom: 5 }}
      >
        <Button
          style={{ width: "100%" }}
          icon={<SettingOutlined />}
          onClick={() => {
            setShowModal(true);
          }}
        />
      </Col>
      <Modal
        title="Configurações da Página"
        open={showModal}
        onCancel={() => setShowModal(false)}
        footer={[
          <Button onClick={() => setShowModal(false)}>Fechar</Button>,
          <Button type="primary" onClick={handleSave}>
            Salvar
          </Button>,
        ]}
      >
        <Form
          layout="vertical"
          onFinish={() => setShowModal(false)}
          requiredMark={false}
          form={form}
        >
          <h3>Exibir as colunas: </h3>

          <Row>
            {collumnsConsumptionHours.map((field, index) => (
              <Col md={12}>
                <Checkbox
                  key={index}
                  checked={selectedFields.includes(field)}
                  onChange={(event) =>
                    handleSelectField(field, event.target.checked)
                  }
                >
                  {field}
                </Checkbox>
              </Col>
            ))}
          </Row>
        </Form>
      </Modal>
    </Row>
  );
};
