import { useState, useEffect } from "react";
import { dynamoGet } from "../../../service/apiDsmDynamo";
import * as customerController from "../../../controllers/clients/clientsController";

const useComplianceData = () => {
  const [existsNotification, setExistsNotification] = useState(false);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const complianceData = await dynamoGet(
          `${process.env.REACT_APP_STAGE}-compliance-monitoring`
        );
        const filteredCustomers = await getCustomers(complianceData);

        for (let i = 0; i < filteredCustomers.length; i++) {
          if (filteredCustomers[i].status === true) {
            setExistsNotification(true);
            break;
          }
        }
      } catch (error) {
        console.error("Error fetching data:", error);
      }
      localStorage.setItem("executedCheckCompliance", "true");
    };
    if (localStorage.getItem("executedCheckCompliance") !== "true") fetchData();
  }, []);

  const getCustomers = async (complianceData) => {
    let temp = [];
    const customerRoute = "customers/read/hasActiveContracts";
    const status = { status: 1 };

    let data = await customerController.getCustomerByParameter(
      customerRoute,
      status
    );

    data.forEach((c) => {
      if (Array.isArray(c.payment_percent)) {
        c.payment_percent.forEach((p) => {
          if (p.percentage && p.contract_id) {
            const status = existsItemUncheck(c, "3", complianceData);
            const customerWithCompliance = {
              status: status,
              customerData: c,
            };
            temp.push(customerWithCompliance);
          }
        });
      }

      if (c.payment_percent?.percentage && c.payment_percent?.contract_id) {
        const status = existsItemUncheck(c, "3", complianceData);
        const customerWithCompliance = {
          status: status,
          customerData: c,
        };
        temp.push(customerWithCompliance);
      }
    });

    return temp;
  };

  const existsItemUncheck = (customer, mode, complianceData) => {
    let checklist = [];

    checklist = complianceData?.find((i) => i.customerId === customer.id);

    const includesFalse = checklist?.items?.some(
      (item) => item?.status === false
    );

    if (includesFalse) {
      if (mode === "1") return 3;
      else return true;
    } else {
      if (mode === "1") return 1;
      else return false;
    }
  };

  return { existsNotification };
};

export default useComplianceData;
