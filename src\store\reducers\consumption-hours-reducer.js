import { createSlice } from "@reduxjs/toolkit";
import moment from "moment";

const REDUCER_NAME = "consumptionHours";

export const COLLUMNS_CONSUMPTION_HOURS = [
  "DSM ID",
  "<PERSON><PERSON><PERSON>",
  "<PERSON><PERSON><PERSON>",
  "<PERSON><PERSON><PERSON>",
  "<PERSON><PERSON><PERSON>",
  "<PERSON>",
  "Squad",
  "Horas Contratadas",
  "Horas Expiradas",
  "Horas Consumidas",
  "Saldo",
  "Horas Acumuladas",
  "Horas Excedentes",
  "Valor Hora",
  "Total Excedente",
  "Consumo Médio",
  "Total Horas Expiradas",
];

const allOption = { value: 0, label: "Todos" };

const INITIAL_STATE = {
  search: "",
  dtEnd: moment().format('YYYY-MM-DD'),
  dtStart: moment().format('YYYY-MM-DD'),
  contracts: [],
  updatedHoursContracts: [],
  squadOptions: [],
  contractTypes: [allOption],
  selectedSquads: [allOption],
  filteredContracts: [],
  selectedStatus: allOption,
  selectedHourType: allOption,
  selectedContract: 0,
  collumnsToShow: COLLUMNS_CONSUMPTION_HOURS,
  collumnsConsumptionHours: COLLUMNS_CONSUMPTION_HOURS,
  pageSelected: 1,
};

const consumptionHoursSlice = createSlice({
  name: REDUCER_NAME,
  initialState: INITIAL_STATE,
  reducers: {
    setConsumptionStateReduce(state, action) {
      state[action.payload.field] = action.payload.value;
    },
    setConsumptionSearchStateReduce(state, action) {
      state["contracts"] = action.payload.contracts;
      state["pageSelected"] = action.payload.pageSelected;
    },
    setUpdatedHoursContractsReduce(state, action) {
      state["updatedHoursContracts"] = action.payload.updatedHoursContracts;
    },
  },
});

export const {
  setConsumptionStateReduce,
  setConsumptionSearchStateReduce,
  setUpdatedHoursContractsReduce,
} = consumptionHoursSlice.actions;

export default consumptionHoursSlice.reducer;
