import React, { Component, useState, useRef } from 'react'
import { convertTo<PERSON><PERSON>, Editor, RichUtils, EditorState } from 'draft-js'
// import { Editor } from 'react-draft-wysiwyg'
import draftToHtml from 'draftjs-to-html'
import 'react-draft-wysiwyg/dist/react-draft-wysiwyg.css'

export const DraftTextEditor = ({value, handleDraftItems, itemName}) => {
  const [editorState, setEditorState] = useState(() => EditorState.createEmpty())

  const editor = useRef(null)

  const onChange = editorState => {
    setEditorState(editorState)
    const currentState = editorState.getCurrentContent()
    const { blocks } = convertToRaw(currentState)
    handleDraftItems(itemName, draftToHtml(convertToRaw(currentState)) )
  }

  // const onBold = () => {
  //   const newState = RichUtils.toggleInlineStyle(editorState, 'BOLD')
  //   setEditorState(newState)
  // }

  return (
    <div>
      {/* <div>
        <button onClick={onBold}>
          Bold  
        </button>  
      </div> */}
      <Editor

        // toolbarHidden
        ref={editor}
        editorState={editorState}
        onChange={onChange}
        placeholder={'Escreva aqui...'}
        wrapperClassName="wrapper-class"
        editorClassName="editor-class"
        toolbarClassName="demo-toolbar-absolute"
        toolbar={{
          inline: { inDropdown: false },
          list: { inDropdown: true },
          textAlign: { inDropdown: true },
          link: { inDropdown: true }
        }}
      />
      {/* <textarea
          disabled
          value={draftToHtml(convertToRaw(editorState.getCurrentContent()))}
        /> */}
    </div>
  )
}

// export class DraftTextEditor extends Component {
//   state = {
//     editorState: EditorState.createEmpty(),
//     value: {}
//   }

//   onChange = editorState => {
//     this.setState({ editorState })
//   }

//   render() {
//     return (
//       <div>
//         <Editor
//           // toolbarHidden
//           editorState={this.state.editorState}
//           wrapperClassName="wrapper-class"
//           editorClassName="editor-class"
//           toolbarClassName="toolbar-class"
//           onChange={this.onChange}
//           toolbar={{
//             inline: { inDropdown: false },
//             list: { inDropdown: true },
//             textAlign: { inDropdown: true },
//             link: { inDropdown: true }
//           }}
//         />
//         {/* <textarea
//           disabled
//           value={draftToHtml(convertToRaw(editorState.getCurrentContent()))}
//         /> */}
//       </div>
//     )
//   }
// }
