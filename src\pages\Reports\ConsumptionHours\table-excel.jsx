import { realMask } from "../../../utils/masks";
import * as controller from "../../../controllers/reports/surplus-hours-controller";
import { CSVLink } from "react-csv";
import { Button } from "antd";
import { FileExcelOutlined } from "@ant-design/icons";
import { useMemo } from "react";
import { shallowEqual, useSelector } from "react-redux";

export function TableExcel({ data, children }) {
  const collumnsToShow = useSelector(
    (state) => state.globalPersist.consumption.collumnsToShow,
    shallowEqual
  );

  const allCollumns = [
    {
      label: "ID",
      key: "itsm_id",
    },
    {
      label: "Contrato",
      key: "name",
    },
    {
      label: "Tipo",
      key: "type_hours",
    },
    {
      label: "Mês",
      key: "period",
    },
    {
      label: "Cliente",
      key: "customerID",
    },
    {
      label: "Status",
      key: "status",
    },
    {
      label: "Squad",
      key: "squad",
    },
    {
      label: "<PERSON>ras <PERSON>trata<PERSON>",
      key: "totalHoursInMinutes",
    },
    {
      label: "Horas Expiradas",
      key: "expiredHours",
    },
    {
      label: "Horas Consumidas",
      key: "consumptionTotalMinutes",
    },
    {
      label: "Horas Acumuladas",
      key: "accumulatedHoursMinutes",
    },
    {
      label: "Saldo",
      key: "finalBalanceHours",
    },
    {
      label: "Horas Excedentes",
      key: "surplusHoursMinutes",
    },
    {
      label: "Valor Hora",
      key: "excess_cost",
    },
    {
      label: "Total Excedente",
      key: "surplusValue",
    },
    {
      label: "Consumo Médio",
      key: "averageConsumption",
    },
    {
      label: "Total Horas Expiradas",
      key: "totalExpiredHours",
    },
  ];

  const collumns = useMemo(() => {
    return allCollumns.filter((c) => collumnsToShow.includes(c.label));
  }, [collumnsToShow]);

  const formactedData = useMemo(() => {
    const isExcel = true;

    return data.map((item) => {
      return {
        itsm_id: item.identifications.itsm_id,
        name: item.name,
        type_hours: item.type_hours,
        customerID: item.customer.itsm_id,
        squad: item.squad,
        totalHoursInMinutes: controller.formactMinutosToHours(
          item.totalHoursInMinutes,
          isExcel
        ),
        finalBalanceHours: controller.formactMinutosToHours(
          item.finalBalanceHours,
          isExcel
        ),
        accumulatedHoursMinutes: controller.formactMinutosToHours(
          item.accumulatedHoursMinutes,
          isExcel
        ),
        consumptionTotalMinutes: controller.formactMinutosToHours(
          item.consumptionTotalMinutes,
          isExcel
        ),
        surplusHoursMinutes: controller.formactMinutosToHours(
          item.surplusHoursMinutes,
          isExcel
        ),
        excess_cost: realMask(item.excess_cost.toFixed(2)),
        surplusValue: realMask(item.surplusValue.toFixed(2)),
        status: item.active ? "Ativo" : "Inativo",
        averageConsumption: controller.formactMinutosToHours(
          item.averageConsumption,
          isExcel
        ),
        expiredHours: controller.formactMinutosToHours(
          item.expiredHours,
          isExcel
        ),
        totalExpiredHours: controller.formactMinutosToHours(
          item.totalExpiredHours,
          isExcel
        ),
        period: item.period,
      };
    });
  }, [data]);

  return (
    <CSVLink
      style={{ width: "100%" }}
      separator={";"}
      headers={collumns}
      data={formactedData}
      filename={"Consumo de Horas x Contrato.csv"}
    >
      {children}
    </CSVLink>
  );
}
