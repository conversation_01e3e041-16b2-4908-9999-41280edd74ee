import "moment/locale/pt-br";
import locale from "antd/es/date-picker/locale/pt_BR";

import { DatePicker, Row, Typography } from "antd";
import { setDateState } from "../../../store/actions/customers-action";
import { useEffect, useState } from "react";
import moment from "moment";

export const DateFilters = () => {
  const [date, setDate] = useState({ dtStart: null, dtEnd: null });
  const { RangePicker } = DatePicker;

  useEffect(() => {
    setDateState({ field: "dtStart", value: date.dtStart });
    setDateState({ field: "dtEnd", value: date.dtEnd });
  }, [date]);

  const offset = 0;

  const disabledDate = (current) => {
    return current && current > moment().endOf("day");
  };

  return (
    <>
      <Typography.Text>Filtrar por período:</Typography.Text>
      <RangePicker
        picker="month"
        placeholder={["Inicio", "Fim"]}
        defaultValue={[null, null]}
        disabledDate={disabledDate}
        locale={locale}
        style={{ borderRadius: "4px", height: "34px", width: "100%" }}
        onChange={(moment) => {
          if (moment !== null) {
            setDate({ dtStart: moment[0], dtEnd: moment[1] });
          }
        }}
      />
    </>
  );
};
