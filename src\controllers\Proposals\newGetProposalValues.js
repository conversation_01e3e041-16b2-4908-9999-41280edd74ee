import { moneyMask } from "../../utils/money-maks";

export function getProposalValues(item, type, spread = null) {
  const totalValues = item?.totalValues?.find(
    (v) => v.hourClass === "hourValue"
  )?.values;

  let month = item?.monthSelected ? item?.monthSelected : ["12"];
  let greatestMonth = month[0];

  for (let i = 0; i < month.length; i++) {
    if (greatestMonth < month[i]) {
      greatestMonth = month[i];
    }
  }
  month = greatestMonth;

  let currencyFormat = Intl.NumberFormat("pt-BR");

  if (totalValues == [] || !totalValues) return 0;

  if (type === "setup") {
    let valuesByMonth = totalValues.find((i) => i.month === month)?.values;
    let totalValue8x5 = valuesByMonth?.find(
      (i) => i.formName === "setup8x5"
    )?.totalValue;
    let totalValue24x7 = valuesByMonth?.find(
      (i) => i.formName === "setup24x7"
    )?.totalValue;

    if (spread?.find((s) => s?.month === month).value > 0) {
      return 0;
    } else {
      return currencyFormat.format(totalValue8x5 + totalValue24x7)
        ? moneyMask((totalValue8x5 + totalValue24x7).toFixed(2))
        : 0;
    }
  } else if (type === "sust") {
    let valuesByMonth = totalValues.find((i) => i.month === month)?.values;
    let totalValue8x5 = valuesByMonth?.find(
      (i) => i.formName === "sust8x5"
    )?.totalValue;
    let totalValue24x7 = valuesByMonth?.find(
      (i) => i.formName === "sust24x7"
    )?.totalValue;

    if (spread?.find((s) => s.month === month).value > 0) {
      return Number(spread?.find((s) => s.month === month).value).toFixed(2);
    } else {
      return currencyFormat.format(totalValue8x5 + totalValue24x7)
        ? moneyMask((totalValue8x5 + totalValue24x7).toFixed(2))
        : 0;
    }
  } else if (type === "setupsust") {
    if (item.totalValues) {
      let valuesByMonth = totalValues.find(
        (i) => i.month === item.month
      ).values;
      let totalValue8x5Setup = valuesByMonth?.find(
        (i) => i.formName === "setup8x5"
      )?.totalValue;
      let totalValue24x7Setup = valuesByMonth?.find(
        (i) => i.formName === "setup24x7"
      )?.totalValue;
      let totalValue8x5Sust = valuesByMonth?.find(
        (i) => i.formName === "sust8x5"
      )?.totalValue;
      let totalValue24x7Sust = valuesByMonth?.find(
        (i) => i.formName === "sust24x7"
      )?.totalValue;
      const sum =
        totalValue8x5Setup +
        totalValue24x7Setup +
        totalValue8x5Sust +
        totalValue24x7Sust;
      return moneyMask(sum.toFixed(2));
    } else {
      return 0;
    }
  } else {
    return 0;
  }
}
