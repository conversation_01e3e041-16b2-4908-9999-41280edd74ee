import {
  BgColorsOutlined,
  FilterOutlined,
  StarFilled,
  StarOutlined,
} from "@ant-design/icons";
import { React, useEffect } from "react";
import { <PERSON><PERSON><PERSON>, Bar, XAxis, Tooltip, ResponsiveContainer } from "recharts";
import { Card, Col, Modal, Popover, Row, Spin, Typography } from "antd";
import { useState } from "react";
import ColorPicker from "../../ColorPicker";
import moment from "moment";
import { otrsGet } from "../../../service/apiOtrs";
import NewContractsTable from "../../Table/NewContracts";
import { MainBarChart } from "../../Graphs/BarGraph";

moment.updateLocale("pt", {
  months: [
    "Janeiro",
    "Fevereiro",
    "Março",
    "Abril",
    "Maio",
    "Junho",
    "Julho",
    "Agosto",
    "Setembro",
    "Outubro",
    "Novembro",
    "Dezembro",
  ],
});

export default function NewContracts(props) {
  const { Title, Text } = Typography;
  const { contractTypes, favorites } = props;
  const [isFavorite, setIsFavorite] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [colors, setColors] = useState();
  const [loading, setLoading] = useState(true);
  const [graphValues, setGraphValues] = useState();
  const [tableData, setTableData] = useState();

  const GRAPH_NAME = "NewContracts";

  const data = [
    {
      name: moment().subtract("4", "months").format("MMM"),
      contratos: graphValues?.oneMonth.length,
      amt: 2400,
    },
    {
      name: moment().subtract("3", "months").format("MMM"),
      contratos: graphValues?.twoMonth.length,
      amt: 2210,
    },
    {
      name: moment().subtract("2", "months").format("MMM"),
      contratos: graphValues?.threeMonth.length,
      amt: 2290,
    },
    {
      name: moment().subtract("1", "months").format("MMM"),
      contratos: graphValues?.fourMonth.length,
      amt: 2290,
    },
    {
      name: moment().format("MMM"),
      contratos: graphValues?.fiveMonth.length,
      amt: 2290,
    },
  ];

  async function updateFavorite() {
    await props?.setVisionFavorite(GRAPH_NAME);
    setIsFavorite(checkIsVisible());
  }

  useEffect(() => {
    getGraphColor();
    getContractsData();
  }, [contractTypes]);
  
  useEffect(() => {
    setIsFavorite(checkIsVisible());
  },[favorites])

  const getContractsData = () => {
    otrsGet("read/contract/all/0").then((res) => {
      let contracts = res.data;
      let values = {
        oneMonth: [],
        twoMonth: [],
        threeMonth: [],
        fourMonth: [],
        fiveMonth: [],
      };
      for (let contract in contracts) {
        if (
          moment(contracts[contract].create_time) >
            moment().subtract("4", "months") &&
          moment(contracts[contract].create_time) < moment()
        ) {
          if (
            moment(contracts[contract].create_time).month() ===
            moment().subtract("4", "months").month()
          ) {
            values.oneMonth.push(contracts[contract]);
          } else if (
            moment(contracts[contract].create_time).month() ===
            moment().subtract("3", "months").month()
          ) {
            values.twoMonth.push(contracts[contract]);
          } else if (
            moment(contracts[contract].create_time).month() ===
            moment().subtract("2", "months").month()
          ) {
            values.threeMonth.push(contracts[contract]);
          } else if (
            moment(contracts[contract].create_time).month() ===
            moment().subtract("1", "months").month()
          ) {
            values.fourMonth.push(contracts[contract]);
          }
          if (
            moment(contracts[contract].create_time).month() === moment().month()
          ) {
            values.fiveMonth.push(contracts[contract]);
          }
        }
      }
      setGraphValues(values);
      setLoading(false);
    });
  };

  function getGraphColor() {
    let storage_color = JSON.parse(localStorage.getItem("colors"));
    if (storage_color) {
      setColors(storage_color[GRAPH_NAME]);
    }
  }

  function changeColors(colors) {
    let storage_color = JSON.parse(localStorage.getItem("colors"));
    if (!storage_color) {
      storage_color = {};
    }
    storage_color[GRAPH_NAME] = colors;
    localStorage.setItem("colors", JSON.stringify(storage_color));
    setColors(colors);
  }

  function checkIsVisible() {
    let localStorageData = JSON.parse(localStorage.getItem("favorites"));
    if (localStorageData) {
      var index = localStorageData.findIndex(
        (value) => value.component === GRAPH_NAME
      );

      return index !== -1;
    } else {
      return false;
    }
  }

  function showModal(event) {
    switch (event.activeTooltipIndex) {
      case 0:
        setTableData(graphValues.oneMonth);
        break;

      case 1:
        setTableData(graphValues.twoMonth);
        break;

      case 2:
        setTableData(graphValues.threeMonth);
        break;

      case 3:
        setTableData(graphValues.fourMonth);
        break;

      case 4:
        setTableData(graphValues.fiveMonth);
        break;

      default:
        break;
    }
    setModalVisible(true);
  }

  function closeModal() {
    setModalVisible(false);
  }

  return (
    <Card bordered="false" style={{ borderRadius: "20px", height: "100%" }}>
      {loading ? (
        <Row
          style={{
            minWidth: "275px",
            height: "250px",
            justifyContent: "center",
            alignItems: "center",
          }}
        >
          <Col style={{ display: "flex", flexDirection: "column" }}>
            <Spin />
            <Text>Carregando...</Text>
          </Col>
        </Row>
      ) : (
        <>
          <Row>
            <Col span={18}>
              <Title level={4} style={{ fontWeight: 400 }}>
                Novos contratos
              </Title>
            </Col>
            <Col span={3}>
              {!isFavorite ? (
                <StarOutlined onClick={updateFavorite} className="star-icon" />
              ) : (
                <StarFilled onClick={updateFavorite} className="star-icon" />
              )}
            </Col>
            <Col span={3}>
              <Popover
                placement="left"
                title={"Personalização de cor"}
                content={() => {
                  return (
                    <ColorPicker
                      changeColors={changeColors}
                      options={[1]}
                      state={colors}
                    />
                  );
                }}
              >
                <BgColorsOutlined className="star-icon" />
              </Popover>
            </Col>
          </Row>
          <div style={{ width: "100%", height: "70%" }}>
            <MainBarChart
              data={data}
              colors={colors}
              showModalFunction={showModal}
              dataKey="contratos"
            />
          </div>
          <Modal
            width={1000}
            closable={false}
            cancelButtonProps={{ style: { display: "none" } }}
            okButtonProps={{
              style: {
                display: "none",
                backgroundColor: "#43914F",
                color: "white",
                fontWeight: "bold",
                borderRadius: "5px",
              },
            }}
            visible={modalVisible}
          >
            <NewContractsTable
              tableData={tableData}
              contractTypes={contractTypes}
              closeModal={closeModal}
            />
          </Modal>
        </>
      )}
    </Card>
  );
}
