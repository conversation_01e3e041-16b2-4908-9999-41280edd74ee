import moment from "moment";

export const formattedCommitments = (commitments) => {
  return commitments
    .filter((commitment) => {
      return Array.isArray(commitment.commitmentRange);
    })
    .map((commitment) => {
      const [startDate, endDate] = commitment.commitmentRange.map((d) =>
        moment(d).format("YYYY-MM")
      );
      commitment.commitmentRange = {
        startDate,
        endDate,
      };
      return commitment;
    });
};
