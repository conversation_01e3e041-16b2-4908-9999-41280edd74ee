import { createSlice } from "@reduxjs/toolkit";

import { COLLUMNS_CONSUMPTION_HOURS } from "./consumption-hours-reducer";

const REDUCER_NAME = "globalPersist";

const INITIAL_STATE = {
  consumption: {
    collumnsToShow: COLLUMNS_CONSUMPTION_HOURS,
  },
  technicalProposal: {
    loadingPersist: false,
  },
};

const globalPersistSlice = createSlice({
  name: REDUCER_NAME,
  initialState: INITIAL_STATE,
  reducers: {
    setGlobalPersistReduce(state, action) {
      state[action.payload.field] = action.payload.value;
    },
  },
});

export const { setGlobalPersistReduce } = globalPersistSlice.actions;

export default globalPersistSlice.reducer;
