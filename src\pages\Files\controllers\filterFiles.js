export function filterFiles(files, search, actionsState, dateRange) {
  const filteredFiles = files?.filter((e) => {
    const data = [
      e?.name
        ? e?.name
            ?.toString()
            .toLocaleLowerCase()
            .startsWith(search?.toLowerCase()) ||
          e?.name
            ?.toString()
            .toLocaleLowerCase()
            .includes(search?.toLowerCase())
        : false,
      e?.customerName
        ? e?.customerName
            ?.toString()
            .toLocaleLowerCase()
            .startsWith(search?.toLowerCase()) ||
          e?.customerName
            ?.toString()
            .toLocaleLowerCase()
            .includes(search?.toLowerCase())
        : false,
      e?.type
        ? e?.type
            ?.toString()
            .toLocaleLowerCase()
            .startsWith(search?.toLocaleLowerCase()) ||
          e?.type
            ?.toString()
            .toLocaleLowerCase()
            .includes(search?.toLocaleLowerCase())
        : false,
    ];

    if (actionsState === "Todas" || actionsState === "") {
      for (let i = 0; i < data.length; i++) {
        if (data[i] === true) {
          return e;
        }
      }
    } else if (actionsState === "Incident Management") {
      for (let i = 0; i < data.length; i++) {
        if (data[i] === true) {
          return e?.category === "Incident Management";
        }
      }
    } else if (actionsState === "Non-service affecting incidents") {
      for (let i = 0; i < data.length; i++) {
        if (data[i] === true) {
          return e?.category === "Non-service affecting incidents";
        }
      }
    } else if (actionsState === "Performance analysis") {
      for (let i = 0; i < data.length; i++) {
        if (data[i] === true) {
          return e?.category === "Performance analysis";
        }
      }
    } else if (actionsState === "Assets/resources") {
      for (let i = 0; i < data.length; i++) {
        if (data[i] === true) {
          return e?.category === "Assets/resources";
        }
      }
    } else if (actionsState === "Exceptions") {
      for (let i = 0; i < data.length; i++) {
        if (data[i] === true) {
          return e?.category === "Exceptions";
        }
      }
    }

    for (let i = 0; i < data.length; i++) {
      if (data[i] === true) {
        return e;
      }
    }

    if (search === "") return e;

    return null;
  });
  return filteredFiles;
}
