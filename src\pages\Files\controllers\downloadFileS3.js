import axios from "axios";
const FileDownload = require("js-file-download");

export const downloadFileS3 = async (file) => {
  let fileType = file.type.split("/");
  fileType = fileType[fileType.length - 1];

  const backUrl = `${process.env.REACT_APP_API_NEW}s3/link`;
  const body = {
    bucket: `${process.env.REACT_APP_STAGE}-files-archive`,
    key: `${file.customerId}-${encodeURI(file.customerName)}/${
      file.id
    }.${fileType}`,
    operation: "get",
  };
  const headers = {
    headers: {
      authorization: localStorage.getItem("jwt"),
    },
  };

  const {
    data: { data },
  } = await axios.post(backUrl, body, headers);

  await axios
    .get(data, {
      responseType: "blob",
    })
    .then((response) => {
      FileDownload(response.data, `${file.name}`);
      return true;
    })
    .catch((err) => {
      console.log("ERROR ON FILE DOWNLOAD", { err });
      return false;
    });
};
