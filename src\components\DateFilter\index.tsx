import React from "react";
import "moment/locale/pt-br";
import locale from "antd/es/date-picker/locale/pt_BR";

import { Col, DatePicker, Typography } from "antd";
import { differenceInYears } from "date-fns";
import moment from "moment";

type DateFilterProps = {
  label: string;
  onChange: (value: moment.Moment | null) => void;
  defaultValue?: moment.Moment;
  limit?: boolean;
};

export const DateFilter = ({
  label,
  onChange,
  defaultValue,
  limit,
}: DateFilterProps) => {
  return (
    <>
      <Typography.Text>{label}</Typography.Text>
      <DatePicker
        placeholder="Selecione um mês"
        picker="month"
        style={{ width: "100%" }}
        locale={locale}
        onChange={(value) => onChange(value)}
        defaultValue={defaultValue}
        disabledDate={
          limit
            ? (current) => {
                return (
                  differenceInYears(new Date(), current.toDate()) < 13 ||
                  current > moment()
                );
              }
            : undefined
        }
      />
    </>
  );
};
