import { DatePicker } from "antd";

import { setUploadFilesState } from "../../../store/actions/files-action";

export const Date = () => {
  const handleChange = (e) => {
    if (e !== null) {
      setUploadFilesState({
        field: "dateRange",
        value: e.utc().format(),
      });
    } else {
      setUploadFilesState({
        field: "dateRange",
        value: "",
      });
    }
  };

  return (
    <>
      <DatePicker
        placeholder="Selecione a Data"
        picker="month"
        onChange={(e) => handleChange(e)}
      />
    </>
  );
};
