import { useRef, useState } from 'react'
import styledSheet from './style.module.scss'

import { CameraOutlined } from "@ant-design/icons";
import { Spin } from 'antd';

export const EmailFooter = () => {
    const imageRef = useRef(null)
    const inputImageRef = useRef(null)
    const [loading, setLoading] = useState(false)

    function selectNewBackgroundImage() {
        if (inputImageRef.current)
            inputImageRef.current.click()
    }

    async function changeBackgroundImage() {
        try {
            if (inputImageRef.current) {
                const { files } = inputImageRef.current
    
                if (imageRef.current)
                    imageRef.current.src = ''
    
                if (files) {
                    const file = files[0]
    
                    setLoading(true)
                    const image = new Image();
                    image.onload = async () => {
    
                        const canvas = document.createElement('canvas');
    
                        canvas.width = image.naturalWidth;
                        canvas.height = image.naturalHeight;
                        canvas.getContext('2d').drawImage(image, 0, 0);
    
                        const blob = await resizeImage(canvas)
    
                        const imageURL = new File([blob], 'my-new-name.webp', { type: blob.type });
                        var reader = new FileReader();
                        reader.readAsDataURL(imageURL);
                        reader.onload = function () {
                            if (imageRef.current)
                                imageRef.current.src = reader.result

                                setLoading(false)
                        };
    
                    };
                    image.src = URL.createObjectURL(file)
                }
            }
        } catch (error) {
            setLoading(false)
        }
    }

    async function resizeImage(canvas) {
        let blob = ""
        let maxSize = 100000;
        let imageQuality = 1;

        blob = await new Promise(resolve => {
            canvas.toBlob((blob) => { resolve(blob) }, 'image/webp');
        });

        if (blob.size > maxSize) {
            while (blob.size > maxSize && imageQuality > 0) {
                imageQuality = imageQuality - 0.1
                blob = await new Promise(resolve => {
                    canvas.toBlob((blob) => { resolve(blob) }, 'image/webp', imageQuality);
                });
            }
        }

        return blob
    }

    return (
        <table
            border={0}
            cellPadding="0"
            cellSpacing="0"
            width="100%"
            style={{ backgroundClip: '#ffffff' }}
        >
            <tbody>
                <tr>
                    <td
                        style={{
                            color: '#4a423c',
                            fontFamily: 'arial, helvetica, sans-serif',
                            fontSize: 0,
                            paddingRight: 0,
                            paddingLeft: 0,
                            paddingTop: 0
                        }}
                    >
                        <table
                            border={0}
                            cellPadding="0"
                            cellSpacing="0"
                            style={{ width: 610 }}
                        >
                            <tbody>
                                <tr>
                                    <td
                                        style={{
                                            color: '#4a423c',
                                            fontFamily: 'arial, helvetica, sans-serif',
                                            fontSize: 16,
                                            position: 'relative'
                                        }}
                                    >
                                        <img
                                            alt=""
                                            ref={imageRef}
                                            style={{
                                                border: 0,
                                                display: 'inline-block',
                                                maxWidth: '100%',
                                                verticalAlign: 'middle',
                                                width: '100%',
                                                height: 131
                                            }}
                                        />

                                        {
                                            loading ?
                                                <div style={{
                                                    position: 'absolute',
                                                    width: '100%',
                                                    height: '100%',
                                                    top: 0,
                                                    justifyContent: 'center',
                                                    alignItems: 'center',
                                                    display: 'flex',
                                                    flexDirection: 'column'
                                                }}>

                                                    <Spin />
                                                    <label>Carregando . . .</label>
                                                </div>
                                                :
                                                null
                                        }

                                        <div
                                            className={styledSheet["footer-image-container"]}
                                            style={{ display: 'none' }}
                                            onClick={selectNewBackgroundImage}
                                        >
                                            <CameraOutlined />

                                            <input
                                                style={{ display: 'none' }}
                                                type="file"
                                                ref={inputImageRef}
                                                onChange={changeBackgroundImage}
                                            />
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </td>
                </tr>
            </tbody>
        </table>
    )
}