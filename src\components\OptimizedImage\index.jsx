import React, { useState, useRef, useEffect } from 'react';
import { Skeleton } from 'antd';

/**
 * OptimizedImage Component
 * Provides lazy loading, progressive loading, and error handling for images
 */
const OptimizedImage = ({
  src,
  alt,
  placeholder,
  className,
  style,
  width,
  height,
  lazy = true,
  progressive = false,
  fallback = null,
  onLoad,
  onError,
  ...props
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isError, setIsError] = useState(false);
  const [isInView, setIsInView] = useState(!lazy);
  const imgRef = useRef(null);
  const observerRef = useRef(null);

  // Intersection Observer for lazy loading
  useEffect(() => {
    if (!lazy || isInView) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsInView(true);
          observer.disconnect();
        }
      },
      {
        threshold: 0.1,
        rootMargin: '50px',
      }
    );

    if (imgRef.current) {
      observer.observe(imgRef.current);
      observerRef.current = observer;
    }

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [lazy, isInView]);

  const handleLoad = (event) => {
    setIsLoaded(true);
    if (onLoad) {
      onLoad(event);
    }
  };

  const handleError = (event) => {
    setIsError(true);
    if (onError) {
      onError(event);
    }
  };

  // Generate responsive image sources
  const generateSrcSet = (baseSrc) => {
    if (!baseSrc) return '';
    
    const extension = baseSrc.split('.').pop();
    const baseName = baseSrc.replace(`.${extension}`, '');
    
    // Generate different sizes (assuming you have these available)
    return [
      `${baseName}_400w.${extension} 400w`,
      `${baseName}_800w.${extension} 800w`,
      `${baseName}_1200w.${extension} 1200w`,
      `${baseSrc} 1600w`
    ].join(', ');
  };

  // Progressive loading placeholder
  const renderPlaceholder = () => {
    if (placeholder) {
      return typeof placeholder === 'string' ? (
        <img
          src={placeholder}
          alt={alt}
          className={`${className} blur-sm`}
          style={{
            ...style,
            filter: 'blur(5px)',
            transition: 'filter 0.3s ease',
          }}
        />
      ) : (
        placeholder
      );
    }

    return (
      <Skeleton.Image
        style={{
          width: width || '100%',
          height: height || 200,
          ...style,
        }}
      />
    );
  };

  // Error fallback
  const renderError = () => {
    if (fallback) {
      return fallback;
    }

    return (
      <div
        className={className}
        style={{
          ...style,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          backgroundColor: '#f5f5f5',
          color: '#999',
          fontSize: '14px',
          width: width || '100%',
          height: height || 200,
        }}
      >
        Imagem não disponível
      </div>
    );
  };

  // Don't render anything if lazy loading and not in view
  if (lazy && !isInView) {
    return (
      <div
        ref={imgRef}
        className={className}
        style={{
          ...style,
          width: width || '100%',
          height: height || 200,
        }}
      >
        {renderPlaceholder()}
      </div>
    );
  }

  // Render error state
  if (isError) {
    return renderError();
  }

  return (
    <div
      ref={imgRef}
      className={className}
      style={{
        position: 'relative',
        ...style,
      }}
    >
      {/* Placeholder while loading */}
      {!isLoaded && renderPlaceholder()}
      
      {/* Main image */}
      <img
        src={src}
        srcSet={generateSrcSet(src)}
        sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
        alt={alt}
        onLoad={handleLoad}
        onError={handleError}
        style={{
          ...style,
          opacity: isLoaded ? 1 : 0,
          transition: 'opacity 0.3s ease',
          position: isLoaded ? 'static' : 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          objectFit: 'cover',
        }}
        loading={lazy ? 'lazy' : 'eager'}
        decoding="async"
        {...props}
      />
    </div>
  );
};

/**
 * WebP Image Component with fallback
 */
export const WebPImage = ({ src, fallbackSrc, ...props }) => {
  const [supportsWebP, setSupportsWebP] = useState(null);

  useEffect(() => {
    // Check WebP support
    const checkWebPSupport = () => {
      const canvas = document.createElement('canvas');
      canvas.width = 1;
      canvas.height = 1;
      const dataURL = canvas.toDataURL('image/webp');
      setSupportsWebP(dataURL.indexOf('data:image/webp') === 0);
    };

    checkWebPSupport();
  }, []);

  if (supportsWebP === null) {
    return <OptimizedImage {...props} src={fallbackSrc || src} />;
  }

  const imageSrc = supportsWebP && src.includes('.webp') ? src : fallbackSrc || src;

  return <OptimizedImage {...props} src={imageSrc} />;
};

/**
 * Avatar Image Component with optimizations
 */
export const OptimizedAvatar = ({ 
  src, 
  name, 
  size = 40, 
  shape = 'circle',
  ...props 
}) => {
  const getInitials = (name) => {
    if (!name) return '?';
    return name
      .split(' ')
      .map(word => word[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const fallback = (
    <div
      style={{
        width: size,
        height: size,
        borderRadius: shape === 'circle' ? '50%' : '4px',
        backgroundColor: '#00B050',
        color: 'white',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        fontSize: size * 0.4,
        fontWeight: 'bold',
      }}
    >
      {getInitials(name)}
    </div>
  );

  return (
    <OptimizedImage
      src={src}
      alt={name}
      width={size}
      height={size}
      fallback={fallback}
      style={{
        borderRadius: shape === 'circle' ? '50%' : '4px',
        objectFit: 'cover',
      }}
      {...props}
    />
  );
};

export default OptimizedImage;
