import { DeleteOutlined, PlusOutlined } from "@ant-design/icons";
import { Button, Col, Input, Row, Table, Typography, message } from "antd";
import { setUploadFileFilesState } from "../../../../../../store/actions/files-action";
import { validateEmail } from "../../../../../../utils/validators";
import { useState } from "react";
import { shallowEqual, useSelector } from "react-redux";

export const EmailsTable = () => {
  const { Text } = Typography;
  const [currentEmail, setCurrentEmail] = useState("");

  const uploadFile = useSelector(
    (state) => state.files.uploadFile,
    shallowEqual
  );

  const handleAddEmail = () => {
    const isValidEmail = validateEmail(currentEmail);
    if (isValidEmail) {
      const newEmails = [...uploadFile.emails, currentEmail];
      setUploadFileFilesState({
        field: "uploadFile",
        value: { ...uploadFile, emails: newEmails },
      });
      setCurrentEmail("");
    } else {
      message.warning("Insira um email válido!");
    }
  };

  const handleDeleteEmail = (value) => {
    const remainingEmails = uploadFile.emails.filter((e) => e !== value);
    setUploadFileFilesState({
      field: "uploadFile",
      value: { ...uploadFile, emails: remainingEmails },
    });
  };

  const columns = [
    {
      title: "Email",
      dataIndex: "email",
      key: "email",
      width: "90%",
      render: (text, record) => {
        return <Text>{record}</Text>;
      },
    },
    {
      title: "Remover",
      dataIndex: "remove",
      key: "remove",
      align: "center",
      render: (text, record) => (
        <Button
          icon={<DeleteOutlined />}
          danger
          type="text"
          onClick={() => {
            handleDeleteEmail(record);
          }}
        ></Button>
      ),
    },
  ];

  return (
    <Row gutter={[8, 8]}>
      <Col span={24}>
        <Row justify="space-between" gutter={[8, 8]}>
          <Col span={21}>
            <Input
              placeholder="Insira um email"
              value={currentEmail}
              onChange={(e) => setCurrentEmail(e.target.value)}
            />
          </Col>
          <Col span={3}>
            <Row justify={"end"}>
              <Button
                icon={<PlusOutlined />}
                type="primary"
                onClick={handleAddEmail}
              ></Button>
            </Row>
          </Col>
        </Row>
        <Row style={{ marginTop: 10 }}>
          <Col span={24}>
            <Table columns={columns} dataSource={uploadFile.emails || []} />
          </Col>
        </Row>
      </Col>
    </Row>
  );
};
