.table-container {
  margin: 0 auto;
  width: 100%;
  height: 382px;
  background-color: rgb(255, 255, 255);
  box-shadow: 0px 0px 10px 2px rgba(113, 113, 113, 0.25);
  border-radius: 24px;
}

.table-container-top {
  display: flex;
  justify-content: space-around;
  align-items: center;
  margin-bottom: 13px;
}

.container-grid {
  display: grid;
  padding-left: 30px;
  grid-template-areas: 
  'A B'
  'C D'
  'E F';
}

.circle {
  border: 6px solid #19934A;
  border-radius: 50%;
  padding: 20%;
  height: 60px;
  width: 60px;
  display: inline-block;
}
  
.grid1{
  margin-right: 20px;
}

.grid2{
  justify-self: center;
}

.card-projects {
  width: 215px;
  height: 5.5em;
  display: flex;
  padding: 10px;
  align-items: center;
  box-shadow: 0px 0px 10px 1px rgba(64, 64, 64, 0.25);
  border-radius: 10px;
  margin-bottom: 15px;
}

.area-top-left {
  grid-area: A;
}

.area-top-right {
  grid-area: B;
}

.area-middle-left {
  grid-area: C;
}

.area-middle-right {
  grid-area: D;
}

.area-bottom-left {
  grid-area: E;
}

.area-bottom-right {
  grid-area: F;
}