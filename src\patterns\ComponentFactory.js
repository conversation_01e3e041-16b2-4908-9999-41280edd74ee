import React from 'react';
import { Button, Input, Select, DatePicker, Upload, Switch } from 'antd';
import { UploadOutlined } from '@ant-design/icons';

/**
 * Component Factory Pattern
 * Creates components based on configuration objects
 * Follows the Factory Pattern and Single Responsibility Principle
 */

/**
 * Base Component Interface
 * Defines the contract that all components must follow
 */
class BaseComponent {
  constructor(config) {
    this.config = config;
    this.validate();
  }

  validate() {
    if (!this.config.type) {
      throw new Error('Component type is required');
    }
  }

  render() {
    throw new Error('render method must be implemented');
  }
}

/**
 * Button Component Implementation
 */
class ButtonComponent extends BaseComponent {
  render() {
    const { 
      text, 
      type = 'default', 
      size = 'middle', 
      onClick, 
      disabled = false,
      loading = false,
      icon,
      ...props 
    } = this.config;

    return (
      <Button
        type={type}
        size={size}
        onClick={onClick}
        disabled={disabled}
        loading={loading}
        icon={icon}
        {...props}
      >
        {text}
      </Button>
    );
  }
}

/**
 * Input Component Implementation
 */
class InputComponent extends BaseComponent {
  render() {
    const { 
      placeholder, 
      value, 
      onChange, 
      type = 'text',
      size = 'middle',
      disabled = false,
      maxLength,
      ...props 
    } = this.config;

    return (
      <Input
        type={type}
        placeholder={placeholder}
        value={value}
        onChange={onChange}
        size={size}
        disabled={disabled}
        maxLength={maxLength}
        {...props}
      />
    );
  }
}

/**
 * Select Component Implementation
 */
class SelectComponent extends BaseComponent {
  render() {
    const { 
      options = [], 
      value, 
      onChange, 
      placeholder,
      size = 'middle',
      disabled = false,
      mode,
      ...props 
    } = this.config;

    return (
      <Select
        value={value}
        onChange={onChange}
        placeholder={placeholder}
        size={size}
        disabled={disabled}
        mode={mode}
        {...props}
      >
        {options.map(option => (
          <Select.Option key={option.value} value={option.value}>
            {option.label}
          </Select.Option>
        ))}
      </Select>
    );
  }
}

/**
 * DatePicker Component Implementation
 */
class DatePickerComponent extends BaseComponent {
  render() {
    const { 
      value, 
      onChange, 
      placeholder,
      format = 'DD/MM/YYYY',
      size = 'middle',
      disabled = false,
      ...props 
    } = this.config;

    return (
      <DatePicker
        value={value}
        onChange={onChange}
        placeholder={placeholder}
        format={format}
        size={size}
        disabled={disabled}
        {...props}
      />
    );
  }
}

/**
 * Upload Component Implementation
 */
class UploadComponent extends BaseComponent {
  render() {
    const { 
      text = 'Upload', 
      onChange, 
      accept,
      multiple = false,
      maxCount = 1,
      ...props 
    } = this.config;

    return (
      <Upload
        onChange={onChange}
        accept={accept}
        multiple={multiple}
        maxCount={maxCount}
        {...props}
      >
        <Button icon={<UploadOutlined />}>{text}</Button>
      </Upload>
    );
  }
}

/**
 * Switch Component Implementation
 */
class SwitchComponent extends BaseComponent {
  render() {
    const { 
      checked, 
      onChange, 
      checkedChildren,
      unCheckedChildren,
      size = 'default',
      disabled = false,
      ...props 
    } = this.config;

    return (
      <Switch
        checked={checked}
        onChange={onChange}
        checkedChildren={checkedChildren}
        unCheckedChildren={unCheckedChildren}
        size={size}
        disabled={disabled}
        {...props}
      />
    );
  }
}

/**
 * Component Factory
 * Implements Factory Pattern to create components
 */
class ComponentFactory {
  constructor() {
    this.componentMap = new Map([
      ['button', ButtonComponent],
      ['input', InputComponent],
      ['select', SelectComponent],
      ['datepicker', DatePickerComponent],
      ['upload', UploadComponent],
      ['switch', SwitchComponent],
    ]);
  }

  /**
   * Register a new component type
   * Follows Open/Closed Principle - open for extension
   */
  registerComponent(type, componentClass) {
    if (!componentClass.prototype instanceof BaseComponent) {
      throw new Error('Component must extend BaseComponent');
    }
    this.componentMap.set(type, componentClass);
  }

  /**
   * Create a component instance
   */
  createComponent(config) {
    const ComponentClass = this.componentMap.get(config.type);
    
    if (!ComponentClass) {
      throw new Error(`Unknown component type: ${config.type}`);
    }

    return new ComponentClass(config);
  }

  /**
   * Render a component from configuration
   */
  renderComponent(config, key = null) {
    try {
      const component = this.createComponent(config);
      return React.cloneElement(component.render(), { key });
    } catch (error) {
      console.error('Error rendering component:', error);
      return <div key={key}>Error: {error.message}</div>;
    }
  }

  /**
   * Get available component types
   */
  getAvailableTypes() {
    return Array.from(this.componentMap.keys());
  }
}

/**
 * Singleton instance of ComponentFactory
 * Implements Singleton Pattern
 */
class ComponentFactorySingleton {
  constructor() {
    if (ComponentFactorySingleton.instance) {
      return ComponentFactorySingleton.instance;
    }
    
    this.factory = new ComponentFactory();
    ComponentFactorySingleton.instance = this;
  }

  static getInstance() {
    if (!ComponentFactorySingleton.instance) {
      ComponentFactorySingleton.instance = new ComponentFactorySingleton();
    }
    return ComponentFactorySingleton.instance;
  }

  getFactory() {
    return this.factory;
  }
}

/**
 * Form Builder using Factory Pattern
 * Demonstrates composition and Factory usage
 */
export class FormBuilder {
  constructor() {
    this.factory = ComponentFactorySingleton.getInstance().getFactory();
    this.fields = [];
  }

  /**
   * Add a field to the form
   */
  addField(config) {
    this.fields.push(config);
    return this; // Method chaining
  }

  /**
   * Add multiple fields
   */
  addFields(configs) {
    this.fields.push(...configs);
    return this;
  }

  /**
   * Remove a field by index
   */
  removeField(index) {
    this.fields.splice(index, 1);
    return this;
  }

  /**
   * Clear all fields
   */
  clearFields() {
    this.fields = [];
    return this;
  }

  /**
   * Render the complete form
   */
  render(formProps = {}) {
    return (
      <div {...formProps}>
        {this.fields.map((fieldConfig, index) => 
          this.factory.renderComponent(fieldConfig, `field-${index}`)
        )}
      </div>
    );
  }

  /**
   * Get form configuration as JSON
   */
  toJSON() {
    return {
      fields: this.fields,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Load form from JSON configuration
   */
  fromJSON(json) {
    if (json.fields && Array.isArray(json.fields)) {
      this.fields = json.fields;
    }
    return this;
  }
}

/**
 * Hook for using ComponentFactory
 */
export const useComponentFactory = () => {
  const factory = ComponentFactorySingleton.getInstance().getFactory();
  
  return {
    createComponent: (config) => factory.createComponent(config),
    renderComponent: (config, key) => factory.renderComponent(config, key),
    registerComponent: (type, componentClass) => factory.registerComponent(type, componentClass),
    getAvailableTypes: () => factory.getAvailableTypes(),
  };
};

/**
 * Export the singleton instance
 */
export const componentFactory = ComponentFactorySingleton.getInstance().getFactory();

export default ComponentFactory;
