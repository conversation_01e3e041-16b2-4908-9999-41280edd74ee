image: amazon/aws-cli:latest

pipelines:
  branches:
    main:
      - step:
          name: Deploy to S3 (repo and branch-specific folder)
          deployment: Production
          script:
            - yum install -y zip
            - zip -r $BITBUCKET_REPO_SLUG.zip . -x ".git/*" "bitbucket-pipelines.yml"
            - echo "Starting S3 upload process for repository $BITBUCKET_REPO_SLUG and branch $BITBUCKET_BRANCH..."
            - aws s3 cp $BITBUCKET_REPO_SLUG.zip s3://$S3_BUCKET_NAME/$BITBUCKET_REPO_SLUG/$BITBUCKET_BRANCH/ --exclude ".git/*" --exclude "bitbucket-pipelines.yml"
            - echo "Upload complete to s3://$S3_BUCKET_NAME/$BITBUCKET_REPO_SLUG/$BITBUCKET_BRANCH/"
    hml:
      - step:
          name: Deploy to S3 (repo and branch-specific folder)
          deployment: Staging
          script:
            - yum install -y zip
            - zip -r $BITBUCKET_REPO_SLUG.zip . -x ".git/*" "bitbucket-pipelines.yml"
            - echo "Starting S3 upload process for repository $BITBUCKET_REPO_SLUG and branch $BITBUCKET_BRANCH..."
            - aws s3 cp $BITBUCKET_REPO_SLUG.zip s3://$S3_BUCKET_NAME/$BITBUCKET_REPO_SLUG/$BITBUCKET_BRANCH/ --exclude ".git/*" --exclude "bitbucket-pipelines.yml"
            - echo "Upload complete to s3://$S3_BUCKET_NAME/$BITBUCKET_REPO_SLUG/$BITBUCKET_BRANCH/"
    dev:
      - step:
          name: Deploy to S3 (repo and branch-specific folder)
          deployment: Test
          script:
            - yum install -y zip
            - zip -r $BITBUCKET_REPO_SLUG.zip . -x ".git/*" "bitbucket-pipelines.yml"
            - echo "Starting S3 upload process for repository $BITBUCKET_REPO_SLUG and branch $BITBUCKET_BRANCH..."
            - aws s3 cp $BITBUCKET_REPO_SLUG.zip s3://$S3_BUCKET_NAME/$BITBUCKET_REPO_SLUG/$BITBUCKET_BRANCH/ --exclude ".git/*" --exclude "bitbucket-pipelines.yml"
            - echo "Upload complete to s3://$S3_BUCKET_NAME/$BITBUCKET_REPO_SLUG/$BITBUCKET_BRANCH/"