import React from 'react';
import { Alert } from 'antd';
import { getEnvironmentBanner, getEnvironmentInfo } from '../../config/environments';

/**
 * Environment Banner Component
 * Shows environment information for non-production environments
 */
const EnvironmentBanner = ({ showDetails = false }) => {
  const banner = getEnvironmentBanner();
  const envInfo = getEnvironmentInfo();

  // Don't show banner in production
  if (!banner) {
    return null;
  }

  const message = showDetails 
    ? `${banner.text} - ${envInfo.url}`
    : banner.text;

  const description = showDetails ? (
    <div style={{ fontSize: '12px', marginTop: '4px' }}>
      <div>Ambiente: {envInfo.name}</div>
      <div>URL: {envInfo.url}</div>
      <div>Versão: {process.env.REACT_APP_VERSION || 'N/A'}</div>
    </div>
  ) : null;

  return (
    <Alert
      message={message}
      description={description}
      type="info"
      showIcon
      style={{
        margin: '8px',
        borderRadius: '4px',
        backgroundColor: banner.backgroundColor,
        borderColor: banner.color,
        color: banner.color,
      }}
      closable={!showDetails}
    />
  );
};

/**
 * Environment Info Component (for footer or debug panel)
 */
export const EnvironmentInfo = () => {
  const envInfo = getEnvironmentInfo();

  return (
    <div style={{ 
      fontSize: '11px', 
      color: '#666', 
      padding: '4px 8px',
      borderTop: '1px solid #f0f0f0',
      backgroundColor: '#fafafa'
    }}>
      {envInfo.name} | {envInfo.url} | v{process.env.REACT_APP_VERSION || 'dev'}
    </div>
  );
};

export default EnvironmentBanner;
