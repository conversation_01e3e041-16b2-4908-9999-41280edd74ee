import { useContext, useEffect, useRef, useState } from "react"

import ReactQuill from "react-quill"
import 'react-quill/dist/quill.snow.css';
import { EmailAutomationContext } from "../../../contexts/email-automation";
import { EmailAutomationReducerType } from "../../../contexts/reducers/email-automation-reducer";

const modules = {
    toolbar: [
        [{ 'header': [1, 2, false] }],
        ['bold', 'italic', 'underline', 'strike'],
        [{ 'list': 'ordered' }, { 'list': 'bullet' }, { 'indent': '-1' }, { 'indent': '+1' }],
    ]
}

const formats = [
    'header',
    'bold', 'italic', 'underline', 'strike',
    'list', 'bullet', 'indent',
    'link', 'image'
]

export const TextEditor = ({ value, setIsEdit, positionItem }) => {
    const { emailAutomationState, setEmailAutomationState } = useContext(EmailAutomationContext)
    const { templateList } = emailAutomationState
    const textEditorRef = useRef(null)
    const [text, setText] = useState(value)

    useEffect(() => {
        if (textEditorRef)
            textEditorRef.current.focus()

    }, [textEditorRef])

    function handleOnBlur(event, type) {
        if (type === 'silent')
            return

        setIsEdit(false)

        if (positionItem !== null) {
            const newTemplateListStr = JSON.stringify(templateList)
            const newTemplateList = JSON.parse(newTemplateListStr)

            newTemplateList[positionItem].value = text
            setEmailAutomationState({
                type: EmailAutomationReducerType.SET_TEMPLATE_LIST,
                value: newTemplateList
            })
        }
    }

    return (
        <ReactQuill
            theme="snow"
            value={text}
            style={{ maxWidth: 610 }}
            formats={formats}
            modules={modules}
            onKeyPress={(event) => console.log(event.key)}
            ref={textEditorRef}
            onBlur={handleOnBlur}
            onChange={setText}
        />
    )
}