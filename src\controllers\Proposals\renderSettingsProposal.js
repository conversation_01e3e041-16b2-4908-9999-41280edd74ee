import { apiProposals, getHeader } from "../../utils/api";

export async function renderSettingsProposal(state, premisseRef) {
  setTimeout(async () => {
    const headers = getHeader();

    let premisses = state.specialFields[2].value;
    let factors = state.specialFields[5].value;
    let convered = state.specialFields[3].value;

    // state["isPipedrive"] = true;
    // If proposal was created from pipedrive integration (isPipedrived === true)
    // and its technical status === "não inicializada" and premisses === "",
    // run API that sets a default value for its field

    if (
      state.isPipedrive === true &&
      premisses === "" &&
      state.status === "não inicializada"
    ) {
      premisses = await apiProposals
        .get("setting/propose-pdf-premises", { headers })
        .then((response) => {
          const { setting } = response.data;
          return setting.value.replace(/\\"/, "").replace(/\\n/, "");
        })
        .catch((error) => {
          return "";
        });
    }

    if (state.isPipedrive === true && factors === "") {
      factors = await apiProposals
        .get("setting/propose-pdf-factors", { headers })
        .then((response) => {
          const { setting } = response.data;
          return setting.value.replace(/\\"/, "").replace(/\\n/, "");
        })
        .catch((error) => {
          return "";
        });
    }
    premisseRef.current = premisses;

    if (state.isPipedrive === true && convered === "") {
      convered = await apiProposals
        .get("setting/propose-pdf-not-covered", { headers })
        .then((response) => {
          const { setting } = response.data;
          return setting.value.replace(/\\"/, "").replace(/\\n/, "");
        })
        .catch((error) => {
          return "";
        });
    }

    return {
      premisses: premisses,
      main_factors: factors,
      extra_points: convered,
    };
  }, 500);
}
