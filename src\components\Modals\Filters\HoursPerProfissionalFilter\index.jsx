import { useState } from "react";
import { React } from "react";
import {
  Checkbox,
  Select,
  Button,
  Form,
  Typography,
  Row,
  Col,
  message,
  Tooltip,
} from "antd";
import { CloseCircleOutlined } from "@ant-design/icons";
import { otrsGet } from "../../../../service/apiOtrs";
import { filterByOrderOfInput } from "../../../../utils/filterByOrderOfInput";

function HoursPerProfissionalFilter(props) {
  const { Title, Text } = Typography;
  const { Option } = Select;
  const [form] = Form.useForm();
  const [disableContract, setDisableContract] = useState(true);
  const [contractsLoading, setContractsLoading] = useState(false);

  function onFilterFailed() {}

  function clear() {
    form.setFieldsValue({
      billable: false,
      Nbillable: false,
      client: undefined,
      contract: undefined,
    });
    props?.setContractsFromCustomer(undefined);
  }

  const sortAlphaObj = (array) => {
    // Verificar se array é válido e é realmente um array
    if (!array || !Array.isArray(array)) {
      return [];
    }

    const sortedArray = array.sort((a, b) => {
      if (a.name > b.name) {
        return 1;
      }
      if (a.name < b.name) {
        return -1;
      }
      return 0;
    });

    return sortedArray;
  };

  const sortAlpha = (array) => {
    // Verificar se array é válido e é realmente um array
    if (!array || !Array.isArray(array)) {
      return [];
    }

    const sortedArray = array.sort((a, b) => {
      if (a > b) {
        return -1;
      }
      if (a < b) {
        return 1;
      }
      return 0;
    });
    return sortedArray;
  };

  async function getContracts(event) {
    setContractsLoading(true);
    setDisableContract(false);
    await otrsGet(`read/contract/customer/${event}`).then(
      async (clientContracts) => {
        let arrayNames = [];
        Object.entries(clientContracts.data).forEach((val) => {
          arrayNames.push(val[1]);
        });
        if (arrayNames.length === 0)
          message.warning("Nenhum contrato associado a esse cliente");
        props?.setContractsFromCustomer(arrayNames);
      }
    );
    setContractsLoading(false);
  }

  function onFilterFinish(values) {
    props.setLoading(true);
    props.filter(values);
    props.handleVisibleChange();
  }

  return (
    <Form
      form={form}
      style={{ display: "flex", flexDirection: "column", width: "300px" }}
      name="basic"
      initialValues={{
        remember: true,
      }}
      onFinish={onFilterFinish}
      onFinishFailed={onFilterFailed}
      autoComplete="off"
    >
      <Row justify="space-between" align="middle">
        <Title level={5} style={{ fontWeight: 400, margin: "0px" }}>
          Horas por mês
        </Title>
        <Button type="text" onClick={() => props.handleVisibleChange()}>
          <CloseCircleOutlined />
        </Button>
      </Row>
      <Row>
        <Col span={10}>
          <Form.Item
            valuePropName="checked"
            name="billable"
            initialValue={
              props?.currentFilter?.billable
                ? props.currentFilter.billable
                : false
            }
          >
            <Checkbox>Billada</Checkbox>
          </Form.Item>
        </Col>
        <Col span={14}>
          <Form.Item
            valuePropName="checked"
            name="Nbillable"
            initialValue={
              props?.currentFilter?.Nbillable
                ? props.currentFilter.Nbillable
                : false
            }
          >
            <Checkbox>Não Billada</Checkbox>
          </Form.Item>
        </Col>
      </Row>

      <Form.Item
        name="client"
        initialValue={
          props?.currentFilter?.client ? props.currentFilter.client : undefined
        }
        rules={[
          {
            required: true,
            message: "Selecione um cliente",
          },
        ]}
      >
        <Select
          showSearch
          style={{ width: "100%" }}
          placeholder="Selecione um cliente"
          optionFilterProp="children"
          filterOption={(input, option) =>
            filterByOrderOfInput(input, option?.props?.children)
          }
          onChange={(event) => getContracts(event)}
          filterSort={(optionA, optionB) => {
            return optionA.children
              .toLowerCase()
              .localeCompare(optionB.children.toLowerCase());
          }}
        >
          {props?.clients.map((val, index) => {
            return (
              val.name !== "" && (
                <Option key={index} value={val?.identifications?.itsm_id}>
                  {val?.names?.name || val?.names?.fantasy_name}
                </Option>
              )
            );
          })}
        </Select>
      </Form.Item>
      <Tooltip
        title={
          form.getFieldValue("client") === undefined
            ? "Selecione um cliente para habilitar a seleção contratos"
            : ""
        }
      >
        <Form.Item
          name="contract"
          initialValue={
            props?.currentFilter?.contract &&
            props.currentFilter.contract !== null
              ? props.currentFilter.contract
              : undefined
          }
        >
          <Select
            showSearch
            loading={contractsLoading}
            style={{ width: "100%" }}
            placeholder="Selecionar contrato"
            disabled={props?.contractsFromCustomer ? false : true}
            optionFilterProp="children"
            filterOption={(input, option) =>
              filterByOrderOfInput(input, option?.props?.children)
            }
          >
            {sortAlpha(props?.contractsFromCustomer)?.map((val, index) => (
              <Option key={index} value={val.id}>
                {val.name}
              </Option>
            ))}
          </Select>
        </Form.Item>
      </Tooltip>
      <Row justify="space-between">
        <Button type="text" onClick={clear}>
          Limpar
        </Button>
        <Button type="primary" htmlType="submit" className="filter-submit-btn">
          Aplicar
        </Button>
      </Row>
    </Form>
  );
}

export default HoursPerProfissionalFilter;
