import { dsmApiProvider } from "../../../provider/dsm-api-provider";
import { otrsWebserviceProvider } from "../../../provider/otrs-webservice-provider";
import { getItemsByDynamicIndex } from "../../../service/apiDsmDynamo";
import { setAllBillingState } from "../../../store/actions/billing-action";

const RESPONSE_TYPE = {
  BOOLEAN: "bool",
  INTEGER: "int",
};

export function existsItemUncheckSort(
  customer,
  responseType,
  complianceData,
  complianceItems
) {
  let checklist = [];
  if (responseType === RESPONSE_TYPE.BOOLEAN) {
    checklist = complianceData?.find((i) => i.customerId === customer.id);
  } else {
    checklist = complianceItems?.find((i) => i.customerId === customer.id);
  }

  let filteredItems = [];

  for (let i = 0; i < checklist?.items.length; i++) {
    const item = checklist?.items[i];

    if (
      item.name !== "MFA na conta root habilitado" &&
      item.name !== "Método de pagamento configurado para invoice" &&
      item.name !== "CNPJ configurado como 18.342.439/0001-68" &&
      item.name !== "Herança habilitada nas TaxSettings"
    ) {
      filteredItems.push(item);
    }
  }

  const includesFalse = filteredItems.some((item) => item.status === false);

  if (includesFalse) {
    if (responseType === RESPONSE_TYPE.INTEGER) {
      return 3;
    } else {
      return true;
    }
  } else {
    if (responseType === RESPONSE_TYPE.INTEGER) {
      return 1;
    } else {
      return false;
    }
  }
}
export async function existsItemUncheck(
  customer,
  responseType,
  complianceData,
  complianceItems
) {
  let checklist = [];
  if (responseType === RESPONSE_TYPE.BOOLEAN) {
    checklist = complianceData?.find((i) => i.customerId === customer.id);
  } else {
    checklist = complianceItems?.find((i) => i.customerId === customer.id);
  }

  let filteredItems = [];

  for (let i = 0; i < checklist?.items.length; i++) {
    const item = checklist?.items[i];

    if (
      item.name !== "MFA na conta root habilitado" &&
      item.name !== "Método de pagamento configurado para invoice" &&
      item.name !== "CNPJ configurado como 18.342.439/0001-68" &&
      item.name !== "Herança habilitada nas TaxSettings"
    ) {
      filteredItems.push(item);
    }
  }

  const includesFalse = filteredItems.some((item) => item.status === false);

  if (includesFalse) {
    if (responseType === RESPONSE_TYPE.INTEGER) {
      return 3;
    } else {
      return true;
    }
  } else {
    if (responseType === RESPONSE_TYPE.INTEGER) {
      return 1;
    } else {
      return false;
    }
  }
}

export const getCurrentCustomerComplianceData = async (customers) => {
  let complianceData = [];
  const table = `${process.env.REACT_APP_STAGE}-compliance-monitoring`;
  const index = "customerId";

  setAllBillingState({
    field: "complianceMonitoringLoading",
    value: true,
  });

  let formattedItems = customers;

  const promises = customers.map(async (customer) => {
    const customerId = customer.id;
    return getItemsByDynamicIndex(table, index, customerId);
  });

  const results = await Promise.all(promises);

  results.forEach((data, i) => {
    const customerId = customers[i].id;
    complianceData.push(data.shift());
    formattedItems = JSON.parse(JSON.stringify(formattedItems)).map((c) => {
      if (c.id === customerId) {
        c.complianceLoading = false;
      }
      return c;
    });

    if (i === customers.length - 1) {
      setAllBillingState({
        field: "complianceMonitoringLoading",
        value: false,
      });
    }
  });
  complianceData = complianceData.filter((c) => c !== undefined);

  complianceData = complianceData.filter((c) => c.customerId !== undefined);
  complianceData = complianceData.map((c) => {
    return {
      ...c,
      isCompliant: !c?.items
        .filter(
          (i) =>
            i.name !== "MFA na conta root habilitado" &&
            i.name !== "Método de pagamento configurado para invoice" &&
            i.name !== "CNPJ configurado como 18.342.439/0001-68" &&
            i.name !== "Herança habilitada nas TaxSettings"
        )
        .some((i) => i.status === false),
    };
  });
  setAllBillingState({ field: "complianceItems", value: complianceData });
};

export const initPage = async (complianceData) => {
  if (complianceData.length > 0) return;
  setAllBillingState({ field: "loading", value: true });
  const provider = dsmApiProvider();
  const headers = {
    dynamodb: `${process.env.REACT_APP_STAGE}-customers`,
  };
  const { data } = await provider.get("customers/read/billing?status=1", {
    headers: headers,
  });
  const contracts = await provider.get("contracts?active=1");

  let customers = data.data.Items;
  customers = customers.map((c) => {
    return {
      ...c,
      complianceLoading: true,
    };
  });
  setAllBillingState({ field: "customers", value: customers });
  setAllBillingState({ field: "contracts", value: contracts.data });
  setAllBillingState({ field: "loading", value: false });
};

export const getBillingDetails = async ({ year, month }) => {
  const provider = dsmApiProvider();
  const { data } = await provider.get(
    `/read-invoices?fullDate=${year}-${month}`
  );

  return data;
};

export const handleDateSelection = async (date, setBilling, setLoading) => {
  setLoading(true);
  let { data } = await getBillingDetails({
    month: parseInt(date.split("-").shift()),
    year: date.split("-").pop(),
  });
  let billingData = data.Items;

  setBilling(billingData);
  setLoading(false);
};

export const getTicketsByCustomerByDate = async (
  customerId,
  date,
  contract
) => {
  const provider = otrsWebserviceProvider();
  const { month, year } = date;
  let { data } = await provider.get(
    `/read/tickets/byContractBilling?customer_id=${customerId}&month=${month}&year=${year}`
  );

  data = data.filter(
    (t) => t.contract_id === contract?.identifications?.itsm_id
  );
  return data;
};

export const formatDateTickets = (date) => {
  if (!date) throw new Error("Invalid date");
  let formattedDate = {
    year: date.year(),
    month: date.month() + 1,
  };

  return formattedDate;
};

export const handleDateChange = async (
  date,
  setLoading,
  setSearch,
  setTickets,
  customerId,
  contract,
  setDate
) => {
  let formattedDate = formatDateTickets(date);
  setLoading(true);
  setDate(date);
  let tickets = await getTicketsByCustomerByDate(
    customerId,
    formattedDate,
    contract
  );
  setSearch("");
  setTickets(tickets);
  setLoading(false);
};
