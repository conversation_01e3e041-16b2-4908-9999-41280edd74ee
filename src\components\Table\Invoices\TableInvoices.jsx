import { BsBoxArrowRight } from "react-icons/bs";
import { Button, Table } from "antd";

export const TableInvoices = () => {
  const columns = [
    {
      title: "Conta",
      dataIndex: "account",
      key: "account",
      sorter: (a, b) => a.account - b.account,
      sortDirections: ["descend", "ascend"],
    },
    {
      title: "Cliente",
      dataIndex: "client",
      key: "client",
    },
    {
      title: "Unblended",
      dataIndex: "unblended",
      key: "unblended",
    },
    {
      title: "Blended",
      dataIndex: "blended",
      key: "blended",
    },
    {
      title: "Net",
      dataIndex: "net",
      key: "net",
    },
    {
      title: "Invoice",
      dataIndex: "invoice",
      key: "invoice",
    },
    {
      title: "Payer",
      dataIndex: "payer",
      key: "payer",
    },
    {
      title: "Moeda",
      dataIndex: "currency",
      key: "currency",
    },
    {
      title: "<PERSON><PERSON><PERSON>",
      dataIndex: "month",
      key: "month",
    },
  ];
  const data = [
    {
      account: "Conta",
      client: "Nome do Cliente",
      unblended: "unblended",
      blended: "blended",
      net: "net",
      invoice: "Invoices",
      payer: "Payer",
      currency: "currency",
      month: "mês",
    },
  ];

  const pagination = {
    data: [],
  };

  return (
    <>
      <Table
        style={{ minWidth: "100%", marginTop: "20px" }}
        dataSource={data}
        columns={columns}
        pagination={pagination}
      />
    </>
  );
};
