import { Button, Form, Input, message, Modal, Select, Tooltip } from "antd";
import { useState } from "react";
import { dynamoPut } from "../../../service/apiDsmDynamo";
import { InfoCircleOutlined } from "@ant-design/icons";
import { logBillingCreate } from "../../../controllers/audit/logBillingCreate";
import { initPage } from "../../../pages/Billing/controllers/complianceControllers";
import { moneyMask } from "../../../utils/money-maks";

export const CreateBilling = (props) => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [loadingContract, setLoadingContracts] = useState(false);
  const { contracts, clients, data, setFilteredCustomers } = props;
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();
  const { Option } = Select;

  const contractAlreadyAdded = (contract) => {
    if (Array.isArray(data) && data.length > 0) {
      const dataWithPayment = data.filter(
        (e) =>
          Array.isArray(e.payment_percent) &&
          e.payment_percent.some(
            (payment) => payment.percentage && payment.contract_id
          )
      );
      const foundContract = dataWithPayment.find(
        (e) =>
          Array.isArray(e.payment_percent) &&
          e.payment_percent.some(
            (payment) => payment.contract_id == contract.id
          )
      );

      return foundContract;
    }
  };

  if (!contracts && !loadingContract) {
    return setLoadingContracts(true);
  }

  if (contracts && loadingContract) {
    return setLoadingContracts(false);
  }

  const handleSubmit = async ({ contract, percentage, taxes, led_support }) => {
    setLoading(true);
    const { contract_id, customer_id } = JSON.parse(contract);
    percentage = percentage + "%";
    taxes = taxes + "%";

    let created_at = new Date(),
      updated_at = new Date();

    try {
      const customer = clients.find((c) => c.id === customer_id);
      const paymentPercent = customer?.payment_percent || [];

      dynamoPut(`${process.env.REACT_APP_STAGE}-customers`, customer_id, {
        payment_percent: [
          ...paymentPercent,
          {
            customer_id,
            contract_id,
            percentage,
            created_at,
            updated_at,
            taxes,
            led_support,
          },
        ],
      });
      logBillingCreate(contract_id, customer_id);

      await initPage([]);
      message.success("Billing cadastrado com sucesso!");
      setLoading(false);
      handleCancel();
    } catch (error) {
      console.log(error);
      message.error("Ocorreu um erro ao tentar cadastrar o billing.");
      setLoading(false);
    }
  };

  const showModal = async () => {
    setIsModalVisible(true);
  };

  const handleOk = () => {
    form.submit();
  };

  const handleCancel = () => {
    form.resetFields();
    setIsModalVisible(false);
  };

  const handleLedSupportChange = (e) => {
    const value = e.target.value;
    const formattedValue = moneyMask(value);
    e.currentTarget.value = formattedValue ? `US$ ${formattedValue}` : "";
  };

  return (
    <>
      <Button type="primary" onClick={showModal}>
        Cadastrar Billing
      </Button>
      <Modal
        title="Cadastre um Billing"
        open={isModalVisible}
        onOk={() => {
          handleOk();
        }}
        confirmLoading={loading}
        okText="Adicionar Billing"
        onCancel={handleCancel}
        cancelText="Cancelar"
      >
        <Form
          layout="vertical"
          onFinish={async () => {
            handleSubmit({
              contract: form.getFieldValue("contract"),
              percentage: form.getFieldValue("percentage"),
              taxes: form.getFieldValue("taxes"),
              led_support: form.getFieldValue("led_support"),
            });
          }}
          requiredMark={false}
          form={form}
          initialValues={{
            taxes: 55,
          }}
        >
          <Form.Item
            name="contract"
            label="Contrato"
            rules={[{ required: true, message: "Preencha este campo." }]}
          >
            <Select
              placeholder="Selecione um contrato"
              showSearch
              optionFilterProp="children"
              filterOption={(input, option) =>
                option.children?.toLowerCase().indexOf(input?.toLowerCase()) >=
                0
              }
              loading={loadingContract}
            >
              {contracts
                ?.filter(
                  (e) =>
                    e.active === 1 && e.name.toLowerCase().includes("billing")
                )
                ?.filter((e) => !contractAlreadyAdded(e))
                ?.map((e) => {
                  return (
                    <Option
                      value={JSON.stringify({
                        contract_id: e.id,
                        customer_id: e?.customer.id,
                      })}
                    >
                      {e?.name}
                    </Option>
                  );
                })}
            </Select>
          </Form.Item>
          <Form.Item
            rules={[{ required: true, message: "Preencha este campo." }]}
            label={
              <>
                Porcentagem de Desconto
                <Tooltip title="Este é um campo relacionado ao desconto em cima da Payer Account.">
                  <Button type="text">
                    <InfoCircleOutlined />
                  </Button>
                </Tooltip>
              </>
            }
            name="percentage"
          >
            <Input placeholder="Porcentagem de Desconto" suffix="%" />
          </Form.Item>
          <Form.Item label={"Impostos"} name="taxes">
            <Input
              placeholder="Porcentagem de Impostos"
              type="number"
              suffix="%"
            />
          </Form.Item>
          <Form.Item label={"Led Support"} name="led_support">
            <Input
              placeholder="Led Support"
              onInput={handleLedSupportChange}
              type="text"
            />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};
