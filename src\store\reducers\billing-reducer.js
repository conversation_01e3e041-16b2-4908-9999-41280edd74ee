import { createSlice } from "@reduxjs/toolkit";
import { totalValuesInitialState } from "../../constants/totalValuesInitialState";

const REDUCER_NAME = "billing";

const INITIAL_STATE = {
  loading: false,
  customers: [],
  contracts: [],
  finops: [],
  complianceItems: [],
  complianceMonitoring: [],
  complianceMonitoringLoading: [],
};

const billingSlice = createSlice({
  name: REDUCER_NAME,
  initialState: INITIAL_STATE,
  reducers: {
    setAllBillingReduce: (state, action) => {
      state[action.payload.field] = action.payload.value;
    },
    cleanBillingReduce: (state) => {
      Object.keys(INITIAL_STATE).forEach((key) => {
        state[key] = INITIAL_STATE[key];
      });
    },
  },
});

export const { setAllBillingReduce, cleanBillingReduce } = billingSlice.actions;

export default billingSlice.reducer;
