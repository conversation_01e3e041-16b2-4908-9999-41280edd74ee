.kanbanContainer {
  width: 1098px;
  height: 80vh;
  background-color: white;
  border-radius: 24px;
}

.kanbanHeader {
  width: 100%;
  display: flex;
  padding: 10px;
  justify-content: space-between;
}

.kanbanHeaderLeft {
  display: flex;
  align-items: center;
  gap: 30px;
}

.btnGreen {
  background-color: #43914f;
  font-weight: bold;
  color: white;
  box-shadow: 0px 0px 10px 2px rgba(64, 64, 64, 0.25);
  border-radius: 5px;
  height: 44px;
}

.select {
  width: 122px;
  height: 44px;
}

.filterContent {
  width: 100px;
  margin-top: 20px;
  text-align: center;
  justify-content: center;
  display: flex;
  cursor: "pointer";
}

.columnsContainer {
  width: 100%;
  height: 80%;
  border-radius: 10px;
  display: flex;
  gap: 10px;
}

.columnSingle {
  border-radius: 10px;
  height: 100%;
  width: calc(100% / 5);
  background-color: #f0f2f5;
}

.columnHeader {
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 16px;
}

.swapIcon {
  color: black;
  font-size: 20px;
  rotate: 90deg;
}

.columItems {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 20px;
  overflow-y: scroll;
  height: 80%;
}

.columItems::-webkit-scrollbar {
  width: 1px; /* width of the entire scrollbar */
}

.item {
  width: 90%;
  align-self: center;
  background-color: white;
  border-radius: 5px;
  padding: 10px;
  box-shadow: 0px 0px 3.98134px 1.59254px rgba(103, 204, 113, 0.25);
  cursor: pointer;
}

.itemTitle {
  display: flex;
  justify-content: space-between;
}

.itemTitle h3 {
  padding: 5px;
}

.flex {
  display: flex;
  align-items: center;
  gap: 14px;
}

.itemTitle p {
  padding: 5px;
  background-color: #ffaeae;
  border-top-right-radius: 5px;
  border-bottom-left-radius: 5px;
}

.itemInfos {
  display: flex;
  flex-direction: column;
  text-align: start;
}

.dateTime {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 5px;
  gap: 10px;
}

.appstoreIcon {
  color: #43914f;
  font-size: 30px;
}

.container {
  height: 601px;
  width: 20vw;
  background-color: white;
  box-shadow: 0px 0px 10px 2px rgba(113, 113, 113, 0.25);
  border-radius: 24px;
  transition: 1s all;
}

.contentHeader {
  display: flex;
  justify-content: space-around;
  align-items: center;
  text-align: center;
  padding: 20px;
}

.contentHeader p {
  margin-top: 20px;
}

.priority {
  padding: 10px;
  background-color: #ffaeae;
  border-radius: 4px;
}

.btnClose {
  background-color: #43914f;
  color: white;
  display: flex;
  padding: 20px;
  font-weight: bold;
  border-radius: 8px;
  align-items: center;
}

.title {
  display: flex;
  justify-content: space-between;
  padding: 0 20px;
}

.btnDots {
  background-color: transparent;
  font-weight: bold;
  cursor: pointer;
  border: none;
  font-size: 30px;
}

.status {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 0 20px;
}

.statusIcon {
  color: grey;
}
.statusClient {
  padding: 10px;
  border-radius: 4px;
  border: solid 1px green;
}

.dates {
  display: flex;
  align-items: center;
  text-align: center;
  justify-content: space-between;
  padding: 0 20px;
}

.singleDate {
  display: flex;
  align-items: center;
  gap: 10px;
}

.singleDate p {
  margin: 20px 0;
}

.owner {
  display: flex;
  align-items: center;
  gap: 10px;
}

.progressContainer {
  display: flex;
  gap: 8px;
}

.progress {
  margin: 20px 0px;
}

.progressTimer {
  padding: 5px 30px;
  margin: 0 5px;
  box-shadow: 0px 0px 2.39101px 1.59401px rgba(113, 113, 113, 0.25);
  border-radius: 8px;
}
.kanban-card {
  width: 1000px;
  height: 620px;
  border-radius: 20px;
}

.kanban-container {
  width: 100%;
  display: flex;
  padding: 10px;
  justify-content: space-between;
}

.ant-modal-body{
  max-width: 90vw;
}

.kanban-items {
  display: flex;
  align-items: center;
  gap: 24px;
}
.kanban-tickets-items {
  width: 100%;
  height: 90%;
  display: flex;
  gap: 4px;
  border-radius: 20px;
}
.kanban-tickets-column {
  border-radius: 8px;
  height: 100%;
  width: calc(100% / 5);
  background-color: #f0f2f5;
}

/* p-3 flex items-center justify-between text-base */
.kanban-tickets-column-container {
  padding: 12px;
  display: flex;
  align-items: baseline;
  font-size: 11px;
  justify-content: space-between;
}

.kanban-single-column {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  overflow-y: scroll;
  height: 90%;
}

.kanban-single-column::-webkit-scrollbar {
  width: 10px;
  background-color: rgba(206, 206, 206, 0.5); /* color of the scroll thumb */
  border-radius: 5px; /* roundness of the scroll thumb */
}

.kanban-single-column::-webkit-scrollbar-thumb {
  background-color: rgba(137, 137, 137, 0.6); /* color of the scroll thumb */
  height: 20px;
  border-radius: 10px; /* roundness of the scroll thumb */
}

/* w-11/12 items-center bg-white rounded p-2 shadow-md cursor-pointer */
.kanban-item {
  width: 91.6%;
  align-items: baseline;
  background-color: white;
  border-radius: 4px;
  padding: 10px;
  box-shadow: 1px 1px 1px black;
  cursor: pointer;
}

.kanban-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.new-backlog {
  font-weight: bold;
  line-height: 1em;
  margin-right: 8px;
}

.text-xs-client,
.text-xs-owner,
.text-xs-date {
  text-align: left;
  line-height: 1em;
  margin-bottom: 10px;
}

.text-center {
  color: #d1d2d4;
}

.time-ticket {
  display: flex;
  gap: 2px;
}

.text-xs-ticket {
  padding: 0;
  border-radius: 7px;
  border: 1px solid;
  line-height: 1em;
}

.text-xs-time {
  margin-right: 4px;
}

.lateral-bar {
  width: 270px;
  height: auto;
  border-radius: 25px;
  padding: 12px;
  box-sizing: border-box;
  box-shadow: 2px 2px 2px 2px #c7c3c3;
}

.lateral-bar-top {
  display: flex;
  justify-content: space-around;
  gap: 6px;
}

.lateral-bar-header {
  display: flex;
  align-items: center;
  justify-content: space-around;
  margin-bottom: 10px;
  width: 100%;
}

.lateral-bar-header-title {
  display: flex;
  gap: 2px;
}

.lateral-bar-ticket-status {
  font-size: 17px;
  display: flex;
  align-items: center;
  padding: 4px;
  border-radius: 7px;
  border: 1px solid;
  line-height: 1em;
  width: 80px;
}

.lateral-bar-x-button {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #ff3c3c;
  color: white;
  font-weight: bold;
  border-radius: 10px;
  font-size: 16px;
  width: 34px;
  height: 34px;
}

.lateral-bar-second-line {
  display: flex;
  justify-content: space-around;
  align-items: center;
  margin: 10px 0 20px 0;
}

.lateral-bar-second-line h3 {
  line-height: 1.1em;
}

.lateral-bar-second-line p {
  font-weight: bold;
}

.btnDots {
  font-weight: bold;
  font-size: 40px;
}

.lateral-bar-dots {
  display: flex;
  align-items: center;
  height: 100px;
}

.lateral-bar-third-line {
  display: flex;
  align-items: baseline;
}

.lateral-bar-third-line-status-and-icon {
  display: flex;
  gap: 4px;
  margin-right: 60px;
}

.lateral-bar-third-line-status {
  border: 2px solid rgb(106, 221, 108);
  line-height: 1em;
  padding: 8px;
  border-radius: 10px;
}

.owner-title {
  display: flex;
  align-items: baseline;
  gap: 4px;
}
