import { useEffect, useState } from 'react'
import { HeaderMenu } from "../../components/HeaderMenu";
import { SideMenu } from "../../components/SideMenu";
import { Card, Layout } from "antd";
import { Content } from "antd/lib/layout/layout";
import { Technician } from "../../components/Graphics/Technician";
import { BornupGraph } from "../../components/Graphics/BornupGraph";
import { TotalProjects } from "../../components/Graphics/TotalProjects";
import { ProjectsByClient } from "../../components/Graphics/ProjectsByClient";
import { Col, Row } from 'antd'

export const CockpitPdms = (props) => {
  const { collapsed, setCollapsed } = props;

  return (
    <Layout style={{ minHeight: "100vh" }}>
      <HeaderMenu collapsed={collapsed} setCollapsed={setCollapsed} />
      <Layout>
        <SideMenu collapsed={collapsed} />
        <Content style={{ padding: "2em", rowGap:"500px" }}>
          <Row gutter={[12, 24]}>
            <Col span={12}>
              <TotalProjects />
            </Col>
            <Col span={12}>
              <ProjectsByClient />
            </Col>
            <Col span={12}>
              <Technician />
            </Col>
            {/* <Col span={12}> <BornupGraph /> </Col> */}
          </Row>
        </Content>
      </Layout>
    </Layout>
  );
}