# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.1.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

### Added

-

### Changed

-

### Removed

-

## [1.6.45] - 25-07-2024

### Added

- Adding otrs notification when allowing access (Switch roles page)
- Increment of contact information in DSM (Clients page)
- Creating the executives filter in the contracts menu (Contract page)
- Adding file presence indicators within the proposal modal (Commercial Proposal and Technical Proposal page)

### Compatibility

- [1.x.x] dsm-back-end
-

## [1.2.45] - 04-07-2024

### Added

- Fix bug PLAT-1851 - Add validation for files added to the modal (Technical proposal page)
- Fix bug PLAT-1903 - Variable content when editing contact is not coming correctly (Audit page)
- Fix bug PLAT-2124 - Executive activation status is only being displayed after screen update (Executive modal - Contract page)
- Fix bug PLAT-2171 - Change "Cancel" button to "Cancelar" in the contact creation modal (Executive modal - Contract page)
- Fix bug PLAT-1947 - Change "Cancel" button to "Cancelar" (Manage User page)
- Fix bug PLAT-2336 - Change "Cancel" button to "Cancelar" (Manage Permission page)
- Fix bug PLAT-1772 - When deactivating a contract, the update is not displayed immediately (Clients page)
- Fix bug PLAT-2281 - Contatos aparecendo como inativados no OTRS e Ativos no DSM (Clients page)
- Fix bug PLAT-2220 - Remove favorite icon from modals (Cockpit Analyst page)
- Fix bug PLAT-2120 - Select existing tag while creating a service
- Fix bug PLAT-1945 - Order username column switch roles
- Fix bug PLAT-1844 - Block first sub activity

### Compatibility

- [1.x.x] dsm-back-end
-

## [1.2.33] - 13-06-2024

### Added

- Fix bug PLAT-1841 -Service is automatically activated after an edit when it is deactivated (Services Catalog page)
- Fix bug PLAT-1843 -Failed to add sub activity after deleting the first activity in a management service (Services Catalog page)
- Fix bug PLAT-1839 -Bug when adding another activity to the service (Services Catalog page)
- Fix bug PCD-3 - Prevent editing email from a contact to an existing one (Contact modal - Clients page)
- Fix bug PLAT-1962 - When adding permission only to the professional or client tab, the tab that is displayed is the wallet (Cockpit page)
- Fix bug PLAT-1843 - Color of the registered management service tag is not being displayed correctly when editing (Services Catalog page)
- Fix bug PLAT-1909 - Audit of contact deactivation/activation is showing as if it were an edit
- Fix bug PLAT-1931 - Create audit to inform the creation of a commercial proposal from a template
- Fix bug PLAT-1932 - Billing registration audit is not being carried out
- Fix bug PLAT-1934 - Incorrect user deactivation audit name
- Fix bug PLAT-1935 - When clicking to deactivate a user, the button that should be "Activate" remains as deactivate
- Fix bug PLAT-1937 - Rename audit name for contact activation and deactivation
- Fix bug PLAT-1954 - It is now possible to update a contact's email to an existing one
- Fix bug PLAT-1960 - Name of the dashboard module being displayed when releasing only the Billing, invoice or audit screen permission
- Addition of the EDP commitment graph (Billing page)

### Compatibility

- [1.x.x] dsm-back-end
-

## [1.1.19] - 04-06-2024

### Added

- Addition of other discounts column (Invoices page)

### Compatibility

- [1.x.x] dsm-back-end
-

## [1.0.19] - 23-05-2024

### Added

- Fix bug PCD-3 - Prevent editing email from a contact to an existing one (Contact modal - Clients page)
- Fix bug PCD-13 - Permission for the home page is giving access to all pages in the dashboard module (Home page)
- Fix bug PCD-6 - It is possible to register a permission without first selecting a screen and a permission (Add Permission modal - Manage Permission page )
- Fix bug PCD-7 - "View All Switch Role Requests" functionality is not working individually (Home page)
- Fix bug PCD-10 - View files even without having "View files" permission (Files page)
- Fix bug PCD-50 - ITSM and CRM is not showing only with the contracts modal permissions (Clients page)
- Fix bug PCD-4 - Exporting hours consumption report in PDF not working (Consumption Hours page)
- Fix bug PCD-52 - It is possible to edit the contract name to an existing contract name (Contract edit modal - Contract page)
- Fix bug PCD-50 - ITSM AND CSM is not appearing on the screen with functionalities only from the contracts modal (Contract modal - Clients page)
- Fix bug PCD-53 - It is being allowed to click on the button to add executive, even when fields are not filled in (Contract page)
- Fix bug PCD-54 - Create a rule to prevent the effective start date from being greater than the end date (Contract page)
- Fix bug PCD-5 - When editing a deactivated technical/commercial proposal, the proposal is automatically activated (Commercial Proposal page)
- Fix bug PCD-49 - Hours type filter does not work (Consumption Hours page)
- Fix bug PCD-51 - Contracts modal is not green, even though there are contracts (Clients page)
- Fix bug - When we add a client to DSM, the audit shows that an edit was made (Audit page)
- Fix bug PCD-65 - It is possible to register the same account for a customer more than once (Clients page)
- Fix bug PCD-9 - Cockpit Analyst with modals in infinite loop when adding only the screen as permission (Cockpit Analyst page)
- Fix bug PCD-17 -The contact is not defined in the commercial proposal (Commercial Proposal page)

### Compatibility

- [1.x.x] dsm-back-end
-

## [1.0.1] - 09-05-2024

### Added

- Tax ID column (Clients page)

### Compatibility

- [1.x.x] dsm-back-end
- []
