export const dsmContractsMockedData = [
  {
    id: 1,
    active: 0,
    approved_proposal: "SUST-E2",
    block_emails: "sds",
    customer: {
      id: 2,
      itsm_id: 3,
    },
    excess_cost: 0,
    identifications: {
      crm_id: null,
      itsm_id: 537,
    },
    name: "contrato teste",
    squad: "SUST-E2",
    target_dates: {
      expected_close_date: "2021/10/21 00:00:00",
      expected_start_date: "2020/10/21 00:00:00",
    },
    total_hours: 0,
    type_hours: "Ilimitado",
  },
  {
    id: 1,
    active: 1,
    approved_proposal: "SUST-E2",
    block_emails: "sds",
    customer: {
      id: 2,
      itsm_id: 3,
    },
    excess_cost: 0,
    identifications: {
      crm_id: null,
      itsm_id: 537,
    },
    name: "contrato teste",
    squad: "SUST-E2",
    target_dates: {
      expected_close_date: "2021/10/21 00:00:00",
      expected_start_date: "2020/10/21 00:00:00",
    },
    total_hours: 0,
    type_hours: "Ilimitado",
  },
  {
    id: 1,
    active: 1,
    approved_proposal: "SUST-E2",
    block_emails: "sds",
    customer: {
      id: 2,
      itsm_id: 3,
    },
    excess_cost: 0,
    identifications: {
      crm_id: null,
      itsm_id: 537,
    },
    name: "contrato teste",
    squad: "SUST-E2",
    target_dates: {
      expected_close_date: "2021/10/21 00:00:00",
      expected_start_date: "2020/10/21 00:00:00",
    },
    total_hours: 0,
    type_hours: "Ilimitado",
  },
  {
    id: 1,
    active: 0,
    approved_proposal: "SUST-E2",
    block_emails: "sds",
    customer: {
      id: 2,
      itsm_id: 3,
    },
    excess_cost: 0,
    identifications: {
      crm_id: null,
      itsm_id: 537,
    },
    name: "contrato teste",
    squad: "SUST-E2",
    target_dates: {
      expected_close_date: "2021/10/21 00:00:00",
      expected_start_date: "2020/10/21 00:00:00",
    },
    total_hours: undefined,
    type_hours: "Ilimitado",
  },
  {
    id: 1,
    active: 0,
    approved_proposal: "SUST-E2",
    block_emails: "sds",
    customer: {
      id: 2,
      itsm_id: 3,
    },
    excess_cost: 0,
    identifications: {
      crm_id: null,
      itsm_id: 537,
    },
    name: "contrato teste",
    squad: "SUST-E2",
    target_dates: {
      expected_close_date: "2021/10/21 00:00:00",
      expected_start_date: "2020/10/21 00:00:00",
    },
    total_hours: "23",
    type_hours: "Ilimitado",
  },
  {
    id: 1,
    active: 0,
    approved_proposal: "SUST-E2",
    block_emails: "sds",
    customer: {
      id: 2,
      itsm_id: 3,
    },
    excess_cost: 0,
    identifications: {
      crm_id: null,
      itsm_id: 537,
    },
    name: "contrato teste",
    squad: "SUST-E2",
    target_dates: {
      expected_close_date: "2021/10/21 00:00:00",
      expected_start_date: "2020/10/21 00:00:00",
    },
    total_hours: 50,
    type_hours: "Ilimitado",
  },
];
