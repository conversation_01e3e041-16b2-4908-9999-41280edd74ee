import { Row, <PERSON>, Typo<PERSON>, <PERSON>, But<PERSON> } from "antd";
import { useContext, useState } from "react";
import { ProposalContext } from "../../contexts/totalValuesProposals";
import { TotalValuesProposalType } from "../../contexts/reducers/totalValuesProposal";

export const TotalCalculatorCards = () => {
  const { Title, Text } = Typography;
  const { proposalState, setProposalState } = useContext(ProposalContext);

  const filteredTotalValues = proposalState?.totalValues?.filter(
    (v) => v.hourClass === "hourValue"
  )[0].values;

  const sumTotalOfHours = (array) => {
    let result = array.reduce((acc, curr) => {
      return acc + curr.totalValue;
    }, 0);
    return Number(result.toFixed(2));
  };

  const formatedFormName = (str) => {
    if (str.includes("8x5")) return "8x5";

    if (str.includes("24x7")) return "24x7";
  };

  const handleSpreadSetup = (setupHours, sustHours) => {
    const spreadSetupValue = proposalState.spreadSetup[0].value;
    console.log({ spreadSetupValue });
    const sumSetupValue = sumTotalOfHours(setupHours);
    let newSpread = proposalState?.spreadSetup;

    if (spreadSetupValue > 0) {
      for (let i = 0; i < newSpread.length; i++) {
        newSpread[i].value = 0;
      }
      setProposalState({
        type: TotalValuesProposalType.SET_SPREAD_SETUP,
        value: newSpread,
      });
      return;
    }

    for (let i = 0; i < newSpread.length; i++) {
      const newSustValue =
        sumTotalOfHours(sustHours) + sumSetupValue / Number(newSpread[i].month);
      newSpread[i].value = newSustValue;
    }

    setProposalState({
      type: TotalValuesProposalType.SET_SPREAD_SETUP,
      value: newSpread,
    });
  };

  return (
    <Row gutter={[16, 16]}>
      {filteredTotalValues
        .filter((i) => i.month === proposalState.month)
        ?.map((item, key) => {
          let allSetupHours = item.values.filter((i) => i.type === "SETUP");
          let allSustHours = item.values.filter((i) => i.type === "SUST");
          return (
            item.values.type !== "SEC" && (
              <>
                <Col style={{ height: "100%" }} key={key} span={10}>
                  <Card style={{ display: "flex" }} key={key}>
                    <Title level={4}>SETUP</Title>
                    <ul style={{ padding: "0" }}>
                      {allSetupHours.map((currentHour, key) => {
                        return (
                          <li style={{ textAlign: "justify" }} key={key}>
                            <Text>
                              {`
                            ${currentHour.totalHours} horas, em regime
                            ${formatedFormName(currentHour.formName)} - R$
                            ${currentHour.totalValue}
                          `}
                            </Text>
                          </li>
                        );
                      })}
                    </ul>
                    <Title level={5} style={{ fontWeight: 400 }}>
                      Total: R$
                      {proposalState?.spreadSetup[0].value > 0
                        ? 0
                        : sumTotalOfHours(allSetupHours)}
                    </Title>
                  </Card>
                </Col>
                <Col span={10}>
                  <Card>
                    <Title level={4}>SUST</Title>
                    <ul style={{ padding: "0" }}>
                      {allSustHours.map((currentHour, key) => {
                        return (
                          <li style={{ textAlign: "justify" }} key={key}>
                            <Text>
                              {`
                           ${currentHour.totalHours} horas mensais, em regime
                           ${formatedFormName(currentHour.formName)} - R$
                           ${currentHour.totalValue}
                          `}
                            </Text>
                          </li>
                        );
                      })}
                    </ul>
                    <Title level={5} style={{ fontWeight: 400 }}>
                      Total: R$
                      {proposalState?.spreadSetup[0].value > 0
                        ? proposalState?.spreadSetup[0].value.toFixed(2)
                        : sumTotalOfHours(allSustHours)}
                    </Title>
                  </Card>
                </Col>
                <Col
                  span={4}
                  style={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "end",
                  }}
                >
                  <Button
                    type={
                      proposalState?.spreadSetup[0].value > 0
                        ? "default"
                        : "primary"
                    }
                    onClick={() => {
                      handleSpreadSetup(allSetupHours, allSustHours);
                    }}
                  >
                    Diluir Valor Setup
                  </Button>
                </Col>
              </>
            )
          );
        })}
    </Row>
  );
};
