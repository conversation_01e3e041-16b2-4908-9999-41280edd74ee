import { useState, useEffect } from "react";

import { Row, Typography } from "antd";

export const NextUpdateField = () => {
  const [nextUpdate, setNextUpdate] = useState("");

  useEffect(() => {
    setTimeout(() => {
      const now = new Date();
      let minutes = now.getMinutes();
      let diffMinutes = minutes % 5;

      now.setMinutes(now.getMinutes() + (5 - diffMinutes));
      minutes = now.getMinutes().toString().padStart(2, "0");

      const hours = now.getHours().toString().padStart(2, "0");

      setNextUpdate(`${hours}h${minutes}`);
    }, [1000]);
  }, [nextUpdate]);

  return (
    <Row style={{ justifyContent: "flex-end" }}>
      <Typography.Text type="danger" strong={true}>
        *
      </Typography.Text>
      <Typography.Text>
        Atenção: próxima sincronização ocorrerá as {nextUpdate}
      </Typography.Text>
    </Row>
  );
};
