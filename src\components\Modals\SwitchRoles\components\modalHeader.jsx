import {
  <PERSON>,
  Col,
  Space,
  Button,
  Input,
  Typography,
  Popconfirm,
  Tooltip,
} from "antd";
import { Select } from "../../../../components/Select";
import { activeInactiveSelectOptions } from "../../../../constants/activeInactiveSelectOptions";
import { useState } from "react";

export const SolicitationsModalHeader = (props) => {
  const { Text } = Typography;
  const {
    setSearch,
    isMultipleAccounts,
    setIsMultipleAccounts,
    setState,
    origin,
    selectedRowKeys,
    setSelectedRowKeys,
    revokeFunction,
    tableData,
  } = props;
  const [revokeLoading, setRevokeLoading] = useState(false);
  const revokeAllSelected = async () => {
    setRevokeLoading(true);
    for (let i = 0; i < selectedRowKeys.length; i++) {
      await revokeFunction(selectedRowKeys[i], "revoke");
    }
    setRevokeLoading(false);
    setSelectedRowKeys([]);
  };

  return (
    <Row justify="space-between">
      <Col span={12} style={{ marginBottom: "1em", borderRadius: "15px" }}>
        <Space>
          <Input
            placeholder="Faça uma busca"
            style={{
              height: "35px",
              borderRadius: "7px",
            }}
            onChange={(e) => setSearch(e.target.value)}
          />
          {origin === "client" && tableData.length > 0 ? (
            <Space>
              <Button
                type="primary"
                onClick={() => {
                  setIsMultipleAccounts(!isMultipleAccounts);
                }}
              >
                {isMultipleAccounts
                  ? "Selecionar um acesso"
                  : "Selecionar múltiplos acessos"}
              </Button>
              {isMultipleAccounts && (
                <Popconfirm
                  okButtonProps={{
                    danger: true,
                    disabled: selectedRowKeys?.length === 0 ? true : false,
                  }}
                  cancelText="Cancelar"
                  okText={
                    selectedRowKeys?.length === 0
                      ? "Selecione ao menos uma conta"
                      : "Desabilitar"
                  }
                  title="Tem certeza que deseja desabilitar os acessos selecionados? Para recuperá-los será necessário solicitar novamente."
                  onConfirm={revokeAllSelected}
                  loading={revokeLoading}
                >
                  <Button>Desabilitar</Button>
                </Popconfirm>
              )}
            </Space>
          ) : null}
        </Space>
      </Col>
      <Col>
        <Space>
          <Text>Filtrar por: </Text>
          <Select
            onChange={setState}
            defaultValue="todos"
            options={activeInactiveSelectOptions}
          />
        </Space>
      </Col>
    </Row>
  );
};
