import { Row, Col, Image, Space, Divider } from "antd";
import Logo from "../../assets/images/logo_full.png";
import { LoadingOutlined } from "@ant-design/icons";
import { useNavigate } from "react-router-dom";
import React, { useEffect, useState } from "react";
import { jwtDecode } from "jwt-decode";

import axios from "axios";
import { dynamoGet } from "../../service/apiDsmDynamo";
import { cognitoPutRole } from "../../service/apiCognito";

const MFA = () => {
  const navigate = useNavigate();
  const [hasProcessed, setHasProcessed] = useState(false);

  const code = window.location.href.split("code=")[1];

  // Proteção contra loop infinito
  const loopProtectionKey = 'mfa_loop_protection';
  const maxAttempts = 3;
  const attemptWindow = 60000; // 1 minuto

  const authenticate = async () => {
    // Verificar se deve usar simulação local
    const isLocalhost = window.location.hostname === 'localhost';
    const hasLocalBackend = process.env.REACT_APP_LOCAL_BACKEND === 'true';
    const isDevelopment = process.env.NODE_ENV === 'development';

    // Usar simulação se for localhost OU se REACT_APP_LOCAL_BACKEND=true
    const shouldUseLocalAuth = isLocalhost || (isDevelopment && hasLocalBackend);

    console.log('🔍 DEBUG - Configuração de ambiente:', {
      isLocalhost,
      hasLocalBackend,
      isDevelopment,
      shouldUseLocalAuth
    });

    if (shouldUseLocalAuth) {
      // Simular dados de usuário para desenvolvimento
      const mockEmail = '<EMAIL>';
      const mockToken = 'dev-mock-token-' + Date.now();

      localStorage.setItem("@dsm/mail", mockEmail);
      localStorage.setItem("@dsm/name", mockEmail.split("@")[0]);
      localStorage.setItem("@dsm/username", mockEmail.split("@")[0]);
      localStorage.setItem("jwt", mockToken);
      localStorage.setItem("@dsm/permission", "1"); // Permissão básica
      localStorage.setItem("@dsm/time", new Date());

      navigate("/");
      return;
    }

    // Em ambientes remotos, usar backend configurado via variáveis de ambiente
    console.log('☁️ AMBIENTE REMOTO: Usando backend configurado via variáveis de ambiente');

    const cognitoAxios = axios.create({
      withCredentials: false, // CORS: Sem credentials para API externa
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json'
      }
    });

    console.log('🔍 DEBUG - Fazendo requisição para:', process.env.REACT_APP_COGNITO_PARSE);

    const response = await cognitoAxios.post(process.env.REACT_APP_COGNITO_PARSE, {
      code: code,
    });

    console.log('🔍 DEBUG - Resposta do cognito/parse:', response.data);

    const { data: { data: { id_token } } } = response;

    const { email } = jwtDecode(id_token);

    localStorage.setItem("@dsm/mail", email);
    localStorage.setItem("@dsm/name", email.split("@")[0]);
    localStorage.setItem("@dsm/username", email.split("@")[0]);
    localStorage.setItem("jwt", id_token);

    let cognitoUsers = [];
    try {
      const response = await axios.get(`${process.env.REACT_APP_API_PERMISSION}/cognito/read`, {
        headers: { authorization: id_token },
      });
      
      // ramos
      cognitoUsers = Array.isArray(response.data) ? 
        response.data : 
        response.data?.data || [];
        
    } catch (error) {
      console.error('❌ Erro ao buscar usuários do Cognito:', error);
      if (error.response) {
        console.error('❌ Resposta de erro:', error.response.data);
      }
      console.warn('⚠️ Continuando com lista vazia de usuários');
    }


    // Verificar se há diferenças de case ou espaços
    const emailsWithDetails = cognitoUsers?.map(u => ({
      email: u.email,
      emailLength: u.email?.length,
      emailTrimmed: u.email?.trim(),
      emailLower: u.email?.toLowerCase(),
      matches: u.email === email,
      matchesLower: u.email?.toLowerCase() === email?.toLowerCase(),
      user: u.user || 'N/A'
    })) || [];

    let user = cognitoUsers?.find((user) => user.email === email);

    // Se não encontrou, tentar busca case-insensitive
    if (!user) {
      user = cognitoUsers?.find((user) => user.email?.toLowerCase() === email?.toLowerCase());
      if (user) {
        console.log('✅ Usuário encontrado com busca case-insensitive!');
      }
    }

    console.log('🔍 DEBUG - ATUALIZADO 10:00:', user);

    // Verificação de segurança para evitar erro
    if (!user) {
      console.error('❌ Usuário não encontrado na lista do Cognito');
      return navigate("/unauthorized");
    }

    const permissionKey = process.env.REACT_APP_STAGE !== "prod"
      ? process.env.REACT_APP_STAGE + "_permission"
      : "permission";

    const userPermission = user[permissionKey];
    
    if (!userPermission) {
      try {
        const users = await dynamoGet("azure-ad-users");
        const permissions = await dynamoGet(`${process.env.REACT_APP_STAGE}-permissions`);
        
        let role;
        const u = users.find((user) => user.userPrincipalName === email);

        if (!u) {
          console.log("Usuário não encontrado...");
          return navigate("/unauthorized");
        }

        switch (u?.role) {
          case "full_admin":
            role = permissions.find(
              ({ name_permission }) => name_permission === "Admin"
            ).id;
            break;

          case "basic_access":
            role = permissions.find(
              ({ name_permission }) => name_permission === "Básico"
            ).id;
            break;

          case "dcq_access":
            role = permissions.find(
              ({ name_permission }) => name_permission === "DCQ"
            ).id;
            break;

          case "financial_access":
            role = permissions.find(
              ({ name_permission }) => name_permission === "Financeiro"
            ).id;
            break;

          case "project_access":
            role = permissions.find(
              ({ name_permission }) => name_permission === "Projetos"
            ).id;
            break;

          case "sales_access":
            role = permissions.find(
              ({ name_permission }) => name_permission === "Vendas"
            ).id;
            break;

          case "security_admin":
            role = permissions.find(
              ({ name_permission }) => name_permission === "Admin Segurança"
            ).id;
            break;

          default:
            return navigate("/unauthorized");
        }

        localStorage.setItem("@dsm/permission", role);
        localStorage.setItem("@dsm/time", new Date());

        await cognitoPutRole({
          role,
          user: user.user,
          stage: process.env.REACT_APP_STAGE,
        });

        return navigate("/");
      } catch (error) {
        console.error('❌ Erro ao obter permissão alternativa:', error);
        return navigate("/unauthorized");
      }
    } else {
      localStorage.setItem("@dsm/time", new Date());

      // Usar a mesma lógica de permissionKey para consistência
      const userPermission = user[permissionKey];

      if (userPermission) {
        localStorage.setItem("@dsm/permission", userPermission);
      } else {
        console.warn('⚠️ Nenhuma permissão encontrada para o usuário. Usando permissão padrão.');
        localStorage.setItem("@dsm/permission", "default-permission");
      }

      navigate("/");
    }
  };

  useEffect(() => {
    // Evitar processamento duplo (React StrictMode)
    if (hasProcessed) return;

    async function getData() {
      try {
        setHasProcessed(true);
        await authenticate();
      } catch (error) {
        console.error('❌ Erro na autenticação MFA:', error);
        setHasProcessed(false); // Permitir nova tentativa em caso de erro

        // Em desenvolvimento local, não redirecionar para login para evitar loop
        const isLocalDev = process.env.NODE_ENV === 'development' && window.location.hostname === 'localhost';
        if (isLocalDev) {
          console.warn('🔄 LOCALHOST: Erro ignorado para evitar loop infinito');
          console.warn('💡 Em ambientes remotos, isso redirecionaria para /login');

          // Mostrar erro na tela em vez de redirecionar
          alert('Erro de autenticação em desenvolvimento local. Verifique o console.');
          return;
        }

        // Em produção, redirecionar para login
        navigate('/login');
      }
    }

    if (code) {
      getData();
    } else {
      navigate('/login');
    }
  }, []);

  return (
    <>
      <Row
        align="middle"
        justify="center"
        style={{ minHeight: "100vh", background: "#ebedef" }}
      >
        <Col
          xs={18}
          lg={12}
          xl={6}
          style={{
            display: "flex",
            justifyContent: "center",
            padding: "2em",
            borderRadius: "10px",
            border: "1px solid #c9c9c9",
            flexDirection: "column",
            backgroundColor: "#ffffff",
          }}
        >
          <Space direction="vertical" align="center" size="middle">
            <Image preview={false} src={Logo}></Image>
            <Divider>
              Aguarde um momento...
              <br />
              Estamos te autenticando!
            </Divider>
          </Space>

          <LoadingOutlined style={{ fontSize: "48px" }} />
        </Col>
      </Row>
    </>
  );
};

export default MFA;
