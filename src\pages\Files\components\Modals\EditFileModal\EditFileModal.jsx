import { useState, useMemo } from "react";
import { useSelector, shallowEqual } from "react-redux";

import { Row, Col, Button, Modal, Input, message } from "antd";
import { EditOutlined, DeleteOutlined } from "@ant-design/icons";

import { useToggle } from "../../../../../hooks/useToggle";
import { dynamoPut } from "../../../../../service/apiDsmDynamo";

import { setUploadFilesState } from "../../../../../store/actions/files-action";

import { EditFileTags } from "./components/EditFileTags";
import { InputFileNameEditModal } from "./components/InputFileNameEditModal";
import { SelectCategoryEditModal } from "./components/SelectCategoryEditModal";

import * as controller from "../../../../../controllers/files/filesController";
import { INITIAL_STATE } from "../../../../../store/reducers/files-reducer";
import { logNewAuditAction } from "../../../../../controllers/audit/logNewAuditAction";

export const EditFileModal = ({ file }) => {
  const [toggleModalValue, toggle] = useToggle();
  const [loading, setLoading] = useState(false);
  const selectedFile = useSelector(
    (state) => state.files.selectedFile,
    shallowEqual
  );
  const allFiles = useSelector((state) => state.files.files, shallowEqual);

  const handleUpdateFile = async () => {
    const tableName = `${process.env.REACT_APP_STAGE}-files-archive`;
    let isError = false;

    setLoading(true);

    try {
      await dynamoPut(tableName, selectedFile.id, selectedFile);

      let newFilesStr = JSON.stringify(allFiles);
      let newFiles = JSON.parse(newFilesStr);

      const index = allFiles.findIndex((f) => f.id === selectedFile.id);
      newFiles[index] = selectedFile;

      setUploadFilesState({ field: "files", value: newFiles });
      message.success("Arquivo atualizado com sucesso!");

      const username = localStorage.getItem("@dsm/username");
      const title = "Edição de Arquivo";
      const description = `${username} realizou a edição do seguinte arquivo: ${selectedFile.name}`;
      logNewAuditAction(username, title, description);
    } catch (error) {
      isError = true;
      console.log(error);
      message.error(
        "Ops um erro correu, não foi possivel atualizar os dados do arquivo!"
      );
    }

    setLoading(false);

    if (!isError) toggle();
  };

  const handleDeleteFile = async () => {
    setLoading(true);
    let isError = false;

    try {
      await controller.deleteFile(selectedFile);
      message.success("Arquivo deletado com sucesso");
    } catch (error) {
      isError = true;
      console.log(error);
      message.error(
        "Ops... um erro ocorreu, não foi possivel remover o arquivo"
      );
    }

    setLoading(false);

    if (!isError) {
      setUploadFilesState({
        field: "selectedFile",
        value: INITIAL_STATE.selectedFile,
      });
      toggle();
    }

    controller.getFiles(selectedFile);
  };

  const isDisabled = useMemo(() => {
    const { name, tags, category } = selectedFile;

    if (name.length === 0 || tags.length === 0 || category.length === 0)
      return true;

    return false;
  }, [selectedFile]);

  return (
    <>
      <Button
        type="text"
        icon={<EditOutlined />}
        onClick={() => {
          toggle();
          setUploadFilesState({
            field: "selectedFile",
            value: file,
          });
        }}
      />

      <Modal
        title={file.name || ""}
        open={toggleModalValue}
        width={"40%"}
        closable={false}
        footer={
          <Row justify={"space-between"}>
            <Col span={12}>
              <Row justify={"start"}>
                <Button
                  type="danger"
                  onClick={handleDeleteFile}
                  disabled={loading}
                  loading={loading}
                >
                  Excluir <DeleteOutlined />
                </Button>
              </Row>
            </Col>
            <Col span={12}>
              <Row justify={"end"}>
                <Button
                  type="text-color"
                  onClick={() => {
                    setUploadFilesState({
                      field: "selectedFile",
                      value: INITIAL_STATE.selectedFile,
                    });
                    toggle();
                  }}
                >
                  Cancelar
                </Button>
                <Button
                  type="primary"
                  onClick={handleUpdateFile}
                  disabled={isDisabled || loading}
                  loading={loading}
                >
                  Alterar
                </Button>
              </Row>
            </Col>
          </Row>
        }
      >
        <Col span={24}>
          <Row>Cliente:</Row>
          <Row>
            <Input disabled={true} value={file.customerName} />
          </Row>

          <Row style={{ marginTop: "16px" }}>
            <InputFileNameEditModal />
          </Row>

          <Row style={{ marginTop: "16px" }}>
            <SelectCategoryEditModal />
          </Row>

          <Row style={{ marginTop: "16px" }}>Tags:</Row>
          <Row>
            <EditFileTags />
          </Row>
        </Col>
      </Modal>
    </>
  );
};
