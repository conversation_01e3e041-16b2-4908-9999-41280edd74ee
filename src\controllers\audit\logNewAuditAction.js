import { dynamoGenericPost } from "../../service/apiDynamoGeneric";

export async function logNewAuditAction(username, title, description) {
  if (!username || username === "") {
    const returnMessage = {
      statusCode: 501,
      message: `<PERSON>rror adding audit action log. Using wrong parameter when calling the function. Wrong parameter considered: 'username'`,
    };
    return returnMessage;
  }

  if (!title || title === "") {
    const returnMessage = {
      statusCode: 502,
      message: `<PERSON>rror adding audit action log. Using wrong parameter when calling the function. Wrong parameter considered: 'title'`,
    };
    return returnMessage;
  }

  if (!description || description === "") {
    const returnMessage = {
      statusCode: 503,
      message: `Error adding audit action log. Using wrong parameter when calling the function. Wrong parameter considered: 'description'`,
    };
    return returnMessage;
  }

  try {
    await dynamoGenericPost(`${process.env.REACT_APP_STAGE}-audits`, {
      username,
      name: title,
      description,
      created_at: new Date(),
      updated_at: new Date(),
    });
    const returnMessage = {
      statusCode: 200,
      message: `Success adding audit action log: ${title}`,
    };
    return returnMessage;
  } catch (error) {
    const returnMessage = {
      statusCode: 500,
      message: `Error adding audit action log. Error posting audit item on dynamodb table. Error message: ${error}`,
    };
    return returnMessage;
  }
}
