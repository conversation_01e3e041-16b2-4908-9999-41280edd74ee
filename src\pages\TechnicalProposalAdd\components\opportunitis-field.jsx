import React, { useState, useMemo } from "react";
import { Row, Col, Typography, Select } from "antd";
import { useSelector, shallowEqual } from "react-redux";
import { AddOpportunity } from "../../../components/Modals/TechnicalProposals/AddOportunity";
import Cookies from "js-cookie";

import { setTechnicalProposalState } from "../../../store/actions/technical-proposal-action";
import { AddNewOportunity } from "../../../components/Modals/TechnicalProposals/AddNewOportunity";

const { Title } = Typography;
const { Option } = Select;

export const OpportunitiesField = () => {
  const allOpportunities = useSelector(
    (state) => state.technicalProposal.opportunities,
    shallowEqual
  );
  const selectedOpportunity = useSelector(
    (state) => state.technicalProposal.selectedOpportunity,
    shallowEqual
  );

  return (
    <Col sm={24} md={12} lg={6}>
      <Row>
        <Title level={5} style={{ fontWeight: "400" }}>
          Oportunidade
        </Title>
      </Row>
      <Row>
        <Col span={20}>
          <Select
            id="selectedOpportunity"
            placeholder={"Número da oportunidade"}
            value={selectedOpportunity}
            showSearch
            optionFilterProp="children"
            filterOption={(input, option) =>
              option?.children.includes(input?.toLowerCase())
            }
            filterSort={(optionA, optionB) =>
              optionA?.children - optionB?.children
            }
            onSelect={(e) => {
              setTechnicalProposalState({
                field: "selectedOpportunity",
                value: e,
              });
            }}
            style={{ width: "100%" }}
          >
            {allOpportunities !== ""
              ? allOpportunities.map((value, index) => (
                  <Option key={index} value={value}>
                    {value}
                  </Option>
                ))
              : null}
          </Select>
        </Col>
        <Col span={4}>
          <AddNewOportunity />
        </Col>
      </Row>
    </Col>
  );
};
