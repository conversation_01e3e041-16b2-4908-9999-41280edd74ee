import moment from "moment";
import { dsmProvider } from "../../../../provider/dsm-provider";
import { moneyMask } from "../../../../utils/money-maks";

export const getPayerInvoices = async (customer, range) => {
  const provider = dsmProvider();
  const payerAccountList = customer.accounts.filter((account) => account.payer);

  const newInvoices = await Promise.all(
    payerAccountList.map(async (payerAccount) => {
      try {
        const { data } = await provider.get(
          `/read-invoices?payerAccount=${payerAccount.account_id}&startDate=${range.startDate}&endDate=${range.endDate}`
        );
        return data.data.Items;
      } catch (error) {
        console.log(error);
        return null;
      }
    })
  );
  const flattenedInvoices = newInvoices.flat();
  const formattedInvoices = formatInvoiceList(flattenedInvoices);
  return formattedInvoices;
};

export const formatInvoiceList = (invoices) => {
  const groupInvoices = {};

  invoices?.forEach((i) => {
    const formattedDate = moment(i?.full_date, "YYYY-MM").format("MMM/YYYY");

    if (!groupInvoices[formattedDate]) {
      groupInvoices[formattedDate] = {
        name: formattedDate,
        cost: 0,
      };
    }

    i?.accounts?.forEach((account) => {
      const accountCost = parseFloat(account.cost);
      if (account.line_item_type?.toLowerCase() !== "tax") {
        if (accountCost > 0) {
          groupInvoices[formattedDate].cost += accountCost;
        } else {
          groupInvoices[formattedDate].cost -= Math.abs(accountCost);
        }
      }
    });
  });

  const formattedInvoices = Object.values(groupInvoices).map((item) => ({
    name: item.name,
    $: item.cost?.toFixed(2),
    formattedValue: `$${moneyMask(item.cost.toFixed(2))}`,
  }));

  formattedInvoices.sort((a, b) => {
    const dateA = moment(a.name, "MMM/YYYY");
    const dateB = moment(b.name, "MMM/YYYY");
    return dateA.isBefore(dateB) ? -1 : 1;
  });

  return formattedInvoices;
};

export const calculateTotal = (invoices) => {
  let total = 0;
  invoices.forEach((invoice) => {
    total += parseFloat(invoice.$);
  });
  total = moneyMask(`${total.toFixed(2)}`.replace(/\./g, ""));

  return `$${total}`;
};

export const calculatePercentage = (invoices, commitmentValue) => {
  const total = invoices.reduce((acc, cur) => acc + parseFloat(cur.$), 0);
  const percentage =
    (total / commitmentValue.replace(/\./g, "").replace(/,/g, ".")) * 100;
  return percentage.toFixed(2);
};

export const calculateContractPercentage = async (contract, allInvoices) => {
  const totalCommitmentValue = contract.edp_commitment.reduce(
    (sum, item) =>
      sum +
      parseFloat(item.commitmentValue.replace(/\./g, "").replace(/,/g, ".")),
    0
  );

  const total = allInvoices.reduce((acc, cur) => acc + parseFloat(cur.$), 0);
  const percentage = (total / totalCommitmentValue) * 100;
  return percentage.toFixed(2);
};
