import { useContext, useRef, useState } from "react"
import { Input } from 'antd'
import { EmailAutomationContext } from "../../../contexts/email-automation"
import { EmailAutomationReducerType } from "../../../contexts/reducers/email-automation-reducer"

export const CollumnItem = ({ value, indexTable, indexRow, indexCollumn }) => {
    const { emailAutomationState, setEmailAutomationState } = useContext(EmailAutomationContext)
    const { templateList } = emailAutomationState
    const collumWidth =  100 / templateList[indexTable].value[indexRow].collumns.length

    const inputRef = useRef(null)
    const [isEdit, setIsEdit] = useState(false)

    function onBlurInput() {
        if (inputRef.current) {
            const inputValue = inputRef.current.input.value

            let hasChanged = false
            if (inputValue !== value)
                hasChanged = true

            if (hasChanged)
                updateCollumnValue(inputValue)

        }
        setIsEdit(false)
    }

    function updateCollumnValue(text) {
        const newTemplateListStr = JSON.stringify(templateList)
        const newTemplateList = JSON.parse(newTemplateListStr)

        const table = newTemplateList[indexTable]
        const row = table.value[indexRow]
        const collumn = row.collumns[indexCollumn]

        collumn.value = text

        row.collumns[indexCollumn] = collumn
        table.value[indexRow] = row
        newTemplateList[indexTable] = table

        setEmailAutomationState({
            type: EmailAutomationReducerType.SET_TEMPLATE_LIST,
            value: newTemplateList
        })
    }

    function editCollumn() {
        setIsEdit(true)
        setTimeout(() => {

            if (inputRef.current)
                inputRef.current.focus()
        }, 300)
    }

    return (
        <td
            style={{
                color: '#4a423c',
                fontFamily: 'arial, helvetica, sans-serif',
                border: '1px solid #CCC',
                borderRadius: '3px',
                textAlign: 'center',
                height: '40px',
                width: `${collumWidth}%`,
            }}
            onClick={editCollumn}
        >
            {
                !isEdit ?
                    <>
                        <p style={{ marginTop: 8, paddingLeft: 6, paddingRight: 6  }}>{value}</p>
                    </>
                    :
                    <Input
                        onBlur={onBlurInput}
                        ref={inputRef}
                        defaultValue={value}
                        onKeyDown={(event) => event.key === 'Enter' && onBlurInput()}
                        style={{ height: '100%' }}
                    />
            }
        </td>
    )
}