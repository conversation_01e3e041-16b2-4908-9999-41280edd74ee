import { React, useEffect, useState } from "react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>Axis,
  <PERSON><PERSON><PERSON>s,
  CartesianGrid,
  Tooltip,
  ReferenceLine,
} from "recharts";
import {
  BgColorsOutlined,
  FilterFilled,
  StarFilled,
  StarOutlined,
  LeftCircleOutlined,
  RightCircleOutlined,
} from "@ant-design/icons";
import { Card, Col, Popover, Row, Typography, Modal } from "antd";
import ColorPicker from "../../ColorPicker";
import HoursLimitGraphFilter from "../../Modals/Filters/HoursLimitGraphFilter";
import moment from "moment";
import "./HoursLimitGraph.css";
import { otrsGet, otrsPost } from "../../../service/apiOtrs";
import { MainBarChart } from "../../Graphs/BarGraph";

function HoursLimitGraph(props) {
  const { Title, Text } = Typography;
  const [showModalFunction, setShowModalFunction] = useState(false);
  const [isFavorite, setIsFavorite] = useState(false);
  const [colors, setColors] = useState();
  const [data, setData] = useState([]);
  const [barChartData, setBarChartData] = useState(false | []);
  const [modalVisible, setModalVisible] = useState(false);

  const GRAPH_NAME = "HoursLimitGraph";
  const LIMIT = 12;
  const notBillableContracts = [
    "Darede Apoio Técnico",
    "Darede Labs",
    "Darede - Atendimento 8x5",
    "Darede - Atendimento 24x7",
    "Darede SI",
  ];

  function handleVisibleChange() {
    setModalVisible(!modalVisible);
  }

  function checkIsVisible() {
    let localStorageData = JSON.parse(localStorage.getItem("favorites"));
    if (localStorageData) {
      var index = localStorageData.findIndex(
        (value) => value.component === GRAPH_NAME
      );

      return index !== -1;
    } else {
      return false;
    }
  }

  function changeColors(colors) {
    let storage_color = JSON.parse(localStorage.getItem("colors"));
    if (!storage_color) {
      storage_color = {};
    }
    storage_color[GRAPH_NAME] = colors;
    localStorage.setItem("colors", JSON.stringify(storage_color));
    setColors(colors);
  }

  async function updateFavorite() {
    await props?.setVisionFavorite(GRAPH_NAME);
    setIsFavorite(checkIsVisible());
  }

  function getGraphColor() {
    let storage_color = JSON.parse(localStorage.getItem("colors"));
    if (storage_color) {
      setColors(storage_color[GRAPH_NAME]);
    }
  }

  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length > 0) {
      return (
        <div
          style={{
            backgroundColor: "white",
            padding: "10px",
            borderRadius: "10px",
            boxShadow: "2px 2px 10px black",
          }}
          className="tooltip"
        >
          <Row>
            <Text>
              {`Não billada: ${
                Math.floor(payload[0].payload?.notBillable.value) +
                "h" +
                Math.floor(
                  (payload[0].payload?.notBillable.value -
                    Math.floor(payload[0].payload?.notBillable.value)) *
                    60
                ) +
                "m"
              }`}
            </Text>
          </Row>
          <Row>
            <Text>
              {`Billada: 
            ${
              Math.floor(payload[0].payload?.billable.value) +
              "h" +
              Math.floor(
                (payload[0].payload?.billable.value -
                  Math.floor(payload[0].payload?.billable.value)) *
                  60
              ) +
              "m"
            }`}
            </Text>
          </Row>
          <Row>
            <Text>{`Total: ${
              Math.floor(payload[0].payload?.notBillable.value) +
              Math.floor(payload[0].payload?.billable.value) +
              "h" +
              Math.floor(
                (payload[0].payload?.notBillable.value +
                  payload[0].payload?.billable.value -
                  Math.floor(
                    payload[0].payload?.notBillable.value +
                      payload[0].payload?.billable.value
                  )) *
                  60
              ) +
              "m"
            }`}</Text>
          </Row>
        </div>
      );
    }

    return null;
  };

  async function getWeeklyHours() {
    const today = moment();
    const startDay = moment().subtract(6, "day");

    let { data } = await otrsGet(
      `read/ticket/person/${today.month() + 1}/${today.year()}`
    );

    if (startDay.month() !== today.month()) {
      const lastMonth = await otrsGet(
        `read/ticket/person/${startDay.month() + 1}/${startDay.year()}`
      );
      data = data.concat(lastMonth.data);
    }

    const newData = [];
    for (let item of data) {
      if (moment(item.update_time) >= startDay) {
        const isNotBillable = await checkBillableTicket(item);
        newData.push({ ...item, isBillable: !isNotBillable });
      }
    }

    setData(newData);
  }

  async function checkBillableTicket(ticket) {
    const isNotBillable = notBillableContracts.some(
      (item) => ticket.contract_name === item
    );

    return isNotBillable;
  }

  function getFilteredChartData(filter) {
    let filteredData = [];
    if (filter.professional === undefined && filter.billable === undefined) {
      setBarChartData(0);
      return;
    }
    if (filter.professional) {
      filteredData = data.filter((item) => item.user === filter.professional);

      if (filter.billable && filter.billable !== "all") {
        filteredData = filteredData.filter(
          (item) => item.isBillable === (filter.billable === "billable")
        );
      }
    }
    formatChartPayload(filteredData);
  }

  function formatChartPayload(data) {
    const payload = [];
    for (let i = 6; i >= 0; i--) {
      payload.push({
        table: moment().subtract(i, "days").format("DD/MM"),
        billable: { value: 0, formattedValue: 0 },
        notBillable: { value: 0, formattedValue: 0 },
        total: 0,
      });
    }

    Object.entries(data).forEach((item) => {
      const value = item[1];

      const logDate = moment(value.update_time).format("DD/MM");
      const payloadItem = payload.find((item) => item.table === logDate);
      const index = payload.indexOf(payloadItem);
      console.log("value", value);
      if (value.isBillable) {
        payload[index].billable.value += parseFloat(value.time_consumed);
      } else {
        payload[index].notBillable.value += parseFloat(value.time_consumed);
      }
    });

    for (let item of payload) {
      const index = payload.indexOf(item);
      payload[index].billable.value = item.billable.value / 60;
      payload[index].notBillable.value = item.notBillable.value / 60;
    }

    for (let item of payload) {
      const index = payload.indexOf(item);
      let aux = 0;

      if (item.billable.value > LIMIT) {
        aux = item.billable.value - LIMIT;
        payload[index].billable.formattedValue = LIMIT;
        payload[index].total += aux + payload[index].notBillable.value;
      } else if (item.billable.value + item.notBillable.value > LIMIT) {
        aux = item.notBillable.value + item.billable.value - LIMIT;
        payload[index].notBillable.formattedValue = LIMIT - item.billable.value;
        payload[index].total += aux;
      } else {
        payload[index].notBillable.formattedValue = item.notBillable.value;
        payload[index].billable.formattedValue = item.billable.value;
        payload[index].total += item.notBillable.value + item.billable.value;
      }
    }

    setBarChartData(payload);
  }

  const centerFlexItem = {
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
  };

  const billableHours = ["Billada", "Não Billada", "Acima do Limite"];
  useEffect(() => {
    setIsFavorite(checkIsVisible());
    getGraphColor();
    getWeeklyHours();
  }, [props.favorites]);

  return (
    <Card
      bordered="false"
      style={{
        borderRadius: "20px",
        height: "100%",
        boxShadow: "0 0 10px rgba(0,0,0,0.1)",
        minWidth: "476px",
        minHeight: "414px",
      }}
    >
      <Row justify="space-between" style={{ marginBottom: "20px" }}>
        <Col span={18}>
          <Title level={4} style={{ fontWeight: 400, marginBottom: "0px" }}>
            Limite diário de horas
          </Title>
        </Col>
        <Col span={2} style={centerFlexItem}>
          <Popover
            open={modalVisible}
            onOpenChange={handleVisibleChange}
            placement="left"
            content={() => {
              return (
                <HoursLimitGraphFilter
                  professionals={props.users}
                  handleVisibleChange={handleVisibleChange}
                  filter={getFilteredChartData}
                />
              );
            }}
            trigger="click"
          >
            <div
              style={{
                display: "flex",
                textAlign: "center",
                cursor: "pointer",
                gap: "5px",
              }}
              className="filter"
            >
              <FilterFilled className="filter-icon" />
            </div>
          </Popover>
        </Col>
        <Col span={2} style={centerFlexItem}>
          {!isFavorite ? (
            <StarOutlined onClick={updateFavorite} className="star-icon" />
          ) : (
            <StarFilled onClick={updateFavorite} className="star-icon" />
          )}
        </Col>
        <Col span={2} style={centerFlexItem}>
          <Popover
            placement="left"
            title={"Personalização de cor"}
            content={() => {
              return (
                <ColorPicker
                  changeColors={changeColors}
                  options={[1, 2]}
                  state={colors}
                />
              );
            }}
          >
            <BgColorsOutlined className="star-icon" />
          </Popover>
        </Col>
      </Row>
      {barChartData ? (
        <>
          <div className="graphMargin">
            <MainBarChart
              tooltip={<CustomTooltip />}
              margin={{
                top: 30,
                right: 50,
                left: 20,
                bottom: 0,
              }}
              axis={
                <>
                  <CartesianGrid vertical={false} strokeDasharray="3 3" />
                  <XAxis fontSize={16} dataKey="table" />
                  <YAxis fontSize={16} />
                </>
              }
              bars={
                <>
                  <Bar
                    dataKey="billable.formattedValue"
                    name="Bilada"
                    stackId="a"
                    fill={colors ? colors[1].color : `#43914F`}
                  />
                  <Bar
                    dataKey="notBillable.formattedValue"
                    name="Não bilada"
                    stackId="b"
                    fill={colors ? colors[2].color : `#435E91`}
                  />
                </>
              }
              data={barChartData}
              colors={colors}
            />
          </div>
        </>
      ) : (
        <Row justify="center" align="middle" style={{ height: "250px" }}>
          <Text>Selecione um profissional*</Text>
        </Row>
      )}
    </Card>
  );
}

export default HoursLimitGraph;
