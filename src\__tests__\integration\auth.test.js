/**
 * Testes de integração para autenticação HttpOnly
 * Testa fluxos críticos de autenticação e autorização
 */

import { httpOnlyAuthService } from '../../services/httpOnlyAuthService';
import { authService } from '../../services/authService';
import axios from 'axios';

jest.mock('axios');
const mockedAxios = axios;

// Mock do logger
jest.mock('../../utils/logger', () => ({
  logger: {
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
    debug: jest.fn()
  }
}));

delete window.location;
window.location = { href: '' };

describe('Autenticação HttpOnly - Testes de Integração', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    document.cookie = '';
    localStorage.clear();
  });

  describe('httpOnlyAuthService', () => {
    test('deve autenticar usuário com código válido', async () => {
      // Mock da resposta do Cognito
      mockedAxios.post.mockResolvedValueOnce({
        data: {
          data: {
            id_token: 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************.test'
          }
        }
      });

      // Mock da resposta do backend para definir cookie
      mockedAxios.post.mockResolvedValueOnce({
        data: {
          success: true,
          user: { email: '<EMAIL>' },
          expiresIn: 28800
        }
      });

      const result = await httpOnlyAuthService.authenticate('test-code');

      expect(result).toHaveProperty('token');
      expect(result).toHaveProperty('user');
      expect(result.user.email).toBe('<EMAIL>');
    });

    test('deve falhar com código inválido', async () => {
      mockedAxios.post.mockRejectedValueOnce(new Error('Invalid code'));

      await expect(httpOnlyAuthService.authenticate('invalid-code'))
        .rejects.toThrow('Invalid code');
    });

    test('deve verificar autenticação via backend quando suportado', async () => {
      // Simular suporte HttpOnly
      httpOnlyAuthService.isHttpOnlySupported = true;

      mockedAxios.get.mockResolvedValueOnce({
        data: {
          authenticated: true,
          user: { email: '<EMAIL>' },
          tokenStatus: { needsRefresh: false }
        }
      });

      const isAuth = await httpOnlyAuthService.isAuthenticated();

      expect(isAuth).toBe(true);
      expect(mockedAxios.get).toHaveBeenCalledWith(
        expect.stringContaining('/auth/verify'),
        expect.objectContaining({ withCredentials: true })
      );
    });

    test('deve fazer fallback para cookie manual quando backend não suporta', async () => {
      // Simular não suporte HttpOnly
      httpOnlyAuthService.isHttpOnlySupported = false;
      
      // Simular cookie manual
      document.cookie = 'dsm_token=test-token; path=/';

      const isAuth = await httpOnlyAuthService.isAuthenticated();

      expect(isAuth).toBe(true);
    });

    test('deve renovar token automaticamente', async () => {
      mockedAxios.post.mockResolvedValueOnce({
        data: {
          success: true,
          token: 'new-token',
          expiresIn: 28800
        }
      });

      await httpOnlyAuthService.refreshToken();

      expect(mockedAxios.post).toHaveBeenCalledWith(
        expect.stringContaining('/auth/refresh'),
        {},
        expect.objectContaining({ withCredentials: true })
      );
    });

    test('deve fazer logout corretamente', async () => {
      // Simular que HttpOnly é suportado
      httpOnlyAuthService.isHttpOnlySupported = true;

      mockedAxios.post.mockResolvedValueOnce({
        data: { success: true }
      });

      await httpOnlyAuthService.logout();

      // Verificar se o logout foi chamado (pode ser via interceptor global)
      expect(mockedAxios.post).toHaveBeenCalled();
    });
  });

  describe('Interceptors Axios', () => {
    test('deve configurar withCredentials corretamente para APIs internas', () => {
      const config = {
        url: '/api/test',
        headers: {}
      };

      // Simular interceptor request
      const result = httpOnlyAuthService.setupAxiosDefaults();
      
      // Verificar se interceptors foram configurados
      expect(httpOnlyAuthService.interceptorsConfigured).toBe(true);
    });

    test('deve adicionar Bearer token quando HttpOnly não suportado', () => {
      httpOnlyAuthService.isHttpOnlySupported = false;
      document.cookie = 'dsm_token=test-token; path=/';

      const config = {
        url: '/api/test',
        headers: {}
      };

      // Simular comportamento do interceptor
      const token = httpOnlyAuthService.getTokenFromCookie();
      expect(token).toBe('test-token');
    });

    test('deve tratar erro 401 com auto-refresh', async () => {
      const originalRequest = {
        url: '/api/test',
        headers: {}
      };

      const error = {
        response: { status: 401 },
        config: originalRequest
      };

      // Mock do refresh token
      mockedAxios.post.mockResolvedValueOnce({
        data: { success: true }
      });

      // Mock da repetição da requisição
      mockedAxios.request.mockResolvedValueOnce({
        data: { success: true }
      });

      // Simular comportamento do interceptor de resposta
      // (Este teste verifica a lógica, não executa o interceptor real)
      expect(error.response.status).toBe(401);
      expect(originalRequest).toBeDefined();
    });
  });

  describe('Compatibilidade entre serviços', () => {
    test('authService não deve configurar interceptors quando httpOnlyAuthService está ativo', () => {
      // Verificar se authService não configura interceptors
      const authServiceInstance = new (require('../../services/authService').default.constructor)();
      
      // authService não deve ter interceptors configurados
      expect(authServiceInstance.setupAxiosInterceptors).toBeUndefined();
    });

    test('deve usar httpOnlyAuthService como serviço principal', () => {
      expect(httpOnlyAuthService).toBeDefined();
      expect(httpOnlyAuthService.isAuthenticated).toBeDefined();
      expect(httpOnlyAuthService.authenticate).toBeDefined();
      expect(httpOnlyAuthService.logout).toBeDefined();
    });
  });

  describe('Gerenciamento de Estado', () => {
    test('deve limpar dados de autenticação no logout', async () => {
      // Simular dados no localStorage
      localStorage.setItem('@dsm/mail', '<EMAIL>');
      localStorage.setItem('@dsm/name', 'Teste');
      
      // Simular cookie
      document.cookie = 'dsm_token=test-token; path=/';

      mockedAxios.post.mockResolvedValueOnce({
        data: { success: true }
      });

      await httpOnlyAuthService.logout();

      // Verificar limpeza
      expect(localStorage.getItem('@dsm/mail')).toBeNull();
      expect(localStorage.getItem('@dsm/name')).toBeNull();
    });

    test('deve migrar tokens do localStorage para cookies', () => {
      // Simular token no localStorage
      localStorage.setItem('jwt', 'legacy-token');

      // Verificar se migração é detectada
      const hasLegacyTokens = localStorage.getItem('jwt');
      expect(hasLegacyTokens).toBe('legacy-token');
    });
  });
});

describe('Fluxos Críticos de Autenticação', () => {
  test('Fluxo completo: Login → Verificação → Logout', async () => {
    // 1. Login
    mockedAxios.post
      .mockResolvedValueOnce({
        data: {
          data: {
            id_token: 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************.test'
          }
        }
      })
      .mockResolvedValueOnce({
        data: {
          success: true,
          user: { email: '<EMAIL>' }
        }
      });

    const loginResult = await httpOnlyAuthService.authenticate('test-code');
    expect(loginResult.user.email).toBe('<EMAIL>');

    // 2. Verificação
    httpOnlyAuthService.isHttpOnlySupported = true;
    mockedAxios.get.mockResolvedValueOnce({
      data: {
        authenticated: true,
        user: { email: '<EMAIL>' }
      }
    });

    const isAuth = await httpOnlyAuthService.isAuthenticated();
    expect(isAuth).toBe(true);

    // 3. Logout
    mockedAxios.post.mockResolvedValueOnce({
      data: { success: true }
    });

    await httpOnlyAuthService.logout();
    expect(mockedAxios.post).toHaveBeenCalledWith(
      expect.stringContaining('/auth/logout'),
      {},
      expect.objectContaining({ withCredentials: true })
    );
  });
});
