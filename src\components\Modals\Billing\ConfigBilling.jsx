import {
  Button,
  Form,
  message,
  Modal,
  Popconfirm,
  Select,
  Tag,
  Typography,
} from "antd";
import { useEffect, useState } from "react";
import { dynamoGetById } from "../../../service/apiDsmDynamo";
import {
  configBilling,
  resetBilling,
} from "../../../pages/Billing/controllers/configControllers";
import { LoadingOutlined } from "@ant-design/icons";
import { logNewAuditAction } from "../../../controllers/audit/logNewAuditAction";

export const ConfigBilling = (props) => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [payerAccountOptions, setPayerAccountOptions] = useState();
  const [loading, setLoading] = useState(false);
  const { contract } = props;
  const [billingConfigured, setBillingConfigured] = useState(
    contract?.billingConfigured
  );
  const [showMessage, setShowMessage] = useState(false);
  const [optionsLoading, setOptionsLoading] = useState(false);
  const { Text } = Typography;
  const [form] = Form.useForm();
  const { Option } = Select;

  useEffect(() => {
    setBillingConfigured(contract?.billingConfigured);
  }, [contract]);

  const getPayerAccountOptions = async () => {
    let payerOptions = null;
    setOptionsLoading(true);

    const customer = await dynamoGetById(
      `${process.env.REACT_APP_STAGE}-customers`,
      contract.customer_id
    );
    if (Array.isArray(customer.accounts) && customer.accounts.length > 0) {
      const accounts = customer.accounts.filter((a) => a.payer);
      payerOptions = accounts;
      !payerOptions.length && setShowMessage(true);
    } else {
      setShowMessage(true);
    }
    setOptionsLoading(false);
    return payerOptions;
  };

  const handleSubmit = async ({ payer_account }) => {
    setLoading(true);
    const response = await configBilling(payer_account, contract.id);

    if (response === 200) {
      setTimeout(() => {
        setLoading(false);
        message.success("Billing configurado com sucesso!");
        setBillingConfigured(true);
      }, 10000);
      const username = localStorage.getItem("@dsm/username");
      const title = "Configuração de Billing";
      const description = `${username} configurou o billing para o contrato: ${
        contract.name
      } de DSM ID: ${contract.dsm_id || ""}`;

      await logNewAuditAction(username, title, description);
    } else {
      setLoading(false);
    }

    handleCancel();
  };

  const showModal = async () => {
    setIsModalVisible(true);
    setPayerAccountOptions(await getPayerAccountOptions());
  };

  const handleOk = () => {
    form.submit();
  };

  const handleCancel = () => {
    form.resetFields();
    setIsModalVisible(false);
  };

  const handleConfirm = async () => {
    setLoading(true);
    const response = await resetBilling(contract.payer_account, contract.id);

    if (response === 200) {
      setTimeout(() => {
        setLoading(false);
        message.success("Billing desconfigurado com sucesso!");
        setBillingConfigured(false);
      }, 10000);
      const username = localStorage.getItem("@dsm/username");
      const title = "Desconfiguração de Billing";
      const description = `${username} desconfigurou o billing para o contrato: ${
        contract.name
      } de DSM ID: ${contract.dsm_id || ""}`;

      await logNewAuditAction(username, title, description);
    } else {
      setLoading(false);
    }
  };

  return (
    <>
      {billingConfigured ? (
        <Popconfirm
          placement="leftBottom"
          title={`Tem certeza que deseja desconfigurar a payer accout ${contract.payer_account}?`}
          style={{
            backgroundColor: "transparent",
            border: "none",
            cursor: "pointer",
            color: "black",
            zIndex: 1,
          }}
          onConfirm={() => handleConfirm()}
          cancelText="Cancelar"
        >
          <Button type="primary" danger>
            {loading && <LoadingOutlined width="80" />}Desconfigurar Billing
          </Button>
        </Popconfirm>
      ) : (
        <>
          <Button type="primary" onClick={showModal}>
            {loading && <LoadingOutlined width="80" />}
            Configurar Billing
          </Button>
          <Modal
            title="Configurar Billing"
            open={isModalVisible}
            onOk={() => {
              handleOk();
            }}
            confirmLoading={loading}
            okText="Configurar Billing"
            okButtonProps={{ disabled: showMessage || optionsLoading }}
            onCancel={handleCancel}
            cancelText="Cancelar"
            width={"550px"}
          >
            <Form
              layout="vertical"
              onFinish={handleSubmit}
              requiredMark={false}
              form={form}
            >
              <Form.Item
                name="payer_account"
                label="Payer Account"
                rules={[{ required: true, message: "Preencha este campo." }]}
              >
                <Select
                  placeholder="Selecione uma payer account"
                  showSearch
                  loading={optionsLoading}
                >
                  {payerAccountOptions?.map((e, index) => {
                    return (
                      <Option value={e.account_id} key={index}>
                        {e.account_id} - {e.profile}
                      </Option>
                    );
                  })}
                </Select>
              </Form.Item>
              {showMessage && (
                <Text style={{ color: "red" }}>
                  O cliente do contrato selecionado não possui uma payer account
                  vinculada.
                </Text>
              )}
            </Form>
          </Modal>
        </>
      )}
    </>
  );
};
