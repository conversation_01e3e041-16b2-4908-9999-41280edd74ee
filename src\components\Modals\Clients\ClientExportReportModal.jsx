import React, { useState } from "react";
import {
  Mo<PERSON>,
  But<PERSON>,
  Checkbox,
  Row,
  Col,
  message,
  Select,
  Tag,
  Typography,
  Space,
} from "antd";
import { DownloadOutlined } from "@ant-design/icons";
import {
  addFieldInSelectedFields,
  generateExcel,
  downloadExcel,
  removeFieldInSelectedFields,
} from "../../../controllers/clients/clientsGenerateExcelController";

export const ClientExportReportModal = ({ data }) => {
  const [showModal, setShowModal] = useState(false);
  const [loading, setLoading] = useState(false);
  const [selectedFields, setSelectedFields] = useState([
    {
      label: "Clientes",
      subItems: [
        "DSM ID",
        "Data de Criação",
        "CRM do cliente",
        "ITSM do cliente",
        "Nome do cliente",
        "Nome fantasia",
        "CNPJ",
      ],
    },
  ]);
  const { Option } = Select;

  const fields = [
    {
      label: "Clientes",
      subItems: [
        "DSM ID",
        "Data de Criação",
        "CRM do cliente",
        "ITSM do cliente",
        "Nome do cliente",
        "Nome fantasia",
        "CNPJ",
      ],
    },
    {
      label: "Informações",
      subItems: ["Sócios", "Endereço"],
    },
    {
      label: "Contatos",
      subItems: [
        "DSM ID do contato",
        "CRM do contato",
        "ITSM do contato",
        "Primeiro nome",
        "Último nome",
        "Telefone",
        "Email",
      ],
    },
    {
      label: "Contratos",
      subItems: [
        "DSM ID do contrato",
        "CRM do contrato",
        "ITSM do contrato",
        "Nome do contrato",
        "Data início",
        "Data fim",
        "Valor do contrato",
        "Valor da Calculadora AWS",
        "Escopo",
        "Tipo de Horas",
        "ID do Contrato",
        "Total de Horas",
      ],
    },
    {
      label: "Contas",
      subItems: ["Perfil", "Conta"],
    },
  ];

  function handleSelectField(field, isChecked) {
    let newSelectedFields = [];

    if (isChecked) {
      newSelectedFields = addFieldInSelectedFields(
        field,
        fields,
        selectedFields
      );
    }
    if (!isChecked) {
      newSelectedFields = removeFieldInSelectedFields(field, selectedFields);
    }
    setSelectedFields(newSelectedFields);
  }

  async function handleGenerateExcel() {
    if (selectedFields.length === 0) {
      return message.error("Selecione pelo menos um campo para o relatório.");
    } else {
      setLoading(true);
      try {
        const blob = await generateExcel(data, selectedFields);
        downloadExcel(blob, "Relatório de Clientes.xlsx");
        setShowModal(false);
        setLoading(false);
        message.success("Arquivo gerado com sucesso!");
      } catch (error) {
        console.log(error);
        setLoading(false);
        message.error("Erro ao gerar arquivo.");
      }
    }
  }

  function handlerCheckAll(event, field) {
    const selectedItem = `Todos-${field.label}`;
    event.value = field.label;
    handleSelectField(selectedItem, event.target.checked);
  }

  return (
    <>
      <Button
        type="primary"
        style={{ cursor: "pointer" }}
        onClick={() => setShowModal(true)}
      >
        <DownloadOutlined />
        Exportar Relatório
      </Button>
      <Modal
        width={1000}
        title="Selecione os campos que você deseja exportar no relatório:"
        open={showModal}
        onCancel={() => setShowModal(false)}
        footer={[
          <Button onClick={() => setShowModal(false)}>Fechar</Button>,
          <Button
            type="primary"
            loading={loading}
            onClick={async () => await handleGenerateExcel()}
          >
            <DownloadOutlined />
            Exportar
          </Button>,
        ]}
      >
        <Row justify="space-between">
          {fields.map(
            (field, index) =>
              field.subItems && (
                <Col key={index}>
                  <Space
                    style={{
                      display: "flex",
                      flexDirection: "column",
                      alignItems: "flex-start",
                    }}
                  >
                    <Typography.Text>{field.label}</Typography.Text>
                    <Select
                      placeholder={"Selecione"}
                      value={"Selecione"}
                      style={{ width: "10rem" }}
                      mode="multiple"
                    >
                      <Option key={field.label}>
                        <Checkbox
                          checked={selectedFields.some((selectedField) =>
                            selectedField.subItems.includes(
                              `Todos-${field.label}`
                            )
                          )}
                          onChange={(event) => {
                            handlerCheckAll(event, field);
                          }}
                        >
                          Todos
                        </Checkbox>
                      </Option>
                      {field.subItems.map((subItem) => (
                        <>
                          <Option key={subItem}>
                            <Checkbox
                              value={subItem}
                              checked={selectedFields.some((selectedField) =>
                                selectedField.subItems.includes(subItem)
                              )}
                              onChange={(event) => {
                                handleSelectField(
                                  subItem,
                                  event.target.checked
                                );
                              }}
                            >
                              {subItem}
                            </Checkbox>
                          </Option>
                        </>
                      ))}
                    </Select>
                  </Space>
                </Col>
              )
          )}
        </Row>
        <Col style={{ marginTop: "20px" }}>
          <Typography.Text>Selecionados</Typography.Text>
          <Row>
            {selectedFields.length &&
              selectedFields.map((field, index) =>
                field.subItems.map(
                  (subItem) =>
                    !subItem.includes("Todos") && (
                      <Tag
                        key={subItem}
                        closable
                        onClose={(e) => {
                          e.preventDefault();
                          handleSelectField(subItem, false);
                        }}
                        style={{
                          marginTop: "6px",
                        }}
                      >
                        {subItem}
                      </Tag>
                    )
                )
              )}
          </Row>
        </Col>
      </Modal>
    </>
  );
};
