import React, {useEffect, useState, useRef} from 'react'
import {<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>} from 'recharts'
import {ClientByCategory} from '../../Table/ClientByCategory';
import {otrsGet, otrsPost} from "../../../service/apiOtrs";
import { Card, Typography } from 'antd';

const CustomerCategory = (props) => {
    const { Title, Text } = Typography;
    const [modalVisible, setModalVisible] = useState(true)
    const [data, setData] = useState([])
    const [allContracts, setAllContracts] = useState([])
    const [primeClients, setPrimeClients] = useState([])
    const [silverClients, setSilverClients] = useState([])
    const [goldClients, setGoldClients] = useState([])
    const [vipClients, setVipClients] = useState([])
    const [categoryName, setCategoryName] = useState()
    const [allClients, setAllClients] = useState([
        {
            key: '1',
            client: 'Intelbras',
            id: 'ITVQEVEQVEQVQEVQEV',
            category: 'VIP',
        },
        {
            key: '2',
            client: 'Logikee',
            id: 'LGQVQEVEQVQVEQ',
            category: 'Silver',
        },
        {
            key: '3',
            client: 'Kabum',
            id: 'KBQVQEVQEVQVQVEQV',
            category: 'Gold',
        },
        {
            key: '4',
            client: 'Service Up',
            id: 'SUP',
            category: 'Prime',
        },
        {
            key: '5',
            client: 'Darede',
            id: 'DRQEVQEVEQVEQVEQVEQV',
            category: 'Silver',
        },
        {
            key: '6',
            client: 'Americanas',
            id: 'AMQVQEVQEVQEVEQV',
            category: 'VIP',
        },
        {
            key: '7',
            client: 'Darede Outro',
            id: 'DOQCQCQWCQEVEQVEQV',
            category: 'VIP',
        },
        {
            key: '8',
            client: 'Amazom',
            id: 'AZQFWQFQEQFEQVQVQ',
            category: 'VIP',
        }
    ])

    let vip = []
    let silver = []
    let gold = []
    let prime = []
    let amountOfContracts = 0

    async function getAllContracts() {
        await otrsGet("read/contract/all/0").then((res) => {
            setAllContracts(res.data);
        }).catch((error) => {
            console.log(error);
        })
    }

    function sumClientsPerCategory() {
        allClients.map((client) => {
            if(client.category === 'VIP') {
                vip.push(client)
            } else if (client.category === 'Silver') {
                silver.push(client)
            } else if (client.category === 'Gold') {
                gold.push(client)
            } else {
                prime.push(client)
            }
        })

        setVipClients(vip)
        setSilverClients(silver)
        setGoldClients(gold)
        setPrimeClients(prime)
    }

    function putValueOfClientsInCategory() {
        setData(
            [
                {name: 'Prime', value: primeClients.length, fill: '#9747FF'},
                {name: 'Silver', value: silverClients.length, fill: '#43914F'},
                {name: 'Gold', value: goldClients.length, fill: '#D1F010'},
                {name: 'VIP', value: vipClients.length, fill: '#0053F4'},
            ]
        )
    }

    function getPieCategory(category) {
        setCategoryName(category.name)
    }

    function showModal() {
        setModalVisible(!modalVisible)
    }

    useEffect(() => {
        sumClientsPerCategory()
        getAllContracts()
    }, [])

    useEffect(() => {
        putValueOfClientsInCategory()
        getAmountOfContracts()
    }, [primeClients, allContracts])

    function getAmountOfContracts() {
        allClients.map((client) => {
            Object.entries(allContracts).forEach((contract) => {
                if (contract[1].customer_id === client.id) {
                    amountOfContracts++
                }
            })
            client.amountOfContracts = amountOfContracts
            amountOfContracts = 0
        })
    }

    return (
        <Card bordered={false} 
            style={{
                borderRadius: '20px', 
                height: '100%',
                boxShadow: "0 0 10px rgba(0,0,0,0.1)",
            }}
        >
            {modalVisible ? (
                <>
                    <Title level={3} style={{fontWeight: 400}}>Categorias de Clientes</Title>
                    <div className="card-container">
                        <PieChart
                            width={500}
                            height={250}
                        >
                            <Pie
                                onClick={(value) => {{
                                    showModal()
                                    getPieCategory(value)
                                }
                                }}
                                data={data}
                                startAngle={360}
                                endAngle={0}
                                cx={155}
                                cy={120}
                                outerRadius={120}
                                fill="#43914F"
                                dataKey="value"
                            >
                            </Pie>
                            <Legend
                                formatter={(value, entry, index) => (
                                    <span className="text-lg text-black">{`${value} (${entry.payload.value})`}</span>
                                )}
                                className="text-xs"
                                layout="vetical"
                                verticalAlign="middle"
                                align="right"
                            />
                        </PieChart>
                    </div>
                </>
            ) : (
                <ClientByCategory
                    allClients={allClients}
                    categoryName={categoryName}
                    allContracts={allContracts}
                    sumClientsPerCategory={sumClientsPerCategory}
                    showModal={showModal}
                />
            )}
        </Card>
    )
}

export {CustomerCategory}