import React, { useEffect, useState } from "react";
import {
  <PERSON><PERSON><PERSON>,
  Legend,
  Bar,
  Bar<PERSON>hart,
  CartesianGrid,
  XAxis,
  <PERSON>Axis,
  Cell,
} from "recharts";
import "./OpenTickets.css";
import { Modal, Popover, Spin, Typography, Row, Col, Card } from "antd";
import { FilterFilled, BgColorsOutlined } from "@ant-design/icons";
import OpenTicketsKanban from "../../Table/OpenTickets";
import OpenTicketsFilter from "../../Modals/Filters/OpenTicketsFilter";
import ColorPicker from "../../ColorPicker";
import { otrsPost } from "../../../service/apiOtrs";
import moment from "moment";
import { CockpitsHeader } from "../../CockpitsHeader";
import { chartPayload, defaultColors, data } from "./constants/data";
function OpenTickets(props) {
  const { isModal, allCustomers } = props;
  const { Title, Text } = Typography;
  const [modalVisible, setModalVisible] = useState(false);
  const [popoverVisible, setPopoverVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [colors, setColors] = useState();
  const [chartData, setChartData] = useState(chartPayload);
  const [showMessage, setShowMessage] = useState(false);
  const GRAPH_NAME = "OpenTickets";

  function handleCancel() {
    setModalVisible(false);
  }

  function showModal() {
    setModalVisible(!modalVisible);
  }

  function swapVisible() {
    let object = structuredClone(props.componentVisibles);
    object.kanban = !object.kanban;
    props.setComponentVisibles(object);
  }

  function hiddenPopover() {
    setPopoverVisible(!popoverVisible);
  }

  function checkIsVisible() {
    let localStorageData = JSON.parse(localStorage.getItem("favorites"));
    if (localStorageData) {
      var index = localStorageData.findIndex(
        (value) => value.component === GRAPH_NAME
      );

      return index !== -1;
    } else {
      return false;
    }
  }

  function getGraphColor() {
    let storage_color = JSON.parse(localStorage.getItem("colors"));
    if (storage_color) {
      setColors(storage_color[GRAPH_NAME]);
    }
  }

  function changeColors(colors) {
    let storage_color = JSON.parse(localStorage.getItem("colors"));
    if (!storage_color) {
      storage_color = {};
    }
    storage_color[GRAPH_NAME] = colors;
    localStorage.setItem("colors", JSON.stringify(storage_color));
    setColors(colors);
  }

  async function getFilteredChartData(filters) {
    const { owner, customer, date } = filters;
    setLoading(true);
    if (owner) {
      try {
        let { data } = await otrsPost("read/tickets/pdm", { params: owner });
        data = data?.filter((item) => item.owner_ticket === owner);
        if (customer) {
          data = data?.filter(
            (item) =>
              item?.client_name?.toLowerCase() === customer.toLowerCase()
          );
        }

        if (date) {
          const filterDate = moment(date).format("DD-MM-YYYY");
          data = data.filter((item) => {
            let createDate = moment(item.ticket_create_time).format(
              "DD-MM-YYYY"
            );
            return filterDate === createDate;
          });
        }
        if (data.length > 0) {
          const chartPayloadCopy = JSON.parse(JSON.stringify(chartPayload));
          for (let ticket of data) {
            for (let item of chartPayloadCopy) {
              if (ticket.ticket_state === "open")
                ticket.ticket_state = "Em andamento";
              if (ticket.ticket_state === item.status) {
                item.value++;
                item.tickets.push(ticket);
              }
            }
          }

          setChartData(chartPayloadCopy);
        } else {
          setShowMessage(true);
        }
      } catch (err) {
        console.log(err);
      }
    }
    setLoading(false);
  }

  return (
    <Card
      bordered="false"
      style={{
        borderRadius: "20px",
        height: "100%",
        boxShadow: "0 0 10px rgba(0,0,0,0.1)",
        minWidth: "465px",
        minHeight: "393px",
        alignItems: loading ? "center" : "",
        minWidth: "450px",
        minHeight: "393px",
        alignItems: loading ? "center" : "",
        display: loading ? "flex" : "",
        justifyContent: loading ? "center" : "",
      }}
    >
      <CockpitsHeader
        title={
          <Col span={18}>
            <Title level={4} style={{ fontWeight: 400 }}>
              Tickets abertos
            </Title>
          </Col>
        }
        hasFavoriteOption={false}
        filter={
          <Popover
            open={popoverVisible}
            onOpenChange={hiddenPopover}
            placement="left"
            content={() => {
              return (
                <OpenTicketsFilter
                  hiddenPopover={hiddenPopover}
                  filter={getFilteredChartData}
                  allCustomers={allCustomers}
                />
              );
            }}
            trigger="click"
          >
            <div
              style={{
                display: "flex",
                textAlign: "center",
                cursor: "pointer",
                gap: "5px",
              }}
              className="filter"
            >
              <FilterFilled className="filter-icon" />
            </div>
          </Popover>
        }
        colorPicker={
          <Popover
            placement="left"
            title={"Personalização de cor"}
            content={() => {
              return (
                <ColorPicker
                  changeColors={changeColors}
                  currentComponent={"OpenTicketsGraph"}
                  options={data?.datasets[0]?.data.map((val, index) => {
                    return index;
                  })}
                  initialColors={defaultColors}
                  state={colors}
                />
              );
            }}
          >
            <BgColorsOutlined className="star-icon" />
          </Popover>
        }
      />
      <div>
        {loading ? (
          <>
            <Row>
              <div
                style={{
                  minHeight: "255px",
                  width: "100%",
                  display: "flex",
                  justifyContent: "center",
                  flexDirection: "column",
                  alignItems: "center",
                }}
              >
                <Spin />
              </div>
            </Row>
            <Row gutter={[16]}>
              {chartData.map((item, index) => {
                return (
                  <Col span={12} key={index}>
                    <Card
                      style={{
                        borderColor: colors
                          ? colors[index]?.color
                          : chartData[index]?.fill,
                        marginBottom: "5px",
                        borderRadius: "8px",
                      }}
                    >
                      <div
                        style={{
                          backgroundColor: colors
                            ? colors[index]?.color
                            : chartData[index].fill,
                        }}
                        className="bar"
                      ></div>
                      <Title level={5} style={{ fontWeight: 400 }}>
                        {item.name}
                      </Title>
                      <Text>{item.value}</Text>
                    </Card>
                  </Col>
                );
              })}
            </Row>
          </>
        ) : (
          <Row justify="center">
            <Col style={{ overflow: "clip" }}>
              {chartData.find((item) => item?.tickets?.length > 0) && (
                <>
                  <div style={{ width: "100%", height: "100%" }}>
                    <BarChart
                      width={437}
                      height={255}
                      data={chartData}
                      margin={{
                        top: 5,
                        right: 30,
                        left: 0,
                        bottom: 5,
                      }}
                      onClick={() => (isModal ? showModal() : swapVisible())}
                      barSize={10}
                      barGap={5}
                    >
                      <CartesianGrid strokeDasharray="1 1" />
                      <XAxis hide={true} dataKey="name" orientation="top" />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Bar dataKey="value" legendType="none">
                        {chartData.map((entry, index) => (
                          <Cell
                            key={`cell-${index}`}
                            fill={
                              colors
                                ? colors[index]?.color
                                : chartData[index]?.fill
                            }
                          />
                        ))}
                      </Bar>
                    </BarChart>
                  </div>
                </>
              )}
            </Col>

            <Row gutter={[16]} style={{ marginTop: "20px" }}>
              {chartData.map((item, index) => {
                return (
                  <Col span={12} key={index}>
                    <Card
                      style={{
                        height: "80px",
                        borderColor: colors
                          ? colors[index]?.color
                          : chartData[index]?.fill,
                        marginBottom: "5px",
                        borderRadius: "8px",
                        position: "relative",
                        display: "flex",
                        flexDirection: "column",
                        justifyContent: "center",
                      }}
                    >
                      <div
                        style={{
                          backgroundColor: colors
                            ? colors[index]?.color
                            : chartData[index].fill,
                          position: "absolute",
                          left: "23px",
                          top: "26px",
                        }}
                        className="bar"
                      ></div>
                      <Title level={5} style={{ fontWeight: 400 }}>
                        {item.name}
                      </Title>
                      <Text>{item.value}</Text>
                    </Card>
                  </Col>
                );
              })}
              {showMessage && (
                <Col>
                  <Text> Nenhum ticket encontrado para o filtro aplicado*</Text>
                </Col>
              )}
            </Row>
          </Row>
        )}
      </div>

      <Modal
        style={{
          display: "flex",
          justifyContent: "center",
          width: "90vw",
          maxWidth: "1300px",
        }}
        cancelButtonProps={{ style: { display: "none" } }}
        open={modalVisible}
        footer={null}
        onCancel={handleCancel}
        closable={false}
      >
        <OpenTicketsKanban showModal={showModal} data={chartData} />
      </Modal>
    </Card>
  );
}

export default OpenTickets;
