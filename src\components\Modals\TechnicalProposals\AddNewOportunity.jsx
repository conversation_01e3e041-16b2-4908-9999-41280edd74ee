import { useState, useEffect } from "react";
import { shallowEqual, useSelector } from "react-redux";
import {
  PlusOutlined,
  DeleteOutlined,
  ContactsOutlined,
  CheckOutlined,
} from "@ant-design/icons";
import {
  <PERSON><PERSON>,
  Modal,
  Row,
  Col,
  Table,
  Typography,
  Input,
  Form,
  message,
  Popconfirm,
} from "antd";

import { store } from "../../../store/store";
import { setTechnicalProposalState } from "../../../store/actions/technical-proposal-action";

import { useToggle } from "../../../hooks/useToggle";

const { Title } = Typography;

export const AddNewOportunity = () => {
  const opportunities = useSelector(
    (state) => state.technicalProposal.opportunities,
    shallowEqual
  );

  const [currentOpportunity, setCurrentOpportunity] = useState([]);

  const [toggleValue, toggleModal] = useToggle();
  const [removeSelectedCustomer, setRemoveSelectedCustomer] = useState(false);

  const [oportunity, setOportunity] = useState("");

  useEffect(() => {
    setCurrentOpportunity(opportunities);
  }, [opportunities]);

  function handleAddOpportunity() {
    if (!oportunity) return message.error("Oportunidade inválida");

    setCurrentOpportunity((state) => [...state, oportunity]);
    setOportunity("");
  }

  function handleSave() {
    setTechnicalProposalState({
      field: "opportunities",
      value: currentOpportunity,
    });

    if (removeSelectedCustomer) {
      setTimeout(() => {
        setTechnicalProposalState({
          field: "selectedOpportunity",
          value: "",
        });
      }, 1000);

      setRemoveSelectedCustomer(false);
    }

    message.success("Informações salvas com sucesso");

    toggleModal();
  }

  function handleCancel() {
    setOportunity("");

    setCurrentOpportunity(opportunities);

    toggleModal();
    setRemoveSelectedCustomer(false);
  }

  function handleRemoveOpportunity(index) {
    const { selectedOpportunity } = store.getState().technicalProposal;

    const currentSelectOpportunity = opportunities[index];
    if (selectedOpportunity === currentSelectOpportunity) {
      setRemoveSelectedCustomer(true);
    }

    const newOpportunities = [...currentOpportunity];
    newOpportunities.splice(index, 1);

    setCurrentOpportunity(newOpportunities);
  }

  return (
    <>
      <Button type="text" onClick={toggleModal}>
        <PlusOutlined />
      </Button>
      <Modal
        title="Adicionar Contato"
        visible={toggleValue}
        width={"40%"}
        closable={false}
        destroyOnClose={true}
        footer={null}
        onCancel={() => {}}
      >
        <Form layout="horizontal" onFinish={() => {}}>
          <Row justify="center" gutter={[16, 16]}>
            <Col span={24}>
              <LabelTitle label="Oportunidade" />
              <Input
                value={oportunity}
                placeholder="Digite o número da oportunidade (Ex.: 337)"
                onChange={(e) => setOportunity(e.target.value)}
              />
            </Col>
          </Row>

          <br />

          <Row justify="center">
            <Form.Item>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={handleAddOpportunity}
              >
                Adicionar
              </Button>
            </Form.Item>
          </Row>
        </Form>

        <Row style={{ marginBottom: "1rem" }}>
          <Col span={24}>
            {currentOpportunity.length > 0 ? (
              <TableOpportunities
                data={currentOpportunity.map((opportunity, index) => {
                  return {
                    id: index,
                    name: opportunity,
                  };
                })}
                onRemove={handleRemoveOpportunity}
              />
            ) : (
              <Row justify="center" gutter={[24, 16]}>
                <Col span={24}>
                  <Row justify="center">
                    <ContactsOutlined style={{ fontSize: "5rem" }} />
                  </Row>
                  <Row justify="center">
                    <Title level={5} style={{ fontWeight: "400" }}>
                      Não existem outros oportunidades inseridos selecionados
                    </Title>
                  </Row>
                </Col>
              </Row>
            )}
          </Col>
        </Row>

        <Row gutter={[8, 8]} justify={"space-between"}>
          {opportunities.length !== currentOpportunity.length ? (
            <Popconfirm
              title="Ao cancelar você perderá suas modificações, tem certeza?"
              onConfirm={handleCancel}
              okText="Sim"
              cancelText="Não"
            >
              <Button type="text-color">
                Cancelar
                <DeleteOutlined />
              </Button>
            </Popconfirm>
          ) : (
            <Button type="text-color" onClick={handleCancel}>
              Cancelar
              <DeleteOutlined />
            </Button>
          )}
          <Button type="primary" onClick={handleSave}>
            Salvar
            <CheckOutlined />
          </Button>
        </Row>
      </Modal>
    </>
  );
};

const TableOpportunities = ({ data, onRemove }) => {
  const columns = [
    {
      title: "Deal",
      dataIndex: "name",
      key: "name",
      sorter: (a, b) => a?.toString().localeCompare(b?.toString()),
    },
    {
      title: "Remover",
      dataIndex: "id",
      key: "id",
      align: "center",
      render: (id, props) => {
        return (
          <Popconfirm
            title={"Tem certeza que deseja remover esta oportunidade?"}
            okText={"Sim"}
            cancelText={"Não"}
            onConfirm={(e) => onRemove(id)}
          >
            <Button danger type="text" icon={<DeleteOutlined />}></Button>
          </Popconfirm>
        );
      },
    },
  ];

  return (
    <Table
      dataSource={data}
      columns={columns}
      scroll={{ x: 100 }}
      pagination={true}
    />
  );
};

const LabelTitle = ({ label }) => {
  return (
    <Title level={5} style={{ fontWeight: "400", fontSize: "14px" }}>
      {label}
    </Title>
  );
};
