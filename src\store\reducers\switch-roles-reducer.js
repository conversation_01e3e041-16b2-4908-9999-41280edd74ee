import { createSlice } from "@reduxjs/toolkit";

const REDUCER_NAME = "switchRole";

const INITIAL_STATE = {
  permissionSets: [],
};

const switchRoleSlice = createSlice({
  name: REDUCER_NAME,
  initialState: INITIAL_STATE,
  reducers: {
    setSwitchRoleStateReduce(state, action) {
      state[action.payload.field] = action.payload.value;
    },
  },
});

export const { setSwitchRoleStateReduce } = switchRoleSlice.actions;

export default switchRoleSlice.reducer;
