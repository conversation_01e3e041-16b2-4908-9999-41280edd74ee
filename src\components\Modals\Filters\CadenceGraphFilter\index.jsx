import { useEffect } from 'react'
import { React, useState } from 'react'
import { otrsGet } from '../../../../service/apiOtrs'
import './CadenceGraphFilter.css'
import { Select, DatePicker, Button, Form } from 'antd'
import { CloseSquareFilled } from '@ant-design/icons'

function CadenceGraphFilter(props) {
    const clients = ['Todos', 'Logikee', 'Darede']
    const { Option } = Select
    const [form] = Form.useForm();
    const [allClients, setAllClients] = useState([])

    function onFilterFinish(values) {
        props.handlePopoverVisibility()
    }

    function onFilterFailed() {}

    function clear() {
        form.resetFields();
    }

    useEffect(() => {
        otrsGet("read/customer/all/0").then((resp) => {
            let arrayNames = []
            Object.entries(resp.data).forEach((val) => {
                arrayNames.push(val[1].name)
            })
            setAllClients(arrayNames)
        }).catch((error)=>{
            console.log(error)
        })

    }, [])

    return (
        <Form
            form={form}
            className="form"
            name="basic"
            onFinish={onFilterFinish}
            onFinishFailed={onFilterFailed}
            autoComplete="off">
             <button>
                <CloseSquareFilled />
            </button>
            <Form.Item name="Carteira">
                <Select
                        showSearch
                        className="cadence-select"
                        placeholder="Carteira"
                        optionFilterProp="children"
                        filterOption={(input, option) =>
                            option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0 ||
                            option.props.value.toLowerCase().indexOf(input.toLowerCase()) >= 0
                        }>
                            <Option value={"RF-1"}>RF-1</Option>
                    </Select>
            </Form.Item>
            <Form.Item name="Profissional">
                <Select
                    showSearch
                    className="cadence-select"
                    placeholder="Profissional"
                    optionFilterProp="children"
                    filterOption={(input, option) =>
                        option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0 ||
                        option.props.value.toLowerCase().indexOf(input.toLowerCase()) >= 0
                    }>
                        <Option value={"RF-1"}>RF-1</Option>
                </Select>
            </Form.Item>
            <Form.Item name="cliente">
                <Select
                    showSearch
                    className="cadence-select"
                    placeholder="Cliente"
                    optionFilterProp="children"
                    filterOption={(input, option) =>
                        option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0 ||
                        option.props.value.toLowerCase().indexOf(input.toLowerCase()) >= 0
                    }>
                    {allClients.map((val,index) => {
                        return <Option key={index} value={val}>{val}</Option>
                    })}
                </Select>
            </Form.Item>




            <div className="cadence-date">
                <Form.Item name="dataAbertura">
                    <div>
                        <p>Data</p>
                        <DatePicker />
                    </div>
                </Form.Item>
            </div>
            <div className="buttons">
                <Button className="filter-submit-btn" onClick={clear}>Limpar</Button>
                <Button
                    htmlType="submit"
                    className="filter-clear-btn">
                    Aplicar
                </Button>
            </div>
        </Form>
    )
}

export default CadenceGraphFilter
