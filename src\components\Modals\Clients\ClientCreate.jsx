import Axios from "axios";
import { useState } from "react";
import { MaskedInput } from "antd-mask-input";
import { otrsPost } from "../../../service/apiOtrs";
import { dynamoPost } from "../../../service/apiDsmDynamo";
import { Button, Col, Input, Modal, Row, Form, message } from "antd";
import { getCNPJ } from "../../../utils/getCNPJ";
import {
  checkCNPJExists,
  formatCreateDSMCustomerBody,
  formatCreateOTRSCustomerBody,
} from "./controllers/clientCreateModal";
export const ClientCreateModal = (props) => {
  const [loading, setLoading] = useState(false);
  const [showModal, setShowModal] = useState();
  const { getProspects, customers } = props;
  const [form] = Form.useForm();

  const createCustomer = async (data) => {
    setLoading(true);

    const customers_formatted = [];

    Object.entries(customers).forEach((obj) => {
      customers_formatted.push(obj[1]);
    });

    const checkCNPJExistance = checkCNPJExists(customers_formatted, form, data);
    if (checkCNPJExistance) {
      setLoading(false);
      return checkCNPJExistance.message;
    }

    try {
      let cnpj = await getCNPJ(data.cnpj);
      if (cnpj?.data?.status === 404 || cnpj?.data?.status === 400) {
        form.resetFields();
        setLoading(false);
        return message.error(
          "CPNJ inválido. Por favor, verifique o CNPJ e tente novamente!"
        );
      }
      const { razao_social } = cnpj;
      const otrs_post = formatCreateOTRSCustomerBody(data, cnpj);

      let otrs;

      try {
        otrs = await otrsPost("create/customer", otrs_post);
      } catch (error) {
        setLoading(false);
        console.log(error);
        return message.error(
          "Ocorreu um erro ao tentar criar o cliente no OTRS :("
        );
      }
      const submit_data = formatCreateDSMCustomerBody(data, cnpj, otrs);

      try {
        await dynamoPost(
          `${process.env.REACT_APP_STAGE}-customers`,
          submit_data
        );
      } catch (error) {
        setLoading(false);
        console.log(error);
        return message.error(
          "O cliente foi criado no OTRS, mas ocorreu um erro ao tentar criar o cliente no DSM :("
        );
      }

      await getProspects();
      message.success("Cliente criado com sucesso!");

      const username = localStorage.getItem("@dsm/username");

      const title = "Cliente Editado";

      const description = `${username} criou o cliente ${razao_social}.`;

      dynamoPost(`${process.env.REACT_APP_STAGE}-audits`, {
        username: username,
        name: title,
        description: description,
        created_at: new Date(),
        updated_at: new Date(),
      });

      form.resetFields();
      setShowModal(false);
      setLoading(false);
    } catch (err) {
      form.resetFields();
      setLoading(false);
      message.error(
        "Ops! Ocorreu um erro inesperado ao tentar criar este cliente..."
      );
    }
  };

  return (
    <>
      <Button
        type="primary"
        onClick={async () => {
          setShowModal(true);
        }}
      >
        Cadastrar cliente
      </Button>
      <Modal
        title="Criar Cliente"
        open={showModal}
        onCancel={() => setShowModal(false)}
        footer={[
          <Button onClick={() => setShowModal(false)}>Fechar</Button>,
          <Button
            type="primary"
            loading={loading}
            onClick={() => form.submit()}
          >
            Cadastrar
          </Button>,
        ]}
      >
        <Row align="middle" justify="center">
          <Col span={24}>
            <Form
              form={form}
              onFinish={createCustomer}
              name="control-hooks"
              layout="vertical"
              requiredMark={false}
            >
              <Form.Item label="CRM ID" name="crm_id">
                <Input placeholder="CRM ID" />
              </Form.Item>
              <Form.Item
                rules={[{ required: true, message: "Preencha este campo." }]}
                label="CNPJ"
                name="cnpj"
              >
                <MaskedInput mask="00.000.000/0000-00" placeholder="CNPJ" />
              </Form.Item>
              <Form.Item
                rules={[{ required: true, message: "Preencha este campo." }]}
                label="Nome Fantasia"
                name="fantasy_name"
              >
                <Input placeholder="Nome Fantasia" />
              </Form.Item>
            </Form>
          </Col>
        </Row>
      </Modal>
    </>
  );
};
