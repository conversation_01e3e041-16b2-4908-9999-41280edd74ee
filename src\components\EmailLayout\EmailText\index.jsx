import { useContext, useRef, useEffect } from 'react'
import styledSheet from './style.module.scss'

import { DeleteOutlined } from '@ant-design/icons'

import { EmailAutomationContext } from '../../../contexts/email-automation'

import { Tag } from "antd";
import { EmailAutomationReducerType } from '../../../contexts/reducers/email-automation-reducer';

export const EmailText = ({ setIsEdit, value, positionItem }) => {
    const { emailAutomationState, setEmailAutomationState } = useContext(EmailAutomationContext)
    const { templateList } = emailAutomationState
    const spanValueRef = useRef(null)

    useEffect(() => {
        if (spanValueRef.current) {
            spanValueRef.current.innerHTML = ''
            spanValueRef.current.innerHTML = value
        }
    }, [value])

    function onRemove() {
        const newTemplateList = templateList.filter((template, index) => index !== positionItem)

        setEmailAutomationState({
            type: EmailAutomationReducerType.SET_TEMPLATE_LIST,
            value: newTemplateList
        })
    }

    return (
        <>
            <div
                className={styledSheet['text-container']}
                onClick={() => setIsEdit(true)}
                style={{
                    textAlign: 'center',
                    marginBottom: 20
                }}
            >
                <p
                    style={{
                        textAlign: 'center',
                    }}
                >
                    <span style={{ fontSize: 16 }}>
                        <span style={{ fontFamily: 'arial, helvetica, sans-serif' }}>
                            <span style={{ lineHeight: '110%', }}>
                                <span style={{ lineHeight: '110%' }} id="span-value" ref={spanValueRef}>
                                </span>
                            </span>
                        </span>
                    </span>
                </p>
                <div
                    className={styledSheet['text-options']}
                    onClick={onRemove}
                    style={{ display: 'none' }}
                >
                    <Tag color="red">
                        <DeleteOutlined />
                    </Tag>
                    
                </div>
            </div>
        </>
    )
}