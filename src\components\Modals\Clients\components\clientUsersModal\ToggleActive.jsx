import { Button, Popconfirm, Row, Tag } from "antd";

export const ToggleActive = ({ item, changeActive, active }) => {
  return (
    <Row justify="center">
      {active === 1 ? (
        <Popconfirm
          placement="leftBottom"
          title="Tem certeza que deseja desativar este contato?"
          style={{
            backgroundColor: "transparent",
            border: "none",
            cursor: "pointer",
            color: "black",
          }}
          onConfirm={() => changeActive(item)}
          cancelText="Cancelar"
        >
          <Button style={{ padding: "0" }} type="text">
            <Tag color="red">Desativar</Tag>
          </Button>
        </Popconfirm>
      ) : (
        <Popconfirm
          placement="leftBottom"
          title="Tem certeza que deseja ativar este contato?"
          style={{
            backgroundColor: "transparent",
            border: "none",
            cursor: "pointer",
            color: "black",
          }}
          onConfirm={() => changeActive(item)}
          cancelText="Cancelar"
        >
          <Button style={{ padding: "0" }} type="text">
            <Tag color="green">Ativar</Tag>
          </Button>
        </Popconfirm>
      )}
    </Row>
  );
};
