/**
 * Componente para exibir status de autenticação e conectividade
 * Útil para debugging e monitoramento
 */

import React, { useState, useEffect } from 'react';
import { Card, Badge, Button, Descriptions, Space, Alert } from 'antd';
import { 
  CheckCircleOutlined, 
  CloseCircleOutlined, 
  ReloadOutlined,
  InfoCircleOutlined 
} from '@ant-design/icons';
import { httpOnlyAuthService } from '../../services/httpOnlyAuthService';
import { testConnection } from '../../services/apiClient';
import { logger } from '../../utils/logger';

export const AuthStatus = ({ showDetails = false }) => {
  const [authStatus, setAuthStatus] = useState(null);
  const [connectionStatus, setConnectionStatus] = useState(null);
  const [loading, setLoading] = useState(false);

  const checkStatus = async () => {
    setLoading(true);
    try {
      const auth = await httpOnlyAuthService.getAuthStatus();
      setAuthStatus(auth);

      const connection = await testConnection();
      setConnectionStatus(connection);

      logger.debug('Status verificado', { auth, connection });
    } catch (error) {
      logger.error('Erro ao verificar status', { error: error.message });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    checkStatus();
  }, []);

  const getAuthBadge = () => {
    if (!authStatus) return <Badge status="processing" text="Verificando..." />;
    
    if (authStatus.authenticated) {
      return (
        <Badge 
          status="success" 
          text={`Autenticado (${authStatus.method})`}
          icon={<CheckCircleOutlined />}
        />
      );
    } else {
      return (
        <Badge 
          status="error" 
          text="Não autenticado"
          icon={<CloseCircleOutlined />}
        />
      );
    }
  };

  const getConnectionBadge = () => {
    if (connectionStatus === null) return <Badge status="processing" text="Verificando..." />;
    
    if (connectionStatus) {
      return (
        <Badge 
          status="success" 
          text="API Conectada"
          icon={<CheckCircleOutlined />}
        />
      );
    } else {
      return (
        <Badge 
          status="error" 
          text="API Desconectada"
          icon={<CloseCircleOutlined />}
        />
      );
    }
  };

  if (!showDetails) {
    return (
      <Space>
        {getAuthBadge()}
        {getConnectionBadge()}
        <Button 
          icon={<ReloadOutlined />} 
          size="small" 
          onClick={checkStatus}
          loading={loading}
        />
      </Space>
    );
  }

  return (
    <Card 
      title="Status de Autenticação" 
      size="small"
      extra={
        <Button 
          icon={<ReloadOutlined />} 
          onClick={checkStatus}
          loading={loading}
        >
          Atualizar
        </Button>
      }
    >
      <Space direction="vertical" style={{ width: '100%' }}>
        {/* Status de Autenticação */}
        <Descriptions size="small" column={1}>
          <Descriptions.Item label="Autenticação">
            {getAuthBadge()}
          </Descriptions.Item>
          <Descriptions.Item label="Conectividade">
            {getConnectionBadge()}
          </Descriptions.Item>
        </Descriptions>

        {/* Detalhes do Usuário */}
        {authStatus?.authenticated && authStatus.user && (
          <Descriptions title="Usuário" size="small" column={1}>
            <Descriptions.Item label="Email">
              {authStatus.user.email}
            </Descriptions.Item>
            <Descriptions.Item label="Nome">
              {authStatus.user.name}
            </Descriptions.Item>
            {authStatus.user.role && (
              <Descriptions.Item label="Role">
                {authStatus.user.role}
              </Descriptions.Item>
            )}
            {authStatus.user.permissions && (
              <Descriptions.Item label="Permissões">
                {Array.isArray(authStatus.user.permissions) 
                  ? authStatus.user.permissions.join(', ')
                  : authStatus.user.permissions
                }
              </Descriptions.Item>
            )}
          </Descriptions>
        )}

        {/* Status do Token */}
        {authStatus?.tokenStatus && (
          <Descriptions title="Token" size="small" column={1}>
            <Descriptions.Item label="Válido">
              {authStatus.tokenStatus.valid ? 'Sim' : 'Não'}
            </Descriptions.Item>
            <Descriptions.Item label="Precisa Renovar">
              {authStatus.tokenStatus.needsRefresh ? 'Sim' : 'Não'}
            </Descriptions.Item>
            <Descriptions.Item label="Tem Refresh Token">
              {authStatus.tokenStatus.hasRefreshToken ? 'Sim' : 'Não'}
            </Descriptions.Item>
            {authStatus.tokenStatus.expiresAt && (
              <Descriptions.Item label="Expira em">
                {new Date(authStatus.tokenStatus.expiresAt).toLocaleString()}
              </Descriptions.Item>
            )}
          </Descriptions>
        )}

        {/* Alertas */}
        {authStatus?.method === 'manual-cookie' && (
          <Alert
            message="Usando Cookie Manual"
            description="O backend ainda não suporta cookies HttpOnly. Usando fallback manual."
            type="warning"
            icon={<InfoCircleOutlined />}
            showIcon
          />
        )}

        {authStatus?.method === 'httponly-backend' && (
          <Alert
            message="Cookies HttpOnly Ativos"
            description="Sistema de segurança avançado ativo com cookies HttpOnly."
            type="success"
            icon={<CheckCircleOutlined />}
            showIcon
          />
        )}

        {authStatus?.error && (
          <Alert
            message="Erro de Autenticação"
            description={authStatus.error}
            type="error"
            icon={<CloseCircleOutlined />}
            showIcon
          />
        )}
      </Space>
    </Card>
  );
};

export default AuthStatus;
