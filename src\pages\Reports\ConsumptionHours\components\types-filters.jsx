import { shallowEqual, useSelector } from "react-redux";
import { Col, Typography } from "antd";

import { Select } from "../../../../components/Select";

import { setConsumptionState } from "../../../../store/actions/consumption-hours-action";

const hourTypeOptions = [
    { value: 0, label: "Todos" },
    { value: 1, label: "Excedentes" },
    { value: 2, label: "Não Excedentes" },
];

const contractStatusOptions = [
    { value: 0, label: "Todos" },
    { value: 1, label: "Ativos" },
    { value: 2, label: "Inativos" },
];

export const TypeFilters = () => {
    return (
        <>
            <TypeField
                label={'Tipo de horas:'}
                selector={'selectedHourType'}
                options={hourTypeOptions}
                offset={1}
            />

            <TypeField
                label={'Status:'}
                selector={'selectedStatus'}
                options={contractStatusOptions}
                offset={1}
            />
        </>
    )
}

const TypeField = ({ label, selector, options, offset = 0 }) => {
    const state = useSelector(state => state.consumption[selector], shallowEqual)

    return (
        <Col
            xl={{ span: 4, offset }}
            lg={{ span: 4, offset }}
            md={{ span: 6, offset }}
            sm={{ span: 10 }}
            style={{ marginBottom: 10 }}
        >
            <Typography.Text>{label}</Typography.Text>
            <Select
                onChange={(value) => setConsumptionState({ field: selector, value })}
                options={options}
                value={state}
                style={{ width: "100%" }}
            />
        </Col>
    )
}