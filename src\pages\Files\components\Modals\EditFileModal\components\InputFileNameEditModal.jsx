import { Col, Row, Typography, Input } from "antd";
import { useSelector, shallowEqual } from "react-redux";
import { setSelectedFileFieldState } from "../../../../../../store/actions/files-action";

const { Text } = Typography;

export const InputFileNameEditModal = () => {
  const fileName = useSelector(
    (state) => state.files.selectedFile.name,
    shallowEqual
  );

  return (
    <Col span={24}>
      <Row>
        <Text>Nome do arquivo: </Text>
      </Row>
      <Row>
        <Input
          status={fileName ? "" : "error"}
          onChange={(e) => {
            setSelectedFileFieldState({
              field: "name",
              value: e.target.value,
            });
          }}
          value={fileName}
          style={{ width: "100%" }}
        />
        {!fileName ? (
          <>
            <Text type={"danger"}>Preenchimento Obrigatório</Text>
          </>
        ) : null}
      </Row>
    </Col>
  );
};
