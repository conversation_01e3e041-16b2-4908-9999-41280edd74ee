import {
  But<PERSON>,
  Modal,
  Row,
  Form,
  Table,
  message,
  Tooltip,
  Popconfirm,
  Tag,
  Typography,
} from "antd";
import { useMemo, useState } from "react";
import { TeamOutlined, EditOutlined, DeleteOutlined } from "@ant-design/icons";
import {
  checkDaredeFullRole,
  dynamoGetById,
  dynamoPost,
  dynamoPut,
  invokeStateMachine,
  sendWebhookNotification,
} from "../../../service/apiDsmDynamo";
import { otrsPost } from "../../../service/apiOtrs";
import { format } from "date-fns";
import { otrsWebServicePost } from "../../../service/otrsWebService";
import { AccessPopover } from "./components/clientAccountsModal/AccessPopover";
import { filterTableData } from "../../../utils/filterTableData";
import { ModalHeader } from "./components/clientAccountsModal/ModalHeader";
import { sendCreateSwitchRoleFailEmail } from "./components/clientAccountsModal/SendEmail";
import { CustomRow } from "./components/clientAccountsModal/CustomRow";
import { filterTableColumns } from "../../../utils/filterTableColumns";
import {
  checkTicketType,
  formatAccountBodyRequest,
} from "./controllers/clientAccountsModal";
import { SnippetsOutlined } from "@ant-design/icons";

export const ClientAccountsModal = (props) => {
  const {
    clients,
    client,
    permissions,
    permissionSets,
    currentUserAccesses,
    refreshUserSolicitations,
  } = props;
  const uuid = require("uuid");
  const [allAccessForm] = Form.useForm();
  const [selectedAccounts, setSelectedAccounts] = useState([]);
  const [isMultipleAccounts, setIsMultipleAccounts] = useState(false);
  const [form] = Form.useForm();
  const [switchForm] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [solicitations, setSolicitations] = useState();
  const [accountID, setAccountID] = useState([]);
  const [hasAccess, setHasAccess] = useState("none");
  const [loadingAccess, setLoadingAccess] = useState(false);
  const [showModal, setShowModal] = useState();
  const [hidden, setHidden] = useState(true);
  const [search, setSearch] = useState("");
  const [edit, setEdit] = useState("");
  const [loadingRequest, setLoadingRequest] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [loadingAccount, setLoadingAccount] = useState(false);
  const username = localStorage.getItem("@dsm/username");
  const [accounts, setAccounts] = useState([]);
  const fieldEdit = [];
  const { Text } = Typography;
  function uniqByKeepLast(data, key) {
    return [...new Map(data.map((x) => [key(x), x]).values())];
  }

  const getAccountInfo = async (user) => {
    form.resetFields();

    setHidden(false);

    setEdit({
      id: user?.id,
      account_id: user?.account_id,
      profile: user?.profile,
      region: user?.region,
      customer_id: user?.customer_id,
      con_status: user?.con_status,
      payer: user?.payer,
    });

    form.setFieldsValue({
      id: user?.id,
      account_id: user?.account_id,
      profile: user?.profile,
      region: user?.region,
      customer_id: user?.customer_id,
      con_status: user?.con_status,
      payer: user?.payer,
    });
    setIsEditing(true);
  };

  const handleGetAccountAfterSubmit = async () => {
    setLoading(true);
    try {
      const { accounts } = await dynamoGetById(
        `${process.env.REACT_APP_STAGE}-customers`,
        client.id
      );
      setAccounts(accounts);
    } catch (err) {
      console.log(err);
    }
    setLoading(false);
  };

  const removeAccount = async (item) => {
    setLoading(true);
    try {
      const updateClient = {
        ...client,
        accounts: accounts.filter((e) => e.id !== item.id),
      };

      await dynamoPut(
        `${process.env.REACT_APP_STAGE}-customers`,
        client.id,
        updateClient
      );

      await handleGetAccountAfterSubmit();
      const username = localStorage.getItem("@dsm/username");
      const title = "Conta AWS Removida";
      const description = `${username} removeu o perfil AWS '${
        accounts.find((e) => e.id === item.id).profile
      }' do cliente ${client?.names?.fantasy_name || client?.names?.name}.`;
      dynamoPost(`${process.env.REACT_APP_STAGE}-audits`, {
        username: username,
        name: title,
        description: description,
        created_at: new Date(),
        updated_at: new Date(),
      });
      message.success("Conta excluída com sucesso!");
    } catch (error) {
      setLoading(false);
      console.log(error);
      message.error("Erro ao tentar remover conta, tente novamente.");
    }
  };

  const handleSubmit = async (data) => {
    data.payer = data.payer ? data.payer : false;
    setLoadingRequest(true);
    data.account_id = data.account_id.replace(/ /g, "");

    if (!data.profile.match(/^[A-Za-z-Dash_Punctuation]+$/)) {
      setLoadingRequest(false);
      return message.error("Insira apenas letras no campo de perfil.");
    }

    if (data.id !== undefined) {
      if (
        accounts.some(
          (e) =>
            (data.id !== e.id && e.account_id === data.account_id) ||
            (data.id !== e.id &&
              e?.profile?.toLowerCase() === data?.profile?.toLowerCase())
        )
      ) {
        setLoadingRequest(false);
        return message.error(
          "Não é permitido a conta AWS com o mesmo ID ou Nome de Perfil."
        );
      }

      try {
        const response = await checkDaredeFullRole(data.account_id);
        if (response.success) {
          data["role_exists"] = true;
        }
      } catch (err) {
        data["role_exists"] = false;
        await sendCreateSwitchRoleFailEmail(data.account_id);
        console.error("Erro ao verificar a role: ", err);
      }

      const updateClient = formatAccountBodyRequest(
        client,
        data,
        isEditing,
        accounts
      );
      await dynamoPut(
        `${process.env.REACT_APP_STAGE}-customers`,
        client.id,
        updateClient
      )
        .then(async () => {
          message.success("Conta editada com sucesso!");
          form.resetFields();
          const username = localStorage.getItem("@dsm/username");

          let arr = [];

          const newArr = uniqByKeepLast(fieldEdit, (e) => e.split(" ")[0]);

          newArr.forEach((e) => {
            return arr.push(e[1]);
          });

          const title = "Conta AWS Editada";

          const description = `${username} editou a conta AWS '${
            edit.profile
          }' do cliente ${
            client?.names?.fantasy_name || client?.names?.name
          }, com as seguintes alterações: ${arr}.`;

          dynamoPost(`${process.env.REACT_APP_STAGE}-audits`, {
            username: username,
            name: title,
            description: description,
            created_at: new Date(),
            updated_at: new Date(),
          });

          setHidden(true);
          await handleGetAccountAfterSubmit();
          setLoadingRequest(false);
        })
        .catch(() => {
          message.error("Erro ao tentar editar conta...");
          setLoadingRequest(false);
        });
    } else {
      if (
        accounts.some(
          (e) =>
            e.account_id === data.account_id ||
            e?.profile?.toLowerCase() === data?.profile?.toLowerCase()
        )
      ) {
        setLoadingRequest(false);
        return message.error(
          "Não é permitido a conta AWS com o mesmo ID ou Nome de Perfil."
        );
      }

      data["id"] = uuid.v4();
      data["created_at"] = format(new Date(), "yyyy-MM-dd HH:mm:ss");
      data["updated_at"] = format(new Date(), "yyyy-MM-dd HH:mm:ss");

      try {
        console.log('🔍 ClientAccountsModal: Verificando role para account:', data.account_id);
        const response = await checkDaredeFullRole(data.account_id);

        if (response.success) {
          console.log('✅ ClientAccountsModal: Role existe e pode ser assumida');
          data["role_exists"] = true;
        } else {
          console.warn('⚠️ ClientAccountsModal: Role não pode ser assumida');
          data["role_exists"] = false;
        }
      } catch (err) {
        console.error('❌ ClientAccountsModal: Erro ao verificar role:', err.message);
        data["role_exists"] = false;

        // Tentar enviar email de falha, mas não falhar se der erro
        try {
          await sendCreateSwitchRoleFailEmail(data.account_id);
          console.log('✅ ClientAccountsModal: Email de falha enviado');
        } catch (emailError) {
          console.error('❌ ClientAccountsModal: Erro ao enviar email de falha:', emailError.message);
          // Continua o processo mesmo se o email falhar
        }

        console.error("Erro ao verificar a role: ", err);
      }

      const updateClient = formatAccountBodyRequest(
        client,
        data,
        isEditing,
        accounts
      );

      await dynamoPut(
        `${process.env.REACT_APP_STAGE}-customers`,
        client.id,
        updateClient
      )
        .then(async () => {
          message.success("Conta cadastrada com sucesso!");
          form.resetFields();

          const username = localStorage.getItem("@dsm/username");

          const title = "Conta AWS adicionada";

          const description = `${username} criou o perfil AWS '${
            data.profile
          }' no cliente ${client?.names?.fantasy_name || client?.names?.name}.`;

          dynamoPost(`${process.env.REACT_APP_STAGE}-audits`, {
            username: username,
            name: title,
            description: description,
            created_at: new Date(),
            updated_at: new Date(),
          });

          setHidden(true);

          await handleGetAccountAfterSubmit();
          setLoadingRequest(false);
        })
        .catch(() => {
          message.error("Erro ao tentar cadastrar conta...");
          setLoadingRequest(false);
        });
    }
  };

  async function requestAccountAccess(data) {
    setLoadingAccess(true);
    const username = localStorage.getItem("@dsm/username");

    let ticket = await otrsPost("read/tickets/number", {
      params: data.ticket_id.replace(/ /g, "").replace(/\t/g, ""),
    });

    ticket = ticket.data[0];

    if (!ticket) {
      message.error("Ticket inexistente, insira um número correto.");
      return setLoadingAccess(false);
    }

    if (ticket.ticket_state.includes("closed")) {
      message.error("Ticket já encerrado.");
      return setLoadingAccess(false);
    }

    let ticketTypeConditions = checkTicketType(ticket, data);

    if (ticketTypeConditions?.status === false) {
      setLoadingAccess(false);
      return message.error(ticketTypeConditions.message);
    }

    if (
      client?.identifications?.itsm_id?.toString() !==
      ticket?.customer_id?.toString()
    ) {
      message.error("Este ticket não pertence a este cliente.");
      return setLoadingAccess(false);
    }

    if (username.toString() !== ticket?.owner_ticket?.toString()) {
      message.error("Este ticket pertence a outro usuário.");
      return setLoadingAccess(false);
    }

    try {
      sendWebhookNotification(accountID, client, data);
    } catch (error) {
      console.log(error);
    }

    data["time"] = parseInt(data.time);
    data["ticket_id"] = 0;
    data["role"] = "darede-full";

    try {
      try {
        otrsWebServicePost("tickets/close", {
          title: "Acesso solicitado em Switch Role",
          communicationType: "Email",
          sender: localStorage.getItem("@dsm/mail"),
          description: `Usuário ${localStorage.getItem(
            "@dsm/username"
          )} solicitou acesso na(s) conta(s) '${accountID}' com a role '${
            data.role
          }'.`,
          tktNumber: parseInt(data.ticket_id),
          contract: 0,
          tktId: ticket.ticket_id,
          closed: "open",
        });
      } catch (error) {
        console.log(error);
      }

      for (let a of accountID) {
        if (!currentUserAccesses?.find((e) => e.account_id === a)) {
          let invokeStateMachineBody = {
            action: "ask",
            account_id: a,
            requestedTime: data.time * 60,
            client_ticket: parseInt(ticket.ticket_number),
            stage: process.env.REACT_APP_STAGE,
            client_name: client?.names?.fantasy_name,
            ticket_id: 0,
            role: permissionSets.data.find(
              (ps) => ps.user === localStorage.getItem("@dsm/username")
            )
              ? "darede-full"
              : localStorage.getItem("@dsm/username").replace(".", "-"),
            username: localStorage.getItem("@dsm/username"),
          };

          await invokeStateMachine(invokeStateMachineBody);
          switchForm.resetFields();
          allAccessForm.resetFields();
          setHasAccess("not_yet");

          dynamoPost(`${process.env.REACT_APP_STAGE}-audits`, {
            username: username,
            name: "Acesso Switch Role",
            description: `${username} solicitou acesso para a conta '${a}' no cliente '${client?.names?.fantasy_name}'.`,
            created_at: new Date(),
            updated_at: new Date(),
          });
        }
      }
      setLoadingAccess(false);
      setSelectedAccounts([]);
      setAccountID([]);
      message.success("Sucesso ao solicitar o acesso, em breve será liberado.");
    } catch (error) {
      console.log(error);
      setLoadingAccess(false);
      message.error(
        "Ops... ocorreu um erro ao tentar solicitar este acesso..."
      );
    }
    setTimeout(async () => {
      await refreshUserSolicitations();
    }, 2000);
  }

  const columns = [
    {
      code: "view_account_profile",
      title: "Perfil",
      dataIndex: "profile",
      key: "profile",
      sorter: (a, b) => a.profile.localeCompare(b.profile),
      sortDirections: ["descend", "ascend"],
    },
    {
      code: "view_account_account",
      title: "Conta",
      dataIndex: "account_id",
      key: "account_id",
      render: (account_id, item) => (
        <>
          <Tooltip title="Solicite acesso para realizar a troca de role no AWS Console">
            <AccessPopover
              permissionSets={permissionSets}
              setHasAccess={(e) => setHasAccess(e)}
              hasAccess={hasAccess}
              loadingAccess={loadingAccess}
              loadingAccount={loadingAccount}
              setSolicitations={(e) => setSolicitations(e)}
              setLoadingAccount={(e) => setLoadingAccount(e)}
              setAccountID={(e) => setAccountID(e)}
              account_id={account_id}
              currentUserAccesses={currentUserAccesses}
              requestAccountAccess={requestAccountAccess}
              item={item}
              client={client}
            />
          </Tooltip>
          <Tooltip title="Clique para copiar">
            <Button
              icon={<SnippetsOutlined style={{ color: "#00B050" }} />}
              onClick={() => {
                message.success("Copiado para área de transferência!");

                navigator.clipboard.writeText(account_id);
              }}
              type="text"
            />
          </Tooltip>
        </>
      ),
    },
    {
      code: "view_account_view",
      title: "Visualizar",
      dataIndex: "account_id",
      key: "account_id",
      render: (account_id, item) => (
        <a
          rel="noreferrer"
          target="_blank"
          onClick={async () => {
            await dynamoPost(`${process.env.REACT_APP_STAGE}-audits`, {
              username: username,
              name: "Acesso na Conta",
              description: `${username} acessou a conta '${account_id}' no cliente '${
                client?.names?.fantasy_name
              } com a role ${localStorage
                .getItem("@dsm/username")
                .replace(".", "-")}.`,
              created_at: new Date(),
              updated_at: new Date(),
            });
          }}
          style={{ color: "#0f9347" }}
          href={`https://signin.aws.amazon.com/switchrole?account=${account_id}&roleName=darede&displayName=${item.profile}`}
        >
          Clique aqui para entrar na conta
        </a>
      ),
    },
    {
      code: "view_account_profile",
      title: "Payer Account",
      dataIndex: "payer",
      key: "payer",
      align: "center",
      sorter: (a, b) => a.payer - b.payer,
      sortDirections: ["descend", "ascend"],
      render: (_) =>
        _ ? <Tag color="green">Sim</Tag> : <Tag color="default">Não</Tag>,
    },
    {
      code: "view_account_profile",
      title: "Roles Criadas",
      dataIndex: "role_exists",
      key: "role_exists",
      align: "center",
      sorter: (a, b) => a.role_exists - b.role_exists,
      sortDirections: ["descend", "ascend"],
      render: (_) =>
        _ ? (
          <Tag color="green">Sim</Tag>
        ) : (
          <Tag color="default">
            <a
              href={process.env.REACT_APP_CREATE_ROLE_LINK}
              target="_blank"
              rel="noopener noreferrer"
            >
              Criar Roles
            </a>
          </Tag>
        ),
    },
    {
      code: "view_account_edit",
      title: "Editar",
      dataIndex: "id",
      key: "id",
      width: "1%",
      render: (id, item) => {
        return (
          <Row justify="center">
            <Tooltip title="Editar informações da conta">
              <Button type="text" onClick={() => getAccountInfo(item)}>
                <EditOutlined />
              </Button>
            </Tooltip>
          </Row>
        );
      },
    },
    {
      code: "view_account_remove",
      title: "Excluir",
      dataIndex: "id",
      key: "id",
      width: "1%",
      render: (field, item) => {
        return (
          <Row justify="center">
            <Tooltip title="Remover esta conta">
              <Popconfirm
                placement="leftBottom"
                title="Certeza de que deseja remover esta conta?"
                onConfirm={() => removeAccount(item)}
                cancelText="Não"
                okText="Sim"
              >
                <Button type="text" danger>
                  <DeleteOutlined />
                </Button>
              </Popconfirm>
            </Tooltip>
          </Row>
        );
      },
    },
  ];

  const pagination = {
    data: [],
  };

  function getChange(key) {
    for (let i = 0; i < Object.entries(edit).length; i++) {
      if (Object.entries(edit)[i][0] === key) {
        return Object.values(edit)[i];
      }
    }
  }

  const tableData = useMemo(() => {
    let data = accounts || [];
    let filteredData = client.accounts || [];

    filteredData = filterTableData({
      searchFields: ["account_id", "profile"],
      search,
      data,
    });
    return filteredData;
  }, [search, accounts, showModal]);

  const rowSelection = {
    type: "checkbox",
    selectedRowKeys: selectedAccounts,
    onChange: (selectedRowKeys, selectedRows) => {
      setSelectedAccounts(selectedRowKeys);
    },
    columnTitle: <></>,
    getCheckboxProps: (record) => ({
      disabled:
        ((currentUserAccesses.length >= 10 ||
          selectedAccounts.length + currentUserAccesses.length >= 10) &&
          !selectedAccounts.includes(record.account_id)) ||
        currentUserAccesses?.find((e) => e.account_id === record.account_id),
      account_id: record.account_id,
    }),
  };

  const customRow = (props) => {
    return (
      <CustomRow
        props={props}
        selectedAccounts={selectedAccounts}
        currentUserAccesses={currentUserAccesses}
      />
    );
  };

  return (
    <>
      <Tooltip title="Visualizar contas AWS">
        <Button
          type="text"
          style={{
            color: client?.accounts?.length ? "#0f9347" : "#333",
          }}
          onClick={() => {
            setAccounts(client.accounts);
            setShowModal(true);
          }}
        >
          <TeamOutlined />
        </Button>
      </Tooltip>
      <Modal
        title="Contas AWS"
        width="65vw"
        open={showModal}
        onCancel={() => {
          setShowModal(false);
          setHidden(true);
          setSelectedAccounts([]);
          form.resetFields();
        }}
        closable={false}
        footer={[
          <Button type="primary" onClick={() => setShowModal(false)}>
            Fechar
          </Button>,
        ]}
      >
        <ModalHeader
          permissions={permissions}
          client={client}
          clients={clients}
          form={form}
          setHidden={() => setHidden(!hidden)}
          setIsEditing={() => setIsEditing(!isEditing)}
          hidden={hidden}
          isEditing={isEditing}
          setEdit={(e) => setEdit(e)}
          edit={edit}
          setSearch={(e) => setSearch(e)}
          setAccounts={(e) => setAccounts(e)}
          accounts={selectedAccounts}
          setAccountID={(e) => setAccountID(e)}
          setLoadingRequest={(e) => setLoadingRequest(e)}
          setLoading={(e) => setLoading(e)}
          setLoadingAccount={(e) => setLoadingAccount(e)}
          handleGetAccountAfterSubmit={handleGetAccountAfterSubmit}
          formatAccountBodyRequest={formatAccountBodyRequest}
          handleSubmit={handleSubmit}
          getAccountInfo={getAccountInfo}
          fieldEdit={fieldEdit}
          getChange={getChange}
          removeAccount={removeAccount}
          allAccessForm={allAccessForm}
          loadingAccess={loadingAccess}
          requestAccountAccess={requestAccountAccess}
          loadingRequest={loadingRequest}
          setIsMultipleAccounts={(e) => setIsMultipleAccounts(e)}
          isMultipleAccounts={isMultipleAccounts}
          solicitations={currentUserAccesses}
          getSoliciations={refreshUserSolicitations}
          permissionSets={permissionSets}
          origin="client"
        />
        <Row justify="end">
          {tableData && tableData.length > 0 ? (
            <Text style={{ margin: "5px 10px 5px 0px" }}>
              Total: {tableData.length}
            </Text>
          ) : null}
        </Row>
        <Row
          align="center"
          style={{
            flexWrap: "nowrap",
            overflowX: "auto",
            paddingBottom: "10px",
          }}
        >
          <Table
            rowSelection={isMultipleAccounts ? rowSelection : null}
            rowKey={(item) => item.account_id}
            loading={loading}
            style={{ minWidth: "100%" }}
            dataSource={tableData}
            columns={filterTableColumns(columns, permissions)}
            components={{
              body: {
                row: isMultipleAccounts ? customRow : null,
              },
            }}
            pagination={pagination}
          />
        </Row>
      </Modal>
    </>
  );
};
