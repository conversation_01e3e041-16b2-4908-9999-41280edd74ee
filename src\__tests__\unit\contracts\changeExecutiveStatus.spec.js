import { changeExecutiveStatus } from "../../../components/Modals/Contracts/controllers/changeActiveExecutive";
import { dynamoPut } from "../../../service/apiDsmDynamo";

jest.mock("../../../service/apiDsmDynamo", () => ({
  dynamoPut: jest.fn(),
}));

describe("changeExecutiveStatus", () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  it("Should change executive status correctly", async () => {
    const data = {
      id: 123,
      email: "<EMAIL>",
      type: "SDM",
      active: 0,
    };

    const contract = {
      id: 456,
      name: "Nome do Contrato",
      executives: [
        { id: 123, email: "<EMAIL>", type: "SDM", active: 0 },
        { id: 124, email: "<EMAIL>", type: "SDM", active: 1 },
      ],
      identifications: {
        itsm_id: 8877,
      },
    };

    const expectedContract = {
      id: 456,
      name: "Nome do Contrato",
      executives: [
        { id: 123, email: "<EMAIL>", type: "SDM", active: 1 },
        { id: 124, email: "<EMAIL>", type: "SDM", active: 1 },
      ],
      identifications: {
        itsm_id: 8877,
      },
    };

    dynamoPut.mockResolvedValueOnce();

    const updatedContract = await changeExecutiveStatus(data, contract);

    expect(updatedContract.executives).toEqual(expectedContract.executives);
  });
});
