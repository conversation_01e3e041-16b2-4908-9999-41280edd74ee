import React, {useState, useEffect} from 'react'
import {Table, Radio} from 'antd'
import {ArrowLeftOutlined} from '@ant-design/icons'
import '../ProfessionalTickets/professional.css'
import {ContestHours} from '../ContestHours'

const columns = [
    {
        title: 'Titulo',
        dataIndex: 'title',
        key: 'title',
        render: (title) => <p className="table-font">{title}</p>,
    },
    {
        title: 'Horas',
        dataIndex: 'hours',
        key: 'hours',
        render: (hours) => <p className="table-font">{hours}</p>,
    },
    Table.SELECTION_COLUMN,
]

const ProfessionalTickets = (props) => {
    const [contestTicketView, setContestTicketView] = useState(false)
    const [selectedKey, setSelectedKey] = useState([])
    const [selectedTickets, setSelectedTickets] = useState([])
    const [data, setData] = useState([])
    const {showModal} = props

    function aproveAll() {
        setData([])
    }

    function handleContestTicketView() {
        selectTicketByKey()
        setContestTicketView(!contestTicketView)
    }

    function searchTickerPerBase(value) {
        console.log('Olhando value', value.target.value) // return value of Radio selected -> after I have the back-end, change the value to jira and otrs
    }

    function changeItemSelected(value) {
        setSelectedKey(value)
    }

    function selectTicketByKey() {
        let ticket = data.filter((item) => selectedKey.some((key) => key === item.key))

        setSelectedTickets(ticket)
    }

    useEffect(() => {
        setData([
            {
                key: '1',
                title: 'Criar api',
                hours: '5h',
            },
            {
                key: '2',
                title: 'Ola api',
                hours: '5h',
            }
        ])
    }, [props.favorite])

    return (
        <>
            {!contestTicketView ? (
                <>
                    {data.length === 0 ? (
                        <>
                            <div className="table-card-top">
                                <button className="filter-clear-btn back-button" onClick={() => showModal()}>
                                    <ArrowLeftOutlined/>
                                </button>
                                <h2>Tickets do profissional</h2>
                                <Radio.Group onChange={(value) => searchTickerPerBase(value)}>
                                    <Radio value={1}>Jira</Radio>
                                    <Radio value={2}>Otrs</Radio>
                                </Radio.Group>
                                <div className='button'>
                                    <button>
                                        Aprovar
                                    </button>
                                </div>
                            </div>
                            <div>
                                <Table
                                    rowSelection={{}}
                                    scroll={{y: 200}}
                                    pagination={false}
                                    columns={columns}
                                    dataSource={data}
                                >
                                </Table>
                                <h3>Todos tickets aprovados!</h3>
                                <p>Sem tickets para aprovar</p>
                            </div>
                        </>
                    ) : (
                        <>
                            <div className="table-card-top">
                                <button className="filter-clear-btn back-button" onClick={() => showModal()}>
                                    <ArrowLeftOutlined/>
                                </button>
                                <div className="table-head">
                                    <div className="table-rightHeader">
                                        <h2 className='professional-ticket-title'>Tickets do profissional</h2>
                                        <Radio.Group className='buttons-selection'
                                                     onChange={(value) => searchTickerPerBase(value)}>
                                            <Radio className='buttons-selection' value={1}>Jira</Radio>
                                            <Radio className='buttons-selection' value={2}>Otrs</Radio>
                                        </Radio.Group>
                                    </div>
                                </div>
                                {selectedKey.length === 0 ? (<div className="button">
                                    <button onClick={() => aproveAll()}>
                                        Aprovar
                                    </button>
                                </div>) : (<div className="button">
                                    <button className="contest" onClick={() => handleContestTicketView()}>
                                        Contestar
                                    </button>
                                </div>)}
                            </div>
                            <div>
                                <Table
                                    rowSelection={{
                                        hideSelectAll: true,
                                        onChange: (value) => {
                                            changeItemSelected(value)
                                        }
                                    }}
                                    scroll={{y: 200}}
                                    pagination={false}
                                    columns={columns}
                                    dataSource={data}
                                >
                                </Table>
                            </div>
                        </>
                    )}
                </>
            ) : (
                <ContestHours selectedTickets={selectedTickets} handleContestTicketView={handleContestTicketView}/>
            )}
        </>
    )
}

export {ProfessionalTickets}
