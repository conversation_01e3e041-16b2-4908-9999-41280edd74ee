import { Table, Row, Col, Typography } from "antd";
import "../../../styles/table.css";
import moment from "moment";
import { useEffect, useState } from "react";
import {
  getUpdatedContractHours,
  groupBy,
  renderTotalHours,
  renderTotalHoursByITSM,
} from "../../../controllers/cockpit/cockpitController";

function AverageConsumptionTable(props) {
  const { data, index, showModal } = props;
  const { Text } = Typography;
  const [contractWithChangedHours, setContractWithChangedHours] = useState([]);
  const currentData = data[index].tickets;

  const getMostRecentChangedHours = (contractsWithChangedHours) => {
    let mostRecentChangedHours = [];
    contractsWithChangedHours.forEach((contract) => {
      if (contract.length > 0) {
        mostRecentChangedHours.push(
          contract.sort(
            (a, b) =>
              new Date(b.updated_consumption_hours) -
              new Date(a.updated_consumption_hours)
          )[0]
        );
      }
    });
    return mostRecentChangedHours;
  };

  useEffect(() => {
    const loadData = async () => {
      let changedHoursData = await getUpdatedContractHours(
        currentData,
        "itsm_id",
        "contract_id"
      );
      changedHoursData = groupBy(changedHoursData, "itsm_id");
      changedHoursData = getMostRecentChangedHours(
        Object.values(changedHoursData)
      );

      setContractWithChangedHours(changedHoursData);
    };

    loadData();
  }, [props]);
  const columns = [
    {
      title: "ID do contrato",
      dataIndex: "contract_id",
      key: "contract_id",
      // render: (client) => <p className="table-font">{client}</p>,
    },
    {
      title: "Nome do contrato",
      dataIndex: "contract_name",
      key: "contract_name",
      // render: (contractName) => <p className="table-font">{contractName}</p>,
    },
    {
      title: "Mês",
      dataIndex: "create_time",
      key: "month",
      render: (ticket) => (
        <p style={{ textTransform: "capitalize" }}>
          {moment(ticket).format("MMMM")}
        </p>
      ),
    },
    {
      title: "Total de horas",
      dataIndex: "contract_total_hours",
      key: "contract_total_hours",
      sorter: (a, b) => a.contract_total_hours - b.contract_total_hours,
      render: (totalHours, data) => {
        if (
          contractWithChangedHours.find(
            (contract) => contract.itsm_id == data.contract_id
          )
        ) {
          return renderTotalHoursByITSM(
            totalHours,
            { hasChangedHours: true, id: data.contract_id },
            contractWithChangedHours
          );
        }
        return <Text>{totalHours}</Text>;
      },
    },
    {
      title: "Horas consumidas",
      dataIndex: "ticket_total_consumed_min",
      key: "ticket_total_consumed_min",
      // sorter: (a, b) => a.averageHours - b.averageHours,
      // render: (averageHours) => <p className="table-font">{averageHours}</p>,
    },
    {
      title: "Caretira",
      dataIndex: "wallet",
      key: "wallet",
      // render: (month) => <p className="table-font">{month}</p>,
    },
  ];

  return currentData.length > 0 ? (
    <Row style={{ marginTop: "20px" }}>
      <Col>
        <Table
          scroll={{ x: 400 }}
          pagination={{ pageSize: 5 }}
          columns={columns}
          dataSource={currentData}
        />
      </Col>
    </Row>
  ) : (
    <Row style={{ height: "250px" }} align="middle" justify="center">
      <Col>
        <Text>Sem dados*</Text>
      </Col>
    </Row>
  );
}

export default AverageConsumptionTable;
