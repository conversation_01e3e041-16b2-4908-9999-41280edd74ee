import React from 'react';
import { render, screen, waitFor, act } from '@testing-library/react';
import { AuthProvider, useAuth } from '../useAuth';
import { authService } from '../../services/authService';

// Mock the authService
jest.mock('../../services/authService', () => ({
  authService: {
    migrateFromLocalStorage: jest.fn(),
    isAuthenticated: jest.fn(),
    getUserInfo: jest.fn(),
    setAuthToken: jest.fn(),
    logout: jest.fn(),
    setUserInfo: jest.fn(),
  }
}));

// Test component that uses the useAuth hook
const TestComponent = () => {
  const { user, isLoading, isAuthenticated, login, logout, hasPermission } = useAuth();

  return (
    <div>
      <div data-testid="loading">{isLoading ? 'Loading' : 'Not Loading'}</div>
      <div data-testid="authenticated">{isAuthenticated ? 'Authenticated' : 'Not Authenticated'}</div>
      <div data-testid="user">{user ? JSON.stringify(user) : 'No User'}</div>
      <button data-testid="login-btn" onClick={() => login('token', { name: 'Test User' })}>
        Login
      </button>
      <button data-testid="logout-btn" onClick={logout}>
        Logout
      </button>
      <div data-testid="permission">{hasPermission('admin') ? 'Has Admin' : 'No Admin'}</div>
    </div>
  );
};

const renderWithAuthProvider = (component) => {
  return render(
    <AuthProvider>
      {component}
    </AuthProvider>
  );
};

describe('useAuth Hook', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('AuthProvider initialization', () => {
    it('should initialize with loading state', async () => {
      authService.migrateFromLocalStorage.mockResolvedValue();
      authService.isAuthenticated.mockResolvedValue(false);

      renderWithAuthProvider(<TestComponent />);

      expect(screen.getByTestId('loading')).toHaveTextContent('Loading');
    });

    it('should set authenticated state when user is authenticated', async () => {
      const mockUser = { name: 'John Doe', email: '<EMAIL>' };
      
      authService.migrateFromLocalStorage.mockResolvedValue();
      authService.isAuthenticated.mockResolvedValue(true);
      authService.getUserInfo.mockReturnValue(mockUser);

      renderWithAuthProvider(<TestComponent />);

      await waitFor(() => {
        expect(screen.getByTestId('loading')).toHaveTextContent('Not Loading');
      });

      expect(screen.getByTestId('authenticated')).toHaveTextContent('Authenticated');
      expect(screen.getByTestId('user')).toHaveTextContent(JSON.stringify(mockUser));
    });

    it('should set unauthenticated state when user is not authenticated', async () => {
      authService.migrateFromLocalStorage.mockResolvedValue();
      authService.isAuthenticated.mockResolvedValue(false);

      renderWithAuthProvider(<TestComponent />);

      await waitFor(() => {
        expect(screen.getByTestId('loading')).toHaveTextContent('Not Loading');
      });

      expect(screen.getByTestId('authenticated')).toHaveTextContent('Not Authenticated');
      expect(screen.getByTestId('user')).toHaveTextContent('No User');
    });

    it('should handle initialization errors gracefully', async () => {
      authService.migrateFromLocalStorage.mockRejectedValue(new Error('Migration failed'));
      authService.isAuthenticated.mockRejectedValue(new Error('Auth check failed'));

      renderWithAuthProvider(<TestComponent />);

      await waitFor(() => {
        expect(screen.getByTestId('loading')).toHaveTextContent('Not Loading');
      });

      expect(screen.getByTestId('authenticated')).toHaveTextContent('Not Authenticated');
    });
  });

  describe('login function', () => {
    it('should successfully login user', async () => {
      const mockUser = { name: 'Test User', email: '<EMAIL>' };
      
      authService.migrateFromLocalStorage.mockResolvedValue();
      authService.isAuthenticated.mockResolvedValue(false);
      authService.setAuthToken.mockResolvedValue(true);

      renderWithAuthProvider(<TestComponent />);

      await waitFor(() => {
        expect(screen.getByTestId('loading')).toHaveTextContent('Not Loading');
      });

      await act(async () => {
        screen.getByTestId('login-btn').click();
      });

      await waitFor(() => {
        expect(screen.getByTestId('authenticated')).toHaveTextContent('Authenticated');
      });

      expect(authService.setAuthToken).toHaveBeenCalledWith('token', mockUser);
    });

    it('should handle login failure', async () => {
      authService.migrateFromLocalStorage.mockResolvedValue();
      authService.isAuthenticated.mockResolvedValue(false);
      authService.setAuthToken.mockResolvedValue(false);

      renderWithAuthProvider(<TestComponent />);

      await waitFor(() => {
        expect(screen.getByTestId('loading')).toHaveTextContent('Not Loading');
      });

      await act(async () => {
        screen.getByTestId('login-btn').click();
      });

      expect(screen.getByTestId('authenticated')).toHaveTextContent('Not Authenticated');
    });
  });

  describe('logout function', () => {
    it('should successfully logout user', async () => {
      const mockUser = { name: 'John Doe', email: '<EMAIL>' };
      
      authService.migrateFromLocalStorage.mockResolvedValue();
      authService.isAuthenticated.mockResolvedValue(true);
      authService.getUserInfo.mockReturnValue(mockUser);
      authService.logout.mockResolvedValue();

      renderWithAuthProvider(<TestComponent />);

      await waitFor(() => {
        expect(screen.getByTestId('authenticated')).toHaveTextContent('Authenticated');
      });

      await act(async () => {
        screen.getByTestId('logout-btn').click();
      });

      await waitFor(() => {
        expect(screen.getByTestId('authenticated')).toHaveTextContent('Not Authenticated');
      });

      expect(authService.logout).toHaveBeenCalled();
    });
  });

  describe('hasPermission function', () => {
    it('should return true for admin permission when user has admin role', async () => {
      const mockUser = { name: 'Admin User', permission: 'admin' };
      
      authService.migrateFromLocalStorage.mockResolvedValue();
      authService.isAuthenticated.mockResolvedValue(true);
      authService.getUserInfo.mockReturnValue(mockUser);

      renderWithAuthProvider(<TestComponent />);

      await waitFor(() => {
        expect(screen.getByTestId('permission')).toHaveTextContent('Has Admin');
      });
    });

    it('should return false for admin permission when user does not have admin role', async () => {
      const mockUser = { name: 'Regular User', permission: 'user' };
      
      authService.migrateFromLocalStorage.mockResolvedValue();
      authService.isAuthenticated.mockResolvedValue(true);
      authService.getUserInfo.mockReturnValue(mockUser);

      renderWithAuthProvider(<TestComponent />);

      await waitFor(() => {
        expect(screen.getByTestId('permission')).toHaveTextContent('No Admin');
      });
    });

    it('should return false when user is not authenticated', async () => {
      authService.migrateFromLocalStorage.mockResolvedValue();
      authService.isAuthenticated.mockResolvedValue(false);

      renderWithAuthProvider(<TestComponent />);

      await waitFor(() => {
        expect(screen.getByTestId('permission')).toHaveTextContent('No Admin');
      });
    });
  });

  describe('useAuth without provider', () => {
    it('should throw error when used outside AuthProvider', () => {
      // Suppress console.error for this test
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

      expect(() => {
        render(<TestComponent />);
      }).toThrow('useAuth must be used within an AuthProvider');

      consoleSpy.mockRestore();
    });
  });
});
