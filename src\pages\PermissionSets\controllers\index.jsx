import { message } from "antd";
import { logNewAuditAction } from "../../../controllers/audit/logNewAuditAction";
import { dsmApiProvider } from "../../../provider/dsm-api-provider";
import {
  dynamoDelete,
  dynamoPost,
  dynamoPut,
} from "../../../service/apiDsmDynamo";
import { switchRoleProvider } from "../../../provider/switch-role-api-provider";
import { setSwitchRoleState } from "../../../store/actions/switch-role-action";

export const getPermissionSets = async (setSolicitations = () => {}) => {
  const provider = dsmApiProvider();

  let arr = [];

  let { data } = await provider.get("read/all/0", {
    headers: {
      DynamoDB: `${
        process.env.REACT_APP_STAGE !== "prod" ? "hml-" : ""
      }sso-accounts`,
    },
  });

  data.data.Items.forEach((user) => {
    arr.push({
      linked: user.linked,
      user: user.user,
      arn: user.arn,
      id: user.id,
      users: user.users,
      name: user.name,
    });
  });

  setSolicitations(arr);
  setSwitchRoleState({ field: "permissionSets", value: arr });
};

export const deleteMultiplePermissionSets = async (
  multiplePermissionSets,
  setSolicitations
) => {
  let promises = [];

  multiplePermissionSets.forEach((userId) => {
    promises.push(
      dynamoDelete(
        `${process.env.REACT_APP_STAGE !== "prod" ? "hml-" : ""}sso-accounts`,
        userId
      )
    );
  });

  try {
    await Promise.all(promises);
    await getPermissionSets(setSolicitations);
    const username = localStorage.getItem("@dsm/username");
    const title = "Deleção de PermissionSet";
    const description = `${username} deletou ${multiplePermissionSets.length} permission sets`;
    logNewAuditAction(username, title, description);
    return true;
  } catch (err) {
    return false;
  }
};

export const deletePermissionSet = async (setSolicitations, item) => {
  try {
    await dynamoDelete(
      `${process.env.REACT_APP_STAGE !== "prod" ? "hml-" : ""}sso-accounts`,
      item.id
    );
    const username = localStorage.getItem("@dsm/username");
    const title = "Deleção de PermissionSet";
    const description = `${username} deletou o permission set do usuário ${item.user}`;
    logNewAuditAction(username, title, description);
    message.success("Permission Set deletado com sucesso!");
    getPermissionSets(setSolicitations);
  } catch (err) {
    message.error(
      "Ops... Ocorreu um erro ao tentar deletar este permission set."
    );
  }
};

export const linkPermissionSet = async (setSolicitations, item, setLoading) => {
  try {
    setLoading(true);
    await dynamoPut(
      `${process.env.REACT_APP_STAGE !== "prod" ? "hml-" : ""}sso-accounts`,
      item.id,
      {
        linked: 1,
      }
    );

    const username = localStorage.getItem("@dsm/username");
    const title = "Atualização de PermissionSet";
    const description = `${username} atualizou o permission set do usuário ${item.user}`;
    logNewAuditAction(username, title, description);
    message.success("Atualização realizada com sucesso!");
    setLoading(false);
    getPermissionSets(setSolicitations);
  } catch (err) {
    message.error("Ops... Ocorreu um erro ao tentar atualizar esta permissão.");
    setLoading(false);
  }
};

export const submitPermissionSetData = async (
  form,
  setSolicitations,
  type,
  setLoading,
  item,
  permissionSets,
  setOpen,
  setFormLoading
) => {
  setLoading(true);
  setFormLoading(true);
  try {
    const fieldsData = form.getFieldsValue();

    let userAlreadyAttributed = checkIfUserIsAlreadyLinkedToPermissionSet(
      permissionSets,
      fieldsData.users,
      fieldsData.arn
    );
    let permissionSetAlreadyExists = checkIfPermissionSetAlreadyExists(
      permissionSets,
      fieldsData.name,
      fieldsData.arn
    );
    if (userAlreadyAttributed) {
      message.error(
        <>
          O usuário <strong>{userAlreadyAttributed.user}</strong> já está
          atribuído ao permission set{" "}
          <strong>{userAlreadyAttributed.permissionSet}</strong>.
        </>
      );
      setFormLoading(false);
      setLoading(false);
      return;
    }

    if (type !== "edit") {
      if (permissionSetAlreadyExists === "name") {
        message.error("Já existe um permission set com esse nome.");
        setFormLoading(false);
        setLoading(false);
        return;
      }

      if (permissionSetAlreadyExists === "arn") {
        message.error("Já existe um permission set com esse ARN.");
        setFormLoading(false);
        setLoading(false);
        return;
      }
    }

    let body = {
      ...fieldsData,
      linked: 0,
      user: item.user || "void.user",
      created_at: new Intl.DateTimeFormat("pt-BR").format(new Date()),
      updated_at: new Intl.DateTimeFormat("pt-BR").format(new Date()),
    };

    if (type === "edit") {
      await dynamoPut(
        `${process.env.REACT_APP_STAGE !== "prod" ? "hml-" : ""}sso-accounts`,
        item.id,
        body
      );
    } else {
      await dynamoPost(
        `${process.env.REACT_APP_STAGE !== "prod" ? "hml-" : ""}sso-accounts`,
        body
      );
    }
    message.success(
      `Permission Set ${type === "edit" ? "editado" : "criado"} com sucesso!`
    );
    await getPermissionSets(setSolicitations);
    setLoading(false);
    setFormLoading(false);
    setOpen(false);
  } catch (err) {
    if (err.code === 400) {
      message.error(err.message);
    } else {
      console.error(err);
      message.error(
        "Ops... Ocorreu um erro ao tentar criar este permission set."
      );
    }
    setLoading(false);
  }
};

export const checkIfUserIsAlreadyLinkedToPermissionSet = (
  permissionSets,
  users,
  arn
) => {
  let checkExistence = false;
  permissionSets.forEach((permissionSet) => {
    if (permissionSet.users) {
      users.forEach((user) => {
        if (
          permissionSet.users.find((x) => x.email === user.email) &&
          arn !== permissionSet.arn
        ) {
          checkExistence = {
            user: user.email,
            permissionSet: permissionSet.name,
          };
        }
      });
    }
  });

  return checkExistence;
};

const checkIfPermissionSetAlreadyExists = (permissionSets, psName, arn) => {
  let checkExistence = false;
  permissionSets.forEach((permissionSet) => {
    if (permissionSet.name === psName) {
      checkExistence = "name";
    } else {
      if (permissionSet.arn === arn) {
        checkExistence = "arn";
      }
    }
  });

  return checkExistence;
};
