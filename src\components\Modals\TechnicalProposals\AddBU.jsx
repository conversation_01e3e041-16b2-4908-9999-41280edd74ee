import {
  PlusOutlined,
  DeleteOutlined,
  ExceptionOutlined,
  CheckOutlined
} from '@ant-design/icons'
import { useEffect, useState } from 'react'
import {
  Button,
  Modal,
  Row,
  Col,
  Table,
  Typography,
  Input,
  Form,
  Popconfirm,
  message
} from 'antd'
import { useLocation } from 'react-router-dom'

export const AddBU = busParent => {
  const { Title } = Typography
  const { state } = useLocation()
  const [buForm] = Form.useForm()
  const [showModal, setShowModal] = useState()
  const { bus, setBus } = busParent
  const [childBUs, setChildBUs] = useState([])
  const [currentBU, setCurrentBU] = useState({
    id: '',
    name: ''
  })
  const [listInput, setListInput] = useState(state?.bus)

  const handleSubmit = e => {
    e.preventDefault()
    setBus(childBUs)
    setShowModal(false)
  }

  const handleRemoveItem = props => {
    const prev = childBUs
    setChildBUs(prev.filter(bu => bu.name !== props.name))
    message.success(
      `Unidade de Negócio "${props.name}" removida com sucesso!`
    )
  }

  const handleCloseModal = () => {
    setCurrentBU({})
    buForm.resetFields()
    setShowModal(false)
  }

  const handleAddItem = e => {
    if (childBUs?.find(bu => bu?.name === currentBU?.name)) {
      message.error(`Unidaded e Negócio "${currentBU.name}" já existe!`)
    } else {
      setChildBUs(prev => [...prev, currentBU])
      setCurrentBU({})
      buForm.resetFields(['name'])
      message.success(
        `Unidade de Negócio "${currentBU.name}" adicionada com sucesso!`
      )
    }
  }

  const columns = [
    {
      title: 'Unidades de Negócio',
      dataIndex: 'name',
      key: 'name',
      align: 'center',
      sorter: (a, b) => a?.name?.toString().localeCompare(b?.name?.toString())
    },
    {
      title: 'Remover',
      dataIndex: 'remove',
      key: 'remove',
      align: 'center',
      render: (id, props) => {
        return (
          <Popconfirm
            title={'Tem certeza que deseja remover esta Unidade de Negócio?'}
            okText={'Sim'}
            cancelText={'Não'}
            onConfirm={e => {
              handleRemoveItem(props)
            }}
          >
            <Button danger type="text" icon={<DeleteOutlined />}></Button>
          </Popconfirm>
        )
      }
    }
  ]

  useEffect(() => {
    buForm.resetFields(['bu'])
    setChildBUs(busParent.busParent)
  }, [busParent.busParent])

  return (
    <>
      <Button
        type="text"
        onClick={() => {
          setChildBUs(busParent.busParent)
          setCurrentBU({})
          buForm.resetFields()
          setShowModal(true)
        }}
      >
        <PlusOutlined />
      </Button>
      <Modal
        title="Unidades de Negócios"
        open={showModal}
        width={'40%'}
        closable={false}
        destroyOnClose={true}
        footer={null}
        onCancel={() => {
          setShowModal(false)
          setBus(childBUs)
        }}
      >
        <Form layout="horizontal" form={buForm} onFinish={handleSubmit}>
          <Row
            gutter={[16, 16]}
            justify="center"
            style={{ marginBottom: '1rem' }}
          >
            <Col span={24}>
              <Title
                level={5}
                style={{
                  fontWeight: '400',
                  fontSize: '14px'
                }}
              >
                Unidade de Negócio
              </Title>
              <Form.Item name="name">
                <Input
                  required
                  onChange={e => {
                    setCurrentBU({
                      ...currentBU,
                      name: e.target.value
                    })
                  }}
                />
              </Form.Item>
              <Row justify="center">
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={() => {
                    handleAddItem()
                  }}
                  disabled={!currentBU.name}
                >
                  Adicionar
                </Button>
              </Row>
            </Col>
          </Row>
        </Form>

        <Row style={{ marginBottom: '1rem' }}>
          <Col span={24}>
            {childBUs?.length > 0 ? (
              <Table
                rowKey="id"
                dataSource={childBUs.map((item, index) => {
                  return {
                    id: index,
                    name: item.name
                  }
                })}
                columns={columns}
                scroll={{ x: 100 }}
                pagination={false}
              ></Table>
            ) : (
              <Row justify="center" gutter={[24, 16]}>
                <Col span={24}>
                  <Row justify="center">
                    <ExceptionOutlined style={{ fontSize: '5rem' }} />
                  </Row>
                  <Row justify="center">
                    <Title level={5} style={{ fontWeight: '400' }}>
                      Não existem outros contatos inseridos selecionados
                    </Title>
                  </Row>
                </Col>
              </Row>
            )}
          </Col>
        </Row>

        <Row gutter={[16, 16]} justify={'space-between'}>
          <Button type="text-color" onClick={handleCloseModal}>
            Cancelar <DeleteOutlined />
          </Button>
          <Button type="primary" onClick={handleSubmit}>
            Salvar <CheckOutlined />
          </Button>
        </Row>
      </Modal>
    </>
  )
}
