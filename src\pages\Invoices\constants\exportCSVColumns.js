export const CSVColumns = [
  {
    label: "Dolar",
    key: "dolar",
  },
  {
    label: "Bill Payer Account",
    key: "bill_payer_account_id",
  },
  {
    label: "Account ID",
    key: "line_item_usage_account_id",
  },
  {
    label: "Customer",
    key: "customer",
  },
  { label: "Billing Entity", key: "bill_billing_entity" },
  {
    label: "Invoice ID",
    key: "bill_invoice_id",
  },
  { label: "Month", key: "month" },
  { label: "Year", key: "year" },
  {
    label: "Charges (US$)",
    key: "charges",
  },
  {
    label: "SPP Discount",
    key: "spp_discount",
  },
  {
    label: "EDP Discount",
    key: "total_edp_discount",
  },
  {
    label: "Bundled Discount",
    key: "total_bundled_discount",
  },
  {
    label: "Saving Plans Discount",
    key: "total_saving_plans_discount",
  },
  {
    label: "Credits",
    key: "total_credits",
  },
  {
    label: "Outros Descontos",
    key: "other_discounts",
  },
  { label: "Tax (US$)", key: "tax" },
  { label: "Tax (%)", key: "tax_percentage" },
  { label: "Total (US$)", key: "unblended_cost" },
  { label: "Total (R$)", key: "conversion" },
  {
    label: "Total Discount",
    key: "total_discount",
  },
  {
    label: "Line Item Legal Entity",
    key: "line_item_legal_entity",
  },
  {
    label: "Final Balance (US$)",
    key: "final_balance",
  },
  {
    label: "Final Balance (R$)",
    key: "final_balance_real",
  },
];
