import { useEffect, useMemo, useState } from "react";
import useS<PERSON> from "swr";
import axios from "axios";
import moment from "moment";
import { conceededColumns } from "../../exportCSVColumns/conceededTable";
import { formatConceededExportData } from "../../formatExportHelpers/conceeded";
import { Counter } from "../../../../components/Counter";
import { DynamicTable } from "../../../../components/Table/DynamicTable";
export const ConceededTable = ({
  search,
  searchDate,
  setExportData,
  setExportColumns,
  searchTrigger
}) => {
  const columns = [
    {
      title: "Número do Ticket",
      dataIndex: "ticket",
      key: "ticket",
      align: "center",
      width: "10%",
    },
    {
      title: "Cliente",
      dataIndex: "customer",
      key: "customer",
      sorter: (a, b) => a.customer.localeCompare(b.customer),
    },
    {
      title: "Solicitante",
      dataIndex: "requester",
      key: "requester",
      align: "center",
      sorter: (a, b) => a.requester.localeCompare(b.requester),
    },
    {
      title: "Aprovador",
      dataIndex: "approver",
      key: "approver",
      align: "center",
      sorter: (a, b) => a.approver.localeCompare(b.approver),
    },
    {
      title: "Data da Solicitação",
      dataIndex: "requestDate",
      key: "requestDate",
      align: "center",
      sorter: (a, b) => a.requestDate.localeCompare(b.requestDate),
      render: (date) => {
        return date.replaceAll("-", "/").split(":").slice(0, 2).join(":");
      },
    },
    {
      title: "Horas solicitadas",
      dataIndex: "requestedTime",
      key: "requestedTime",
      align: "center",
    },
    {
      title: "Horas concedidas",
      dataIndex: "conceededTime",
      key: "conceededTime",
      align: "center",
    },
    {
      title: "Data de concessão/negação",
      dataIndex: "conceedDate",
      key: "conceedDate",
      align: "center",
      sorter: (a, b) => a.conceedDate.localeCompare(b.conceedDate),
    },
    {
      title: "Data de revogação",
      dataIndex: "revokeDate",
      key: "revokeDate",
      align: "center",
      sorter: (a, b) => a.revokeDate.localeCompare(b.revokeDate),
    },
    {
      title: "Conta solicitada",
      dataIndex: "requestedAccount",
      key: "requestedAccount",
      align: "center",
    },
  ];
  const [loading, setLoading] = useState(false);
  const [formatedTableData, setFormatedTableData] = useState([]);
  let firstdayOfMonth = moment().startOf("month").format("MM-DD-YYYY");
  let lastdayOfMonth = moment().endOf("month").format("MM-DD-YYYY");
  const searchFields = [
    "ticket",
    "customer",
    "requester",
    "approver",
    "requestDate",
    "requestedAccount",
  ];

  const getSolicitations = async () => {
    const jwt = localStorage.getItem("jwt");
    try {
      setLoading(true);
      const { data } = await axios.get(
        `${process.env.REACT_APP_API_PERMISSION}/read/audits`,
        {
          params: {
            type: 1,
            startDate: searchDate.length > 0 ? searchDate[0] : firstdayOfMonth,
            endDate: searchDate.length > 0 ? searchDate[1] : lastdayOfMonth,
          },
          headers: {
            Authorization: jwt,
          },
        }
      );
      setLoading(false);
      return data.data.Items;
    } catch (error) {
      setLoading(false);
      console.log({ error });
    }
  };

  const { data, mutate } = useSWR(
    `${process.env.REACT_APP_API_PERMISSION}-switch-role-audits-conceeded`,
    getSolicitations
  );

  const formatTableData = (data) => {
    let formatedData = [];

    data.forEach((item) => {
      formatedData.push({
        ticket: item.client_ticket,
        customer: item.client_name,
        requester: item.username,
        requestDate: moment(new Date(item.created_at)).format("DD-MM-YYYY HH:mm:ss"),
        requestedTime: item.requestedTime / 60,
        conceededTime: item.time ? item.time / 60 : "Solicitação negada",
        conceedDate: item.approveDate
          ? moment(new Date(item.approveDate)).format("DD/MM/YYYY HH:mm")
          : moment(new Date(item.updated_at)).format("DD/MM/YYYY HH:mm"),
        requestedAccount: item.account_id,
        revokeDate: item.revokedDate
          ? moment(new Date(item.revokedDate)).format("DD/MM/YYYY HH:mm")
          : "-",
        approver: item.approver
          ? item.approver
          : item.revoker
          ? item.revoker
          : item.denier
          ? item.denier
          : "-",
      });
    });
    setFormatedTableData(formatedData);
    setExportData(formatConceededExportData(formatedData));
    setExportColumns(conceededColumns);
  };

  const filteredData = useMemo(() => {
    return formatedTableData.filter((item) => {
      if (search === "") return item;
      for (let i = 0; i < searchFields.length; i++) {
        if (
          item[searchFields[i]]
            .toString()
            .toLowerCase()
            .includes(search.toLowerCase())
        ) {
          return item;
        }
      }
    });
  }, [search, formatedTableData]);
  useEffect(() => {
    if (data) {
      formatTableData(data);
      mutate();
    }
  }, [data, searchTrigger]);
  return (
    <>
      <Counter tableData={filteredData} />
      <DynamicTable
        columns={columns}
        dataSource={filteredData}
        loading={loading}
        scroll={{ x: "100%" }}
      ></DynamicTable>
    </>
  );
};
