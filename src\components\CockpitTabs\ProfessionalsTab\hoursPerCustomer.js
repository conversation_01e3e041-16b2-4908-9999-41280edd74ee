import { Row, Col, Card, Progress, Typography, Skeleton, Checkbox } from "antd";
import { CheckSquareOutlined } from "@ant-design/icons";
import { differenceInHours } from "date-fns";
import moment from "moment";
import { useEffect, useState } from "react";
export const HoursPerCustomer = ({
  searchFilter,
  searchedDate,
  loading,
  allTimeByCustomerByMonth,
  notBillableContracts,
}) => {
  const { Text } = Typography;
  let currentDate = new Date();
  const [cardsCustomerData, setCardsCustomerData] = useState([]);
  const [billableFilter, setBillableFilter] = useState(false);
  const [notBillableFilter, setNotBillableFilter] = useState(false);

  const formatCardsData = async (currentData) => {
    setCardsCustomerData([]);
    currentData?.map((currentCustomer) => {
      let timePercentage =
        differenceInHours(
          moment(currentDate).toDate().getTime(),
          moment(currentCustomer.start_date).toDate().getTime()
        ) /
        differenceInHours(
          moment(currentCustomer.end_date).toDate().getTime(),
          moment(currentCustomer.start_date).toDate().getTime()
        );
      setCardsCustomerData((prevState) => [
        ...prevState,
        {
          customer_name: currentCustomer.customer_name,
          contract_name: currentCustomer.contract_name,
          contract_id: currentCustomer.contract_id,
          customer_id: currentCustomer.customer_id,
          hoursConsumed:
            currentCustomer.customer_total_consumed_min !== null
              ? parseInt(currentCustomer.customer_total_consumed_min) / 60
              : 0,
          timePassedPercentage: timePercentage < 0 ? 0 : timePercentage,
          contract_start_date: currentCustomer.start_date,
          contract_end_date: currentCustomer.end_date,
        },
      ]);
    });
  };

  const filterByBillableNotBillable = (currentData) => {
    let filteredData = [];
    if (billableFilter === true && notBillableFilter === false) {
      filteredData = currentData.filter((val) => {
        return notBillableContracts?.includes(val.contract_name);
      });
    } else if (billableFilter === false && notBillableFilter === true) {
      filteredData = currentData.filter((val) => {
        return !notBillableContracts?.includes(val.contract_name);
      });
    } else {
      filteredData = currentData;
    }
    return filteredData;
  };

  useEffect(() => {
    formatCardsData(allTimeByCustomerByMonth);
  }, [allTimeByCustomerByMonth]);

  useEffect(() => {
    if (billableFilter !== notBillableFilter) {
      setCardsCustomerData(filterByBillableNotBillable(cardsCustomerData));
    } else {
      if (searchFilter === "") {
        formatCardsData(allTimeByCustomerByMonth);
      } else {
        formatCardsData(allTimeByCustomerByMonth);
      }
    }
  }, [billableFilter, notBillableFilter]);

  return (
    <div style={{ height: "300px", width: "100%" }}>
      <Row
        style={{
          margin: "10px",
        }}
      >
        <Col>
          <Checkbox
            checked={billableFilter}
            onChange={() => {
              setBillableFilter(!billableFilter);
            }}
          >
            <Text>Não billados</Text>
          </Checkbox>
        </Col>
        <Col>
          <Checkbox
            checked={notBillableFilter}
            onChange={() => {
              setNotBillableFilter(!notBillableFilter);
            }}
          >
            <Text>Billados</Text>
          </Checkbox>
        </Col>
      </Row>

      {loading ? (
        <Card style={{ borderRadius: "15px" }}>
          <Skeleton active style={{ width: "200px" }} />
        </Card>
      ) : (
        <Row
          className="items"
          gutter={[24, 24]}
          style={{
            justifyContent: "center",
            overflow: "auto",
          }}
        >
          {cardsCustomerData?.length === 0 && loading === false ? (
            <Col>
              <Text>
                Nenhum trâmite de horas realizado no período de {searchedDate}
              </Text>
            </Col>
          ) : (
            cardsCustomerData
              ?.filter((val, index) => {
                if (searchFilter === "") {
                  return true;
                } else if (
                  val.customer_name
                    ?.toLowerCase()
                    ?.includes(searchFilter?.toLowerCase()) ||
                  val.contract_name
                    ?.toLowerCase()
                    ?.includes(searchFilter?.toLowerCase())
                ) {
                  return true;
                } else if (
                  notBillableContracts?.some(
                    (contract) => contract === val.contract_name
                  ) &&
                  notBillableFilter
                ) {
                  return true;
                } else if (
                  !notBillableContracts?.some(
                    (contract) => contract === val.contract_name
                  ) &&
                  billableFilter
                ) {
                  return true;
                } else {
                  return false;
                }
              })
              ?.map((val, index) => {
                return (
                  <Col span={24}>
                    <Card style={{ borderRadius: "15px", width: "100%" }}>
                      <>
                        <Text
                          style={{
                            textAlign: "left",
                          }}
                        >
                          Cliente: {val.customer_name}
                        </Text>
                        <Progress
                          className="text-green-800"
                          percent={val.timePassedPercentage * 100}
                          strokeColor={
                            val.timePassedPercentage * 100 > 75
                              ? "#dd0000"
                              : "#435E91"
                          }
                          showInfo={false}
                        ></Progress>
                        <Row>
                          <Text>
                            {Math.floor(val.hoursConsumed) +
                              "h" +
                              Math.floor(
                                (val.hoursConsumed -
                                  Math.floor(val.hoursConsumed)) *
                                  60
                              ) +
                              "m"}{" "}
                            utilizadas no mês de {searchedDate}
                          </Text>
                        </Row>
                        <Row>
                          <Text>Contrato: {val.contract_name}</Text>
                        </Row>
                        <Row>
                          <Text>
                            {notBillableContracts?.some(
                              (contract) => contract === val.contract_name
                            ) ? (
                              <Text style={{ color: "#43914F" }}>
                                <CheckSquareOutlined /> Não Billada
                              </Text>
                            ) : (
                              <Text style={{ color: "#ff914F" }}>
                                <CheckSquareOutlined />
                                Billada
                              </Text>
                            )}
                          </Text>
                        </Row>
                        <Row>
                          <Text>
                            {moment().toDate().getTime() >
                            moment(val.contract_end_date).toDate().getTime() ? (
                              <Text style={{ color: "#dd0000" }}>
                                <CheckSquareOutlined /> Contrato Expirado
                              </Text>
                            ) : (
                              <Text style={{ color: "#43914F" }}>
                                <CheckSquareOutlined /> Contrato Ativo
                              </Text>
                            )}
                          </Text>
                        </Row>
                      </>
                    </Card>
                  </Col>
                );
              })
          )}
        </Row>
      )}
    </div>
  );
};
