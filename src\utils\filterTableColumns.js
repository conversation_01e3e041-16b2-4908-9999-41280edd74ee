export const filterTableColumns = (columns, permissions, filterParams) => {
  const filteredCoumns = [];
  columns.forEach((column) => {
    if (
      permissions.find((permission) => permission.code === column.code) &&
      !filterParams
    ) {
      filteredCoumns.push(column);
    } else if (
      permissions.find((permission) => permission.code === column.code) &&
      filterParams.includes(column.code)
    ) {
      filteredCoumns.push(column);
    }
  });
  return filteredCoumns;
};
