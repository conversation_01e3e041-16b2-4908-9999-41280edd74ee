import React, { useEffect, useState } from "react";
import { Row, Col, Typography, Select, Form } from "antd";
import { useSelector, shallowEqual } from "react-redux";

import { setTechnicalProposalState } from "../../../store/actions/technical-proposal-action";

import axios from "axios";

const { Title } = Typography;
const { Option } = Select;

export const ArchitectsField = () => {
    const architects = useSelector(state => state.technicalProposal.architects, shallowEqual)

    const [architectList, setArchitectList] = useState([]);
    const [loading, setLoading] = useState([])

    useEffect(() => {
        getUsers()
    }, [])

    async function getUsers() {
        try {
            setLoading(true)

            // Usar authService para headers consistentes
            const { authService } = await import('../../../services/authService');
            const headers = authService.getAuthHeaders();

            const { data } = await axios.get(
                `${process.env.REACT_APP_API_PERMISSION}/cognito/read`,
                { headers }
            );

            const users = data.data.map(user => user.email)

            setArchitectList(users);
            setLoading(false)
        } catch (error) {
            console.warn('⚠️ Erro ao carregar arquitetos (não crítico):', error.message);
            console.log('🔄 Continuando sem lista de arquitetos...');
            setArchitectList([]);
            setLoading(false)
        }
    }

    return (
        <Col sm={24} md={12} lg={6}>
            <Row>
                <Title level={5} style={{ fontWeight: "400" }}>
                    Arquitetos
                </Title>
            </Row>
            <Row>
                <Col span={24}>
                        <Select
                            id="architects"
                            loading={loading}
                            mode="multiple"
                            value={architects}
                            showSearch
                            placeholder={"Nome do arquiteto"}
                            optionFilterProp="children"
                            filterOption={(input, option) =>
                                option?.children
                                    ?.toLowerCase()
                                    .includes(input?.toLowerCase())
                            }
                            filterSort={(optionA, optionB) =>
                                optionA?.children
                                    ?.toLowerCase()
                                    .localeCompare(optionB.children?.toLowerCase())
                            }
                            onChange={(e) => setTechnicalProposalState({ field: 'architects', value: e })}
                            style={{ width: '100%' }}
                        >

                            {
                                architectList.map((t, index) =>
                                    <Option key={index} value={t}>{t}</Option>
                                )
                            }

                        </Select>
                </Col>
            </Row>
        </Col>
    )
}