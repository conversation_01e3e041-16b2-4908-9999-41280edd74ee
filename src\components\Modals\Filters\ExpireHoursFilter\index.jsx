import { useEffect } from "react";
import { React, useState } from "react";
import { Select, Button, Form, DatePicker, Row, Col, Typography } from "antd";
import { CloseSquareFilled } from "@ant-design/icons";

function ExpireHoursFilter(props) {
  const { Title } = Typography;
  const { Option } = Select;
  const [form] = Form.useForm();

  function onFilterFinish(values) {
    props.handlePopoverVisibility();
  }

  function onFilterFailed() {}

  function clear() {
    form.resetFields();
  }

  useEffect(() => {}, []);

  return (
    <Form
      form={form}
      name="basic"
      onFinish={onFilterFinish}
      onFinishFailed={onFilterFailed}
      autoComplete="off"
    >
      <Row justify="space-between">
        <Col>
          <Title level={5} style={{ fontWeight: 400 }}>
            Horas expiradas/expirar
          </Title>
        </Col>
        <Col>
          <Button type="text" onClick={() => props.handlePopoverVisibility()}>
            X
          </Button>
        </Col>

        <Col span={24}>
          <Form.Item name="client">
            <Select
              showSearch
              className="w-full"
              placeholder="Cliente"
              optionFilterProp="children"
              filterOption={(input, option) =>
                option.props.children
                  .toLowerCase()
                  .indexOf(input.toLowerCase()) >= 0 ||
                option.props.value.toLowerCase().indexOf(input.toLowerCase()) >=
                  0
              }
            >
              <Option value="all">Todos</Option>
              <Option value="sust">Sust-1</Option>
              <Option value="exxx">Exxx</Option>
            </Select>
          </Form.Item>
        </Col>
        <Col span={24}>
          <Form.Item name="contract">
            <Select
              showSearch
              className="w-full"
              placeholder="Contrato"
              optionFilterProp="children"
              filterOption={(input, option) =>
                option.props.children
                  .toLowerCase()
                  .indexOf(input.toLowerCase()) >= 0 ||
                option.props.value.toLowerCase().indexOf(input.toLowerCase()) >=
                  0
              }
            >
              <Option value="all">Todos</Option>
              <Option value="logikee">Darede TI</Option>
              <Option value="darede">Service Up</Option>
            </Select>
          </Form.Item>
        </Col>
        <Col span={24}>
          <Form.Item name="expireHours">
            <Select
              showSearch
              className="w-full"
              placeholder="Vencidas"
              optionFilterProp="children"
              filterOption={(input, option) =>
                option.props.children
                  .toLowerCase()
                  .indexOf(input.toLowerCase()) >= 0 ||
                option.props.value.toLowerCase().indexOf(input.toLowerCase()) >=
                  0
              }
            >
              <Option value="all">Todos</Option>
              <Option value="sust">Sust-1</Option>
              <Option value="exxx">Exxx</Option>
            </Select>
          </Form.Item>
        </Col>
        <Col span={24}>
          <Form.Item name="date">
            <Row justify="start" align="middle">
              <Col span={2}>
                <label htmlFor="">Data: </label>
              </Col>
              <Col span={6}>
                <DatePicker />
              </Col>
            </Row>
          </Form.Item>
          <Form.Item name="buttons">
            <Row justify="space-between">
              <Col>
                <Button type="default" onClick={clear}>
                  {" "}
                  Limpar{" "}
                </Button>
              </Col>
              <Col>
                <Button htmlType="submit" type="primary">
                  {" "}
                  Aplicar{" "}
                </Button>
              </Col>
            </Row>
          </Form.Item>
        </Col>
      </Row>
    </Form>
  );
}

export default ExpireHoursFilter;
