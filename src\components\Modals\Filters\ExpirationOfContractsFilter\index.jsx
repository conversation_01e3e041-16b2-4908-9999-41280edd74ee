import { React, useEffect, useState } from "react";
import { Button, Checkbox, Form, Row, Col, Select, Typography } from "antd";
import { CloseCircleOutlined } from "@ant-design/icons";
import { dynamoGet } from "../../../../service/apiDsmDynamo";
import { otrsGet } from "../../../../service/apiOtrs";

function ExpirationOfContractsFilter(props) {
  const { Title } = Typography;
  const { Option } = Select;
  const [form] = Form.useForm();
  const [allWallets, setAllWallets] = useState([]);
  const [contractTypes, setContractTypes] = useState([]);
  const [allProfessionals, setAllProfessionals] = useState([]);
  const [filterValues, setFilterValues] = useState(props?.filterSelected);

  function onFilterFinish(values) {
    props.filter(values);
    props.handleVisibleChange();
  }

  function clear() {
    setFilterValues({});
    form.resetFields();
  }

  async function getAllProfessionals() {
    const professionalNames = [];

    Object.entries(props.allProfessionals).forEach((item) => {
      const user = item[1];
      professionalNames.push({
        id: user.id,
        login: user.login,
        name: `${user.first_name}  ${user.last_name}`,
      });
    });

    setAllProfessionals(professionalNames);
  }

  function getContractTypes() {
    if (props.contractTypes) {
      setContractTypes(props.contractTypes);
    }
  }

  function getAllWallets() {
    if (props.wallets) {
      setAllWallets(props.wallets);
    }
  }

  const sortAlpha = (array) => {
    // Verificar se array é válido e é realmente um array
    if (!array || !Array.isArray(array)) {
      return [];
    }

    const sortedArray = array.sort((a, b) => {
      if (a.name > b.name) {
        return 1;
      }
      if (a.name < b.name) {
        return -1;
      }
      return 0;
    });

    return sortedArray;
  };

  useEffect(() => {
    getAllWallets();
    getAllProfessionals();
    getContractTypes();
  }, [
    props.wallets,
    props.allProfessionals,
    props.contractTypes,
    props?.filterSelected,
  ]);

  return (
    <Form
      form={form}
      style={{ display: "flex", flexDirection: "column", width: "230px" }}
      name="basic"
      initialValues={{
        remember: true,
      }}
      onFinish={onFilterFinish}
      autoComplete="off"
    >
      <Row justify="space-between" align="middle">
        <Title
          level={5}
          style={{ fontWeight: 400, margin: "0px", marginRight: "10px" }}
        >
          Vencimento por contrato
        </Title>
        <Button
          type="text"
          onClick={() => props.handleVisibleChange()}
          style={{ width: "25px", height: "25px", padding: "0px" }}
        >
          <CloseCircleOutlined style={{ color: "#868383" }} />
        </Button>
      </Row>
      <div
        className="checkbox"
        style={{ display: "flex", justifyContent: "center" }}
      >
        <Form.Item
          style={{ width: "50%", textAlign: "center" }}
          valuePropName="checked"
          name="active"
        >
          <Checkbox
            defaultChecked={filterValues?.active ? filterValues?.active : false}
          >
            Ativo
          </Checkbox>
        </Form.Item>

        <Form.Item
          style={{ width: "50%", textAlign: "center" }}
          valuePropName="checked"
          name="inactive"
        >
          <Checkbox
            defaultChecked={
              filterValues?.inactive ? filterValues?.inactive : false
            }
          >
            Inativo
          </Checkbox>
        </Form.Item>
      </div>

      <Form.Item name="carteira">
        <Select
          defaultValue={
            filterValues?.carteira ? filterValues?.carteira : undefined
          }
          loading={allWallets.length === 0}
          placeholder="Carteira"
        >
          {sortAlpha(allWallets).map((value, index) => {
            return (
              <Option key={index} value={value.name}>
                {value.name}
              </Option>
            );
          })}
        </Select>
      </Form.Item>

      <Form.Item name="sdm">
        <Select
          defaultValue={filterValues?.sdm ? filterValues?.sdm : undefined}
          showSearch
          className="w-full"
          placeholder="SDM"
          optionFilterProp="children"
          loading={allProfessionals.length === 0}
          filterOption={(input, option) =>
            option.props.children.toLowerCase().indexOf(input.toLowerCase()) >=
              0 ||
            option.props.value.toLowerCase().indexOf(input.toLowerCase()) >= 0
          }
        >
          {sortAlpha(allProfessionals).map((professional) => {
            return (
              <Option key={professional.id} value={professional.login}>
                {professional.name}
              </Option>
            );
          })}
        </Select>
      </Form.Item>

      <Form.Item name="contractType">
        <Select
          defaultValue={
            filterValues?.contractType ? filterValues?.contractType : undefined
          }
          showSearch
          style={{ width: "100%" }}
          placeholder="Tipo de contratos"
          optionFilterProp="children"
          loading={contractTypes.length === 0}
          filterOption={(input, option) =>
            option.props.children.toLowerCase().indexOf(input.toLowerCase()) >=
              0 ||
            option.props.value.toLowerCase().indexOf(input.toLowerCase()) >= 0
          }
        >
          {sortAlpha(contractTypes).map((type, index) => {
            return (
              <Option key={index} value={type.name}>
                {type.name}
              </Option>
            );
          })}
        </Select>
      </Form.Item>

      <Row justify="space-between">
        <Col>
          <Button type="text" onClick={clear}>
            Limpar
          </Button>
        </Col>
        <Col>
          <Button type="primary" htmlType="submit">
            Aplicar
          </Button>
        </Col>
      </Row>
    </Form>
  );
}

export default ExpirationOfContractsFilter;
