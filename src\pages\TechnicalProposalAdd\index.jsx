import React, { useContext, useEffect, useState } from "react";
import {
  Layout,
  Card,
  Row,
  Col,
  Popconfirm,
  Button,
  Form,
  message,
  Spin,
} from "antd";
import { ArrowLeftOutlined, SaveOutlined } from "@ant-design/icons";

import { v4 } from "uuid";

import { HeaderMenu } from "../../components/HeaderMenu";
import { SideMenu } from "../../components/SideMenu";
import { useNavigate, useParams } from "react-router-dom";
import { useSelector, shallowEqual } from "react-redux";
import { AddDocument } from "../../components/Modals/TechnicalProposals/NewAddDocument";

import { DisableIncrementScroll } from "../../hooks/DisableIncrementScroll";
import { store } from "../../store/store";

import { submitAddTechnicalController } from "../../controllers/Proposals/sumbitTechnicalAdd";
import { submitEditTechnicalController } from "../../controllers/Proposals/sumbitTechnical";
import {
  ProposalProvider,
  ProposalContext,
} from "../../contexts/totalValuesProposals";

import { ProposalHeader } from "./components/proposal-header";
import { TableServices } from "./components/table-services";
import { ArchitectureAttachModal } from "../../components/Modals/TechnicalProposals/ArchitectureAttachModal";

import { SimpleCollapce } from "./collapces/simple-collapce";
import { PremisseCollapce } from "./collapces/premisses-collapsce";
import { CalculatorCollapce } from "./collapces/calculator-collapce";
import { ExtraPointsCollapce } from "./collapces/extra-points-collapce";
import { MainFactorsCollapce } from "./collapces/main-factors-collapce";
import { AdditionalCostCollapce } from "./collapces/additional-cost-collapce";
import { ProposalsCards } from "../../components/Proposals/newProposalCards";

import {
  cleanTechnicalProposalState,
  setTechnicalProposalState,
  setTechnicalProposalPersistInfoState,
} from "../../store/actions/technical-proposal-action";

import { useDebounce } from "../../hooks/useDebouce";

import {
  getProposal,
  getServices,
  getPersistData,
  updatePersistData,
  deletePersistData,
  removeAllDataFromLocalStorage,
  validateAllFields,
} from "../../controllers/Proposals/proposal-controller";
import { setGlobalPersistState } from "../../store/actions/global-persist-action";

import { ScenarioAttatchModal } from "../../components/Modals/TechnicalProposals/ScenarioAttatchModal";

const { Content } = Layout;

export const TechnicalProposalAdd = () => {
  return (
    <ProposalProvider>
      <TechnicalProposalAddPage />
    </ProposalProvider>
  );
};

export const TechnicalProposalAddPage = () => {
  const currentServices = useSelector((state) => state.services, shallowEqual);
  const navigate = useNavigate();
  const { proposalID, templateID } = useParams();
  const [loading, setLoading] = useState(false);
  const { proposalState, setProposalState } = useContext(ProposalContext);
  const [add_proposal] = Form.useForm();
  const [collapsed, setCollapsed] = useState(false);
  const [services, setServices] = useState(currentServices.services);
  const [currentContacts, setCurrentContacts] = useState([]);
  const [recongnizingHeaderChange, setRecongnizingHeaderChange] =
    useState(false);

  const [customerBrand, setCustomerBrand] = useState("");
  const [messageApi, contextHolder] = message.useMessage();

  const collapseContent = [];

  const Submit = async () => {
    const isValid = validateAllFields();
    if (!isValid) return;

    let user = {
      name: localStorage.getItem("@dsm/username"),
      mail: localStorage.getItem("@dsm/mail"),
    };

    let isSuccess = false;

    setLoading(true);

    window.scrollTo({ top: 0, behavior: "smooth" });

    const { technicalProposal } = store.getState();

    if (technicalProposal.pipedriveDealOwner) {
      user = {
        name: technicalProposal.pipedriveDealOwner.name,
        mail: technicalProposal.pipedriveDealOwner.email,
      };
    }

    const monthSelected = technicalProposal.monthSelected;

    if (monthSelected === undefined || monthSelected === "") {
      setTechnicalProposalState({
        field: "monthSelected",
        value: ["12"],
      });
    }

    if (proposalID) {
      isSuccess = await submitEditTechnicalController(
        collapseContent,
        proposalState,
        customerBrand,
        user
      );
    } else {
      isSuccess = await submitAddTechnicalController(
        add_proposal,
        collapseContent,
        currentContacts,
        services,
        proposalState,
        customerBrand,
        proposalState.idFixed,
        user
      );
    }

    if (!isSuccess) return;

    await deletePersistData();
    removeAllDataFromLocalStorage();
    cleanTechnicalProposalState();

    setLoading(false);
    navigate(-1);
  };

  useEffect(() => {
    initPage();

    return () => {
      if (proposalID) localStorage.removeItem("persist-proposal-id");
    };
  }, []);

  async function initPage() {
    setLoading(true);

    DisableIncrementScroll();
    const services = await getServices();

    const hideMessage = messageApi.loading(
      "Estamos carregando as informações, por favor aguarde...",
      0
    );

    try {
      if (proposalID || templateID) {
        const id = proposalID ? proposalID : templateID;
        const isTemplate = templateID ? true : false;

        if (isTemplate) {
          const persistID = localStorage.getItem("persist-proposal-id");

          if (persistID) {
            const isSuccess = await getPersistData(
              services,
              persistID,
              navigate
            );
            if (isSuccess) {
              hideMessage();
              return;
            }
          }
        }

        localStorage.removeItem("persist-proposal-id");

        const isSuccess = await getProposal(id, services, isTemplate, navigate);

        hideMessage();
        if (!isSuccess) navigate(-1);
      } else {
        const persistID = localStorage.getItem("persist-proposal-id");
        const isSuccess = await getPersistData(services, persistID, navigate);
        if (!isSuccess) {
          const persistID = v4();
          localStorage.setItem("persist-proposal-id", persistID);

          setTechnicalProposalPersistInfoState({
            persistID: persistID,
            readyToPersist: true,
            serviceList: services,
          });
        }

        hideMessage();
      }
      setLoading(false);
    } catch (error) {
      hideMessage();
      setLoading(false);
    }
  }

  return (
    <Layout style={{ minHeight: "100vh" }}>
      {contextHolder}
      <HeaderMenu collapsed={collapsed} setCollapsed={setCollapsed} />
      <Layout>
        <SideMenu collapsed={collapsed} />
        {loading ? (
          <Content style={{ padding: "2em" }}>
            <Card
              style={{
                boxShadow: "0 0 10px rgba(0,0,0,0.1)",
                borderRadius: "20px",
              }}
            >
              <Row justify="center">
                <Spin size="large" />
              </Row>
            </Card>
          </Content>
        ) : (
          <Content style={{ padding: "2em" }}>
            <Card
              style={{
                boxShadow: "0 0 10px rgba(0,0,0,0.1)",
                borderRadius: "20px",
              }}
            >
              <Popconfirm
                title="Ao voltar você perderá os dados do formulário, tem certeza?"
                onConfirm={async () => {
                  await deletePersistData();
                  removeAllDataFromLocalStorage();
                  cleanTechnicalProposalState();
                  navigate(-1);
                }}
                okText="Sim"
                cancelText="Não"
              >
                <Button
                  type="text"
                  style={{ marginBottom: "15px" }}
                  icon={<ArrowLeftOutlined />}
                >
                  {proposalID ? "Editar" : "Criar"} proposta
                </Button>
              </Popconfirm>

              <Row justify="center">
                <Col span={24}>
                  <Card
                    style={{
                      backgroundColor: "#f4f4f4",
                      boxShadow: "0px 4px 4px rgba(0, 0, 0, .5)",
                      borderRadius: "5px",
                    }}
                  >
                    <Form
                      Layout="vertical"
                      style={{ marginBottom: "2rem" }}
                      form={add_proposal}
                      initialValues={{
                        main_aws_investments: [],
                        aditional_costs: [],
                      }}
                      onFinish={(data) => {
                        Submit(data);
                      }}
                    >
                      <ProposalHeader
                        currentContacts={currentContacts}
                        setCurrentContacts={(e) => setCurrentContacts(e)}
                        mainForm={add_proposal}
                        setBrand={(image) => setCustomerBrand(image)}
                        recongnizingHeaderChange={recongnizingHeaderChange}
                        setRecongnizingHeaderChange={(e) =>
                          setRecongnizingHeaderChange(e)
                        }
                      />

                      <SimpleCollapce
                        title="Desafios"
                        fieldName="challenges"
                        formName="proposal_challenge"
                      />

                      <SimpleCollapce
                        title="Cenário apresentado"
                        fieldName="scenarios"
                        formName="proposal_scenarios"
                      >
                        <Row justify="center">
                          <ScenarioAttatchModal />
                        </Row>
                      </SimpleCollapce>

                      <PremisseCollapce
                        title="Premissas"
                        fieldName="premisses"
                        formName="proposal_premisses"
                      />

                      <ExtraPointsCollapce
                        title="Pontos não contemplados"
                        fieldName="extra_points"
                        formName="proposal_extra_points"
                      />

                      <SimpleCollapce
                        title="Arquitetura"
                        fieldName="architecture"
                        formName="proposal_architecture"
                      >
                        <Row justify="center">
                          <ArchitectureAttachModal />
                        </Row>
                      </SimpleCollapce>

                      <MainFactorsCollapce
                        title="Fatores influenciadores"
                        fieldName="main_factors"
                        formName="proposal_main_factors"
                      />

                      <CalculatorCollapce add_proposal={add_proposal} />

                      <AdditionalCostCollapce add_proposal={add_proposal} />

                      <SimpleCollapce
                        title="Resultados esperados"
                        fieldName="expected_results"
                        formName="proposal_expected_results"
                      />

                      <SimpleCollapce
                        title="Observações"
                        fieldName="observations"
                        formName="proposal_observations"
                      />

                      <SimpleCollapce
                        title="Notas Internas"
                        fieldName="internal_notes"
                        formName="proposal_internal_notes"
                      />

                      <ProposalsCards hourClass="hourValue" />
                    </Form>
                  </Card>
                </Col>
              </Row>

              <TableServices
                state={[
                  services,
                  add_proposal.getFieldValue("client_name"),
                  "technical",
                ]}
                setServices={(services) => setServices(services)}
              />

              <Row justify="space-between">
                <Col>
                  <AddDocument />
                </Col>
                <Col>
                  <SaveProposalButton loading={loading} submit={Submit} />
                </Col>
              </Row>
            </Card>
            <PersistData />
          </Content>
        )}
      </Layout>
    </Layout>
  );
};

const SaveProposalButton = ({ loading, submit }) => {
  const loadingPersist = useSelector(
    (state) => state.globalPersist.technicalProposal.loadingPersist,
    shallowEqual
  );

  return (
    <Button
      type="primary"
      onClick={submit}
      loading={loading || loadingPersist}
      disabled={loading || loadingPersist}
    >
      Salvar proposta
      <SaveOutlined />
    </Button>
  );
};

const PersistData = () => {
  const SECONDS_TO_PERSIST = 5;
  const technicalProposal = useSelector(
    (state) => state.technicalProposal,
    shallowEqual
  );

  const debouceStateValue = useDebounce(
    technicalProposal,
    1000 * SECONDS_TO_PERSIST
  );

  useEffect(() => {
    const persistID = localStorage.getItem("persist-proposal-id");

    if (persistID)
      setTechnicalProposalState({ field: "persistID", value: persistID });
  }, []);

  useEffect(() => {
    setGlobalPersistState({
      field: "technicalProposal",
      value: {
        loadingPersist: true,
      },
    });
  }, [technicalProposal]);

  useEffect(() => {
    if (debouceStateValue.readyToPersist) updatePersistData(debouceStateValue);

    setGlobalPersistState({
      field: "technicalProposal",
      value: {
        loadingPersist: false,
      },
    });
  }, [debouceStateValue]);

  return <div style={{ display: "none" }}></div>;
};
