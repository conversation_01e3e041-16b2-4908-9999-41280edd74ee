import React, { useState } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Progress,
  Table,
  Tag,
  Space,
  Button,
  Tooltip,
  Alert,
  Tabs,
  Typography
} from 'antd';
import {
  DashboardOutlined,
  ThunderboltOutlined,
  WarningOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import {
  usePerformance,
  useCoreWebVitals,
  usePerformanceIssues,
  useAPIPerformance
} from '../../hooks/usePerformance';

const { TabPane } = Tabs;
const { Text } = Typography;

/**
 * Componente para monitorar performance da aplicação
 * Exibe Core Web Vitals, métricas de recursos e issues
 */
const PerformanceMonitor = ({ compact = false }) => {
  const [activeTab, setActiveTab] = useState('vitals');
  const { metrics, resourceStats, generateReport, updateMetrics, isSupported } = usePerformance();
  const { vitals, scores, getOverallScore } = useCoreWebVitals();
  const { issues, hasIssues, criticalIssues, warningIssues } = usePerformanceIssues();
  const { getAllAPIStats } = useAPIPerformance();

  /**
   * Obtém cor baseada no score
   */
  const getScoreColor = (score) => {
    switch (score) {
      case 'good': return '#52c41a';
      case 'needs-improvement': return '#faad14';
      case 'poor': return '#ff4d4f';
      default: return '#d9d9d9';
    }
  };

  /**
   * Obtém ícone baseado no score
   */
  const getScoreIcon = (score) => {
    switch (score) {
      case 'good': return <CheckCircleOutlined />;
      case 'needs-improvement': return <WarningOutlined />;
      case 'poor': return <CloseCircleOutlined />;
      default: return <DashboardOutlined />;
    }
  };

  /**
   * Formata valor de métrica
   */
  const formatMetricValue = (metric, value) => {
    if (value === null || value === undefined) return 'N/A';
    
    switch (metric) {
      case 'LCP':
      case 'FCP':
      case 'FID':
      case 'TTFB':
        return `${Math.round(value)}ms`;
      case 'CLS':
        return value.toFixed(3);
      default:
        return Math.round(value);
    }
  };

  /**
   * Formata tamanho em bytes
   */
  const formatBytes = (bytes) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  /**
   * Renderiza Core Web Vitals
   */
  const renderCoreWebVitals = () => {
    const vitalMetrics = [
      { key: 'LCP', name: 'Largest Contentful Paint', description: 'Loading performance' },
      { key: 'FID', name: 'First Input Delay', description: 'Interactivity' },
      { key: 'CLS', name: 'Cumulative Layout Shift', description: 'Visual stability' },
      { key: 'FCP', name: 'First Contentful Paint', description: 'Loading performance' },
      { key: 'TTFB', name: 'Time to First Byte', description: 'Server response' }
    ];

    return (
      <Row gutter={[16, 16]}>
        <Col span={24}>
          <Card size="small" title="Overall Score">
            <div style={{ textAlign: 'center' }}>
              <Tag
                color={getScoreColor(getOverallScore())}
                icon={getScoreIcon(getOverallScore())}
                style={{ fontSize: '16px', padding: '8px 16px' }}
              >
                {getOverallScore().toUpperCase()}
              </Tag>
            </div>
          </Card>
        </Col>
        
        {vitalMetrics.map(({ key, name, description }) => (
          <Col xs={24} sm={12} md={8} lg={6} key={key}>
            <Card size="small">
              <Statistic
                title={
                  <Tooltip title={description}>
                    <Space>
                      {name}
                      {getScoreIcon(scores[key])}
                    </Space>
                  </Tooltip>
                }
                value={formatMetricValue(key, vitals[key])}
                valueStyle={{ color: getScoreColor(scores[key]) }}
              />
              <Progress
                percent={vitals[key] ? Math.min(100, (vitals[key] / (key === 'CLS' ? 0.5 : 5000)) * 100) : 0}
                strokeColor={getScoreColor(scores[key])}
                size="small"
                showInfo={false}
              />
            </Card>
          </Col>
        ))}
      </Row>
    );
  };

  /**
   * Renderiza estatísticas de recursos
   */
  const renderResourceStats = () => {
    const columns = [
      {
        title: 'Type',
        dataIndex: 'type',
        key: 'type',
        render: (type) => <Tag>{type}</Tag>
      },
      {
        title: 'Count',
        dataIndex: 'count',
        key: 'count'
      },
      {
        title: 'Total Size',
        dataIndex: 'size',
        key: 'size',
        render: (size) => formatBytes(size)
      },
      {
        title: 'Avg Duration',
        dataIndex: 'duration',
        key: 'duration',
        render: (duration, record) => `${Math.round(duration / record.count)}ms`
      }
    ];

    const data = Object.entries(resourceStats.byType || {}).map(([type, stats]) => ({
      key: type,
      type,
      ...stats
    }));

    return (
      <Space direction="vertical" style={{ width: '100%' }}>
        <Row gutter={16}>
          <Col span={6}>
            <Statistic
              title="Total Resources"
              value={resourceStats.total || 0}
              prefix={<DashboardOutlined />}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="Total Size"
              value={formatBytes(resourceStats.totalSize || 0)}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="Load Time"
              value={`${Math.round(resourceStats.totalDuration || 0)}ms`}
              prefix={<ThunderboltOutlined />}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="Largest Resource"
              value={resourceStats.largest ? formatBytes(resourceStats.largest.size) : 'N/A'}
            />
          </Col>
        </Row>
        
        <Table
          columns={columns}
          dataSource={data}
          size="small"
          pagination={false}
        />
      </Space>
    );
  };

  /**
   * Renderiza issues de performance
   */
  const renderIssues = () => {
    if (!hasIssues) {
      return (
        <Alert
          message="No Performance Issues"
          description="Your application is performing well!"
          type="success"
          icon={<CheckCircleOutlined />}
          showIcon
        />
      );
    }

    return (
      <Space direction="vertical" style={{ width: '100%' }}>
        {criticalIssues.length > 0 && (
          <Alert
            message={`${criticalIssues.length} Critical Issues`}
            description="These issues significantly impact user experience"
            type="error"
            showIcon
          />
        )}
        
        {warningIssues.length > 0 && (
          <Alert
            message={`${warningIssues.length} Performance Warnings`}
            description="These issues may affect performance"
            type="warning"
            showIcon
          />
        )}
        
        {issues.map((issue, index) => (
          <Card key={index} size="small">
            <Space direction="vertical">
              <Space>
                <Tag color={issue.severity === 'high' ? 'red' : 'orange'}>
                  {issue.severity.toUpperCase()}
                </Tag>
                <Text strong>{issue.type}</Text>
              </Space>
              <Text>{issue.message}</Text>
              <Text type="secondary">
                Current: {formatMetricValue(issue.type, issue.value)} | 
                Target: {formatMetricValue(issue.type, issue.threshold)}
              </Text>
            </Space>
          </Card>
        ))}
      </Space>
    );
  };

  /**
   * Renderiza métricas de API
   */
  const renderAPIMetrics = () => {
    const apiStats = getAllAPIStats();
    
    const columns = [
      {
        title: 'API',
        dataIndex: 'name',
        key: 'name'
      },
      {
        title: 'Calls',
        dataIndex: 'totalCalls',
        key: 'totalCalls'
      },
      {
        title: 'Avg Time',
        dataIndex: 'averageTime',
        key: 'averageTime',
        render: (time) => `${Math.round(time)}ms`
      },
      {
        title: 'Success Rate',
        dataIndex: 'successRate',
        key: 'successRate',
        render: (_, record) => {
          const rate = (record.successCount / record.totalCalls) * 100;
          return (
            <Progress
              percent={rate}
              size="small"
              status={rate > 95 ? 'success' : rate > 90 ? 'active' : 'exception'}
            />
          );
        }
      }
    ];

    const data = Object.entries(apiStats).map(([name, stats]) => ({
      key: name,
      name,
      ...stats,
      successCount: stats.successCount || 0
    }));

    return (
      <Table
        columns={columns}
        dataSource={data}
        size="small"
        pagination={false}
      />
    );
  };

  if (!isSupported) {
    return (
      <Alert
        message="Performance API Not Supported"
        description="Your browser doesn't support the Performance API"
        type="warning"
        showIcon
      />
    );
  }

  if (compact) {
    return (
      <Card
        size="small"
        title="Performance"
        extra={
          <Button size="small" icon={<ReloadOutlined />} onClick={updateMetrics}>
            Refresh
          </Button>
        }
      >
        <Row gutter={8}>
          <Col span={8}>
            <Statistic
              title="LCP"
              value={formatMetricValue('LCP', vitals.LCP)}
              valueStyle={{ color: getScoreColor(scores.LCP), fontSize: '14px' }}
            />
          </Col>
          <Col span={8}>
            <Statistic
              title="FID"
              value={formatMetricValue('FID', vitals.FID)}
              valueStyle={{ color: getScoreColor(scores.FID), fontSize: '14px' }}
            />
          </Col>
          <Col span={8}>
            <Statistic
              title="CLS"
              value={formatMetricValue('CLS', vitals.CLS)}
              valueStyle={{ color: getScoreColor(scores.CLS), fontSize: '14px' }}
            />
          </Col>
        </Row>
      </Card>
    );
  }

  return (
    <Card
      title="Performance Monitor"
      extra={
        <Space>
          <Button icon={<ReloadOutlined />} onClick={updateMetrics}>
            Refresh
          </Button>
          <Button onClick={() => generateReport()}>
            Export Report
          </Button>
        </Space>
      }
    >
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab="Core Web Vitals" key="vitals">
          {renderCoreWebVitals()}
        </TabPane>
        
        <TabPane tab="Resources" key="resources">
          {renderResourceStats()}
        </TabPane>
        
        <TabPane tab="Issues" key="issues">
          {renderIssues()}
        </TabPane>
        
        <TabPane tab="API Performance" key="api">
          {renderAPIMetrics()}
        </TabPane>
      </Tabs>
    </Card>
  );
};

export default PerformanceMonitor;
