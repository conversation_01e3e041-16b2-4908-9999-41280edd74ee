
.headerTicket{
  display: flex;
  justify-content: space-between;
  padding: 20px 30px;
  align-items: center;
  margin-bottom: 5px;
}

.cardsContainer-per-wallet{
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 24px;
  width: 100%;
}

.icon{
  color: #1EBD71;
}

.select{
  width: 150px;
  float: left;
  text-align: left;
}

.recharts-legend-wrapper .recharts-default-legend .recharts-legend-item {
  cursor: pointer;
}

.cardsContainer-per-wallet > div > div {
  height: 250px !important;
  width: 180px !important;
  overflow-y: auto;
  word-break: break-word;
  font-size: 10px;
  margin-bottom: 3px;
}

.cardsContainer-per-wallet > div > div::-webkit-scrollbar{
  background-color: rgba(206, 206, 206, 0.5);    /* color of the scroll thumb */
  border-radius: 5px;       /* roundness of the scroll thumb */
  width: 10px;
}

.cardsContainer-per-wallet > div > div::-webkit-scrollbar-thumb{
  background-color: rgba(137, 137, 137, 0.6);    /* color of the scroll thumb */
  height: 20px;
  border-radius: 10px;       /* roundness of the scroll thumb */
}




