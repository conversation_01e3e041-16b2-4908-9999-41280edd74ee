import { Typography } from "antd";

export const ExpandableText = ({
  text,
  rows = 2,
  symbol = "mais",
  expanded,
  cursor,
  itemId,
  verifyItem,
}) => {
  const { Paragraph } = Typography;
  return (
    <Paragraph
      style={{ cursor }}
      ellipsis={
        expanded === true
          ? {
              rows,
              expandable: true,
              symbol,
            }
          : false
      }
    >
      {text}
    </Paragraph>
  );
};
