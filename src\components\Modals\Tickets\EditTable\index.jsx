import { Button, Modal, Form, Checkbox, Row, Col } from "antd";
import { MoreOutlined } from "@ant-design/icons";
import { useState } from "react";

export const EditTable = (props) => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const { stateColumns, setStateColumns } = props;
  const [form] = Form.useForm();

  const showModal = () => {
    setIsModalVisible(true);
  };

  const handleOk = () => {
    form.submit();
  };

  const handleCancel = () => {
    setIsModalVisible(false);
  };

  const handleSubmit = async (data) => {
    setStateColumns(
      stateColumns?.filter((a) => {
        if (data.lista.includes(a.code)) {
          return a.code;
        }
      })
    );
    setIsModalVisible(false);
  };

  return (
    <>
      <Button
        type="text"
        icon={<MoreOutlined />}
        onClick={showModal}
        style={{ boxShadow:'none', height: 'auto', color: '#fff' }}
      ></Button>
      <Modal
        title="Configuração"
        open={isModalVisible}
        onOk={handleOk}
        onCancel={handleCancel}
      >
        <Form layout="vertical" onFinish={handleSubmit} form={form}>
          <Form.Item name="lista" label="Fila selecionada:">
            <Checkbox.Group
              style={{
                width: "100%",
              }}
            >
              {stateColumns &&
                <Row>
                {
                  stateColumns?.map(column => {
                    return(
                      <Col span={8}>
                        <Checkbox value={column.code}>{column.title}</Checkbox>
                      </Col>
                    )
                  })
                }
                
              </Row>}
            </Checkbox.Group>
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};
