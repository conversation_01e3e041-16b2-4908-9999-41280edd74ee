import React, { useEffect, useState } from "react";
import { Row, Col } from "antd";
import { useLocation } from "react-router-dom";
import { SelectImage } from "../../../components/NewSelectImage";

import { ProjectName } from "./project-name";
import { CustomerField } from "./customer-field";
import { TypeField } from "./type-field";
import { StatusField } from "./status-field";
import { ContactsField } from "./contacts-field";
import { ArchitectsField } from "./architects-field";
import { BusField } from "./bus-field";
import { OpportunitiesField } from "./opportunitis-field";

export const ProposalHeader = (proposalProps) => {
  const { state } = useLocation();
  const [selectedCustomer, setSelectedCustomer] = useState(
    state !== null && !window.location.href.includes("add")
      ? {
          names: {
            name: state?.customer?.names?.name
              ? state?.customer?.names?.name
              : "",
            fantasy_name: state?.customer?.names?.fantasy_name
              ? state?.customer?.names?.fantasy_name
              : "",
          },
          contacts: state?.customer?.contacts ? state.customer.contacts : [],
          cnpj: state?.customer?.cnpj ? state?.customer?.cnpj : "",
        }
      : {
          names: { name: "", fantasy_name: "" },
          contacts: [],
          cnpj: "",
        }
  );

  const [contacts, setContacts] = useState(
    selectedCustomer ? selectedCustomer.contacts : []
  );

  useEffect(() => {
    setSelectedCustomer({
      ...selectedCustomer,
      contacts: contacts,
    });
  }, [contacts]);

  useEffect(() => {
    if (proposalProps) {
      proposalProps?.setCurrentContacts(selectedCustomer);
      setContacts(selectedCustomer.contacts);
    }
  }, [selectedCustomer]);

  return (
    <Col span={24}>
      <Row>
        <Col span={18}>
          <Row style={{ marginBottom: "15px" }} gutter={[4, 0]}>
            <ProjectName />
            <CustomerField />
          </Row>

          <Row style={{ marginBottom: "15px" }} gutter={[4, 0]}>
            <TypeField />
            <StatusField />
          </Row>
        </Col>
        <Col span={6}>
          <SelectImage />
        </Col>
      </Row>

      <Row
        justify="space-between"
        align="middle"
        style={{ marginBottom: "2rem" }}
        gutter={[12, 0]}
      >
        <ContactsField />

        <ArchitectsField />

        <BusField />

        <OpportunitiesField />
      </Row>
    </Col>
  );
};
