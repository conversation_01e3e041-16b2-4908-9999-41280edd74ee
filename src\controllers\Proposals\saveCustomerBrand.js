import { apiProposals, getHeader } from "../../utils/api";

export async function saveCustomerBrand(customerName, customerBrand) {
  const headers = getHeader();
  if (customerBrand) {
    let formData = new FormData();
    formData.append("image", customerBrand);

    const name = customerName.replace(/\/|\\/gm, "-");

    await apiProposals
      .put(`/brand/${name}`, formData, { headers })
      .catch((error) => {
        console.log(error);
      });
  }
}
