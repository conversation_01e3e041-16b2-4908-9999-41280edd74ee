import { FilterFilled, StarFilled, StarOutlined } from "@ant-design/icons";
import React, { useEffect, useState } from "react";
import { otrsGet, otrsPost } from "../../../service/apiOtrs";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, Tooltip } from "recharts";
import { Card, Col, Modal, Popover, Row, Spin, Typography } from "antd";
import ReservedHoursFilter from "../../Modals/Filters/ReservedHoursFilter";
import moment from "moment";

export default function ReservedHours(props) {
  const { Title, Text } = Typography;
  const [isFavorite, setIsFavorite] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [allClients, setAllClients] = useState([]);
  const [allWallets, setAllWallets] = useState([]);
  const [allContracts, setAllContracts] = useState([]);
  const [filteredContracts, setFilteredContracts] = useState([]);
  const [filteredData, setFilteredData] = useState([]);
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [popoverVisibility, setPopoverVisibility] = useState(false);
  const [filterSelected, setFilterSelected] = useState(false);

  const GRAPH_NAME = "ReservedHours";
  const monthsName = [
    "Janeiro",
    "Fevereiro",
    "Mar",
    "Abril",
    "Maio",
    "Junho",
    "Julho",
    "Agosto",
    "Setembro",
    "Outubro",
    "Novembro",
    "Dezembro",
  ];

  async function updateFavorite() {
    await props?.setVisionFavorite(GRAPH_NAME);
    setIsFavorite(checkIsVisible());
  }

  function handlePopoverVisibility() {
    setPopoverVisibility(!popoverVisibility);
  }

  function showModal() {
    setModalVisible(!modalVisible);
  }

  function handleOk() {
    setModalVisible(false);
  }

  function handleCancel() {
    setModalVisible(false);
  }

  function filterContracts(name) {
    let filteredContractsArray = [];
    Object.entries(allContracts).forEach((val) => {
      if (val[1].document_id === name) {
        filteredContractsArray.push(val[1]);
      }
    });
    setFilteredContracts(filteredContractsArray);
  }

  function filterContractByClient(name) {
    let filteredDataArray = [];
    Object.entries(filteredContracts).forEach((val) => {
      if (val[1].customer_id === name) {
        filteredDataArray.push(val[1]);
      }
    });
    setFilteredData(filteredDataArray);
  }

  async function handleDataGraph(filter) {
    setData([]);
    setLoading(true);

    if (filter.client) {
      let tickets = await otrsPost("read/tickets/customerInRange", {
        params: {
          start: moment(filter.rangeDate[0]).format("YYYY-MM-DD") + "T00:00:00:000Z",
          end: moment(filter.rangeDate[1]).format("YYYY-MM-DD") + "T00:00:00:000Z",
          customerId: filter.client,
        },
      }).then((response) => {
        return response.data;
      });
      if (tickets.length === 0) {
        setData([]);
        setLoading(false);
        return null;
      }

      if (filter.wallet) {
        tickets = tickets.filter(
          (i) => i.wallet.toLowerCase() === filter.wallet.toLowerCase()
        );
      }
      const formatedTicketsWithContracts = await formatTicketsWithContracts(
        tickets
      );

      const formatedData = await formatContractsForChart(
        formatedTicketsWithContracts
      );

      setData(formatedData);
      setLoading(false);
    } else if (filter.wallet && !filter.client) {
      let contractsByWallet = await getContarctsByWallet(
        filter.rangeDate,
        filter.wallet
      );

      if (contractsByWallet.length === 0) {
        setData([]);
        setLoading(false);
        return null;
      }

      let ticketsByTime = await getTicketsByTime(filter.rangeDate);

      const formatedContracts = await formatContractsWithTickets(
        contractsByWallet,
        ticketsByTime
      );

      const formatedData = await formatContractsForChart(formatedContracts);

      setData(formatedData);
      setLoading(false);
      return null;
    }
  }

  async function formatTicketsWithContracts(tickets) {
    let ticketsWithContracts = [];

    tickets.forEach( (t) => {
      let contractAlreadyExist = ticketsWithContracts.find(
        (tc) => tc.contractId === t.contract_id
      );
      if (!contractAlreadyExist) {
        ticketsWithContracts.push({
          wallet_type: t.wallet,
          month: monthsName[moment(t.create_time).month()],
          name: t.contract_name,
          create_time: t.create_time,
          contractId: t.contract_id,
          type_hours: t.type_hours,
          contract_total_hours: t.contract_total_hours,
          totalConsumedMin: Number(t.ticket_total_consumed_min),
          tickets: [t],
        });
      } else {
        const index = ticketsWithContracts.findIndex(
          (i) => i.contractId == contractAlreadyExist.contractId
        );
        ticketsWithContracts[index].tickets.push(t);
        ticketsWithContracts[index].totalConsumedMin += Number(
          t.ticket_total_consumed_min
        );
      }
    });

    ticketsWithContracts.forEach(async (c) => {
      const totalConsumed = await calculateHoursAndMinutes(c.totalConsumedMin);
      c["consumed_hours"] = totalConsumed.hours;
      c["consumed_minutes"] = totalConsumed.minutes;
    });

    return ticketsWithContracts;
  }

  async function calculateHoursAndMinutes(consumedMin) {
    if (consumedMin >= 0) {
      const hours = Math.floor(consumedMin / 60);
      const minutes = consumedMin % 60;
      return { hours: hours || 0, minutes: minutes || 0 };
    }
  }

  async function formatContractsWithTickets(contracts, tickets) {
    let contractsWithTickets = [];
    contracts.forEach(async (c) => {
      const currentContractTickets = tickets.filter(
        (t) => c.contract_id === t.id_contract
      );

      const totalConsumedMin = currentContractTickets.reduce(
        (acc, curr) => acc + Number(curr.consumed_min),
        0
      );
      const totalConsumed = await calculateHoursAndMinutes(totalConsumedMin);

      if (currentContractTickets.length > 0) {
        c["ticket"] = currentContractTickets;
        c["consumed_hours"] = totalConsumed.hours;
        c["consumed_minutes"] = totalConsumed.minutes;
      } else {
        c["ticket"] = [];
        c["consumed_hours"] = 0;
        c["consumed_minutes"] = 0;
      }

      contractsWithTickets.push(c);
    });

    return contractsWithTickets;
  }

  async function getTicketsByTime(rangeDate) {
    const startValue = rangeDate ? rangeDate[0] : moment();
    const endValue = rangeDate ? rangeDate[1] : moment();

    const { data } = await otrsPost("read/tickets/time", {
      params: {
        start: moment(startValue).format("YYYY-MM-DD") + "T00:00:00:000Z",
        end: moment(endValue).format("YYYY-MM-DD") + "T00:00:00:000Z",
      },
    });

    return data;
  }

  async function getContarctsByWallet(rangeDate, wallet) {
    const startValue = rangeDate ? rangeDate[0] : moment();
    const endValue = rangeDate ? rangeDate[1] : moment();
    const start = moment(startValue).format("YYYY-MM-DD") + "T00:00:00:000Z";
    const end = moment(endValue).format("YYYY-MM-DD") + "T00:00:00:000Z";

    const { data } = await otrsGet(
      `read/contract/walletInRange/0?start=${start}&end=${end}&walletType=${wallet}`
    );
    return Object.values(data);
  }

  async function formatContractsForChart(allContracts) {
    let formatedData = [];

    allContracts.forEach((contract) => {
      formatedData.push({
        wallet: contract.wallet_type,
        name: contract.name,
        createDate: contract.create_time,
        typeHours: contract.type_hours,
        totalHours: contract.contract_total_hours,
        consumed_hours: contract.consumed_hours,
        consumed_minutes: contract.consumed_minutes,
      });
    });

    return formatedData;
  }

  useEffect(() => {
    setIsFavorite(checkIsVisible());
  }, [props.favorites]);

  useEffect(() => {
    setAllClients(props?.allClients);
    setAllWallets(props?.allWallets);
    setAllContracts(props?.allContracts);
  }, []);

  function checkIsVisible() {
    let localStorageData = JSON.parse(localStorage.getItem("favorites"));
    if (localStorageData) {
      let index = localStorageData.findIndex(
        (value) => value.component === GRAPH_NAME
      );
      return index !== -1;
    } else {
      return false;
    }
  }

  const CustomTooltip = (e) => {
    return data.length > 0 ? (
      <div
        style={{
          backgroundColor: "white",
          padding: "10px",
          borderRadius: "8px",
        }}
      >
        <p style={{ marginBottom: "5px", color: "gray", fontSize: "14px" }}>
          Contrato: {data[e.label]?.name}
        </p>
        <p style={{ marginBottom: "5px", color: "gray", fontSize: "14px" }}>
          Carteira: {data[e.label]?.wallet}
        </p>
        <p style={{ color: "#43914F", fontSize: "14px" }}>
          Horas reservadas: {data[e.label]?.totalHours}h
        </p>
        <p style={{ color: "#435E91", fontSize: "14px" }}>
          Horas consumidas: {data[e.label]?.consumed_hours}h{" "}
          {data[e.label]?.consumed_minutes}m
        </p>
      </div>
    ) : (
      <></>
    );
  };

  return (
    <Card bordered={false} style={{ borderRadius: "20px", height: "100%" }}>
      <Row>
        <Col span={18}>
          <Title level={4} style={{ fontWeight: 400 }}>
            Horas Reservadas
          </Title>
        </Col>
        <Col span={3}>
          <Popover
            open={popoverVisibility}
            onOpenChange={handlePopoverVisibility}
            placement="left"
            content={() => {
              return (
                <ReservedHoursFilter
                  allClients={allClients}
                  allWallets={allWallets}
                  filteredContracts={filteredContracts}
                  getFilteredContracts={filterContracts}
                  getFilteredContractByClient={filterContractByClient}
                  getGraphData={handleDataGraph}
                  handlePopoverVisibility={handlePopoverVisibility}
                  setFilterSelected={(value) => setFilterSelected(value)}
                />
              );
            }}
            trigger="click"
          >
            <div style={{ cursor: "pointer" }} className="filter">
              <FilterFilled className="filter-icon" />
            </div>
          </Popover>
        </Col>
        <Col span={3}>
          {!isFavorite ? (
            <StarOutlined onClick={updateFavorite} className="star-icon" />
          ) : (
            <StarFilled onClick={updateFavorite} className="star-icon" />
          )}
        </Col>
      </Row>
      {filterSelected ? (
        loading ? (
          <Row
            align="middle"
            justify="center"
            style={{ height: "250px", minWidth: "275px" }}
          >
            <Col style={{ display: "flex", flexDirection: "column" }}>
              <Spin />
              <Text>Carregando...</Text>
            </Col>
          </Row>
        ) : data.length > 0 ? (
          <div className="graph">
            <BarChart
              onClick={() => props.isModal && showModal()}
              width={340}
              height={300}
              data={data}
              setLoading={false}
            >
              <YAxis />
              <Tooltip content={<CustomTooltip />} />
              <Legend iconType={"circle"} />
              <Bar
                name="Horas Reservadas"
                dataKey="totalHours"
                fill={"#43914F"}
              />
              <Bar
                name="Horas Consumidas"
                type="linear"
                dataKey="consumed_hours"
                stroke="#435E91"
                fill="#435E91"
              />
            </BarChart>
          </div>
        ) : (
          <Row style={{ height: "250px" }} align="middle" justify="center">
            <Col>
              <Text>Sem dados neste mês*</Text>
            </Col>
          </Row>
        )
      ) : (
        <Row style={{ height: "250px" }} align="middle" justify="center">
          <Col>
            <Text>Selecione um cliente ou uma carteira*</Text>
          </Col>
        </Row>
      )}
      {/* <Modal
        closable={true}
        width={"1090px"}
        className="flex justify-center"
        bodyStyle={{ backgroundColor: "transparent" }}
        cancelButtonProps={{ style: { display: "none" } }}
        okButtonProps={{
          style: {
            display: "none",
            backgroundColor: "#43914F",
            color: "white",
            fontWeight: "bold",
            borderRadius: "5px",
          },
        }}
        open={modalVisible}
        onOk={handleOk}
        onCancel={handleCancel}
      >
        <ReservedHoursTable
          filteredData={filteredData}
          allClients={allClients}
          arrayWallet={allWallets}
          filteredContracts={filteredContracts}
          getFilteredContracts={filterContracts}
          getFilteredContractByClient={filterContractByClient}
          getGraphData={handleDataGraph}
          showModal={showModal}
          modalVisible={modalVisible}
        />
      </Modal> */}
    </Card>
  );
}
