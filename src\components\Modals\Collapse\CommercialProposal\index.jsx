import { Collapse, Typography } from "antd";
import { CaretRightOutlined } from "@ant-design/icons";

export const MainCollapse = (props) => {
  const { Panel } = Collapse;
  const { Title } = Typography;
  const { editor, title, onChange } = props;

  function onChangeCollapse(key) {
    const isOpen = key.length === 2;

    if (onChange) onChange(isOpen, title);
  }

  return title === "Notas Internas" ? (
    <>
      <Collapse
        expandIcon={({ isActive }) => (
          <CaretRightOutlined
            style={{ color: "#000" }}
            rotate={isActive ? 90 : 0}
          />
        )}
        style={{
          backgroundColor: "white",
          color: "#fff",
          margin: "3px 0",
        }}
        defaultActiveKey={["1"]}
      >
        <Panel
          style={{
            color: "#fff",
            border: "none",
            boxShadow: "0px 4px 4px rgba(0, 0, 0, .5)",
            marginBottom: "15px",
          }}
          color="#fff"
          ghost
          key={
            localStorage.getItem("CollapseValidator") === "true" ? "1" : null
          }
          header={
            <Title
              level={5}
              style={{ margin: 0, fontWeight: 400, color: "#000" }}
            >
              {title}
            </Title>
          }
        >
          {editor}
        </Panel>
      </Collapse>
    </>
  ) : title === "Custos adicionais" || title === "Calculadora" ? (
    <>
      <Collapse
        expandIcon={({ isActive }) => (
          <CaretRightOutlined
            style={{ color: "#fff" }}
            rotate={isActive ? 90 : 0}
          />
        )}
        style={{
          backgroundColor: "#0F9347",
          color: "#fff",
          margin: "3px 0",
        }}
        defaultActiveKey={["1"]}
        onChange={onChangeCollapse}
      >
        <Panel
          style={{
            color: "#fff",
            border: "none",
            boxShadow: "0px 4px 4px rgba(0, 0, 0, .5)",
            marginBottom: "15px",
          }}
          color="#fff"
          ghost
          key={"1"}
          header={
            <Title
              level={5}
              style={{ margin: 0, fontWeight: 400, color: "#fff" }}
            >
              {title}
            </Title>
          }
        >
          {editor}
        </Panel>
      </Collapse>
    </>
  ) : (
    <>
      <Collapse
        expandIcon={({ isActive }) => (
          <CaretRightOutlined
            style={{ color: "#fff" }}
            rotate={isActive ? 90 : 0}
          />
        )}
        style={{
          backgroundColor: "#0F9347",
          color: "#fff",
          margin: "3px 0",
        }}
        defaultActiveKey={["1"]}
        onChange={onChangeCollapse}
      >
        <Panel
          style={{
            color: "#fff",
            border: "none",
            boxShadow: "0px 4px 4px rgba(0, 0, 0, .5)",
            marginBottom: "15px",
          }}
          color="#fff"
          ghost
          key={
            localStorage.getItem("CollapseValidator") === "true" ? "1" : null
          }
          header={
            <Title
              level={5}
              style={{ margin: 0, fontWeight: 400, color: "#fff" }}
            >
              {title}
            </Title>
          }
        >
          {editor}
        </Panel>
      </Collapse>
    </>
  );
};
