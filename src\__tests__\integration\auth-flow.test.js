import React from 'react';
import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import axios from 'axios';
import { AuthProvider } from '../../hooks/useAuth';
import MFA from '../../pages/MFA';
import { authService } from '../../services/authService';

// Mock dependencies
jest.mock('axios');
jest.mock('../../services/authService');
jest.mock('../../service/apiDsmDynamo');
jest.mock('../../service/apiCognito');
jest.mock('jwt-decode');

const mockedAxios = axios;

// Mock the required services
jest.mock('../../service/apiDsmDynamo', () => ({
  dynamoGet: jest.fn()
}));

jest.mock('../../service/apiCognito', () => ({
  cognitoPutRole: jest.fn()
}));

jest.mock('jwt-decode', () => jest.fn());

const { dynamoGet } = require('../../service/apiDsmDynamo');
const { cognitoPutRole } = require('../../service/apiCognito');
const jwtDecode = require('jwt-decode');

// Mock window.location
delete window.location;
window.location = { href: 'http://localhost:3000/mfa?code=mock-auth-code' };

// Mock navigation
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
}));

describe('Authentication Flow Integration', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Setup environment variables
    process.env.REACT_APP_COGNITO_PARSE = 'https://api.example.com/cognito/parse';
    process.env.REACT_APP_API_PERMISSION = 'https://api.example.com/';
    process.env.REACT_APP_STAGE = 'dev';
  });

  describe('MFA Authentication Flow', () => {
    it('should complete full authentication flow for new user', async () => {
      // Mock Cognito token exchange
      mockedAxios.post.mockResolvedValueOnce({
        data: {
          data: { id_token: 'mock-id-token' }
        }
      });

      // Mock JWT decode
      jwtDecode.mockReturnValue({
        email: '<EMAIL>'
      });

      // Mock user lookup - user exists in Cognito but no permission set
      mockedAxios.get.mockResolvedValueOnce({
        data: {
          data: [
            {
              email: '<EMAIL>',
              user: 'john.doe'
            }
          ]
        }
      });

      // Mock Azure AD users lookup
      dynamoGet.mockResolvedValueOnce([
        {
          userPrincipalName: '<EMAIL>',
          role: 'full_admin'
        }
      ]);

      // Mock permissions lookup
      dynamoGet.mockResolvedValueOnce([
        {
          id: 'admin-permission-id',
          name_permission: 'Admin'
        }
      ]);

      // Mock auth service
      authService.setAuthToken.mockResolvedValue(true);

      // Mock Cognito role update
      cognitoPutRole.mockResolvedValue();

      render(
        <BrowserRouter>
          <AuthProvider>
            <MFA />
          </AuthProvider>
        </BrowserRouter>
      );

      // Wait for authentication flow to complete
      await waitFor(() => {
        expect(mockNavigate).toHaveBeenCalledWith('/');
      }, { timeout: 5000 });

      // Verify the flow
      expect(mockedAxios.post).toHaveBeenCalledWith(
        'https://api.example.com/cognito/parse',
        { code: 'mock-auth-code' }
      );

      expect(jwtDecode).toHaveBeenCalledWith('mock-id-token');

      expect(mockedAxios.get).toHaveBeenCalledWith(
        'https://api.example.com/cognito/read',
        { headers: { authorization: 'mock-id-token' } }
      );

      expect(authService.setAuthToken).toHaveBeenCalledWith(
        'mock-id-token',
        expect.objectContaining({
          email: '<EMAIL>',
          name: 'john.doe',
          username: 'john.doe',
          permission: 'admin-permission-id'
        })
      );

      expect(cognitoPutRole).toHaveBeenCalledWith({
        role: 'admin-permission-id',
        user: 'john.doe',
        stage: 'dev'
      });
    });

    it('should handle existing user with permissions', async () => {
      // Mock Cognito token exchange
      mockedAxios.post.mockResolvedValueOnce({
        data: {
          data: { id_token: 'mock-id-token' }
        }
      });

      // Mock JWT decode
      jwtDecode.mockReturnValue({
        email: '<EMAIL>'
      });

      // Mock user lookup - user exists with permission
      mockedAxios.get.mockResolvedValueOnce({
        data: {
          data: [
            {
              email: '<EMAIL>',
              user: 'existing.user',
              dev_permission: 'existing-permission-id'
            }
          ]
        }
      });

      // Mock auth service
      authService.setAuthToken.mockResolvedValue(true);

      render(
        <BrowserRouter>
          <AuthProvider>
            <MFA />
          </AuthProvider>
        </BrowserRouter>
      );

      await waitFor(() => {
        expect(mockNavigate).toHaveBeenCalledWith('/');
      });

      expect(authService.setAuthToken).toHaveBeenCalledWith(
        'mock-id-token',
        expect.objectContaining({
          email: '<EMAIL>',
          permission: 'existing-permission-id'
        })
      );

      // Should not call dynamoGet for Azure AD users since user already has permission
      expect(dynamoGet).not.toHaveBeenCalled();
    });

    it('should redirect to unauthorized for unknown user', async () => {
      // Mock Cognito token exchange
      mockedAxios.post.mockResolvedValueOnce({
        data: {
          data: { id_token: 'mock-id-token' }
        }
      });

      // Mock JWT decode
      jwtDecode.mockReturnValue({
        email: '<EMAIL>'
      });

      // Mock user lookup - user exists in Cognito but no permission
      mockedAxios.get.mockResolvedValueOnce({
        data: {
          data: [
            {
              email: '<EMAIL>',
              user: 'unknown.user'
            }
          ]
        }
      });

      // Mock Azure AD users lookup - user not found
      dynamoGet.mockResolvedValueOnce([]);

      render(
        <BrowserRouter>
          <AuthProvider>
            <MFA />
          </AuthProvider>
        </BrowserRouter>
      );

      await waitFor(() => {
        expect(mockNavigate).toHaveBeenCalledWith('/unauthorized');
      });
    });

    it('should handle authentication service failure', async () => {
      // Mock Cognito token exchange
      mockedAxios.post.mockResolvedValueOnce({
        data: {
          data: { id_token: 'mock-id-token' }
        }
      });

      jwtDecode.mockReturnValue({
        email: '<EMAIL>'
      });

      mockedAxios.get.mockResolvedValueOnce({
        data: {
          data: [
            {
              email: '<EMAIL>',
              user: 'john.doe',
              dev_permission: 'existing-permission-id'
            }
          ]
        }
      });

      // Mock auth service failure
      authService.setAuthToken.mockResolvedValue(false);

      render(
        <BrowserRouter>
          <AuthProvider>
            <MFA />
          </AuthProvider>
        </BrowserRouter>
      );

      await waitFor(() => {
        expect(mockNavigate).toHaveBeenCalledWith('/login');
      });
    });

    it('should handle network errors gracefully', async () => {
      // Mock network error
      mockedAxios.post.mockRejectedValue(new Error('Network error'));

      const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

      render(
        <BrowserRouter>
          <AuthProvider>
            <MFA />
          </AuthProvider>
        </BrowserRouter>
      );

      // Should not crash the application
      expect(screen.getByRole('img')).toBeInTheDocument(); // Logo should still be visible

      consoleSpy.mockRestore();
    });
  });
});
