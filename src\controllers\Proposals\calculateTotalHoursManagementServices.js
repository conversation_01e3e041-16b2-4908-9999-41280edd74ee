export function calculateTotalHoursManagementServices(
  servicesManagement,
  totalHours8x5
) {
  const totalTaskHoursList = [];
  servicesManagement.forEach((service) => {
    service.tasks.forEach((task) => {
      const { taskTime } = task;

      const totalHours = totalHours8x5;
      let totalHoursTask = 0;

      if (taskTime.type === "percentage") {
        if (totalHours > 0) {
          const taskTimePercentage = taskTime.time;
          totalHoursTask = Math.ceil((totalHours / 100) * taskTimePercentage);
        }
      }

      if (taskTime.type === "hours") {
        totalHoursTask = taskTime.time;
      }

      totalTaskHoursList.push(totalHoursTask);
    });
  });

  return totalTaskHoursList.reduce((a, b) => a + b, 0);
}
