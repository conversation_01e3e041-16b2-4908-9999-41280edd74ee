import { useEffect, useMemo, useState } from "react";
import { Table } from "antd";
import { filterTableData } from "../../../../utils/filterTableData";
import useSWR from "swr";
import axios from "axios";
import moment from "moment";
import { solicitationsColumns } from "../../exportCSVColumns/solicitationsTable";
import { formatSolicitationsExportData } from "../../formatExportHelpers/solicitations";
import { Counter } from "../../../../components/Counter";

export const SolicitationsTable = ({
  search,
  searchDate,
  setExportData,
  setExportColumns,
  searchTrigger
}) => {
  const columns = [
    {
      title: "Número do Ticket",
      dataIndex: "ticket",
      key: "ticket",
      align: "center",
    },
    {
      title: "Cliente",
      dataIndex: "customer",
      key: "customer",
      align: "center",
      sorter: (a, b) => a.customer.localeCompare(b.customer),
    },
    {
      title: "Solicitante",
      dataIndex: "requester",
      key: "requester",
      align: "center",
      sorter: (a, b) => a.requester.localeCompare(b.requester),
    },
    {
      title: "Data da Solicitação",
      dataIndex: "requestDate",
      key: "requestDate",
      align: "center",
      sorter: (a, b) => a.requestDate.localeCompare(b.requestDate),
    },
    {
      title: "Horas solicitadas",
      dataIndex: "requestedTime",
      key: "requestedTime",
      align: "center",
    },
    {
      title: "Conta solicitada",
      dataIndex: "requestedAccount",
      key: "requestedAccount",
      align: "center",
    },
  ];
  const [loading, setLoading] = useState(false);
  const [formatedTableData, setFormatedTableData] = useState([]);
  let firstdayOfMonth = moment().startOf("month").format("MM-DD-YYYY");
  let lastdayOfMonth = moment().endOf("month").format("MM-DD-YYYY");
  const searchFields = [
    "ticket",
    "customer",
    "requester",
    "requestDate",
    "requestedAccount",
  ];

  const getSolicitations = async () => {
    const jwt = localStorage.getItem("jwt");
    try {
      setLoading(true);
      const { data } = await axios.get(
        `${process.env.REACT_APP_API_PERMISSION}/read/audits`,
        {
          params: {
            type: 0,
            startDate: searchDate.length > 0 ? searchDate[0] : firstdayOfMonth,
            endDate: searchDate.length > 0 ? searchDate[1] : lastdayOfMonth,
          },
          headers: {
            Authorization: jwt,
          },
        }
      );
      setLoading(false);
      return data.data.Items;
    } catch (error) {
      setLoading(false);
      console.log({ error });
    }
  };
  const { data, mutate } = useSWR(
    `${process.env.REACT_APP_API_PERMISSION}-switch-role-audits-solicitations`,
    getSolicitations
  );

  const formatTableData = (data) => {
    let formatedData = [];
    if (searchDate !== false) {
      data = data.filter((item) =>
        moment(item.created_at).isBetween(
          moment(searchDate[0]).add(1, "days").format("YYYY-MM-DD HH:mm:ss"),
          moment(searchDate[1])
            .subtract(1, "seconds")
            .format("YYYY-MM-DD HH:mm:ss")
        )
      );
    }
    data.forEach((item) => {
      formatedData.push({
        ticket: item.client_ticket,
        customer: item.client_name,
        requester: item.username,
        requestDate: moment(new Date(item.created_at)).format(
          "DD/MM/YYYY HH:mm"
        ),
        requestedTime: item.requestedTime / 60,
        requestedAccount: item.account_id,
      });
    });
    setFormatedTableData(formatedData);
    setExportData(formatSolicitationsExportData(formatedData));
    setExportColumns(solicitationsColumns);
  };

  const filteredData = useMemo(() => {
    return filterTableData({ searchFields, search, data: formatedTableData });
  }, [search, formatedTableData]);

  useEffect(() => {
    if (data) {
      formatTableData(data);
      mutate();
    }
  }, [data, searchTrigger]);

  return (
    <>
      <Counter tableData={filteredData} />
      <Table
        columns={columns}
        dataSource={filteredData}
        scroll={{ x: "100%" }}
        loading={loading}
      ></Table>
    </>
  );
};
