import {<PERSON><PERSON><PERSON>, <PERSON>, Sector} from "recharts";
import {ArrowLeftOutlined} from "@ant-design/icons";
import {useCallback, useState} from "react";
import './ProjectHoursDetails.css';
import { Button, Typography } from "antd";

const renderActiveShape = ({cx, cy, innerRadius, outerRadius, startAngle, endAngle, fill, payload}) => {

    return (
        <g>
            <text x={cx} y={cy} dy={-30} textAnchor="middle" fill={'black'}>
                {payload.value}
            </text>
            <text x={cx + 110} y={cy + 10} dy={8} textAnchor="right" fill={'black'}>
                100
            </text>
            <text x={cx - 120} y={cy + 10} dy={8} textAnchor="right" fill={'black'}>
                0
            </text>
            <Sector
                cx={cx}
                cy={cy}
                innerRadius={innerRadius}
                outerRadius={outerRadius}
                startAngle={startAngle}
                endAngle={endAngle}
                fill={fill}
            />
        </g>
    )
}

export const ProjectHoursDetails = ({data, toggleToChart}) => {
    const {Title, Text } = Typography;
    const [activeIndex, setActiveIndex] = useState(0);

    const chartData = [
        {name: 'Group A', value: 400, fill: '#43914F'},
        {name: 'Group B', value: 300, fill: '#ccc'},
    ];

    const onPieEnter = useCallback((_, index) => setActiveIndex(index), [setActiveIndex]);

    return (
        <div className="card card-hours-details">
            <div className="card-header header-hours-details">
                <Button onClick={toggleToChart} type="text">
                    <ArrowLeftOutlined/>
                    {data.status}
                </Button>
            </div>
            <div className="subtitle-hours-details">
                <Title level={4} style={{fontWeight: 400}}>Horas contratadas 100 horas</Title>
            </div>
            <PieChart
                className="graphic-hours-details"
                width={500}
                height={200}>
                <Pie
                    activeIndex={activeIndex}
                    activeShape={renderActiveShape}
                    data={chartData}
                    startAngle={180}
                    endAngle={0}
                    cx={225}
                    cy={170}
                    innerRadius={80}
                    outerRadius={160}
                    fill="#43914F"
                    dataKey="value"
                    onMouseEnter={onPieEnter}
                >
                </Pie>
            </PieChart>
        </div>
    )
}