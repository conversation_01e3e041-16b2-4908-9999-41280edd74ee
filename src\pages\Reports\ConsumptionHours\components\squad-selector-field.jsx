import { useState, useMemo } from 'react'
import { shallowEqual, useSelector } from "react-redux";
import { Col, Typography } from "antd";

import { MultiSelect } from "../../../../components/MultiSelect";
import { setConsumptionState } from '../../../../store/actions/consumption-hours-action';

const allOption = { value: 0, label: "Todos" };

export const SquadSelectorField = () => {
  const squads = useSelector(state => state.consumption.squadOptions, shallowEqual)
  const selectedSquads = useSelector(state => state.consumption.selectedSquads, shallowEqual)
  
  const [allSquadsSelect, setAllSquadsSelect] = useState(true);

  const squadOptions = useMemo(() => {
    let options = [];

    options = squads.map((squad) => {
      return {
        value: squad.id,
        label: squad.name,
      };
    });

    options.unshift(allOption);

    return options;
  }, [squads]);

  const handleSelectSquad = (options) => {

    if (options.length > 0) {
      if (allSquadsSelect) {
        const exist = options.findIndex((option) => option.label === "Todos");
        if (exist >= 0) {
          const newOptions = options.filter((opt) => opt.label !== "Todos");
          setConsumptionState({ field: 'selectedSquads', value: newOptions })
          setAllSquadsSelect(false);
          return;
        }
      } else {
        const isAll = options.find((option) => option.label === "Todos");
        if (isAll) {
          setConsumptionState({ field: 'selectedSquads', value: [allOption] })
          setAllSquadsSelect(true);
          return;
        }
      }
    }

    setConsumptionState({ field: 'selectedSquads', value: options })

  };

  return (
    <Col
      xl={{ span: 7, offset: 1 }}
      md={{ span: 7, offset: 1 }}
      lg={{ span: 17, offset: 0 }}
      sm={{ span: 7 }}
      style={{ marginBottom: 10 }}
    >
      <Typography.Text>Squad:</Typography.Text>
      <MultiSelect
        options={squadOptions}
        onChange={handleSelectSquad}
        value={selectedSquads}
      />
    </Col>
  )
}