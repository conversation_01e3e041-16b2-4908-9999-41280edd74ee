import { React, useEffect } from "react";
import { Checkbox, Select, Button, Form, Row } from "antd";
import { CloseCircleOutlined } from "@ant-design/icons";

function TicketsOfWalletManagesFilter(props) {
  const [form] = Form.useForm();
  const { Option } = Select;

  function onFilterFinish(values) {
    props.handlePopoverVisibility();
  }

  function onFilterFailed() {}

  function clear() {
    form.resetFields();
  }

  useEffect(() => {
    clear()
  }, [props.popoverVisibility]);

  return (
    <Form
      form={form}
      style={{ display: "flex",
      flexDirection: "column" ,
      width:"300px"}}
      name="basic"
      initialValues={{
        remember: true,
      }}
      onFinish={onFilterFinish}
      onFinishFailed={onFilterFailed}
      autoComplete="off"
    >
      <div style={{ display: "flex", justifyContent: "space-between", alignItems:"center", marginBottom:"10px", padding:"5px" }}>
        <h2 style={{margin:"0px", fontSize:"19px"}}>Tickets por carteira</h2>
        <CloseCircleOutlined onClick={()=> props.handlePopoverVisibility()} style={{width:"40px", height:"17px", color:"#868383"}} />
      </div>
      <div
        className="checkbox"
        style={{ display: "flex", justifyContent: "center" }}
      >
        <Form.Item
          style={{ width: "50%", textAlign: "center" }}
          valuePropName="checked"
          name="billada"
        >
          <Checkbox>Ativo</Checkbox>
        </Form.Item>

        <Form.Item
          style={{ width: "50%", textAlign: "center" }}
          valuePropName="checked"
          name="Nbillada"
        >
          <Checkbox>Inativo</Checkbox>
        </Form.Item>
      </div>

      <Form.Item name="Carteira">
        <Select placeholder={"Carteira"}>
          <Option value="Todos">Carteira</Option>
          <Option value="Logikee">Logikee</Option>
        </Select>
      </Form.Item>

      <Form.Item name="Cliente">
        <Select placeholder={"Cliente"}>
          <Option value="client">Darede</Option>
          <Option value="Logikee">Logikee</Option>
        </Select>
      </Form.Item>

      <Form.Item name="Contract">
        <Select placeholder={"Contrato"}>
          <Option value="Contract">Darede TI</Option>
          <Option value="Contract">Service Up</Option>
        </Select>
      </Form.Item>

      <Form.Item name="Status">
        <Select placeholder={"Status"}>
          <Option value="Status">Business Unity</Option>
        </Select>
      </Form.Item>

      <Form.Item name="age">
        <Select placeholder={"Idade"}>
          <Option value="age">Proprietario</Option>
        </Select>
      </Form.Item>

      <Row justify="space-between">
        <Button type="default" onClick={clear}>
          Limpar
        </Button>
        <Button type="primary" htmlType="submit">
          Aplicar
        </Button>
      </Row>
    </Form>
  );
}

export default TicketsOfWalletManagesFilter;
