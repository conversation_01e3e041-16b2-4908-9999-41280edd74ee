import { useEffect } from 'react'
import { React, useState } from 'react'
import { Select, Button, Form } from 'antd'
import { CloseSquareFilled } from '@ant-design/icons'

function NewContractsFilter(props) {
    const { Option } = Select;
    const [form] = Form.useForm();
    const { data, contractTypes } = props;

    function onFilterFinish(values) {
        props.filter(values);
        props.handlePopoverVisibility()
    }

    function getClientArray(){}

    function onFilterFailed() {}

    function clear() {
        form.resetFields();
    }
    useEffect(() => {
        getClientArray()
    }, [data, contractTypes])

    return (
        <Form
            form={form}
            className="flex flex-col"
            name="basic"
            onFinish={onFilterFinish}
            onFinishFailed={onFilterFailed}
            autoComplete="off"
        >
            <div style={{ display: 'flex',  justifyContent: 'space-between' }}>
                <h2>Novos contratos</h2>
                <button className='filter-close-btn'>X</button>
            </div>
            <Form.Item name="client">
                <Select
                    showSearch
                    loading={data.length === 0}
                    placeholder='Cliente'
                    optionFilterProp="children"
                    filterOption={(input, option) =>
                        option.props.children.toString.toLowerCase().indexOf(input.toLowerCase()) >= 0 ||
                        option.props.value.toLowerCase().indexOf(input.toLowerCase()) >= 0
                    }>
                    {
                        data.map((value) => {
                            return (
                                <Option key={value.name} value={value.client}>{value.client}</Option>
                            )

                        })
                    }
                </Select>
            </Form.Item>

            <Form.Item name="contractType">
                <Select
                    showSearch
                    className="w-full"
                    placeholder="Tipo de contrato"
                    optionFilterProp="children"
                    filterOption={(input, option) =>
                        option.props.children.toString().toLowerCase().indexOf(input.toLowerCase()) >= 0 ||
                        option.props.value.toLowerCase().indexOf(input.toLowerCase()) >= 0
                    }>
                    {
                        contractTypes.map((value) => {
                            return (
                                <Option key={value.name} value={value.type}>{value.name}</Option>
                            )

                        })
                    }
                </Select>
            </Form.Item>
            <Form.Item name="buttons">
                <div className="buttons">
                    <Button className="filter-clear-btn" onClick={clear}> Limpar </Button>
                    <Button htmlType="submit" className="filter-submit-btn"> Aplicar </Button>
                </div>
            </Form.Item>
        </Form>
    )
}

export default NewContractsFilter
