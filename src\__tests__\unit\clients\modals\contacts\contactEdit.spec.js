import { checksIfEmailAlreadyExists } from "../../../../../components/Modals/Clients/controllers/clientUsersModal";

describe("checksIfEmailAlreadyExists", () => {
  const clients = [
    {
      id: 1,
      contacts: [
        {
          email: "<EMAIL>",
          identifications: { itsm_id: "123" },
        },
        {
          email: "<EMAIL>",
          identifications: { itsm_id: "456" },
        },
      ],
    },
    {
      id: 2,
      contacts: [
        {
          email: "<EMAIL>",
          identifications: { itsm_id: "123" },
        },
      ],
    },
  ];
  it("should return true if email already exists for a different ITSM ID", () => {
    const newContact = {
      email: "<EMAIL>",
      identifications: { itsm_id: "789" },
    };

    const result = checksIfEmailAlreadyExists(clients, newContact, 1);
    expect(result).toBe(true);
  });

  it("should return false if email does not exist or has the same ITSM ID", () => {
    const newContact = {
      email: "<EMAIL>",
      identifications: { itsm_id: "789" },
    };

    const result = checksIfEmailAlreadyExists(clients, newContact, 2);
    expect(result).toBe(false);
  });
});
