import { Button, Modal } from "antd"
import { EyeOutlined } from '@ant-design/icons'
import { useState } from "react"




export const ViewBody = ({body}) => {
    const [showModal, setShowModal] = useState()

    const handleModalOpen = () => {
        setShowModal(true)
    }
    return(
        <>
            <Button icon={<EyeOutlined />} type="text" onClick={handleModalOpen}></Button>
            <Modal
                title="Conteúdo do artigo"
                open={showModal}
                footer={<Button type="primary" onClick={() => setShowModal(false)}>Fechar</Button>}
            >
                <div
                    dangerouslySetInnerHTML={{__html: body}}
                >
                </div>
            </Modal>
        </>
    )
}