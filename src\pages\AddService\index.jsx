import React, { useEffect, useState } from "react";
import {
  Layout,
  Form,
  Card,
  Row,
  Col,
  Input,
  Button,
  Typography,
  Affix,
  Tooltip,
  Select,
  Popconfirm,
} from "antd";
import {
  CloseCircleOutlined,
  ArrowLeftOutlined,
  CheckCircleOutlined,
  ArrowUpOutlined,
} from "@ant-design/icons";
import { estimatedActivities } from "../../constants/servicesFixedData";
import { HeaderMenu } from "../../components/HeaderMenu";
import { SideMenu } from "../../components/SideMenu";
import { useNavigate } from "react-router-dom";
import { v4 } from "uuid";
import { DisableIncrementScroll } from "../../hooks/DisableIncrementScroll";
import {
  handleCheckServiceName,
  handleGetServices,
  handleSubmit,
} from "../../controllers/services/index";
import { ActivitiesContent, TagInput } from "../../components/Services/index";
import { uniqByKeepLast } from "../../utils/uniqueByKeepLast";
import { shallowEqual, useSelector } from "react-redux";

export const AddService = () => {
  const navigate = useNavigate();
  const { Content } = Layout;
  const { Option } = Select;
  const { TextArea } = Input;
  const state = useSelector((state) => state.services.services, shallowEqual);
  const { Title, Text } = Typography;
  const [service_form] = Form.useForm();
  const [activities_form] = Form.useForm();
  const [allServices, setAllServices] = useState([]);
  const [existingName, setExistingName] = useState(false);
  const [toTopVisible, setToTopVisible] = useState(false);
  const [tagName, setTagName] = useState("");
  const [serviceName, setServiceName] = useState("");
  const [serviceDescription, setServiceDescription] = useState("");
  const [disabled, setDisabled] = useState(true);
  const [mainTagColorInput, setMainTagColorInput] = useState("#0f9347");
  const [collapsed, setCollapsed] = useState(false);
  const [selectTagToggle, setSelectTagToggle] = useState(false);
  const [loading, setLoading] = useState(false);

  let arr = [];

  const newArr = uniqByKeepLast(state, (e) => e.tag.name);

  newArr.forEach((e) => {
    return arr.push(e[1]);
  });

  const [activities, setActivities] = useState([
    {
      id: 0,
      name: "",
      description: "",
      active: true,
      management: false,
      subtasks: [{ index: v4(), value: "" }],
      estimates: {
        "8x5setup": 0,
        "24x7setup": 0,
        "8x5sust": 0,
        "24x7sust": 0,
        "8x5dbasec": 0,
        "24x7dbasec": 0,
      },
    },
  ]);
  const [sumHoursForType, setSumHoursForType] = useState([
    { SETUP247Sum: 0 },
    { SETUP85Sum: 0 },
    { SUST247Sum: 0 },
    { SUST85Sum: 0 },
    { DBA247Sum: 0 },
    { DBA85Sum: 0 },
  ]);

  const screenScroll = () => {
    document.documentElement.scrollTop > 360
      ? setToTopVisible(true)
      : setToTopVisible(false);
  };

  useEffect(() => {
    DisableIncrementScroll();

    window.addEventListener("scroll", screenScroll);
    if (state?.length === 0 || !state) {
      handleGetServices(setLoading);
    }
  }, []);

  useEffect(() => {
    setSumHoursForType([
      {
        SETUP247Sum: activities.reduce((acc, cur) => {
          if (cur["estimates"]["24x7setup"]) {
            let sum = Number(acc) + Number(cur["estimates"]["24x7setup"]);
            return sum;
          }
          return acc;
        }, 0),
        SETUP85Sum: activities.reduce((acc, cur) => {
          if (cur["estimates"]["8x5setup"]) {
            let sum = Number(acc) + Number(cur["estimates"]["8x5setup"]);
            return sum;
          }
          return acc;
        }, 0),
        SUST247Sum: activities.reduce((acc, cur) => {
          if (cur["estimates"]["24x7sust"]) {
            let sum = Number(acc) + Number(cur["estimates"]["24x7sust"]);
            return sum;
          }
          return acc;
        }, 0),
        SUST85Sum: activities.reduce((acc, cur) => {
          if (cur["estimates"]["8x5sust"]) {
            let sum = Number(acc) + Number(cur["estimates"]["8x5sust"]);
            return sum;
          }
          return acc;
        }, 0),
        DBA247Sum: activities.reduce((acc, cur) => {
          if (cur["estimates"]["24x7dbasec"]) {
            let sum = Number(acc) + Number(cur["estimates"]["24x7dbasec"]);
            return sum;
          }
          return acc;
        }, 0),
        DBA85Sum: activities.reduce((acc, cur) => {
          if (cur["estimates"]["8x5dbasec"]) {
            let sum = Number(acc) + Number(cur["estimates"]["8x5dbasec"]);
            return sum;
          }
          return acc;
        }, 0),
      },
    ]);

    activities.map(async (item) => {
      if (
        Object.values(item.estimates).find(
          (value) => value !== "" && value > 0
        ) &&
        item.name &&
        item.description &&
        tagName !== "" &&
        serviceName &&
        serviceName !== "" &&
        serviceDescription &&
        serviceDescription !== ""
      ) {
        setDisabled(false);
      } else {
        setDisabled(true);
      }
    });
  }, [activities, serviceName, serviceDescription, tagName]);

  return (
    <Layout style={{ minHeight: "100vh" }}>
      <HeaderMenu collapsed={collapsed} setCollapsed={setCollapsed} />
      <Layout>
        <SideMenu collapsed={collapsed} />
        <Content style={{ padding: "2em" }}>
          <Card
            style={{
              boxShadow: "0 0 10px rgba(0,0,0,0.1)",
              borderRadius: "20px",
            }}
          >
            <Popconfirm
              title="Ao voltar você perderá os dados do formulário, tem certeza?"
              onConfirm={() => {
                navigate(-1);
              }}
              okText="Sim"
              cancelText="Não"
            >
              <Button
                type="text"
                style={{ marginBottom: "15px", padding: "2px 5px 2px 0" }}
              >
                <ArrowLeftOutlined />
                Adicionar Serviço
              </Button>
            </Popconfirm>
            <Row justify="center">
              <Col span={24}>
                <Form
                  form={service_form}
                  layout="vertical"
                  requiredMark={false}
                  onFinish={() => {
                    handleSubmit(
                      activities_form.getFieldsValue(),
                      serviceName,
                      serviceDescription,
                      activities,
                      tagName,
                      false,
                      setLoading,
                      mainTagColorInput,
                      navigate,
                      existingName
                    );
                  }}
                >
                  <Row gutter={[20, 12]} justify="space-between">
                    <Col lg={16} sm={24}>
                      <Row justify="space-between">
                        <Col span={12}>
                          <Form.Item name="name">
                            <Text>
                              Nome{" "}
                              <span
                                style={{
                                  color: "#ff4d4f",
                                  fontWeight: "bold",
                                }}
                              >
                                *
                              </span>
                            </Text>
                            <Input
                              placeholder="Nome do serviço"
                              style={{
                                borderColor: !serviceName ? "#ff4d4f" : "",
                                marginTop: 8,
                              }}
                              onChange={(e) => {
                                handleCheckServiceName(
                                  e.target.value,
                                  allServices,
                                  setExistingName
                                );
                                setServiceName(e.target.value);
                              }}
                            />
                            {!serviceName && (
                              <span
                                style={{
                                  color: "#ff4d4f",
                                  fontSize: 13,
                                }}
                              >
                                * valor não informado, campo obrigatório.
                              </span>
                            )}
                          </Form.Item>
                        </Col>
                        <TagInput
                          selectTagToggle={selectTagToggle}
                          setSelectTagToggle={setSelectTagToggle}
                          tagName={tagName}
                          setTagName={setTagName}
                          state={state}
                          mainTagColorInput={mainTagColorInput}
                          setMainTagColorInput={setMainTagColorInput}
                          arr={arr}
                        />
                      </Row>
                      <Row>
                        <Col span={24}>
                          <Form.Item name="description">
                            <Text>
                              Descrição de serviço{" "}
                              <span
                                style={{
                                  color: "#ff4d4f",
                                  fontWeight: "bold",
                                }}
                              >
                                *
                              </span>
                            </Text>
                            <TextArea
                              rows={6}
                              placeholder="Insira a descrição do serviço"
                              value={serviceDescription}
                              onChange={(e) =>
                                setServiceDescription(e.target.value)
                              }
                              style={{
                                marginTop: "8px",
                              }}
                            />
                            {!serviceDescription && (
                              <span
                                style={{
                                  color: "#ff4d4f",
                                  fontSize: 13,
                                }}
                              >
                                * valor não informado, campo obrigatório.
                              </span>
                            )}
                          </Form.Item>
                        </Col>
                      </Row>
                    </Col>
                    <Col lg={8} sm={24}>
                      <div
                        style={{
                          widht: "100%",
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "center",
                          flexDirection: "column",
                        }}
                      >
                        <Text>Estimativas</Text>
                        <Row
                          gutter={[16, 16]}
                          style={{
                            backgroundColor: "#D9D9D9",
                            padding: "30px",
                            borderRadius: "5px",
                          }}
                        >
                          {estimatedActivities.map(
                            (
                              mainEstimatedActivity,
                              mainEstimatedActivityKey
                            ) => {
                              return (
                                <Col span={12} key={mainEstimatedActivityKey}>
                                  <Row align="center">
                                    {mainEstimatedActivity.title}
                                  </Row>
                                  {sumHoursForType.map(
                                    (
                                      sumHoursForTypeItem,
                                      sumHoursForTypeKey
                                    ) => {
                                      return Object.values(
                                        sumHoursForTypeItem
                                      ).map(
                                        (
                                          sumHoursForTypeItemValue,
                                          sumHoursForTypeValueKey
                                        ) => {
                                          return (
                                            sumHoursForTypeValueKey ===
                                              mainEstimatedActivityKey && (
                                              <Input
                                                placeholder={
                                                  sumHoursForTypeItemValue
                                                }
                                                disabled
                                              />
                                            )
                                          );
                                        }
                                      );
                                    }
                                  )}
                                </Col>
                              );
                            }
                          )}
                        </Row>
                      </div>
                    </Col>
                  </Row>
                </Form>
                <Form
                  layout="vertical"
                  requiredMark={false}
                  form={activities_form}
                  style={{ marginTop: "1rem" }}
                >
                  <Text>Atividades</Text>
                  <ActivitiesContent
                    activities={activities}
                    setActivities={setActivities}
                  />
                </Form>

                <Row justify="space-between">
                  <Button danger type="primary" onClick={() => navigate(-1)}>
                    Cancelar <CloseCircleOutlined />
                  </Button>
                  {disabled === true ? (
                    <Tooltip
                      placement="top"
                      title={"Verifique os dados das atividades"}
                    >
                      <Button
                        type="primary"
                        onClick={() => service_form.submit()}
                        disabled={disabled}
                      >
                        Concluir <CheckCircleOutlined />
                      </Button>
                    </Tooltip>
                  ) : (
                    <Button
                      type="primary"
                      onClick={() => service_form.submit()}
                      disabled={disabled}
                      loading={loading}
                    >
                      Concluir <CheckCircleOutlined />
                    </Button>
                  )}
                </Row>
              </Col>
            </Row>
          </Card>
        </Content>
        {toTopVisible === true && (
          <Affix
            style={{
              clipPath: "circle()",
              position: "fixed",
              right: 5,
              bottom: 5,
              width: "50px",
            }}
          >
            <Button
              style={{ width: "100%" }}
              type="primary"
              onClick={() => window.scrollTo({ top: 0, behavior: "smooth" })}
            >
              <ArrowUpOutlined />
            </Button>
          </Affix>
        )}
      </Layout>
    </Layout>
  );
};
