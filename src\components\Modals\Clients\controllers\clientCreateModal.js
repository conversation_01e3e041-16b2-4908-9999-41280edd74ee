import { message } from "antd";
import { format } from "date-fns";
import { formatCNPJ } from "../../../../utils/getCNPJ";

export const formatCreateOTRSCustomerBody = (data, cnpj) => {
  if (!cnpj) throw new Error("CNPJ is not defined");
  if (!data) throw new Error("Data is not defined");
  const { estabelecimento, razao_social } = cnpj;
  const otrs_post = {
    name: data.fantasy_name,
    cnpj: data.cnpj,
    razaosocial: razao_social,
    street: `${estabelecimento.tipo_logradouro} ${
      estabelecimento.logradouro
    }, ${estabelecimento.bairro}${
      estabelecimento.complemento ? " " + estabelecimento.complemento : ""
    }`,
    country: estabelecimento.pais.nome,
    city: estabelecimento.cidade.nome,
    zip: estabelecimento.cep,
    comments: "",
    url: "",
    faq: "",
  };

  return otrs_post;
};

export const formatCreateDSMCustomerBody = (data, cnpj, otrs) => {
  const {
    natureza_juridica,
    estabelecimento,
    capital_social,
    razao_social,
    socios,
    porte,
  } = cnpj;
  const { bairro, cep, complemento, logradouro, numero, tipo_logradouro } =
    estabelecimento;

  const submit_data = {
    identifications: {
      crm_id: data.crm_id || null,
      itsm_id: otrs.data.customer_id.id,
    },
    social_capital: capital_social,
    legal_nature: natureza_juridica,
    names: {
      fantasy_name: data.fantasy_name,
      name: razao_social,
    },
    phones: {
      ddd1: estabelecimento.ddd1,
      telefone1: estabelecimento.telefone1,
      ddd2: estabelecimento.ddd2,
      telefone2: estabelecimento.telefone2,
      ddd_fax: estabelecimento.ddd_fax,
      fax: estabelecimento.fax,
    },
    cnpj: data.cnpj,
    members: socios,
    address: {
      cep,
      bairro,
      numero,
      logradouro,
      complemento,
      tipo_logradouro,
      pais: estabelecimento.pais.nome,
      cidade: estabelecimento.cidade.nome,
    },
    extent: porte,
    contacts: [],
    accounts: [],
    created_at: format(new Date(), "yyyy-MM-dd HH:mm:ss"),
    updated_at: format(new Date(), "yyyy-MM-dd HH:mm:ss"),
    active: 1,
    has_active_contracts: 2,
    created_at: format(new Date(), "yyyy-MM-dd HH:mm:ss"),
    updated_at: format(new Date(), "yyyy-MM-dd HH:mm:ss"),
  };

  return submit_data;
};

export const checkCNPJExists = (customers_formatted, form, data) => {
  if (!customers_formatted || customers_formatted === null)
    throw new Error("Customers not found");
  if (!form || form === null) throw new Error("Form not found");
  if (!data) throw new Error("Data not found");

  for (const customer of customers_formatted) {
    let currentCustomerCNPJ = customer?.cnpj ? formatCNPJ(customer?.cnpj) : "";
    let newCustomerCNPJ = formatCNPJ(data?.cnpj || "");
    if (currentCustomerCNPJ === newCustomerCNPJ && newCustomerCNPJ !== "") {
      form.resetFields();
      return {
        message: message.error(
          "Já existe um cliente com este CNPJ dentro do OTRS..."
        ),
        error: true,
      };
    }
  }
};
