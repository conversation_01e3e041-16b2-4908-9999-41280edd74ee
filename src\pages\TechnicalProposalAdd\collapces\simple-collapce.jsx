import React from "react";
import { useSelector, shallowEqual } from "react-redux";
import { Row, Col, Form } from "antd";

import RichTextEditor from "../../../components/RichTextEditor/RichTextEditor";
import { MainCollapse } from "../../../components/Collapse/CommercialProposal";

import { setTechnicalProposalState } from "../../../store/actions/technical-proposal-action";

export const SimpleCollapce = ({ fieldName, formName, title, children }) => {
  const value = useSelector(
    (state) => state.technicalProposal[fieldName],
    shallowEqual
  );

  const handleDraftItems = (itemName, value) => {
    setTechnicalProposalState({ field: fieldName, value });
  };

  return (
    <Row>
      <Col span={24} key={0}>
        <MainCollapse
          title={title}
          editor={
            <Form.Item name={formName} initialValue={value}>
              <RichTextEditor
                value={value}
                handleDraftItems={handleDraftItems}
                itemName={fieldName}
              />

              <br />

              {children}
            </Form.Item>
          }
        ></MainCollapse>
      </Col>
    </Row>
  );
};
