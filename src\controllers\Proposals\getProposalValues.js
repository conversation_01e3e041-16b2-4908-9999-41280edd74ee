export function getProposalValues(item, type) {
  const totalValues = item?.totalValues?.find(
    (v) => v.hourClass === "hourValue"
  ).values;
  const month = item?.monthSelected ? item?.monthSelected : "12";
  let currencyFormat = Intl.NumberFormat("pt-BR");
  if (totalValues === {} || !totalValues) return 0;
  if (type === "setup") {
    let valuesByMonth = totalValues.find((i) => i.month === month).values;
    let totalValue8x5 = valuesByMonth?.find(
      (i) => i.formName === "setup8x5"
    )?.totalValue;
    let totalValue24x7 = valuesByMonth?.find(
      (i) => i.formName === "setup24x7"
    )?.totalValue;
    return totalValue8x5 && totalValue24x7
      ? currencyFormat.format(totalValue8x5 + totalValue24x7)
      : 0;
  } else if (type === "sust") {
    let valuesByMonth = totalValues.find((i) => i.month === month).values;
    let totalValue8x5 = valuesByMonth?.find(
      (i) => i.formName === "sust8x5"
    )?.totalValue;
    let totalValue24x7 = valuesByMonth?.find(
      (i) => i.formName === "sust24x7"
    )?.totalValue;
    return totalValue8x5 && totalValue24x7
      ? currencyFormat.format(totalValue8x5 + totalValue24x7)
      : 0;
  } else if (type === "setupsust") {
    if (item.totalValues) {
      let valuesByMonth = totalValues.find(
        (i) => i.month === item.month
      ).values;
      let totalValue8x5Setup = valuesByMonth?.find(
        (i) => i.formName === "setup8x5"
      )?.totalValue;
      let totalValue24x7Setup = valuesByMonth?.find(
        (i) => i.formName === "setup24x7"
      )?.totalValue;
      let totalValue8x5Sust = valuesByMonth?.find(
        (i) => i.formName === "sust8x5"
      )?.totalValue;
      let totalValue24x7Sust = valuesByMonth?.find(
        (i) => i.formName === "sust24x7"
      )?.totalValue;
      const sum =
        totalValue8x5Setup +
        totalValue24x7Setup +
        totalValue8x5Sust +
        totalValue24x7Sust;
      return currencyFormat.format(sum);
    } else {
      return 0;
    }
  } else {
    return 0;
  }
}
