import { message } from "antd";
import { logNewAuditAction } from "../../controllers/audit/logNewAuditAction";
import { apiProposals, getHeader } from "../../utils/api";

export function capitalizeFirstLetter(username) {
  let names = username?.split(".");

  names?.forEach((name, index) => {
    names[index] = name.charAt(0).toUpperCase() + name.slice(1);
  });

  return names?.join(" ");
}

export function payloadPDF(data) {
  const {
    id,
    name,
    customer,
    mainOportunity,
    paymentSelected,
    pipedriveDealOwner,
  } = data;

  let user = {
    name: capitalizeFirstLetter(localStorage.getItem("@dsm/name")) || "",
    mail: localStorage.getItem("@dsm/mail") || "",
  };

  if (pipedriveDealOwner) {
    if (pipedriveDealOwner.name)
      user = {
        name: pipedriveDealOwner.name,
        mail: pipedriveDealOwner.email,
      };
  }

  const coustomerName = customer.name
    ? customer.name
    : customer.names.fantasy_name;

  return {
    customer: coustomerName,
    project: name,
    proposalID: id,
    mainOportunity: mainOportunity,
    paymentMethod: paymentSelected,
    user: user,
  };
}

export async function generatePDF(data) {
  try {
    const headers = getHeader();

    const payload = payloadPDF(data);

    apiProposals
      .post(`/document`, payload, { headers })
      .then((response) => {
        const { url } = response.data;
        window.open(url);
        const username = localStorage.getItem("@dsm/username");
        const title = "Download de PDF";
        const description = `${username} realizou o download do pdf referente a proposta ${payload.name}`;
        logNewAuditAction(username, title, description);
      })
      .catch((error) => {
        if (error.response) {
          const { status } = error.response;

          if (status === 404) {
            message.success(
              "Seu PDF ainda está sendo gerado, aguarde alguns instantes e tente novamente."
            );
            return;
          }
        }

        console.log(error);
        message.error("Ops.. não foi possível baixar o pdf!");
      });
  } catch (error) {
    message.error("Ops.. não foi possível baixar o pdf!");
  }
}
