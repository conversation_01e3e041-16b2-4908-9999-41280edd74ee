export const inputFinopsMockedData = [
  {
    id: "a1b2c3d4e5",
    created_at: "2023-06-11 15:30:00",
    updated_at: "2023-06-11 16:00:00",
    payer_account: "*************",
    identifications: [
      {
        contract_id: "*********",
        customer_id: "*********",
        customer_name: "Empty Company",
      },
    ],
    total: 100,
    month: "January",
    year: 2023,
    accounts: ["Account1", "Account2"],
    invoice_id: "ABC123",
    total_bundled_discount: 0,
    total_credit: 0,
    total_discount: 0,
    total_edp_discount: 0,
    total_neg: 0,
    total_pos: 0,
    total_saving_plans: 0,
    total_spp_discount: 0,
  },
  {
    identifications: [
      {
        contract_id: "*********",
        customer_id: "*********",
        customer_name: "Empty Company",
      },
    ],
    total: 200,
    month: "February",
    year: 2023,
    accounts: ["Account3", "Account4"],
    invoice_id: "DEF456",
    payer_account: "*********",
    total_bundled_discount: 0,
    total_credit: 0,
    total_discount: 0,
    total_edp_discount: 0,
    total_neg: 0,
    total_pos: 0,
    total_saving_plans: 0,
    total_spp_discount: 0,
    updated_at: "2023-06-11 16:00:00",
  },
];

export const outputFinopsMockedData = [
  {
    payer_account: "*************",
    identifications: [
      {
        contract_id: "*********",
        customer_id: "*********",
        customer_name: "Empty Company",
      },
    ],
    total: 100,
    month: "January",
    year: 2023,
    accounts: ["Account1", "Account2"],
    invoice_id: "ABC123",
  },
  {
    payer_account: "*********",
    identifications: [
      {
        contract_id: "*********",
        customer_id: "*********",
        customer_name: "Empty Company",
      },
    ],
    total: 200,
    month: "February",
    year: 2023,
    accounts: ["Account3", "Account4"],
    invoice_id: "DEF456",
  },
];

export const inputInvoicesMockedData = [
  {
    bill_billing_entity: "AWS",
    bill_invoice_id: "**********",
    bill_payer_account_id: "************",
    charges: 52498.22,
    conversion: "0",
    customer: "MOCK TECNOLOGICAS LTDA",
    discount: 0,
    final_balance: 52498.22,
    line_item_legal_entity: "Amazon AWS Serviços Brasil Ltda.",
    line_item_type: "SavingsPlanCoveredUsage",
    line_item_usage_account_id: "************",
    month: 3,
    spp_discount: 0,
    tax: 0,
    total_bundled_discount: 0,
    total_credits: 0,
    total_discount: -52498.22,
    total_edp_discount: 0,
    total_saving_plans_discount: -52498.22,
    unblended_cost: 52498.22,
    year: 2024,
    other_discounts: 0,
  },
  {
    bill_billing_entity: "AWS",
    bill_invoice_id: "**********",
    bill_payer_account_id: "************",
    charges: 152498.22,
    conversion: "0",
    customer: "MOCK TECNOLOGICAS LTDA",
    discount: 0,
    final_balance: 152498.22,
    line_item_legal_entity: "Amazon AWS Serviços Brasil Ltda.",
    line_item_type: "Usage",
    line_item_usage_account_id: "************",
    month: 3,
    spp_discount: 0,
    tax: 0,
    total_bundled_discount: 0,
    total_credits: 0,
    total_discount: 0,
    total_edp_discount: 0,
    total_saving_plans_discount: 0,
    unblended_cost: 152498.22,
    year: 2024,
    other_discounts: 0,
  },
  {
    bill_billing_entity: "AWS",
    bill_invoice_id: "**********",
    bill_payer_account_id: "************",
    charges: 5306.06,
    conversion: "0",
    customer: "MOCK TECNOLOGICAS LTDA",
    discount: 0,
    final_balance: 5306.06,
    line_item_legal_entity: "Amazon AWS Serviços Brasil Ltda.",
    line_item_type: "Tax",
    line_item_usage_account_id: "************",
    month: 3,
    spp_discount: 0,
    tax: 5306.06,
    total_bundled_discount: 0,
    total_credits: 0,
    total_discount: 0,
    total_edp_discount: 0,
    total_saving_plans_discount: 0,
    unblended_cost: 5306.06,
    year: 2024,
    other_discounts: 0,
  },
  {
    bill_billing_entity: "AWS",
    bill_invoice_id: "**********",
    bill_payer_account_id: "************",
    charges: 70080.22,
    conversion: "0",
    customer: "MOCK2 LTDA",
    discount: 0,
    final_balance: 70080.22,
    line_item_legal_entity: "Amazon AWS Serviços Brasil Ltda.",
    line_item_type: "Usage",
    line_item_usage_account_id: "************",
    month: 3,
    spp_discount: 0,
    tax: 0,
    total_bundled_discount: 0,
    total_credits: 0,
    total_discount: 0,
    total_edp_discount: 0,
    total_saving_plans_discount: 0,
    unblended_cost: 70080.22,
    year: 2024,
    other_discounts: 0,
  },
];

export const outputGroupInvoiceByInvoiceId = [
  {
    bill_billing_entity: "AWS",
    bill_invoice_id: "**********",
    bill_payer_account_id: "************",
    charges: "210302.50",
    conversion: "1114603.25",
    customer: "MOCK TECNOLOGICAS LTDA",
    discount: 0,
    final_balance: "210302.50",
    final_balance_real: "1114603.25",
    line_item_legal_entity: "Amazon AWS Serviços Brasil Ltda.",
    line_item_type: "SavingsPlanCoveredUsage",
    line_item_usage_account_id: "************",
    month: 3,
    other_discounts: "0.00",
    spp_discount: "0.00",
    tax: "5306.06",
    tax_percentage: "3.36%",
    total_bundled_discount: "0.00",
    total_credits: "0.00",
    total_discount: "-52498.22",
    total_edp_discount: "0.00",
    total_saving_plans_discount: "-52498.22",
    unblended_cost: "210302.50",
    year: 2024,
  },
  {
    bill_billing_entity: "AWS",
    bill_invoice_id: "**********",
    bill_payer_account_id: "************",
    charges: 70080.22,
    conversion: "0.00",
    customer: "MOCK2 LTDA",
    discount: 0,
    final_balance: "70080.22",
    final_balance_real: "371425.17",
    line_item_legal_entity: "Amazon AWS Serviços Brasil Ltda.",
    line_item_type: "Usage",
    line_item_usage_account_id: "************",
    month: 3,
    other_discounts: "0.00",
    spp_discount: "0.00",
    tax: "0.00",
    total_bundled_discount: "0.00",
    total_credits: "0.00",
    total_discount: "0.00",
    total_edp_discount: "0.00",
    total_saving_plans_discount: "0.00",
    unblended_cost: "70080.22",
    year: 2024,
  },
];

export const outputGroupInvoiceByAccount = [
  {
    bill_billing_entity: "AWS",
    bill_invoice_id: "**********",
    bill_payer_account_id: "************",
    charges: "204996.44",
    conversion: "1086481.13",
    customer: "MOCK TECNOLOGICAS LTDA",
    discount: 0,
    final_balance: "204996.44",
    final_balance_real: "1086481.13",
    line_item_legal_entity: "Amazon AWS Serviços Brasil Ltda.",
    line_item_type: "SavingsPlanCoveredUsage",
    line_item_usage_account_id: "************",
    month: 3,
    other_discounts: "0.00",
    spp_discount: "0.00",
    tax: "0.00",
    tax_percentage: "0.00%",
    total_bundled_discount: "0.00",
    total_credits: "0.00",
    total_discount: "-52498.22",
    total_edp_discount: "0.00",
    total_saving_plans_discount: "-52498.22",
    unblended_cost: "204996.44",
    year: 2024,
  },
  {
    bill_billing_entity: "AWS",
    bill_invoice_id: "**********",
    bill_payer_account_id: "************",
    charges: 5306.06,
    conversion: "0.00",
    customer: "MOCK TECNOLOGICAS LTDA",
    discount: 0,
    final_balance: "5306.06",
    final_balance_real: "28122.12",
    line_item_legal_entity: "Amazon AWS Serviços Brasil Ltda.",
    line_item_type: "Tax",
    line_item_usage_account_id: "************",
    month: 3,
    other_discounts: "0.00",
    spp_discount: "0.00",
    tax: "5306.06",
    total_bundled_discount: "0.00",
    total_credits: "0.00",
    total_discount: "0.00",
    total_edp_discount: "0.00",
    total_saving_plans_discount: "0.00",
    unblended_cost: "5306.06",
    year: 2024,
  },
  {
    bill_billing_entity: "AWS",
    bill_invoice_id: "**********",
    bill_payer_account_id: "************",
    charges: 70080.22,
    conversion: "0.00",
    customer: "MOCK2 LTDA",
    discount: 0,
    final_balance: "70080.22",
    final_balance_real: "371425.17",
    line_item_legal_entity: "Amazon AWS Serviços Brasil Ltda.",
    line_item_type: "Usage",
    line_item_usage_account_id: "************",
    month: 3,
    other_discounts: "0.00",
    spp_discount: "0.00",
    tax: "0.00",
    total_bundled_discount: "0.00",
    total_credits: "0.00",
    total_discount: "0.00",
    total_edp_discount: "0.00",
    total_saving_plans_discount: "0.00",
    unblended_cost: "70080.22",
    year: 2024,
  },
];
