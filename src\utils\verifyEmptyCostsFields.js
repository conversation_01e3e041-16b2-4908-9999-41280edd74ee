import { message } from "antd";

export async function verifyEmptyCostsFields(awsCostsArr, addCosts, formData) {
  let emptyFields = [];
  for (let i = 0; i < awsCostsArr.length; i++) {
    const e = awsCostsArr[i];
    if (!e.calculator_link || e.calculator_link === "") {
      message.error("Preencha o campo Calculadora corretamente");
      emptyFields.push(false);
    } else if (!e.month_value || e.month_value === "") {
      message.error("Preencha o campo Calculadora corretamente");
      emptyFields.push(false);
    } else if (!e.title || e.title === "") {
      message.error("Preencha o campo Calculadora corretamente");
      emptyFields.push(false);
    } else if (!e.year_value || e.year_value === "") {
      message.error("Preencha o campo Calculadora corretamente");
      emptyFields.push(false);
    }
  }
  for (let i = 0; i < addCosts.length; i++) {
    const e = addCosts[i];
    if (!e.month_value || e.month_value === "") {
      message.error("Preencha o campo Custos adicionais corretamente");
      emptyFields.push(false);
    } else if (!e.title || e.title === "") {
      message.error("Preencha o campo Custos adicionais corretamente");
      emptyFields.push(false);
    } else if (!e.year_value || e.year_value === "") {
      message.error("Preencha o campo Custos adicionais corretamente");
      emptyFields.push(false);
    }
  }

  Object.values(formData).forEach((item, key) => {
    if (
      item === undefined ||
      item === null ||
      item === "" ||
      item?.length === 0
    ) {
      switch (Object.keys(formData)[key]) {
        case "project_name":
          if (
            !Object.keys(formData)["project_name"] ||
            Object.keys(formData)["project_name"] === ""
          ) {
            emptyFields.push(false);
          }
        case "project_type":
          if (
            !Object.keys(formData)["project_type"] ||
            Object.keys(formData)["project_type"] === ""
          ) {
            emptyFields.push(false);
          }
        case "project_status":
          if (
            !Object.keys(formData)["project_status"] ||
            Object.keys(formData)["project_status"] === ""
          ) {
            emptyFields.push(false);
          }
        case "project_opportunity":
          if (
            !Object.keys(formData)["project_opportunity"] ||
            Object.keys(formData)["project_opportunity"] === ""
          ) {
            emptyFields.push(false);
          }
        case "project_architects":
          const architectsArray = Object.keys(formData)["project_architects"];
          if (!architectsArray || architectsArray.length === 0) {
            emptyFields.push(false);
          }
        case "project_contacts":
          if (
            !Object.keys(formData)["project_contacts"] ||
            Object.keys(formData)["project_contacts"] === ""
          ) {
            emptyFields.push(false);
          }
        case "project_bu":
          const busArray = Object.keys(formData)["project_bu"];
          if (!busArray || busArray.length === 0) {
            emptyFields.push(false);
          }
      }
    }
  });

  return emptyFields;
}
