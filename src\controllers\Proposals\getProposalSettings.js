import { apiProposals, getHeader } from "../../utils/api";

export async function getProposalSettings() {
  const headers = getHeader();

  const premisses = await apiProposals
    .get("setting/propose-pdf-premises", { headers })
    .then((response) => {
      const { setting } = response.data;
      return setting.value.replace(/\\"/, "").replace(/\\n/, "");
    })
    .catch((error) => {
      return "";
    });

  const factors = await apiProposals
    .get("setting/propose-pdf-factors", { headers })
    .then((response) => {
      const { setting } = response.data;
      return setting.value.replace(/\\"/, "").replace(/\\n/, "");
    })
    .catch((error) => {
      return "";
    });

  const convered = await apiProposals
    .get("setting/propose-pdf-not-covered", { headers })
    .then((response) => {
      const { setting } = response.data;
      return setting.value.replace(/\\"/, "").replace(/\\n/, "");
    })
    .catch((error) => {
      return "";
    });

  return {
    premisses: premisses,
    main_factors: factors,
    extra_points: convered,
  };
}

export async function getPremisesSettings() {
  const headers = getHeader();

  const premisses = await apiProposals
    .get("setting/propose-pdf-premises", { headers })
    .then((response) => {
      const { setting } = response.data;
      return setting.value.replace(/\\"/, "").replace(/\\n/, "");
    })
    .catch((error) => {
      return "";
    });

  return premisses;
}

export async function getFactorsSettings() {
  const headers = getHeader();

  const factors = await apiProposals
    .get("setting/propose-pdf-factors", { headers })
    .then((response) => {
      const { setting } = response.data;
      return setting.value.replace(/\\"/, "").replace(/\\n/, "");
    })
    .catch((error) => {
      return "";
    });

  return factors;
}

export async function getConveredSettings() {
  const headers = getHeader();

  const convered = await apiProposals
    .get("setting/propose-pdf-not-covered", { headers })
    .then((response) => {
      const { setting } = response.data;
      return setting.value.replace(/\\"/, "").replace(/\\n/, "");
    })
    .catch((error) => {
      return "";
    });

  return convered;
}