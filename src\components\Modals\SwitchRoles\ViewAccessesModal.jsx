import {
  Col,
  Row,
  Tag,
  Space,
  Modal,
  Table,
  Button,
  Select,
  Typography,
  Input,
  Tooltip,
} from "antd";
import { useState } from "react";
import { differenceInMinutes } from "date-fns";
import { TeamOutlined } from "@ant-design/icons";

export const ViewAccessesModal = (props) => {
  const { solicitations, client, permissions } = props;
  const [showModal, setShowModal] = useState(false);
  const [time, setTime] = useState(new Date());
  const [search, setSearch] = useState("");
  const [state, setState] = useState("");
  const { Text } = Typography;
  const { Option } = Select;

  function setT() {
    setTime(new Date());
  }

  function diff(a) {
    return differenceInMinutes(new Date(time), new Date(a));
  }

  const columns = [
    {
      code: "view_actions_ticket",
      dataIndex: "client_ticket",
      title: "Ticket",
      sorter: (a, b) => a.client_ticket - b.client_ticket,
      render: (ticket) => (
        <a
          target="_blank"
          rel="noreferrer"
          style={{ color: "#0f9347" }}
          href={`https://${
            process.env.REACT_APP_STAGE !== "prod" ? "hml." : ""
          }tickets.darede.com.br/otrs/index.pl?Action=AgentTicketZoom;TicketNumber=${ticket}`}
        >
          {ticket}
        </a>
      ),
    },
    {
      code: "view_actions_client",
      dataIndex: "client_name",
      title: "Nome do Cliente",
      sorter: (a, b) => a.client_name.length - b.client_name.length,
    },
    {
      code: "view_actions_user",
      dataIndex: "username",
      title: "Usuário",
      sorter: (a, b) => a.username.length - b.username.length,
    },
    {
      code: "view_actions_time",
      dataIndex: "time",
      title: "Tempo",
      width: "1%",
      sorter: (a, b) => a.time - b.time,
      render: (time) => {
        return <Row justify="center">{time ? time / 60 + "h" : 0 + "h"}</Row>;
      },
    },
    {
      code: "view_actions_allowed",
      dataIndex: "allowed",
      title: "Permitido",
      width: "1%",
      render: (a) =>
        a ? (
          <Row justify="center">
            <Tag color="green">Permitido</Tag>
          </Row>
        ) : (
          <Row justify="center">
            <Tag color="red">Não Permitido</Tag>
          </Row>
        ),
    },
    {
      code: "view_actions_account",
      dataIndex: "account_id",
      title: "Conta",
      width: "1%",
      render: (a, b) =>
        b.username !== localStorage.getItem("@dsm/username") ? (
          <Row justify="center">{b.account_id}</Row>
        ) : b.allowed ? (
          <Row justify="center">
            <a
              rel="noreferrer"
              style={{ color: "#0f9347" }}
              target="_blank"
              href={`https://signin.aws.amazon.com/switchrole?account=${
                b.account_id
              }&roleName=${
                b.role
                  ? b.role
                  : localStorage.getItem("@dsm/username").replace(".", "-")
              }&displayName=${
                b.role
                  ? b.role
                  : localStorage.getItem("@dsm/username").replace(".", "-")
              }`}
            >
              {b.account_id}
            </a>
          </Row>
        ) : (
          <Row justify="center">
            <Tag color="red">Não Permitido</Tag>
          </Row>
        ),
    },
    {
      code: "view_actions_active",
      dataIndex: "active",
      title: "Tempo",
      width: "1%",
      render: (a, item) => {
        return a &&
          item.allowed &&
          item.time - diff(new Date(item.updated_at)) > 0 ? (
          <Row justify="center">
            <Tag color="green">
              Restam {item.time - diff(new Date(item.updated_at))}
              min.
            </Tag>
          </Row>
        ) : a && item.allowed ? (
          <Row justify="center">
            <Tag color="red">Tempo Expirado</Tag>
          </Row>
        ) : !a && item.allowed ? (
          <Row justify="center">
            <Tag color="red">Tempo Expirado</Tag>
          </Row>
        ) : (
          <Row justify="center">
            <Tag color="red">Inativo</Tag>
          </Row>
        );
      },
    },
  ];
  return (
    <>
      <Tooltip
        title="Visualizar acessos de contas solicitadas"
        placement="left"
      >
        <Button
          type="primary"
          onClick={async () => {
            setShowModal(true);
            setInterval(setT, 30000);
          }}
        >
          <TeamOutlined />
        </Button>
      </Tooltip>
      <Modal
        title={"Solicitações para acessar - " + client.client_name}
        style={{ minWidth: "65vw" }}
        open={showModal}
        onCancel={() => {
          setShowModal(false);
        }}
        closable={false}
        footer={[
          <Button key="close-button" type="primary" onClick={() => setShowModal(false)}>
            Fechar
          </Button>,
        ]}
      >
        <Row justify="space-between">
          <Col span={8} style={{ marginBottom: "1em", borderRadius: "15px" }}>
            <Input
              placeholder="Faça uma busca"
              style={{
                height: "35px",
                borderRadius: "7px",
              }}
              onChange={(e) => setSearch(e.target.value)}
            />
          </Col>
          <Col>
            <Space>
              <Text>Filtrar por: </Text>
              <Select onChange={setState} defaultValue="todos">
                <Option value="active">Ativos</Option>
                <Option value="todos">Todos</Option>
                <Option value="inactive">Inativos</Option>
              </Select>
            </Space>
          </Col>
        </Row>
        <Table
          scroll={{ x: "100%" }}
          rowKey={(record) => `${record.client_ticket}-${record.username}-${record.account_id}`}
          dataSource={solicitations?.filter((e) => {
            const data = [
              e?.username !== null && e?.username !== ""
                ? e?.username
                    ?.toLowerCase()
                    .toString()
                    .includes(search.toLowerCase())
                : false,
              e?.ticket_id !== null && e?.ticket_id !== ""
                ? e?.ticket_id.toString().includes(search.toLowerCase())
                : false,
              e?.account_id !== null && e?.account_id !== ""
                ? e?.account_id.toString().includes(search.toLowerCase())
                : false,
            ];
            if (state === "active") {
              for (let i = 0; i < data.length; i++) {
                if (data[i] === true) {
                  return e.active === "Aprovado";
                }
              }
            } else if (state === "inactive") {
              for (let i = 0; i < data.length; i++) {
                if (data[i] === true) {
                  return e.active !== "Aprovado";
                }
              }
            } else if (state === "todos" || state === "") {
              for (let i = 0; i < data.length; i++) {
                if (data[i] === true) {
                  return e;
                }
              }
            }

            if (search === "") return e;

            return null;
          })}
          columns={columns.filter((e) =>
            permissions
              ?.map((permission) => {
                return permission.code;
              })
              .includes(e.code)
          )}
        />
      </Modal>
    </>
  );
};
