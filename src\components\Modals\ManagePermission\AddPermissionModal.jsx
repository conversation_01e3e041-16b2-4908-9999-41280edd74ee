import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  But<PERSON>,
  Row,
  Col,
  Form,
  Input,
  Divider,
  Select,
  Table,
  Tag,
  Popconfirm,
  message,
} from "antd";
import { PlusOutlined, DeleteOutlined } from "@ant-design/icons";
import { dynamoGet, dynamoPost } from "../../../service/apiDsmDynamo";
import { logNewAuditAction } from "../../../controllers/audit/logNewAuditAction";
import { RequiredLabelForm } from "../../RequiredLabelForm";

export const AddPermissionModal = ({ getPermission }) => {
  const [loading, setLoading] = useState(false);
  const [show, setShow] = useState();
  const [selectedPageFunctions, setSelectedPageFunctions] = useState([]);
  const [pageSelection, setPageSelection] = useState();
  const [functionSelected, setFunctionSelected] = useState();
  const [disable, setDisable] = useState(false);
  const [dataTabela, setDataTabela] = useState([]);
  const [permissions, setPermissions] = useState([]);
  const [list, setList] = useState([]);

  const getPermissionData = async () => {
    const permission = await dynamoGet(
      `${process.env.REACT_APP_STAGE}-page-actions`
    );

    setPermissions(permission[0]?.all);
    setList(
      permission[0]?.all?.map(({ page, id }) => {
        return { id, page };
      })
    );
  };

  const [namePermission, setNamePermission] = useState("");

  const [form] = Form.useForm();

  const { Option } = Select;

  const getActions = (namePage) => {
    let actions;
    permissions.forEach(({ page, permissions }) => {
      if (page === namePage) {
        actions = [...permissions];
      }
    });
    return actions;
  };

  function setPage({ key: id, value: page }) {
    setPageSelection({ id, page });
    setSelectedPageFunctions(getActions(page));
  }

  const clearFormAndSets = () => {
    setPageSelection("");
    setFunctionSelected("");
    form.resetFields();
    form.setFieldsValue({ name_permission: namePermission });
  };

  function AddPermission(value) {
    let permissions;
    if (!pageSelection) {
      message.error("Preencha os campos");
      return;
    }

    if (value.target.textContent === "Adicionar todas as ações") {
      permissions = [...selectedPageFunctions];
    } else if (!functionSelected) {
      message.error("Preencha os campos");
      return;
    } else {
      permissions = functionSelected.map(({ key: action, value: code }) => ({
        action,
        code,
      }));
    }

    const objDataTable = {
      ...pageSelection,
      permissions,
    };
    setDataTabela((dataTabela) => [...dataTabela, objDataTable]);
    setList(
      list.filter((currentPage) => currentPage.page !== pageSelection.page)
    );
    clearFormAndSets();
  }

  const DeleteItem = ({ page }) => {
    const newTable = dataTabela.filter((row) => row.page !== page);
    const pagesTable = newTable.map(({ page }) => page);
    setList(permissions.filter(({ page }) => !pagesTable.includes(page)));
    setDataTabela(newTable);
  };

  const columns = [
    {
      title: "Página",
      dataIndex: "page",
      key: "page",
    },
    {
      title: "Funcionalidades",
      dataIndex: "permissions",
      key: "permissions",
      render: (permissions) => (
        <>
          {permissions.map(({ code }) => {
            return <Tag key={code}>{code}</Tag>;
          })}
        </>
      ),
    },
    {
      title: "Excluir",
      dataIndex: "delete",
      key: "delete",
      render: (_, key) => {
        return (
          <Popconfirm
            okText="Sim"
            cancelText="Não"
            title="Tem certeza que deseja excluir esta permissão?"
            placement="bottom"
            onConfirm={() => DeleteItem(key)}
          >
            <Button danger type="primary">
              <DeleteOutlined />
            </Button>
          </Popconfirm>
        );
      },
    },
  ];

  const formatPermissions = (dataTabela) => {
    const permissions = dataTabela.map(({ page, permissions: actions }) => {
      return {
        page,
        actions,
      };
    });
    return {
      name_permission: namePermission,
      permissions,
    };
  };

  const handleSubmit = async () => {
    setLoading(true);
    const permissionFormatted = formatPermissions(dataTabela);
    if (permissionFormatted.permissions.length === 0) {
      message.error("Adicione pelo menos uma permissão");
      return setLoading(false);
    }
    try {
      await dynamoPost(
        `${process.env.REACT_APP_STAGE}-permissions`,
        permissionFormatted
      );
      const username = localStorage.getItem("@dsm/username");
      const title = "Cadastro de Permissão";
      const description = `${username} cadastrou a permissão: '${permissionFormatted.name_permission}'`;
      logNewAuditAction(username, title, description);
      message.success("Permissão adicionada com sucesso!");
    } catch (error) {
      console.log(error);
      message.error(
        "Ocorreu um erro ao tentar adicionar a permissão, tente novamente"
      );
    }
    getPermission();
    form.resetFields();
    setDataTabela([]);
    setShow(false);
    setLoading(false);
  };

  return (
    <>
      <Button
        type="primary"
        onClick={() => {
          getPermissionData();
          setShow(true);
          setList(
            permissions?.map(({ page, id }) => {
              return { id, page };
            })
          );
        }}
      >
        Adicionar Permissão
      </Button>
      <Modal
        title="Adicione uma permissão"
        open={show}
        confirmLoading={loading}
        onOk={() => {
          form.submit();
        }}
        onCancel={() => {
          setShow(false);
          setDataTabela([]);
          form.resetFields();
        }}
        okText="Adicionar"
        cancelText="Cancelar"
      >
        <Row justify="center">
          <Col span={24}>
            <Form
              form={form}
              requiredMark={false}
              layout="vertical"
              onFinish={handleSubmit}
            >
              <Form.Item
                label={<RequiredLabelForm title="Nome da permissão" />}
                name="name_permission"
                rules={[
                  {
                    required: true,
                    message: "Preencha esse o nome da permissão.",
                  },
                ]}
              >
                <Input
                  placeholder="Nome da permissão"
                  onChange={({ target: { value } }) => setNamePermission(value)}
                />
              </Form.Item>

              <Divider />

              <Form.Item
                name="page"
                label="Selecione uma página para liberar acesso"
              >
                <Select
                  placeholder="Selecione uma página"
                  onChange={(_, values) => {
                    setPage(values);
                    form.setFieldsValue({ actions: undefined });
                  }}
                >
                  {list?.map(({ id, page }) => {
                    return (
                      <Option value={page} key={id}>
                        {page}
                      </Option>
                    );
                  })}
                </Select>
              </Form.Item>

              <Form.Item
                name="actions"
                label="Selecione as permissões para liberar acesso"
              >
                <Select
                  mode="multiple"
                  onChange={(_, values) => setFunctionSelected(values)}
                  placeholder="Selecione uma ação"
                >
                  {selectedPageFunctions.map(({ id, action, code }) => {
                    return (
                      <Option disabled={disable} value={code} key={action}>
                        {action}
                      </Option>
                    );
                  })}
                </Select>
              </Form.Item>
              <Row justify="space-between">
                <Col>
                  <Form.Item>
                    <Button
                      type="dashed"
                      onClick={AddPermission}
                      block
                      icon={<PlusOutlined />}
                    >
                      Adicionar todas as ações
                    </Button>
                  </Form.Item>
                </Col>
                <Col>
                  <Form.Item>
                    <Button
                      type="dashed"
                      onClick={AddPermission}
                      block
                      icon={<PlusOutlined />}
                    >
                      Adicionar Permissão
                    </Button>
                  </Form.Item>
                </Col>
              </Row>
            </Form>
            <Table
              scroll={{ x: "auto" }}
              dataSource={dataTabela}
              columns={columns}
            />
          </Col>
        </Row>
      </Modal>
    </>
  );
};
