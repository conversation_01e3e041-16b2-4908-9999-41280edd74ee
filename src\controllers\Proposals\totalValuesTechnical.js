import { totalHoursController } from "./totalHours";

async function getCostManagementValues(cm) {
  let values = [
    { name: "hourValue", values: [] },
    { name: "additionalHourValue", values: [] },
  ];

  cm?.daredeHourValue?.map((h) => {
    values[0].values.push(h);
  });

  cm.daredeAdditionalHours.map((h) => {
    values[1].values.push(h);
  });

  return values;
}

export const totalValuesController = async (
  proposalState,
  costManagement,
  services
) => {
  console.log({ costManagement });
  //cmArr = costManagementArray
  if (costManagement.length === 0) return;

  let cmArr = await getCostManagementValues(costManagement);
  const totalHours = await totalHoursController(services);

  let hourValueClass = proposalState.totalValues.find(
    (v) => v.hourClass === "hourValue"
  );

  if (services?.length > 0) {
    cmArr = cmArr.find((v) => v.name === "hourValue");

    for (let i = 0; i < hourValueClass.values.length; i++) {
      // iterating hour types 'hourValue' and 'additionalHourValue'
      // contractHourType can be of 'hourValue' or 'additionalHourValue'
      let contractHourArr = cmArr.values[i];
      const arrSize = hourValueClass.values[i].values.length;

      for (let n = 0; n < arrSize; n++) {
        // iterating all totalHours values
        let discountValue = 0;
        let hourType = hourValueClass.values[i].values[n]?.formName;
        let hourValue = 0;
        for (let k = 0; k < contractHourArr.values.length; k++) {
          if (contractHourArr.values[k].name === hourType) {
            hourValue = contractHourArr.values[k].value;
            break;
          }
        }
        const totalValueObj = hourValueClass.values[i].values[n];
        const totalHour = totalHours.find((h) => h.formName === hourType);

        if (hourValue !== undefined) {
          const burst = totalValueObj?.burst ? Number(totalValueObj.burst) : 0;

          const totalHoursBytype = Number(totalHour.value) + burst;

          const totalHourRegimeValue = totalHoursBytype * hourValue;
          if (totalValueObj.discountUnit === "percentage") {
            discountValue =
              (totalHourRegimeValue / 100) * totalValueObj.discountValue;
          } else {
            discountValue = totalHour.value * totalValueObj.discountValue;
          }
          let totalValue = totalHourRegimeValue - discountValue;

          if (totalValue < 0) totalValue = 0;

          hourValueClass.values[i].values[n] = {
            formName: totalValueObj.formName,
            type: totalValueObj.type,
            discountUnit: totalValueObj.discountUnit,
            discountValue: totalValueObj.discountValue,
            hourValue: hourValue,
            totalHours: totalHoursBytype,
            totalValue: totalValue,
            burst: burst,
            hours: totalHour.value,
          };
        }
      }
    }

    let additionalTotalValues = proposalState.totalValues.find(
      (v) => v.hourClass === "additionalHourValue"
    );
    let cmArrAdd = await getCostManagementValues(costManagement);

    cmArrAdd = cmArrAdd.find((v) => v.name === "additionalHourValue");

    for (let i = 0; i < additionalTotalValues.values.length; i++) {
      // iterating hour types 'hourValue' and 'additionalHourValue'
      // contractHourType can be of 'hourValue' or 'additionalHourValue'
      let contractHourArr = cmArrAdd.values[i];
      const arrSize = additionalTotalValues.values[i].values.length;

      for (let n = 0; n < arrSize; n++) {
        // iterating all totalHours values
        let discountValue = 0;
        let hourType = additionalTotalValues.values[i].values[n]?.formName;
        let hourValue = 0;
        for (let k = 0; k < contractHourArr.values.length; k++) {
          if (contractHourArr.values[k].name === hourType) {
            hourValue = contractHourArr.values[k].value;
            break;
          }
        }
        const totalValueObj = additionalTotalValues.values[i].values[n];
        const totalHour = totalHours.find((h) => h.formName === hourType);

        if (hourValue !== undefined) {
          const burst = totalValueObj?.burst ? Number(totalValueObj.burst) : 0;

          const totalHoursBytype = Number(totalHour.value) + burst;

          const totalHourRegimeValue = totalHoursBytype * hourValue;
          if (totalValueObj.discountUnit === "percentage") {
            discountValue =
              (totalHourRegimeValue / 100) * totalValueObj.discountValue;
          } else {
            discountValue = totalHour.value * totalValueObj.discountValue;
          }
          let totalValue = totalHourRegimeValue - discountValue;

          if (totalValue < 0) totalValue = 0;

          additionalTotalValues.values[i].values[n] = {
            formName: totalValueObj.formName,
            type: totalValueObj.type,
            discountUnit: totalValueObj.discountUnit,
            discountValue: totalValueObj.discountValue,
            hourValue: hourValue,
            totalHours: totalHoursBytype,
            totalValue: totalValue,
            burst: burst,
            hours: totalHour.value,
          };
        }
      }
    }

    return [hourValueClass, additionalTotalValues];
  } else {
    return proposalState.totalValues;
  }
};
