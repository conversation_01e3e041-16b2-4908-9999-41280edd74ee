import { Row, Col, Spin } from "antd";
import { useEffect, useState } from "react";
import { UploadCustomersSelect } from "./UploadCustomersSelect";
import { dynamoGet } from "../../../service/apiDsmDynamo";
import { UploadCategorySelect } from "./UploadCategorySelect";
import { UploadEmailSelect } from "./UploadEmailSelect";
import { UploadTagSelect } from "./UploadTagSelect";

export const UploadFilesFields = () => {
  const [loading, setLoading] = useState(false);
  const [allCustomers, setAllCustomers] = useState([]);

  useEffect(() => {
    async function getCustomers() {
      setLoading(true);

      const tableCustomers = await dynamoGet(
        `${process.env.REACT_APP_STAGE}-customers`
      );

      const filterDuplicatedNames = [];

      tableCustomers.forEach((c) => {
        if (
          !filterDuplicatedNames.find(
            (t) => t?.names?.fantasy_name === c?.names?.fantasy_name
          )
        ) {
          filterDuplicatedNames.push(c);
        }
      });

      setAllCustomers(filterDuplicatedNames);
      setLoading(false);
    }
    getCustomers();
  }, []);

  return (
    <>
      {loading ? (
        <>
          <Row justify="center">
            <Spin size="large" />
          </Row>
        </>
      ) : (
        <>
          <Row gutter={[16, 8]} style={{ marginBottom: "20px" }}>
            <Col span={6}>
              <UploadCustomersSelect allCustomers={allCustomers} />
            </Col>
            <Col span={6}>
              <UploadCategorySelect />
            </Col>
            <Col span={6}>
              <UploadTagSelect />
            </Col>
            <Col span={6}>
              <UploadEmailSelect />
            </Col>
          </Row>
        </>
      )}
    </>
  );
};
