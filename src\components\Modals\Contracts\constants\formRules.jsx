import { onlyTextFields } from "./onlyTextFields";

/**
 * Regras de validação para formulários de contratos
 * Corrigido: regex problemática e lógica de validação
 */

// Regex corrigida para campos de texto
const TEXT_ONLY_PATTERN = /^[a-zA-Z\s@~`!@#$%^&*()_=+\\';:"\\/?>.<,-]+$/i;

// Regex para diferentes tipos de campos
const VALIDATION_PATTERNS = {
  text: TEXT_ONLY_PATTERN,
  alphanumeric: /^[a-zA-Z0-9\s\-_.]+$/i,
  email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  phone: /^[\d\s\-+()]+$/,
  currency: /^\d+([.,]\d{1,2})?$/,
  percentage: /^(100|[1-9]?\d)(\.\d{1,2})?$/
};

/**
 * Determina o padrão de validação baseado no tipo de campo
 */
const getValidationPattern = (fieldKey) => {
  // Campos específicos que precisam de validação especial
  const fieldPatterns = {
    email: VALIDATION_PATTERNS.email,
    phone: VALIDATION_PATTERNS.phone,
    currency: VALIDATION_PATTERNS.currency,
    percentage: VALIDATION_PATTERNS.percentage,
    // Campos que aceitam alfanuméricos
    contract_number: VALIDATION_PATTERNS.alphanumeric,
    reference: VALIDATION_PATTERNS.alphanumeric,
    // Campos que são apenas texto
    target: VALIDATION_PATTERNS.text,
    invoice_status: VALIDATION_PATTERNS.text
  };

  return fieldPatterns[fieldKey] || VALIDATION_PATTERNS.alphanumeric;
};

/**
 * Gera mensagem de erro apropriada para o tipo de campo
 */
const getValidationMessage = (fieldKey, isRequired) => {
  if (!isRequired) {
    return "Preencha este campo.";
  }

  const messages = {
    email: "Digite um email válido.",
    phone: "Digite um telefone válido.",
    currency: "Digite um valor monetário válido (ex: 1000.50).",
    percentage: "Digite uma porcentagem válida (0-100).",
    target: "Este campo aceita apenas texto e caracteres especiais.",
    invoice_status: "Este campo aceita apenas texto e caracteres especiais."
  };

  return messages[fieldKey] ||
         (onlyTextFields.includes(fieldKey)
           ? "Este campo é obrigatório e não aceita caracteres numéricos."
           : "Preencha este campo corretamente.");
};

export const formRules = (field, disableSignedDate, permissions) => {
  // Determinar se o campo é obrigatório
  const isFieldRequired = () => {
    // Apenas signature_date deve ser obrigatório quando o contrato está assinado
    if (field.key === "signature_date") {
      return !disableSignedDate;
    }

    // performed_period não deve ser forçado como obrigatório
    // Usar apenas a configuração original do campo
    const permission = permissions?.find(
      (permission) => permission.code === field.permissionCode
    );

    return permission ? field.required : false;
  };

  const required = isFieldRequired();

  // Regras para campos que precisam de validação de padrão
  if (onlyTextFields.includes(field.key) || field.validationType) {
    return [
      {
        required,
        pattern: getValidationPattern(field.key),
        message: getValidationMessage(field.key, required),
      },
    ];
  }

  // Regras básicas para outros campos
  return [
    {
      required,
      message: "Preencha este campo.",
    },
  ];
};
