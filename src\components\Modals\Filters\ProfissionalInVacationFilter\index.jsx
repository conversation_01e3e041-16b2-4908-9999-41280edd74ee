import { React, useEffect, useState } from "react";
import { Checkbox, Select, DatePicker, Button, Form } from "antd";
import { CloseSquareFilled } from "@ant-design/icons";

function ProfissionalInVacationFilter(props) {
  console.log(props);
  const { customer, professionals, states } = props;
  const [form] = Form.useForm();
  const { Option } = Select;

  function onFilterFinish(values) {
    props.handlePopoverVisibility();
  }

  function onFilterFailed() {}

  function clear() {
    form.resetFields();
  }

  useEffect(() => {}, []);

  return (
    <Form
      form={form}
      style={{ display: "flex", flexDirection: "column" }}
      name="basic"
      initialValues={{
        remember: true,
      }}
      onFinish={onFilterFinish}
      onFinishFailed={onFilterFailed}
      autoComplete="off"
    >
      <div style={{ display: "flex", justifyContent: "space-between" }}>
        <h2>Profissionais inativos</h2>
        <button className="filter-close-btn">X</button>
      </div>
      <Form.Item name="client">
        <Select
          showSearch
          style={{ width: "100%" }}
          placeholder="Cliente"
          optionFilterProp="children"
          filterOption={(input, option) =>
            option.props.children.toLowerCase().indexOf(input.toLowerCase()) >=
              0 ||
            option.props.value.toLowerCase().indexOf(input.toLowerCase()) >= 0
          }
        >
          <Option value="Todos">Todos</Option>
          <Option value="Logikee">Logikee</Option>
        </Select>
      </Form.Item>
      <Form.Item name="profissional">
        <Select
          showSearch
          style={{ width: "100%" }}
          placeholder="Profissional"
          optionFilterProp="children"
          filterOption={(input, option) =>
            option.props.children.toLowerCase().indexOf(input.toLowerCase()) >=
              0 ||
            option.props.value.toLowerCase().indexOf(input.toLowerCase()) >= 0
          }
        >
          <Option value="Todos">Todos</Option>
          <Option value="Logikee">Logikee</Option>
        </Select>
      </Form.Item>
      <Form.Item name="state">
        <Select
          showSearch
          style={{ width: "100%" }}
          placeholder="Estado"
          optionFilterProp="children"
          filterOption={(input, option) =>
            option.props.children.toLowerCase().indexOf(input.toLowerCase()) >=
              0 ||
            option.props.value.toLowerCase().indexOf(input.toLowerCase()) >= 0
          }
        >
          <Option value="Todos">Todos</Option>
          <Option value="Logikee">Logikee</Option>
        </Select>
      </Form.Item>
      <div className="buttons">
        <Button className="filter-clear-btn" onClick={clear}>
          Limpar
        </Button>
        <Button htmlType="submit" className="filter-submit-btn">
          Aplicar
        </Button>
      </div>
    </Form>
  );
}

export default ProfissionalInVacationFilter;
