import React, { useState } from "react";
import { But<PERSON>, <PERSON>, Modal, Tooltip } from "antd";
import { FileTextOutlined } from "@ant-design/icons";

export const UsefulDocumentationBilling = () => {
  const [showModal, setShowModal] = useState(false);
  return (
    <>
      <Tooltip placement="leftTop" title="Visualizar documentos úteis">
        <Button
          type="primary"
          shape="circle"
          size="large"
          onClick={async () => {
            setShowModal(true);
          }}
          style={{ position: "fixed", bottom: "20px", right: "20px" }}
        >
          <FileTextOutlined />
        </Button>
      </Tooltip>
      <Modal
        title="Documentos úteis"
        open={showModal}
        onCancel={() => setShowModal(false)}
        footer={[
          <Button key={"close_button"} onClick={() => setShowModal(false)}>
            Fechar
          </Button>,
        ]}
      >
        <List>
          <List.Item key={"billing_faq"}>
            <List.Item.Meta
              title={"Link de perguntas frequentes:"}
              description={
                <a href={process.env.REACT_APP_BILLING_FAQ_LINK}>
                  FAQ billing AWS
                </a>
              }
            />
          </List.Item>
          <List.Item key={"documentation"}>
            <List.Item.Meta
              title={"Link das documentações:"}
              description={
                <a href={process.env.REACT_APP_BILLING_DOCUMENTATION_LINK}>
                  Documentações billing AWS
                </a>
              }
            />
          </List.Item>
          <List.Item key={"contract_model"}>
            <List.Item.Meta
              title={"Link dos modelos de contrato:"}
              description={
                <a href={process.env.REACT_APP_BILLING_CONTRACT_MODEL_LINK}>
                  Modelos de contrato de billing
                </a>
              }
            />
          </List.Item>
        </List>
      </Modal>
    </>
  );
};
