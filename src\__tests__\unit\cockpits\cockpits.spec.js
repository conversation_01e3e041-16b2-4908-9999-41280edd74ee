import { dsmContractsMockedData } from "../../../__mocks__/dsmContracts";
import { formatContracts } from "../../../controllers/cockpit/cockpitController";

describe("Cockpits", () => {
  it("should render successfully", () => {
    expect(200).toBe(200);
  });
});

describe("Testing formatContract", () => {
  it("Should render only the active contracts", () => {
    const contracts = formatContracts(dsmContractsMockedData);

    contracts.forEach((c) => {
      expect(c.valid_id).toEqual(1);
    });
  });

  it("Should return empty array if the parameter is undefined", () => {
    const contracts = formatContracts(undefined);

    expect(contracts).toEqual([]);
  });

  it("Should not return any undefined total hours parameter", () => {
    const contracts = formatContracts(dsmContractsMockedData);

    expect(contracts.some((c) => c.total_hours === undefined)).toEqual(false);
  });
  it("Should transform any total hours string into number", () => {
    const contracts = formatContracts(dsmContractsMockedData);

    expect(contracts.every((c) => typeof c.total_hours === "number")).toEqual(
      true
    );
  });
});
