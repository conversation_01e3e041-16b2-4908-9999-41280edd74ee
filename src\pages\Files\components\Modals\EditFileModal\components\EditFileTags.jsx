import { Select, Typography } from "antd";
import { useSelector, shallowEqual } from "react-redux";
import { setSelectedFileFieldState } from "../../../../../../store/actions/files-action";

const { Option } = Select;
const { Text } = Typography;

export const EditFileTags = () => {
  const tags = useSelector(
    (state) => state.files.selectedFile.tags,
    shallowEqual
  );
  const allTags = useSelector((state) => state.files.allTags, shallowEqual);

  return (
    <>
      <Select
        style={{ width: "100%" }}
        mode={"multiple"}
        placeholder={"Selecione as tags do arquivo..."}
        value={tags.map((t) => t)}
        onChange={(value) =>
          setSelectedFileFieldState({ field: "tags", value })
        }
        status={tags.length > 0 ? "" : "error"}
      >
        {allTags.map((option, key) => (
          <Option value={option.name} key={key}>
            {option.name}
          </Option>
        ))}
      </Select>
      {tags.length === 0 ? (
        <>
          <Text type={"danger"}>Preenchimento Obrigatório</Text>
        </>
      ) : null}
    </>
  );
};
