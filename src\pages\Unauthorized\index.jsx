import React from "react";

import { Row, Col, Image, Space, Divider } from "antd";
import Logo from "../../assets/images/logo_full.png";
import { Link } from "react-router-dom";

export const Unauthorized = () => {
  return (
    <>
      <Row
        align="middle"
        justify="center"
        style={{ minHeight: "100vh", background: "#ebedef" }}
      >
        <Col
          xs={18}
          lg={12}
          xl={6}
          style={{
            display: "flex",
            justifyContent: "center",
            padding: "2em",
            borderRadius: "10px",
            border: "1px solid #c9c9c9",
            flexDirection: "column",
            backgroundColor: "#ffffff",
          }}
        >
          <Space direction="vertical" align="center" size="middle">
            <Image preview={false} src={Logo}></Image>
            <Divider style={{ padding: "10px" }}>
              Você está sem permissão em nossa plataforma,
              <br />
              contate o seu gestor imediato.
            </Divider>
          </Space>
          <Space direction="vertical" align="end" size="middle">
            <Link to ="/*">Tente logar novamente</Link>
          </Space>
        </Col>
      </Row>
    </>
  );
};
