import React, { useEffect, useMemo, useState } from "react";
import { CSVLink } from "react-csv";
import { useSelector, shallowEqual } from "react-redux";

import { Button } from "antd";
import { DownloadOutlined } from "@ant-design/icons";

import { dynamoPost } from "../../../../service/apiDsmDynamo";
import { CSVColumns } from "../../constants/exportCSVColumns";

import * as controller from "../../../../controllers/invoices/invoiceController";
import { INVOICE_VIEW_STATES } from "../../../../store/reducers/invoices-reducer";

export const ExportInvoicesCSV = () => {
  const dolar = useSelector((state) => state.invoice.dolar, shallowEqual);
  const view = useSelector((state) => state.invoice.view, shallowEqual);
  const invoices = useSelector((state) => state.invoice.invoices, shallowEqual);
  const permissions = useSelector(
    (state) => state.invoice.permissions,
    shallowEqual
  );
  const collumnsToShow = useSelector(
    (state) => state.invoice.collumnsToShow,
    shallowEqual
  );
  const [invoiceColumns, setInvoiceColumns] = useState([]);
  const username = localStorage.getItem("@dsm/username");

  const invoiceList = useMemo(() => {
    let list = invoices;
    if (view === INVOICE_VIEW_STATES.SUMMARY) {
      list = controller.groupInvoiceByInvoiceId(list, dolar);
    } else {
      list = controller.groupInvoiceByAccount(list, dolar);
    }

    if (list.length > 0) {
      list[0]["dolar"] = parseFloat(dolar || "0")
        .toString()
        .replace(".", ",");
    }

    const formattedInvoiceList = list.map((invoice) => {
      const formatNumber = (number) =>
        number ? parseFloat(number).toLocaleString("pt-BR") : number;

      return {
        ...invoice,
        charges: formatNumber(invoice.charges),
        spp_discount: formatNumber(invoice.spp_discount),
        total_edp_discount: formatNumber(invoice.total_edp_discount),
        total_bundled_discount: formatNumber(invoice.total_bundled_discount),
        total_saving_plans_discount: formatNumber(
          invoice.total_saving_plans_discount
        ),
        total_credits: formatNumber(invoice.total_credits),
        tax: formatNumber(invoice.tax),
        unblended_cost: formatNumber(invoice.unblended_cost),
        conversion: formatNumber(invoice.conversion),
        final_balance: formatNumber(invoice.final_balance),
        total_discount: formatNumber(invoice.total_discount),
        final_balance_real: formatNumber(invoice.final_balance_real),
        other_discounts: formatNumber(invoice.other_discounts),
      };
    });

    return formattedInvoiceList;
  }, [invoices, view, dolar]);

  useEffect(() => {
    const columns = CSVColumns.filter((column) =>
      collumnsToShow.includes(column.label)
    );
    setInvoiceColumns(columns);
  }, [collumnsToShow]);

  return (
    <CSVLink
      style={{ marginRight: "2em" }}
      separator={";"}
      headers={invoiceColumns}
      onClick={async (event, done) => {
        await dynamoPost(`${process.env.REACT_APP_STAGE}-audits`, {
          created_at: new Date(),
          updated_at: new Date(),
          username: username,
          name: "Tabela de invoices exportada",
          description: `${username} exportou a tabela de invoices.`,
        });
      }}
      data={invoiceList}
      filename={"invoices_dsm.csv"}
    >
      {permissions
        .map((permission) => {
          return permission.code;
        })
        .includes("export_invoice") ? (
        <Button type="primary" style={{ width: "100%" }}>
          <DownloadOutlined />
          Exportar Relatório {view === "summary" ? "Sumarizado" : "Detalhado"}
        </Button>
      ) : null}
    </CSVLink>
  );
};
