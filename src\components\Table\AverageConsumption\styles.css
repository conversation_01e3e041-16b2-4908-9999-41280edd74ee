.table-card {
  margin: 0 auto;
  padding: 16px;
  width: 1090px;
  background-color: rgb(255, 255, 255);
  box-shadow: 0px 0px 10px 2px rgba(113, 113, 113, 0.25);
  border-radius: 24px;
}

.table-card-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.btn-hidden {
  cursor: pointer;
}

.button button {
  background-color: #43914f;
  border: none;
  color: white;
  border-radius: 5px;
  padding: 6px 24px;
  box-shadow: 0px 0px 10px 2px rgba(64, 64, 64, 0.25);
  font-weight: bold;
  font-size: 16px;
  line-height: 22px;
}

.table-filter-content {
  height: 20px;
  width: 20px;
  border-radius: 3px;
  border: 1px solid #1ebd71;
  position: relative;
  margin-right: 37px;
}

.table-filter-icon {
  font-size: 12px;
  line-height: 16px;
  color: #1ebd71;
  width: 10px;
  height: 10px;
  position: absolute;
  right: calc(50% - 4px);
}

.container-table {
  width: 100%;
  margin: 0 auto;
}

.priority-container {
  display: flex;
  align-items: center;
}

.priority {
  border-radius: 5px;
}
