/* ✅ Estilos para Tabelas e Paginação DSM - Exatamente como Original */

/* ✅ Paginação em tabelas - estilo exato do original */
.ant-table-pagination.ant-pagination {
  margin: 16px 0 !important;
  text-align: right !important;
  display: flex !important;
  justify-content: flex-end !important;
  align-items: center !important;
  gap: 8px !important;
}

/* ✅ Item ativo da paginação - CORRETO: fundo branco, texto e borda verde */
.ant-table-pagination .ant-pagination-item-active {
  background-color: #ffffff !important;
  border: 2px solid #00B050 !important;
  border-radius: 6px !important;
  min-width: 32px !important;
  height: 32px !important;
  line-height: 28px !important;
  text-align: center !important;
  margin: 0 4px !important;
  box-sizing: border-box !important;
}

.ant-table-pagination .ant-pagination-item-active a {
  color: #00B050 !important;
  font-weight: 500 !important;
  font-size: 14px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 100% !important;
  height: 100% !important;
  text-decoration: none !important;
  padding: 0 !important;
  margin: 0 !important;
}

/* ✅ Garantir que o item ativo não mude no hover */
.ant-table-pagination .ant-pagination-item-active:hover {
  background-color: #ffffff !important;
  border-color: #00B050 !important;
}

.ant-table-pagination .ant-pagination-item-active:hover a {
  color: #00B050 !important;
}

/* ✅ Itens normais da paginação - mesmo formato do item ativo */
.ant-table-pagination .ant-pagination-item {
  border-radius: 6px !important;
  border: 1px solid #d9d9d9 !important;
  background-color: #ffffff !important;
  min-width: 32px !important;
  height: 32px !important;
  line-height: 30px !important;
  text-align: center !important;
  margin: 0 4px !important;
  box-sizing: border-box !important;
}

.ant-table-pagination .ant-pagination-item a {
  color: rgba(0, 0, 0, 0.85) !important;
  font-weight: 400 !important;
  font-size: 14px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 100% !important;
  height: 100% !important;
  text-decoration: none !important;
  padding: 0 !important;
  margin: 0 !important;
}

/* ✅ Hover em itens da paginação - exato como original */
.ant-table-pagination .ant-pagination-item:hover:not(.ant-pagination-item-active) {
  border-color: #00B050 !important;
  background-color: #ffffff !important;
}

.ant-table-pagination .ant-pagination-item:hover:not(.ant-pagination-item-active) a {
  color: #00B050 !important;
}

/* ✅ Botões prev/next - exato como original */
.ant-table-pagination .ant-pagination-prev,
.ant-table-pagination .ant-pagination-next {
  margin: 0 4px !important;
}

.ant-table-pagination .ant-pagination-prev .ant-pagination-item-link,
.ant-table-pagination .ant-pagination-next .ant-pagination-item-link {
  border-radius: 4px !important;
  border: 1px solid #d9d9d9 !important;
  background-color: #ffffff !important;
  width: 32px !important;
  height: 32px !important;
  line-height: 30px !important;
  text-align: center !important;
  color: rgba(0, 0, 0, 0.45) !important;
  font-size: 12px !important;
}

.ant-table-pagination .ant-pagination-prev:hover .ant-pagination-item-link,
.ant-table-pagination .ant-pagination-next:hover .ant-pagination-item-link {
  border-color: #00B050 !important;
  color: #00B050 !important;
  background-color: #ffffff !important;
}

/* ✅ Botões disabled - exato como original */
.ant-table-pagination .ant-pagination-disabled .ant-pagination-item-link {
  border-color: #d9d9d9 !important;
  color: rgba(0, 0, 0, 0.25) !important;
  background-color: #ffffff !important;
}

.ant-table-pagination .ant-pagination-disabled:hover .ant-pagination-item-link {
  border-color: #d9d9d9 !important;
  color: rgba(0, 0, 0, 0.25) !important;
  background-color: #ffffff !important;
  cursor: not-allowed !important;
}

/* ✅ Seletor de tamanho da página - exato como original */
.ant-table-pagination .ant-pagination-options {
  margin-left: 16px !important;
}

.ant-table-pagination .ant-pagination-options-size-changer {
  margin-right: 8px !important;
}

.ant-table-pagination .ant-pagination-options-size-changer .ant-select {
  min-width: 80px !important;
}

.ant-table-pagination .ant-pagination-options-size-changer .ant-select-selector {
  border-color: #d9d9d9 !important;
  border-radius: 4px !important;
  height: 32px !important;
  background-color: #ffffff !important;
  font-size: 14px !important;
}

.ant-table-pagination .ant-pagination-options-size-changer .ant-select-selector:hover {
  border-color: #00B050 !important;
}

.ant-table-pagination .ant-pagination-options-size-changer .ant-select-focused .ant-select-selector {
  border-color: #00B050 !important;
  box-shadow: 0 0 0 2px rgba(0, 176, 80, 0.2) !important;
}

.ant-table-pagination .ant-pagination-options-size-changer .ant-select-selection-item {
  color: rgba(0, 0, 0, 0.85) !important;
  font-weight: 400 !important;
  line-height: 30px !important;
}

/* ✅ Quick jumper input */
.ant-table-pagination .ant-pagination-options-quick-jumper input {
  border-color: #d9d9d9 !important;
}

.ant-table-pagination .ant-pagination-options-quick-jumper input:hover {
  border-color: #00B050 !important;
}

.ant-table-pagination .ant-pagination-options-quick-jumper input:focus {
  border-color: #00B050 !important;
  box-shadow: 0 0 0 2px rgba(0, 176, 80, 0.2) !important;
}

/* ✅ Texto da paginação */
.ant-table-pagination .ant-pagination-total-text {
  color: #404040 !important;
  font-weight: 500 !important;
}

/* ✅ Paginação global (não apenas em tabelas) - exato como original */
.ant-pagination {
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
}

.ant-pagination-item-active {
  background-color: #ffffff !important;
  border: 2px solid #00B050 !important;
  border-radius: 6px !important;
  min-width: 32px !important;
  height: 32px !important;
  line-height: 28px !important;
  text-align: center !important;
  margin: 0 4px !important;
  box-sizing: border-box !important;
}

.ant-pagination-item-active a {
  color: #00B050 !important;
  font-weight: 500 !important;
  font-size: 14px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 100% !important;
  height: 100% !important;
  text-decoration: none !important;
  padding: 0 !important;
  margin: 0 !important;
}

/* ✅ Garantir que o item ativo global não mude no hover */
.ant-pagination-item-active:hover {
  background-color: #ffffff !important;
  border-color: #00B050 !important;
}

.ant-pagination-item-active:hover a {
  color: #00B050 !important;
}

/* ✅ Itens normais da paginação global */
.ant-pagination-item {
  border-radius: 4px !important;
  border: 1px solid #d9d9d9 !important;
  background-color: #ffffff !important;
  min-width: 32px !important;
  height: 32px !important;
  line-height: 30px !important;
  text-align: center !important;
  margin: 0 4px !important;
}

.ant-pagination-item a {
  color: rgba(0, 0, 0, 0.85) !important;
  font-weight: 400 !important;
  font-size: 14px !important;
  display: block !important;
  padding: 0 !important;
}

.ant-pagination-item:hover:not(.ant-pagination-item-active) {
  border-color: #00B050 !important;
  background-color: #ffffff !important;
}

.ant-pagination-item:hover:not(.ant-pagination-item-active) a {
  color: #00B050 !important;
}

/* ✅ Botões de navegação global */
.ant-pagination-prev,
.ant-pagination-next {
  margin: 0 4px !important;
}

.ant-pagination-prev .ant-pagination-item-link,
.ant-pagination-next .ant-pagination-item-link {
  border-radius: 4px !important;
  border: 1px solid #d9d9d9 !important;
  background-color: #ffffff !important;
  width: 32px !important;
  height: 32px !important;
  line-height: 30px !important;
  text-align: center !important;
  color: rgba(0, 0, 0, 0.45) !important;
  font-size: 12px !important;
}

.ant-pagination-prev:hover .ant-pagination-item-link,
.ant-pagination-next:hover .ant-pagination-item-link {
  border-color: #00B050 !important;
  color: #00B050 !important;
  background-color: #ffffff !important;
}

/* ✅ Reticências (...) - exato como original */
.ant-table-pagination .ant-pagination-jump-prev,
.ant-table-pagination .ant-pagination-jump-next {
  margin: 0 4px !important;
  min-width: 32px !important;
  height: 32px !important;
  line-height: 30px !important;
  text-align: center !important;
  color: rgba(0, 0, 0, 0.45) !important;
  font-size: 14px !important;
}

.ant-table-pagination .ant-pagination-jump-prev:hover,
.ant-table-pagination .ant-pagination-jump-next:hover {
  color: #00B050 !important;
}

.ant-table-pagination .ant-pagination-jump-prev::after,
.ant-table-pagination .ant-pagination-jump-next::after {
  color: rgba(0, 0, 0, 0.45) !important;
  font-size: 14px !important;
}

.ant-table-pagination .ant-pagination-jump-prev:hover::after,
.ant-table-pagination .ant-pagination-jump-next:hover::after {
  color: #00B050 !important;
}

/* ✅ Estados disabled globais - estilo original DSM */
.ant-pagination-disabled .ant-pagination-item-link {
  color: rgba(0, 0, 0, 0.25) !important;
  border-color: #d9d9d9 !important;
  border-radius: 6px !important;
  background-color: #ffffff !important;
}

.ant-pagination-disabled:hover .ant-pagination-item-link {
  color: rgba(0, 0, 0, 0.25) !important;
  border-color: #d9d9d9 !important;
  background-color: #ffffff !important;
  cursor: not-allowed !important;
}

/* ✅ Classe específica para paginação customizada - exato como original */
.paginator {
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  margin: 20px 0 !important;
  gap: 8px !important;
}

.paginator .ant-pagination-item {
  border-radius: 4px !important;
  min-width: 32px !important;
  height: 32px !important;
  line-height: 30px !important;
  margin: 0 4px !important;
}

.paginator .ant-pagination-item-active {
  background-color: #ffffff !important;
  border: 2px solid #00B050 !important;
}

.paginator .ant-pagination-item-active a {
  color: #00B050 !important;
  font-weight: 500 !important;
  font-size: 14px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 100% !important;
  height: 100% !important;
  text-decoration: none !important;
  padding: 0 !important;
  margin: 0 !important;
}

/* ✅ Garantir que o item ativo do paginator não mude no hover */
.paginator .ant-pagination-item-active:hover {
  background-color: #ffffff !important;
  border-color: #00B050 !important;
}

.paginator .ant-pagination-item-active:hover a {
  color: #00B050 !important;
}

.paginator .ant-pagination-prev .ant-pagination-item-link,
.paginator .ant-pagination-next .ant-pagination-item-link {
  border-radius: 4px !important;
  width: 32px !important;
  height: 32px !important;
  line-height: 30px !important;
}

/* ✅ PAGINAÇÃO EM MODAIS - MESMO PADRÃO DSM */

/* ✅ Paginação dentro de modais - estilo exato do original */
.ant-modal .ant-table-pagination.ant-pagination,
.ant-modal-body .ant-table-pagination.ant-pagination {
  margin: 16px 0 !important;
  text-align: right !important;
  display: flex !important;
  justify-content: flex-end !important;
  align-items: center !important;
  gap: 8px !important;
}

/* ✅ Item ativo da paginação em modais - CORRETO: fundo branco, texto e borda verde */
.ant-modal .ant-table-pagination .ant-pagination-item-active,
.ant-modal-body .ant-table-pagination .ant-pagination-item-active {
  background-color: #ffffff !important;
  border: 2px solid #00B050 !important;
  border-radius: 6px !important;
  min-width: 32px !important;
  height: 32px !important;
  line-height: 28px !important;
  text-align: center !important;
  margin: 0 4px !important;
  box-sizing: border-box !important;
}

.ant-modal .ant-table-pagination .ant-pagination-item-active a,
.ant-modal-body .ant-table-pagination .ant-pagination-item-active a {
  color: #00B050 !important;
  font-weight: 500 !important;
  font-size: 14px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 100% !important;
  height: 100% !important;
  text-decoration: none !important;
  padding: 0 !important;
  margin: 0 !important;
}

/* ✅ Garantir que o item ativo em modais não mude no hover */
.ant-modal .ant-table-pagination .ant-pagination-item-active:hover,
.ant-modal-body .ant-table-pagination .ant-pagination-item-active:hover {
  background-color: #ffffff !important;
  border-color: #00B050 !important;
}

.ant-modal .ant-table-pagination .ant-pagination-item-active:hover a,
.ant-modal-body .ant-table-pagination .ant-pagination-item-active:hover a {
  color: #00B050 !important;
}

/* ✅ Itens normais da paginação em modais - mesmo formato do item ativo */
.ant-modal .ant-table-pagination .ant-pagination-item,
.ant-modal-body .ant-table-pagination .ant-pagination-item {
  border-radius: 6px !important;
  border: 1px solid #d9d9d9 !important;
  background-color: #ffffff !important;
  min-width: 32px !important;
  height: 32px !important;
  line-height: 30px !important;
  text-align: center !important;
  margin: 0 4px !important;
  box-sizing: border-box !important;
}

.ant-modal .ant-table-pagination .ant-pagination-item a,
.ant-modal-body .ant-table-pagination .ant-pagination-item a {
  color: rgba(0, 0, 0, 0.85) !important;
  font-weight: 400 !important;
  font-size: 14px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 100% !important;
  height: 100% !important;
  text-decoration: none !important;
  padding: 0 !important;
  margin: 0 !important;
}

/* ✅ Hover em itens da paginação em modais - exato como original */
.ant-modal .ant-table-pagination .ant-pagination-item:hover:not(.ant-pagination-item-active),
.ant-modal-body .ant-table-pagination .ant-pagination-item:hover:not(.ant-pagination-item-active) {
  border-color: #00B050 !important;
  background-color: #ffffff !important;
}

.ant-modal .ant-table-pagination .ant-pagination-item:hover:not(.ant-pagination-item-active) a,
.ant-modal-body .ant-table-pagination .ant-pagination-item:hover:not(.ant-pagination-item-active) a {
  color: #00B050 !important;
}

/* ✅ Botões prev/next em modais - exato como original */
.ant-modal .ant-table-pagination .ant-pagination-prev,
.ant-modal .ant-table-pagination .ant-pagination-next,
.ant-modal-body .ant-table-pagination .ant-pagination-prev,
.ant-modal-body .ant-table-pagination .ant-pagination-next {
  margin: 0 4px !important;
}

.ant-modal .ant-table-pagination .ant-pagination-prev .ant-pagination-item-link,
.ant-modal .ant-table-pagination .ant-pagination-next .ant-pagination-item-link,
.ant-modal-body .ant-table-pagination .ant-pagination-prev .ant-pagination-item-link,
.ant-modal-body .ant-table-pagination .ant-pagination-next .ant-pagination-item-link {
  border-radius: 6px !important;
  border: 1px solid #d9d9d9 !important;
  background-color: #ffffff !important;
  width: 32px !important;
  height: 32px !important;
  line-height: 30px !important;
  text-align: center !important;
  color: rgba(0, 0, 0, 0.45) !important;
  font-size: 12px !important;
}

.ant-modal .ant-table-pagination .ant-pagination-prev:hover .ant-pagination-item-link,
.ant-modal .ant-table-pagination .ant-pagination-next:hover .ant-pagination-item-link,
.ant-modal-body .ant-table-pagination .ant-pagination-prev:hover .ant-pagination-item-link,
.ant-modal-body .ant-table-pagination .ant-pagination-next:hover .ant-pagination-item-link {
  border-color: #00B050 !important;
  color: #00B050 !important;
  background-color: #ffffff !important;
}

/* ✅ Botões disabled em modais - exato como original */
.ant-modal .ant-table-pagination .ant-pagination-disabled .ant-pagination-item-link,
.ant-modal-body .ant-table-pagination .ant-pagination-disabled .ant-pagination-item-link {
  border-color: #d9d9d9 !important;
  color: rgba(0, 0, 0, 0.25) !important;
  background-color: #ffffff !important;
}

.ant-modal .ant-table-pagination .ant-pagination-disabled:hover .ant-pagination-item-link,
.ant-modal-body .ant-table-pagination .ant-pagination-disabled:hover .ant-pagination-item-link {
  border-color: #d9d9d9 !important;
  color: rgba(0, 0, 0, 0.25) !important;
  background-color: #ffffff !important;
  cursor: not-allowed !important;
}

/* ✅ Seletor de tamanho da página em modais - exato como original */
.ant-modal .ant-table-pagination .ant-pagination-options,
.ant-modal-body .ant-table-pagination .ant-pagination-options {
  margin-left: 16px !important;
}

.ant-modal .ant-table-pagination .ant-pagination-options-size-changer,
.ant-modal-body .ant-table-pagination .ant-pagination-options-size-changer {
  margin-right: 8px !important;
}

.ant-modal .ant-table-pagination .ant-pagination-options-size-changer .ant-select,
.ant-modal-body .ant-table-pagination .ant-pagination-options-size-changer .ant-select {
  min-width: 80px !important;
}

.ant-modal .ant-table-pagination .ant-pagination-options-size-changer .ant-select-selector,
.ant-modal-body .ant-table-pagination .ant-pagination-options-size-changer .ant-select-selector {
  border-color: #d9d9d9 !important;
  border-radius: 6px !important;
  height: 32px !important;
  background-color: #ffffff !important;
  font-size: 14px !important;
}

.ant-modal .ant-table-pagination .ant-pagination-options-size-changer .ant-select-selector:hover,
.ant-modal-body .ant-table-pagination .ant-pagination-options-size-changer .ant-select-selector:hover {
  border-color: #00B050 !important;
}

.ant-modal .ant-table-pagination .ant-pagination-options-size-changer .ant-select-focused .ant-select-selector,
.ant-modal-body .ant-table-pagination .ant-pagination-options-size-changer .ant-select-focused .ant-select-selector {
  border-color: #00B050 !important;
  box-shadow: 0 0 0 2px rgba(0, 176, 80, 0.2) !important;
}

.ant-modal .ant-table-pagination .ant-pagination-options-size-changer .ant-select-selection-item,
.ant-modal-body .ant-table-pagination .ant-pagination-options-size-changer .ant-select-selection-item {
  color: rgba(0, 0, 0, 0.85) !important;
  font-weight: 400 !important;
  line-height: 30px !important;
}

/* ✅ Reticências (...) em modais - exato como original */
.ant-modal .ant-table-pagination .ant-pagination-jump-prev,
.ant-modal .ant-table-pagination .ant-pagination-jump-next,
.ant-modal-body .ant-table-pagination .ant-pagination-jump-prev,
.ant-modal-body .ant-table-pagination .ant-pagination-jump-next {
  margin: 0 4px !important;
  min-width: 32px !important;
  height: 32px !important;
  line-height: 30px !important;
  text-align: center !important;
  color: rgba(0, 0, 0, 0.45) !important;
  font-size: 14px !important;
}

.ant-modal .ant-table-pagination .ant-pagination-jump-prev:hover,
.ant-modal .ant-table-pagination .ant-pagination-jump-next:hover,
.ant-modal-body .ant-table-pagination .ant-pagination-jump-prev:hover,
.ant-modal-body .ant-table-pagination .ant-pagination-jump-next:hover {
  color: #00B050 !important;
}

.ant-modal .ant-table-pagination .ant-pagination-jump-prev::after,
.ant-modal .ant-table-pagination .ant-pagination-jump-next::after,
.ant-modal-body .ant-table-pagination .ant-pagination-jump-prev::after,
.ant-modal-body .ant-table-pagination .ant-pagination-jump-next::after {
  color: rgba(0, 0, 0, 0.45) !important;
  font-size: 14px !important;
}

.ant-modal .ant-table-pagination .ant-pagination-jump-prev:hover::after,
.ant-modal .ant-table-pagination .ant-pagination-jump-next:hover::after,
.ant-modal-body .ant-table-pagination .ant-pagination-jump-prev:hover::after,
.ant-modal-body .ant-table-pagination .ant-pagination-jump-next:hover::after {
  color: #00B050 !important;
}

/* ✅ Paginação standalone em modais (não em tabelas) */
.ant-modal .ant-pagination:not(.ant-table-pagination),
.ant-modal-body .ant-pagination:not(.ant-table-pagination) {
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  margin: 20px 0 !important;
  gap: 8px !important;
}

.ant-modal .ant-pagination:not(.ant-table-pagination) .ant-pagination-item-active,
.ant-modal-body .ant-pagination:not(.ant-table-pagination) .ant-pagination-item-active {
  background-color: #ffffff !important;
  border: 2px solid #00B050 !important;
  border-radius: 6px !important;
  min-width: 32px !important;
  height: 32px !important;
  line-height: 28px !important;
  text-align: center !important;
  margin: 0 4px !important;
  box-sizing: border-box !important;
}

.ant-modal .ant-pagination:not(.ant-table-pagination) .ant-pagination-item-active a,
.ant-modal-body .ant-pagination:not(.ant-table-pagination) .ant-pagination-item-active a {
  color: #00B050 !important;
  font-weight: 500 !important;
  font-size: 14px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 100% !important;
  height: 100% !important;
  text-decoration: none !important;
  padding: 0 !important;
  margin: 0 !important;
}

/* ✅ MODAL EXECUTIVOS - REGRAS ESPECÍFICAS PARA GARANTIR PADRÃO DSM */

/* ✅ Modal com título "Executivos" - forçar padrão DSM */
.ant-modal[aria-labelledby*="Executivos"] .ant-table-pagination.ant-pagination,
.ant-modal .ant-modal-content:has(.ant-modal-title:contains("Executivos")) .ant-table-pagination.ant-pagination {
  margin: 16px 0 !important;
  text-align: right !important;
  display: flex !important;
  justify-content: flex-end !important;
  align-items: center !important;
  gap: 8px !important;
}

/* ✅ Item ativo da paginação no modal Executivos */
.ant-modal[aria-labelledby*="Executivos"] .ant-table-pagination .ant-pagination-item-active,
.ant-modal .ant-modal-content:has(.ant-modal-title:contains("Executivos")) .ant-table-pagination .ant-pagination-item-active {
  background-color: #ffffff !important;
  border: 2px solid #00B050 !important;
  border-radius: 6px !important;
  min-width: 32px !important;
  height: 32px !important;
  line-height: 28px !important;
  text-align: center !important;
  margin: 0 4px !important;
  box-sizing: border-box !important;
}

.ant-modal[aria-labelledby*="Executivos"] .ant-table-pagination .ant-pagination-item-active a,
.ant-modal .ant-modal-content:has(.ant-modal-title:contains("Executivos")) .ant-table-pagination .ant-pagination-item-active a {
  color: #00B050 !important;
  font-weight: 500 !important;
  font-size: 14px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 100% !important;
  height: 100% !important;
  text-decoration: none !important;
  padding: 0 !important;
  margin: 0 !important;
}

/* ✅ Garantir que o item ativo no modal Executivos não mude no hover */
.ant-modal[aria-labelledby*="Executivos"] .ant-table-pagination .ant-pagination-item-active:hover,
.ant-modal .ant-modal-content:has(.ant-modal-title:contains("Executivos")) .ant-table-pagination .ant-pagination-item-active:hover {
  background-color: #ffffff !important;
  border-color: #00B050 !important;
}

.ant-modal[aria-labelledby*="Executivos"] .ant-table-pagination .ant-pagination-item-active:hover a,
.ant-modal .ant-modal-content:has(.ant-modal-title:contains("Executivos")) .ant-table-pagination .ant-pagination-item-active:hover a {
  color: #00B050 !important;
}

/* ✅ REGRAS UNIVERSAIS PARA TODOS OS MODAIS - MÁXIMA ESPECIFICIDADE */

/* ✅ Forçar padrão DSM em QUALQUER modal com tabela */
.ant-modal .ant-modal-content .ant-modal-body .ant-table-pagination.ant-pagination,
.ant-modal-content .ant-modal-body .ant-table-pagination.ant-pagination,
.ant-modal-body .ant-table-pagination.ant-pagination {
  margin: 16px 0 !important;
  text-align: right !important;
  display: flex !important;
  justify-content: flex-end !important;
  align-items: center !important;
  gap: 8px !important;
}

/* ✅ Item ativo - MÁXIMA ESPECIFICIDADE para todos os modais */
.ant-modal .ant-modal-content .ant-modal-body .ant-table-pagination .ant-pagination-item-active,
.ant-modal-content .ant-modal-body .ant-table-pagination .ant-pagination-item-active,
.ant-modal-body .ant-table-pagination .ant-pagination-item-active {
  background-color: #ffffff !important;
  border: 2px solid #00B050 !important;
  border-radius: 6px !important;
  min-width: 32px !important;
  height: 32px !important;
  line-height: 28px !important;
  text-align: center !important;
  margin: 0 4px !important;
  box-sizing: border-box !important;
}

.ant-modal .ant-modal-content .ant-modal-body .ant-table-pagination .ant-pagination-item-active a,
.ant-modal-content .ant-modal-body .ant-table-pagination .ant-pagination-item-active a,
.ant-modal-body .ant-table-pagination .ant-pagination-item-active a {
  color: #00B050 !important;
  font-weight: 500 !important;
  font-size: 14px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 100% !important;
  height: 100% !important;
  text-decoration: none !important;
  padding: 0 !important;
  margin: 0 !important;
}

/* ✅ Hover protegido - MÁXIMA ESPECIFICIDADE */
.ant-modal .ant-modal-content .ant-modal-body .ant-table-pagination .ant-pagination-item-active:hover,
.ant-modal-content .ant-modal-body .ant-table-pagination .ant-pagination-item-active:hover,
.ant-modal-body .ant-table-pagination .ant-pagination-item-active:hover {
  background-color: #ffffff !important;
  border-color: #00B050 !important;
}

.ant-modal .ant-modal-content .ant-modal-body .ant-table-pagination .ant-pagination-item-active:hover a,
.ant-modal-content .ant-modal-body .ant-table-pagination .ant-pagination-item-active:hover a,
.ant-modal-body .ant-table-pagination .ant-pagination-item-active:hover a {
  color: #00B050 !important;
}

/* ✅ Itens normais - MÁXIMA ESPECIFICIDADE para todos os modais */
.ant-modal .ant-modal-content .ant-modal-body .ant-table-pagination .ant-pagination-item,
.ant-modal-content .ant-modal-body .ant-table-pagination .ant-pagination-item,
.ant-modal-body .ant-table-pagination .ant-pagination-item {
  border-radius: 6px !important;
  border: 1px solid #d9d9d9 !important;
  background-color: #ffffff !important;
  min-width: 32px !important;
  height: 32px !important;
  line-height: 30px !important;
  text-align: center !important;
  margin: 0 4px !important;
  box-sizing: border-box !important;
}

.ant-modal .ant-modal-content .ant-modal-body .ant-table-pagination .ant-pagination-item a,
.ant-modal-content .ant-modal-body .ant-table-pagination .ant-pagination-item a,
.ant-modal-body .ant-table-pagination .ant-pagination-item a {
  color: rgba(0, 0, 0, 0.85) !important;
  font-weight: 400 !important;
  font-size: 14px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 100% !important;
  height: 100% !important;
  text-decoration: none !important;
  padding: 0 !important;
  margin: 0 !important;
}

/* ✅ Hover em itens normais - MÁXIMA ESPECIFICIDADE */
.ant-modal .ant-modal-content .ant-modal-body .ant-table-pagination .ant-pagination-item:hover:not(.ant-pagination-item-active),
.ant-modal-content .ant-modal-body .ant-table-pagination .ant-pagination-item:hover:not(.ant-pagination-item-active),
.ant-modal-body .ant-table-pagination .ant-pagination-item:hover:not(.ant-pagination-item-active) {
  border-color: #00B050 !important;
  background-color: #ffffff !important;
}

.ant-modal .ant-modal-content .ant-modal-body .ant-table-pagination .ant-pagination-item:hover:not(.ant-pagination-item-active) a,
.ant-modal-content .ant-modal-body .ant-table-pagination .ant-pagination-item:hover:not(.ant-pagination-item-active) a,
.ant-modal-body .ant-table-pagination .ant-pagination-item:hover:not(.ant-pagination-item-active) a {
  color: #00B050 !important;
}