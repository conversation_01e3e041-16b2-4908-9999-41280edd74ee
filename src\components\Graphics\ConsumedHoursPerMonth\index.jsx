import { FilterFilled, StarFilled, StarOutlined } from "@ant-design/icons";
import React, { useEffect, useState } from "react";
import { otrsGet, otrsPost } from "../../../service/apiOtrs";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  XAxis,
  <PERSON>A<PERSON><PERSON>,
  Legend,
  CartesianGrid,
  Tooltip,
} from "recharts";
import { Card, Col, Modal, Popover, Row, Spin, Typography } from "antd";
import ConsumedHoursPerMonthTable from "../../Table/ConsumedHoursPerMonth";
import ReservedHoursFilter from "../../Modals/Filters/ReservedHoursFilter";
import moment from "moment";

export default function ConsumedHoursPermonth(props) {
  const { Title, Text } = Typography;
  const [isFavorite, setIsFavorite] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [allClients, setAllClients] = useState([]);
  const [allWallets, setAllWallets] = useState([]);
  const [allContracts, setAllContracts] = useState();
  const [filteredContracts, setFilteredContracts] = useState([]);
  const [filteredData, setFilteredData] = useState([]);
  const [allTicketsData, setAllTicketsData] = useState([]);
  const [totalHoursClient, setTotalHoursClient] = useState(0);
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [popoverVisibility, setPopoverVisibility] = useState(false);
  const [width, setWidth] = React.useState(window.innerWidth);
  const [filterSelected, setFilterSelected] = useState(false);
  let secondaryProportion = window.innerWidth;

  const GRAPH_NAME = "ConsumedHoursPerMonth";
  const monthsName = [
    "Janeiro",
    "Fevereiro",
    "Mar",
    "Abril",
    "Maio",
    "Junho",
    "Julho",
    "Agosto",
    "Setembro",
    "Outubro",
    "Novembro",
    "Dezembro",
  ];

  // async function getAllClients() {
  //   await otrsGet("read/customer/all/0")
  //     .then((resp) => {
  //       setArrayClients(Object.entries(resp.data));
  //     })
  //     .catch((error) => {
  //       console.log(error);
  //     });
  // }

  // async function getAllContracts() {
  //   await otrsGet("read/contract/all/0")
  //     .then((res) => {
  //       setAllContracts(res.data);
  //       setLoading(false);
  //     })
  //     .catch((error) => {
  //       console.log(error);
  //     });
  // }

  // async function getAllWallets() {
  //   await dynamoGet("dev-wallets")
  //     .then((resp) => {
  //       setAllWallets(resp);
  //     })
  //     .catch((error) => {
  //       console.log(error);
  //     });
  // }

  async function getLastThreeMonths() {
    const currentMonth = moment();
    const currentYear = currentMonth.year();

    const secondMonth = moment().subtract(1, "month");
    const secondYear = secondMonth.year();

    const firstMonth = moment().subtract(2, "month");
    const firstYear = firstMonth.year();

    const arrayDates = [
      {
        month: firstMonth.month() + 1,
        year: firstYear,
      },
      {
        month: secondMonth.month() + 1,
        year: secondYear,
      },
      {
        month: currentMonth.month() + 1,
        year: currentYear,
      },
    ];

    const clientsConsume = [];

    for (let i = 0; i < 3; i++) {
      clientsConsume.push(
        await generateClientTimeConsumed(
          arrayDates[i].year,
          arrayDates[i].month
        )
      );
    }

    let dataTemp = [];

    // ✅ Corrigido: usar forEach() em vez de map() para efeitos colaterais
    clientsConsume.forEach((month, index) => {
      let monthData = [];
      month.forEach((client) => {
        monthData.push({
          uv: client.time,
          month: monthsName[arrayDates[index].month - 1],
          contract_id: client.contract_id,
          client: client.client_id,
          client_name: client.client,
        });
      });
      dataTemp.push(monthData);
      return dataTemp;
    });

    setAllTicketsData(dataTemp);
    setLoading(false);
  }

  async function generateClientTimeConsumed(year, month) {
    const today = moment();

    const tickets = await otrsPost("read/tickets/time", {
      params: {
        start: today.format(`${year}`) + `/${month}/01`,
        end: today.format(`${year}`) + `/${month}/31`,
      },
    });

    let ticketsData = [];
    // ✅ Corrigido: usar forEach() em vez de map() para efeitos colaterais
    tickets.data.forEach((item) => {
      ticketsData.push({
        client: item.client_name,
        time: parseInt(item.consumed_min),
        contract_id: item.id_contract,
        client_id: item.customer_id,
      });
    });

    const mergedTicketsByClient = mergeByClientsName(ticketsData);

    const ticketsMergedInHours = transformMinuresInHours(mergedTicketsByClient);

    return ticketsMergedInHours;
  }

  function mergeByClientsName(tickets) {
    const mergedClients = [];
    // ✅ Corrigido: usar forEach() em vez de map() para efeitos colaterais
    tickets.forEach((client) => {
      const contractsDuplicate = mergedClients.find(
        (item) => item.client === client.client
      );
      if (contractsDuplicate) {
        const indexContract = mergedClients.indexOf(contractsDuplicate);
        mergedClients[indexContract].time += client.time;
      } else {
        mergedClients.push(client);
      }
    });

    return mergedClients;
  }

  function transformMinuresInHours(tickesInMinutes) {
    // ✅ Corrigido: usar forEach() em vez de map() para efeitos colaterais
    tickesInMinutes.forEach((item) => {
      const totalMinutes = item.time;
      const hours = Math.floor(totalMinutes / 60);
      const minutes = (totalMinutes % 60) / 100;
      const result = hours + minutes;
      item.time = result;
    });

    return tickesInMinutes;
  }
  function filterContracts(name) {
    let filteredContractsArray = [];

    Object.entries(allContracts).forEach((val) => {
      if (val[1].document_id === name) {
        filteredContractsArray.push(val[1]);
      }
    });

    setFilteredContracts(filteredContractsArray);
  }

  function filterContractByClient(name) {
    let filteredDataArray = [];
    Object.entries(filteredContracts).forEach((val) => {
      if (val[1].customer_id === name) {
        filteredDataArray.push(val[1]);
      }
    });

    setTotalHoursClient(sumTotalHours(filteredDataArray));
    setFilteredData(filteredDataArray);
  }

  function sumTotalHours(filteredDataArray) {
    let totalHours = 0;
    filteredDataArray.forEach((contract) => {
      totalHours += contract.total_hours;
    });

    return totalHours;
  }

  async function handleDataGraph(filter) {
    setLoading(true);
    setData([]);

    if (filter.client) {
      let data = await otrsPost("read/tickets/totalTimeByCustomerInTimeRange", {
        params: {
          start:
            moment(filter.rangeDate).format("YYYY-MM") + "-01T00:00:00:000Z",
          end: moment(filter.rangeDate).format("YYYY-MM") + "-31T00:00:00:000Z",
          customerId: filter.client,
        },
      }).then((res) => res.data);

      if (data.length === 0) {
        setData([]);
        setLoading(false);
        return null;
      }

      if (filter.wallet) {
        data = data.filter(
          (i) => i.wallet_type.toLowerCase() === filter.wallet.toLowerCase()
        );
      }

      const formatedData = await formatContractsForChart(data);

      setData(formatedData);
      setLoading(false);
    } else if (filter.wallet && !filter.client) {
      const start = moment(filter.rangeDate).format("YYYY-MM") + "-0100:00:00";
      const end = moment(filter.rangeDate).format("YYYY-MM") + "-3100:00:00";

      const { data } = await otrsGet(
        `read/contract/walletInRange/0?start=${start}&end=${end}&walletType=${filter.wallet}`
      );
      const arrData = Object.values(data);
      if (arrData.length === 0) {
        setData([]);
        setLoading(false);
        return null;
      }

      const formatedData = await formatContractsForChart(arrData, true);

      setData(formatedData);
      setLoading(false);
    }
  }

  async function formatContractsForChart(allContracts, isWallet) {
    let formatedData = [];

    allContracts.forEach((contract) => {
      let formatedConsumedHours = contract.customer_total_consumed_min / 60;
      formatedData.push({
        wallet: contract.wallet_type,
        name: isWallet ? contract.name : contract.contract_name,
        createDate: contract.create_time,
        typeHours: contract.type_hours,
        totalHours: isWallet
          ? contract.contract_total_hours
          : contract.total_hours,
        cumulativeHours: contract.cumulative_hours,
        consumedMinutes: contract.customer_total_consumed_min,
        consumedHours: formatedConsumedHours.toFixed(2),
      });
    });
    return formatedData;
  }

  useEffect(() => {
    setIsFavorite(checkIsVisible());
  }, [props.favorites]);

  useEffect(() => {
    setAllContracts(props?.allContracts);
    setAllWallets(props?.allWallets);
    setAllClients(props?.allClients);
    getLastThreeMonths();
  }, [props?.allContracts, props?.allWallets, props?.allClients]); // ✅ Adicionadas dependências corretas

  function showModal() {
    setModalVisible(!modalVisible);
  }

  function handleOk() {
    setModalVisible(false);
  }

  function handleCancel() {
    setModalVisible(false);
  }

  function checkIsVisible() {
    let localStorageData = JSON.parse(localStorage.getItem("favorites"));
    if (localStorageData) {
      let index = localStorageData.findIndex(
        (value) => value.component === GRAPH_NAME
      );
      return index !== -1;
    } else {
      return false;
    }
  }

  async function updateFavorite() {
    await props?.setVisionFavorite(GRAPH_NAME);
    setIsFavorite(checkIsVisible());
  }

  function handlePopoverVisibility() {
    setPopoverVisibility(!popoverVisibility);
  }

  function swapVisible() {
    let object = structuredClone(props.componentVisibles);
    object.consumptionTable = !object.consumptionTable;
    props.setComponentVisibles(object);
  }

  const updateWidth = () => {
    setWidth(window.innerWidth);
  };
  React.useEffect(() => {
    window.addEventListener("resize", updateWidth);

    return () => window.removeEventListener("resize", updateWidth);
  }, []); // ✅ Adicionado array de dependências vazio

  const graphWidth = React.useMemo(() => {
    if (width > 1800) {
      const proportion = 1850 / 750;
      return width / proportion;
    } else if (width > 1200 && width < 1800) {
      const proportion = 1850 / 500;
      return width / proportion;
    } else if (width < 1200) {
      const proportion = 1850 / 500;
      return width / proportion;
    }
  }, [width]);

  const secondaryGraphWidth = React.useMemo(() => {
    if (width > 1800) {
      const proportion = 1850 / 800;
      return width / proportion;
    } else if (width > 1200 && width < 1800) {
      const proportion = 1850 / 800;
      return width / proportion;
    } else if (width < 1200) {
      const proportion = 1850 / 1550;
      return width / proportion;
    }
  }, [width]);

  return (
    <Card bordered={false} style={{ borderRadius: "20px", height: "100%" }}>
      <Row>
        <Col span={18}>
          <Title level={4} style={{ fontWeight: 400 }}>
            Acima/abaixo do contratado
          </Title>
        </Col>
        <Col span={3}>
          <Popover
            open={popoverVisibility}
            onOpenChange={handlePopoverVisibility}
            placement="left"
            content={() => {
              return (
                <ReservedHoursFilter
                  allClients={allClients}
                  allWallets={allWallets}
                  hoursConsumed={true}
                  filteredContracts={filteredContracts}
                  getFilteredContracts={filterContracts}
                  getFilteredContractByClient={filterContractByClient}
                  getGraphData={handleDataGraph}
                  handlePopoverVisibility={handlePopoverVisibility}
                  setFilterSelected={(value) => setFilterSelected(value)}
                  consumedHours={true}
                />
              );
            }}
            trigger="click"
          >
            <div style={{ cursor: "pointer" }} className="filter">
              <FilterFilled className="filter-icon" />
            </div>
          </Popover>
        </Col>
        <Col span={3}>
          {!isFavorite ? (
            <StarOutlined onClick={updateFavorite} className="star-icon" />
          ) : (
            <StarFilled onClick={updateFavorite} className="star-icon" />
          )}
        </Col>
      </Row>

      {loading ? (
        <Row
          align="middle"
          justify="center"
          style={{ height: "250px", minWidth: "275px" }}
        >
          <Col style={{ display: "flex", flexDirection: "column" }}>
            <Spin />
            <Text>Carregando...</Text>
          </Col>
        </Row>
      ) : data.length > 0 ? (
        <div className="graph">
          <BarChart
            // onClick={() => (props.isModal ? showModal() : swapVisible())}
            width={
              secondaryProportion
                ? width < 1600
                  ? secondaryGraphWidth - 300
                  : secondaryGraphWidth - 150
                : width < 1600
                ? graphWidth - 300
                : graphWidth - 150
            }
            height={300}
            data={data}
            setLoading={false}
            margin={{
              top: 10,
              right: 14,
              left: 44,
              bottom: 5,
            }}
          >
            <XAxis hide dataKey="name"/>
            <YAxis />
            <Tooltip />
            <CartesianGrid stroke="#f5f5f5" />
            <Legend iconType={"square"} />
            <Bar
              name="Horas Reservadas"
              dataKey="totalHours"
              stackId={"a"}
              barSize={80}
              fill={"#43914F"}
            />
            <Bar
              name="Horas Acumuladas"
              dataKey="cumulativeHours"
              stackId={"a"}
              barSize={80}
              fill={"#881798"}
            />
            <Bar
              name="Horas Consumidas"
              dataKey="consumedHours"
              stackId={"a"}
              barSize={80}
              fill={"#435E91"}
            />
          </BarChart>
        </div>
      ) : (
        <Row style={{ height: "250px" }} align="middle" justify="center">
          <Col>
            <Text>Sem dados neste mês*</Text>
          </Col>
        </Row>
      )}

      <Modal
        closable={true}
        width={"1090px"}
        bodyStyle={{ backgroundColor: "transparent" }}
        cancelButtonProps={{ style: { display: "none" } }}
        okButtonProps={{
          style: {
            display: "none",
            backgroundColor: "#43914F",
            color: "white",
            fontWeight: "bold",
            borderRadius: "5px",
          },
        }}
        open={modalVisible}
        onOk={handleOk}
        onCancel={handleCancel}
      >
        <ConsumedHoursPerMonthTable
          allTicketsData={allTicketsData}
          filteredData={filteredData}
          allClients={allClients}
          allWallets={allWallets}
          filteredContracts={filteredContracts}
          getFilteredContracts={filterContracts}
          getFilteredContractByClient={filterContractByClient}
          getGraphData={handleDataGraph}
          showModal={showModal}
        />
      </Modal>
    </Card>
  );
}
