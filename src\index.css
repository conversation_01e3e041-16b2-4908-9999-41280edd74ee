/* Ant Design v5 uses CSS-in-JS, no need to import CSS */
@import url("https://fonts.googleapis.com/css2?family=Libre+Franklin:wght@300;400;500;600;700&display=swap");

body {
  margin: 0;
  font-family: "Libre Franklin", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto",
    "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue",
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: "Libre Franklin", source-code-pro, Menlo, Monaco, Consolas, "Courier New",
    monospace;
}

.preview-container p{
  text-align: left;
}

.ql-clipboard {
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  user-select: text;    
}