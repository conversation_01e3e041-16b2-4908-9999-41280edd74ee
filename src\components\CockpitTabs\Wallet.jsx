import { Col, Row, Typography } from "antd";
import { useEffect } from "react";

export default function WalletTab(props) {
  const { Title } = Typography;
  useEffect(() => {}, [props.components]);

  /* Checking if the walletComponents array is empty. */
  if (props.walletComponents.length === 0) {
    return <Title level={1}>Nenhuma visão encontrada!</Title>;
  }
  
  return (
    <Row gutter={[20, 20]} justify="space-between">
      {
        /* Mapping the array of objects. */
        props.walletComponents.map((val, index) => {
          return (
            <Col
            key={index}
            xs={24}
            sm={24} 
            md={24} 
            xl={val.name === "Vencimento dos contratos" || val.name === "Gerenciamento de tickets por contrato" ? 11 :12} lg={20}>
              {props.components[val.component]?.object}
            </Col>
          );
        })
      }
    </Row>
  );
}
