import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  Tag,
  Collapse,
  List,
  Typography,
  Card,
} from "antd";
import { LoadingOutlined } from "@ant-design/icons";
import React, { useState } from "react";
import { dynamoGetById } from "../../../service/apiDsmDynamo";

export const VisualizeInfo = (props) => {
  const [showModal, setShowModal] = useState(false);
  const { Text, Paragraph } = Typography;
  const { Panel } = Collapse;
  const { currentCustomer } = props;
  const [client, setClient] = useState(currentCustomer);
  const [loading, setLoading] = useState(false);
  const handleOpenModal = async () => {
    setShowModal(true);
    setLoading(true);
    const customerInfo = await dynamoGetById(
      `${process.env.REACT_APP_STAGE}-customers`,
      currentCustomer.id
    );
    setClient(customerInfo);
    setLoading(false);
  };

  const formatInfo = (info, type = "") => {
    let res = {};

    if (type === "general") {
      for (const key of info) {
        switch (key[0]) {
          case "identifications":
            res["CRM ID"] = key[1].crm_id || "";
            res["ITSM ID"] = key[1].itsm_id || "";
            break;
          case "dsm_id":
            res["DSM ID"] = key[1];
            break;
          default:
            break;
        }
      }
    } else {
      for (const key of info) {
        switch (key[0]) {
          case "cidade":
            res["Cidade"] = key[1];
            break;
          case "bairro":
            res["Bairro"] = key[1];
            break;
          case "cep":
            res["CEP"] = key[1];
            break;
          case "pais":
            res["Pais"] = key[1];
            break;
          case "logradouro":
            res["Logradouro"] = key[1];
            break;
          case "social_capital":
            res["Capital Social"] = key[1];
            break;
          case "legal_nature":
            res["Natureza Legal"] = key[1]?.descricao;
            break;

          default:
            break;
        }
      }
    }

    return Object.entries(res).filter((item) => item[1] !== null);
  };

  const formatMembers = (info) => {
    let res = [];

    for (const key of info) {
      let obj = {};

      obj["Nome"] = key["nome"];
      obj["Data de Entrada"] = key["data_entrada"];
      obj["Qualificação do Sócio"] = key["qualificacao_socio"]?.descricao;
      obj["Faixa Etária"] = key["faixa_etaria"];
      obj["Tipo"] = key["tipo"];

      res.push(obj);
    }

    return res;
  };

  return (
    <>
      <Button
        style={{ padding: "0" }}
        type="text"
        onClick={async () => {
          handleOpenModal();
        }}
      >
        <Tag color="#0f9347">Visualizar</Tag>
      </Button>
      <Modal
        title="Informações do Cliente"
        open={showModal}
        onCancel={() => setShowModal(false)}
        footer={[<Button onClick={() => setShowModal(false)}>Fechar</Button>]}
      >
        <Row align="middle" justify="center">
          {loading ? (
            <LoadingOutlined />
          ) : (
            <Col span={24}>
              <Collapse bordered={false}>
                <Panel header="Informações gerais" key="4">
                  <List
                    itemLayout="horizontal"
                    dataSource={formatInfo(Object.entries(client), "general")}
                    renderItem={(item) => (
                      <List.Item>
                        <List.Item.Meta title={item[0]} description={item[1]} />
                      </List.Item>
                    )}
                  />
                </Panel>
                {client.members && (
                  <Panel header="Sócios" key="1">
                    <List
                      itemLayout="horizontal"
                      grid={{ gutter: 16, column: 1 }}
                      pagination={{ pageSize: 5 }}
                      dataSource={formatMembers(client?.members)}
                      renderItem={(item) => (
                        <List.Item>
                          <Card bordered={false} title={item.Nome}>
                            {Object.entries(item).map(
                              ([key, value]) =>
                                key !== "Nome" && (
                                  <Row gutter={[6, 12]}>
                                    <Col>
                                      <Text>{key}: </Text>
                                    </Col>
                                    <Col>
                                      <Paragraph type="secondary">
                                        {value}
                                      </Paragraph>
                                    </Col>
                                  </Row>
                                )
                            )}
                          </Card>
                        </List.Item>
                      )}
                    />
                  </Panel>
                )}
                {client.address && (
                  <Panel header="Endereço" key="2">
                    <List
                      itemLayout="horizontal"
                      dataSource={formatInfo(Object.entries(client.address))}
                      renderItem={(item) => (
                        <List.Item>
                          <List.Item.Meta
                            title={item[0]}
                            description={item[1]}
                          />
                        </List.Item>
                      )}
                    />
                  </Panel>
                )}
                <Panel header="Informações básicas" key="3">
                  <List
                    itemLayout="horizontal"
                    dataSource={formatInfo(Object.entries(client))}
                    renderItem={(item) => (
                      <List.Item>
                        <List.Item.Meta title={item[0]} description={item[1]} />
                      </List.Item>
                    )}
                  />
                </Panel>
              </Collapse>
            </Col>
          )}
        </Row>
      </Modal>
    </>
  );
};
