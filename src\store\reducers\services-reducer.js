import { createSlice } from "@reduxjs/toolkit";

const REDUCER_NAME = "services";

const INITIAL_STATE = {
  services: [],
};

const servicesSlice = createSlice({
  name: REDUCER_NAME,
  initialState: INITIAL_STATE,
  reducers: {
    setServicesReduce(state, action) {
      state.services = action.payload;
    },
  },
});

export const { setServicesReduce } = servicesSlice.actions;
export default servicesSlice.reducer;
