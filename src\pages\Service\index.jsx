import React, { useEffect, useState } from "react";
import {
  Layout,
  Form,
  Card,
  Row,
  Col,
  Input,
  Button,
  Typography,
  Affix,
  Tooltip,
  Select,
  Popconfirm,
} from "antd";
import {
  CloseCircleOutlined,
  ArrowLeftOutlined,
  CheckCircleOutlined,
  CheckOutlined,
  ArrowUpOutlined,
} from "@ant-design/icons";
import { HeaderMenu } from "../../components/HeaderMenu";
import { SideMenu } from "../../components/SideMenu";
import { useLocation, useNavigate } from "react-router-dom";
import { v4 } from "uuid";
import { TextColor } from "../../hooks/ColorInverter";
import { ActivitiesContent } from "../../components/Services";
import {
  handleCheckServiceName,
  handleGetServices,
  handleSubmit,
} from "../../controllers/services";
import { uniqByKeepLast } from "../../utils/uniqueByKeepLast";
import { shallowEqual, useSelector } from "react-redux";
import { estimatedActivities } from "../../constants/servicesFixedData";

export const Service = () => {
  const navigate = useNavigate();
  const { Content } = Layout;
  const { TextArea } = Input;
  const { Option } = Select;
  const { Text } = Typography;
  const { state } = useLocation();
  const allServices = useSelector(
    (state) => state.services.services,
    shallowEqual
  );
  const [service_form] = Form.useForm();
  const [activities_form] = Form.useForm();
  const [existingName, setExistingName] = useState(false);
  const [toTopVisible, setToTopVisible] = useState(false);
  const [tagName, setTagName] = useState(state[0]?.tag.name);
  const [serviceName, setServiceName] = useState(state[0]?.name);
  const [loading, setLoading] = useState(false);
  const [serviceDescription, setServiceDescription] = useState(
    state[0]?.description
  );

  const [disabled, setDisabled] = useState(true);
  const [mainTagColorInput, setMainTagColorInput] = useState("#0f9347");
  const [collapsed, setCollapsed] = useState(false);
  const [activities, setActivities] = useState([]);
  const [selectTagToggle, setSelectTagToggle] = useState(false);

  let arr = [];

  console.log('🔍 Service - state[1]:', state[1]);

  const newArr = uniqByKeepLast(state[1], (e) => e.tag.name);
  console.log('🔍 Service - newArr após uniqByKeepLast:', newArr);

  newArr.forEach((e) => {
    return arr.push(e[1]);
  });

  console.log('🔍 Service - arr final para Select:', arr);

  const [sumHoursForType, setSumHoursForType] = useState([
    { SETUP247Sum: 0 },
    { SETUP85Sum: 0 },
    { SUST247Sum: 0 },
    { SUST85Sum: 0 },
    { DBA247Sum: 0 },
    { DBA85Sum: 0 },
  ]);

  const screenScroll = () => {
    document.documentElement.scrollTop > 360
      ? setToTopVisible(true)
      : setToTopVisible(false);
  };

  useEffect(() => {
    if (allServices.length === 0 || !allServices) {
      handleGetServices(setLoading);
    }
  }, []);

  useEffect(() => {
    window.addEventListener("scroll", screenScroll);
  }, []);

  useEffect(() => {
    setSumHoursForType([
      {
        SETUP247Sum: activities.reduce((acc, cur) => {
          if (cur["estimates"]["24x7setup"]) {
            let sum = Number(acc) + Number(cur["estimates"]["24x7setup"]);
            return sum;
          }
          return acc;
        }, 0),
        SETUP85Sum: activities.reduce((acc, cur) => {
          if (cur["estimates"]["8x5setup"]) {
            let sum = Number(acc) + Number(cur["estimates"]["8x5setup"]);
            return sum;
          }
          return acc;
        }, 0),
        SUST247Sum: activities.reduce((acc, cur) => {
          if (cur["estimates"]["24x7sust"]) {
            let sum = Number(acc) + Number(cur["estimates"]["24x7sust"]);
            return sum;
          }
          return acc;
        }, 0),
        SUST85Sum: activities.reduce((acc, cur) => {
          if (cur["estimates"]["8x5sust"]) {
            let sum = Number(acc) + Number(cur["estimates"]["8x5sust"]);
            return sum;
          }
          return acc;
        }, 0),
        DBA247Sum: activities.reduce((acc, cur) => {
          if (cur["estimates"]["24x7dbasec"]) {
            let sum = Number(acc) + Number(cur["estimates"]["24x7dbasec"]);
            return sum;
          }
          return acc;
        }, 0),
        DBA85Sum: activities.reduce((acc, cur) => {
          if (cur["estimates"]["8x5dbasec"]) {
            let sum = Number(acc) + Number(cur["estimates"]["8x5dbasec"]);
            return sum;
          }
          return acc;
        }, 0),
      },
    ]);
    activities.map((item) => {
      if (
        Object.values(item.estimates).find(
          (value) => value !== "" && value > 0
        ) &&
        item.name &&
        item.description &&
        tagName !== "" &&
        serviceName &&
        serviceName !== "" &&
        serviceDescription &&
        serviceDescription !== ""
      ) {
        setDisabled(false);
      } else {
        setDisabled(true);
      }
    });
  }, [activities, serviceName, serviceDescription, tagName]);

  useEffect(() => {
    service_form.setFieldsValue({
      name: state[0].name,
      tag_name: tagName,
      tag_color: state[0].tag.rgb,
      description: state[0].description,
    });

    state[0].tasks.map((item, indexKey) => {
      return setActivities((prev) => [
        ...prev,

        {
          id: v4(),
          name: item.name,
          description: item.description,
          estimates: {
            "8x5dbasec": item.estimates["8x5dbasec"],
            "8x5setup": item.estimates["8x5setup"],
            "8x5sust": item.estimates["8x5sust"],
            "24x7dbasec": item.estimates["24x7dbasec"],
            "24x7setup": item.estimates["24x7setup"],
            "24x7sust": item.estimates["24x7sust"],
          },
          subtasks: item.subtasks.map((item, id) => {
            return {
              index: id,
              value: item.description,
            };
          }),
        },
      ]);
    });
  }, []);

  return (
    <Layout style={{ minHeight: "100vh" }}>
      <HeaderMenu collapsed={collapsed} setCollapsed={setCollapsed} />
      <Layout>
        <SideMenu collapsed={collapsed} />
        <Content style={{ padding: "2em" }}>
          <Card
            style={{
              boxShadow: "0 0 10px rgba(0,0,0,0.1)",
              borderRadius: "20px",
            }}
          >
            <Popconfirm
              title="Ao voltar você perderá os dados do formulário, tem certeza?"
              onConfirm={() => {
                navigate(-1);
              }}
              okText="Sim"
              cancelText="Não"
            >
              <Button
                type="text"
                style={{ marginBottom: "15px", padding: "2px 5px 2px 0" }}
              >
                <ArrowLeftOutlined />
                Editar Serviço
              </Button>
            </Popconfirm>
            <Row justify="center">
              <Col span={24}>
                <Form
                  form={service_form}
                  layout="vertical"
                  requiredMark={false}
                  onFinish={() => {
                    handleSubmit(
                      activities_form.getFieldsValue(),
                      serviceName,
                      serviceDescription,
                      activities,
                      tagName,
                      false,
                      setLoading,
                      mainTagColorInput,
                      navigate,
                      existingName,
                      "edit",
                      state[0]
                    );
                  }}
                >
                  <Row gutter={[20, 12]} justify="space-between">
                    <Col lg={16} sm={24}>
                      <Row justify="space-between">
                        <Col span={12}>
                          <Form.Item name="name">
                            <Text>
                              Nome{" "}
                              <span
                                style={{
                                  color: "#ff4d4f",
                                  fontWeight: "bold",
                                }}
                              >
                                *
                              </span>
                            </Text>
                            <Input
                              placeholder="Nome do serviço"
                              style={{
                                borderColor: !serviceName ? "#ff4d4f" : "",
                                marginTop: 8,
                              }}
                              value={serviceName}
                              onChange={(e) => {
                                handleCheckServiceName(
                                  e.target.value,
                                  allServices,
                                  setExistingName
                                );
                                setServiceName(e.target.value);
                              }}
                            />
                            {!serviceName && (
                              <span
                                style={{
                                  color: "#ff4d4f",
                                  fontSize: 13,
                                }}
                              >
                                * valor não informado, campo obrigatório.
                              </span>
                            )}
                          </Form.Item>
                        </Col>
                        <Col span={11}>
                          <Tooltip
                            placement="top"
                            title={
                              <>
                                {selectTagToggle === false
                                  ? "Deseja utilizar uma tag existente?"
                                  : "Deseja criar uma nova tag"}
                                <Row justify="end">
                                  <Col>
                                    <Button
                                      type="primary"
                                      icon={<CheckOutlined />}
                                      onClick={() =>
                                        setSelectTagToggle(!selectTagToggle)
                                      }
                                    ></Button>
                                  </Col>
                                </Row>
                              </>
                            }
                          >
                            {selectTagToggle === false ? (
                              <Row justify="start">
                                <Col lg={22}>
                                  <Form.Item name="tag_name">
                                    <Text>
                                      Nome da Tag{" "}
                                      <span
                                        style={{
                                          color: "#ff4d4f",
                                          fontWeight: "bold",
                                        }}
                                      >
                                        *
                                      </span>
                                    </Text>
                                    <Input
                                      placeholder="Nome da tag"
                                      value={tagName}
                                      style={{
                                        borderColor: !tagName ? "#ff4d4f" : "",
                                        marginTop: 8,
                                      }}
                                      onChange={(event) => {
                                        setTagName(event.target.value);
                                      }}
                                    />
                                  </Form.Item>
                                </Col>

                                <Col lg={1}>
                                  <Form.Item name="tag_color">
                                    <Input
                                      type="color"
                                      value={mainTagColorInput}
                                      onChange={(event) => {
                                        setMainTagColorInput(
                                          event.target.value
                                        );
                                      }}
                                      style={{
                                        width: "2rem",
                                        padding: "0",
                                        clipPath: "circle(32% at center)",
                                        border: "none",
                                        outline: "none",
                                        boxShadow: "none",
                                        marginTop: 30,
                                      }}
                                    />
                                  </Form.Item>
                                </Col>
                              </Row>
                            ) : (
                              <Row type="flex" style={{ alignItems: "center" }}>
                                <Col lg={24}>
                                  <Form.Item
                                    name="tag_name"
                                    rules={[
                                      {
                                        required: true,
                                        message: "Preencha o nome da tag.",
                                      },
                                    ]}
                                  >
                                    <Text>
                                      Nome da Tag{" "}
                                      <span
                                        style={{
                                          color: "#ff4d4f",
                                          fontWeight: "bold",
                                        }}
                                      >
                                        *
                                      </span>
                                    </Text>
                                    <Select
                                      placeholder="Nome da tag"
                                      style={{
                                        marginTop: 8,
                                      }}
                                      onChange={(e, i) => {
                                        setTagName(e);
                                        state[1]?.map((i) =>
                                          i.tag.name === e
                                            ? setMainTagColorInput(i.tag.rgb)
                                            : null
                                        );
                                      }}
                                    >
                                      {arr.map((item) => {
                                        console.log('🔍 Service - Renderizando Option:', item);
                                        return (
                                          <Option value={item.tag.name}>
                                            {TextColor(
                                              item?.tag.rgb,
                                              item?.tag.name
                                            )}
                                          </Option>
                                        );
                                      })}
                                    </Select>
                                  </Form.Item>
                                </Col>
                              </Row>
                            )}
                          </Tooltip>
                        </Col>
                      </Row>
                      <Row>
                        <Col span={24}>
                          <Form.Item name="description">
                            <Text>
                              Descrição de serviço{" "}
                              <span
                                style={{
                                  color: "#ff4d4f",
                                  fontWeight: "bold",
                                }}
                              >
                                *
                              </span>
                            </Text>
                            <TextArea
                              rows={6}
                              placeholder="Insira a descrição do serviço"
                              value={serviceDescription}
                              onChange={(e) => {
                                setServiceDescription(e.target.value);
                              }}
                              style={{
                                marginTop: "8px",
                              }}
                            />
                            {!serviceDescription && (
                              <span
                                style={{
                                  color: "#ff4d4f",
                                  fontSize: 13,
                                }}
                              >
                                * valor não informado, campo obrigatório.
                              </span>
                            )}
                          </Form.Item>
                        </Col>
                      </Row>
                    </Col>
                    <Col lg={8} sm={24}>
                      <div
                        style={{
                          widht: "100%",
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "center",
                          flexDirection: "column",
                        }}
                      >
                        <Text>Estimativas</Text>
                        <Row
                          gutter={[16, 16]}
                          style={{
                            backgroundColor: "#D9D9D9",
                            padding: "30px",
                            borderRadius: "5px",
                          }}
                        >
                          {estimatedActivities.map(
                            (
                              mainEstimatedActivity,
                              mainEstimatedActivityKey
                            ) => {
                              return (
                                <Col span={12} key={mainEstimatedActivityKey}>
                                  <Row align="center">
                                    {mainEstimatedActivity.title}
                                  </Row>
                                  {sumHoursForType.map(
                                    (
                                      sumHoursForTypeItem,
                                      sumHoursForTypeKey
                                    ) => {
                                      return Object.values(
                                        sumHoursForTypeItem
                                      ).map(
                                        (
                                          sumHoursForTypeItemValue,
                                          sumHoursForTypeValueKey
                                        ) => {
                                          return (
                                            sumHoursForTypeValueKey ===
                                              mainEstimatedActivityKey && (
                                              <Input
                                                placeholder={
                                                  sumHoursForTypeItemValue
                                                }
                                                disabled
                                              />
                                            )
                                          );
                                        }
                                      );
                                    }
                                  )}
                                </Col>
                              );
                            }
                          )}
                        </Row>
                      </div>
                    </Col>
                  </Row>
                </Form>
                <Form
                  layout="vertical"
                  requiredMark={false}
                  form={activities_form}
                  style={{ marginTop: "1rem" }}
                >
                  <Text>Atividades</Text>
                  <ActivitiesContent
                    activities={activities}
                    setActivities={setActivities}
                  />
                </Form>

                <Row justify="space-between">
                  <Button danger type="primary" onClick={() => navigate(-1)}>
                    Cancelar <CloseCircleOutlined />
                  </Button>
                  {disabled === true ? (
                    <Tooltip
                      placement="top"
                      title={"Verifique os dados das atividades"}
                    >
                      <Button
                        type="primary"
                        onClick={() => service_form.submit()}
                        disabled={disabled}
                      >
                        Concluir <CheckCircleOutlined />
                      </Button>
                    </Tooltip>
                  ) : (
                    <Button
                      type="primary"
                      onClick={() => service_form.submit()}
                      loading={loading}
                      disabled={disabled}
                    >
                      Concluir <CheckCircleOutlined />
                    </Button>
                  )}
                </Row>
              </Col>
            </Row>
          </Card>
        </Content>
        {toTopVisible === true && (
          <Affix
            style={{
              clipPath: "circle()",
              position: "fixed",
              right: 5,
              bottom: 5,
              width: "50px",
            }}
          >
            <Button
              style={{ width: "100%" }}
              type="primary"
              onClick={() => window.scrollTo({ top: 0, behavior: "smooth" })}
            >
              <ArrowUpOutlined />
            </Button>
          </Affix>
        )}
      </Layout>
    </Layout>
  );
};
