import React, { useState } from 'react'

import { <PERSON>dal, But<PERSON>, Select } from "antd";
import { MailOutlined } from "@ant-design/icons";


export const ModalSendEmail = ({proposal}) => {
    const [openEmailModal, setOpenEmailModal] = useState(false)
    

    const handleOpen = () => {
        setOpenEmailModal(true)
    }

    const sendEmail = () => {
        setOpenEmailModal(false)
    }

    const handleCancel = () => {
        setOpenEmailModal(false)
    }

    console.log( proposal.customer.contacts)

    
    const contacts = proposal.customer.contacts.map(item => {
        return{
            label: item.email,
            value: item.email
        }
    })

    return(
        <>
            <Button type='text' icon={<MailOutlined />} onClick={handleOpen}/>
            <Modal
            title="Selecione os contatos que devem receber o contrato documentado!"
            open={openEmailModal}
            onCancel={handleCancel}
            >
                <Select
                style={{
                    maxWidth: "280px",
                    width: "100%",
                    minWidth: "100px",
                }}
                showSearch
                mode='multiple'
                optionFilterProp="children"
                filterOption={(input, option) => (option?.label ?? '').includes(input)}
                filterSort={(optionA, optionB) => (optionA?.label ?? '').toLowerCase().localeCompare((optionB?. label ?? '').toLowerCase())}
                placeholder="Selecione um e-mail"
                options={contacts}
                >   
                </Select>
                
            </Modal>
        </>
    )
}