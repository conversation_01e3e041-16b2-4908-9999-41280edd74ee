import React, { useState, useEffect } from "react";
import moment from "moment";
import { dynamoGet } from "../../../service/apiDsmDynamo";
import { otrsGet } from "../../../service/apiOtrs";
import { Table, Button, Popover, Row, Typography } from "antd";
import { FilterFilled } from "@ant-design/icons";
import TicketsOfWalletManagesFilter from "../../Modals/Filters/TicketsOfWalletManagesFilter";
import "./TicketsOfWalletManages.css";
import "moment/locale/pt-br";

moment.locale("pt-br");

function TicketsOfWallerManagesTable(props) {
  const { Text } = Typography;
  const [popoverVisibility, setPopoverVisibility] = useState(false);
  const [allContractTypes, setAllContractTypes] = useState([]);
  const [allCustomers, setAllCustomers] = useState([]);
  const [data, setData] = useState();
  const columns = [
    {
      dataIndex: "ticket_priority_id",
      key: "ticket_priority_id",
      render: (ticket_priority_id) => (
        <div className="priority-container">
          <div
            className="priority"
            style={{ background: getPrioriyColor(ticket_priority_id) }}
          ></div>
        </div>
      ),
    },
    {
      title: "Cliente",
      dataIndex: "client_name",
      key: "client_name",
      render: (client_name) => <Text>{client_name}</Text>,
    },
    {
      title: "N° do Ticket",
      dataIndex: "ticket_number",
      key: "ticket_number",
      render: (ticket_number) => <Text>{ticket_number}</Text>,
    },
    {
      title: "Título",
      dataIndex: "subject",
      key: "subject",
      render: (subject) => <Text>{subject}</Text>,
    },
    {
      title: "Proprietário",
      dataIndex: "owner_ticket",
      key: "owner_ticket",
      render: (owner_ticket) => <Text>{owner_ticket}</Text>,
    },
    {
      title: "Tipo",
      dataIndex: "type",
      key: "type",
      render: (type) => <Text>{type}</Text>,
    },
    {
      title: "Abertura",
      dataIndex: "ticket_create_time",
      key: "ticket_create_time",
      render: (ticket_create_time, _) => (
        <Text>{moment(ticket_create_time).format("DD/MM hh:mm")}</Text>
      ),
    },
    {
      title: "Duração",
      dataIndex: "ticket_create_time",
      key: "ticket_create_time",
      render: (ticket_create_time) => (
        <Text>
          {moment(Date.now() - new Date(ticket_create_time).getTime()).days() >
          1
            ? moment(
                Date.now() - new Date(ticket_create_time).getTime()
              ).days() + " dias"
            : moment(
                Date.now() - new Date(ticket_create_time).getTime()
              ).days() + " dia"}{" "}
        </Text>
      ),
    },
  ];

  function getAllContractTypes() {
    dynamoGet("hml-contract-type")
      .then((res) => {
        setAllContractTypes(res);
      })
      .catch((error) => {
        console.log(error);
      });
  }

  function getContractTypeName(typeId) {
    let contractType = allContractTypes.filter((item) => typeId === item.type);
    return contractType.length === 0 ? "-" : contractType[0].name;
  }

  function getAllCustomer() {
    otrsGet("read/customer/all/0").then((response) => {
      setAllCustomers(response.data);
    });
  }

  function getCustomerName(customerId) {
    let customerName = "-";
    Object.entries(allCustomers).forEach((item) => {
      if (item[1].customer_id === customerId) {
        customerName = item[1].name;
      }
    });

    return customerName;
  }

  function getPrioriyColor(priorityId) {
    if (priorityId === 5) {
      return "#FF3C3C";
    } else if (priorityId === 4) {
      return "#F1F43F";
    } else if (priorityId === (3 || 0)) {
      return "#74CF49";
    }
  }

  useEffect(() => {
    setData(props.tickets);
  }, [props.tickets]);

  function handlePopoverVisibility() {
    setPopoverVisibility(!popoverVisibility);
  }

  return (
    <Row justify="space-between" align="middle" gutter={[0, 16]}>
      <div
        style={{
          maxHeight: "400px",
          overflow: "auto",
          scrollbarWidth: "thin",
          width: "100%",
          marginTop: "20px",
        }}
      >
        <Table columns={columns} dataSource={data} />
      </div>
    </Row>
  );
}

export default TicketsOfWallerManagesTable;
