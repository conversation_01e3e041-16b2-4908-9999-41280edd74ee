import {
  BgColorsOutlined,
  FilterFilled,
  StarFilled,
  StarOutlined,
} from "@ant-design/icons";
import React, { useEffect } from "react";
import { MainBarChart } from "../../Graphs/BarGraph";
import { Card, Col, Modal, Popover, Row, Spin, Typography } from "antd";
import { useState } from "react";
import ColorPicker from "../../ColorPicker";
import UnopenedTickersFilter from "../../Modals/Filters/UnopenedTicketsFilter";
import { otrsGet, otrsPost } from "../../../service/apiOtrs";
import moment from "moment";
import UnopenedTickets from "../../Table/UnopenedTickets";
import { CockpitsHeader } from "../../CockpitsHeader";

export default function UnopenedTicketsGraph(props) {
  const { Title, Text } = Typography;
  const [isFavorite, setIsFavorite] = useState(false);
  const [colors, setColors] = useState();
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [data, setData] = useState();
  const [thirtyDaysClients, setThirtyDaysClients] = useState();
  const [sixtyDaysClients, setSixtyDaysClients] = useState();
  const [ninetyDaysClients, setNinetyDaysClients] = useState();
  const [tableData, setTableData] = useState();
  const [contractType, setContractType] = useState(false);
  const [popoverVisibility, setPopoverVisibility] = useState(false);

  const GRAPH_NAME = "UnopenedTicketsGraph";

  async function updateFavorite() {
    await props?.setVisionFavorite(GRAPH_NAME);
    setIsFavorite(checkIsVisible());
  }

  function getGraphColor() {
    let storage_color = JSON.parse(localStorage.getItem("colors"));
    if (storage_color) {
      setColors(storage_color[GRAPH_NAME]);
    }
  }

  function changeColors(colors) {
    let storage_color = JSON.parse(localStorage.getItem("colors"));
    if (!storage_color) {
      storage_color = {};
    }
    storage_color[GRAPH_NAME] = colors;
    localStorage.setItem("colors", JSON.stringify(storage_color));
    setColors(colors);
  }

  function getContractType() {
    setContractType(props.contractType);
  }

  async function get30DaysTickets() {
    const { data } = await otrsPost("read/tickets/allContractsTicketsInRange", {
      params: {
        start:
          moment().subtract(1, "months").format("YYYY-MM-DD") + " 00:00:00",
        end: moment().format("YYYY-MM-DD") + " 00:00:00",
      },
    });

    return data;
  }

  async function get60DaysTickets() {
    const { data } = await otrsPost("read/tickets/allContractsTicketsInRange", {
      params: {
        start:
          moment().subtract(2, "months").format("YYYY-MM-DD") + " 00:00:00",
        end: moment().subtract(1, "months").format("YYYY-MM-DD") + " 00:00:00",
      },
    });

    return data;
  }

  async function get90DaysTickets() {
    const { data } = await otrsPost("read/tickets/allContractsTicketsInRange", {
      params: {
        start:
          moment().subtract(3, "months").format("YYYY-MM-DD") + " 00:00:00",
        end: moment().subtract(2, "months").format("YYYY-MM-DD") + " 00:00:00",
      },
    });

    return data;
  }

  async function setDataChart() {
    const clientsThirtyDays = [];
    const clientsSixtyDays = [];
    const clientsNinetyDays = [];
    const allTickets30Days = await get30DaysTickets();
    const allTickets60Days = await get60DaysTickets();
    const allTickets90Days = await get90DaysTickets();

    let currentActiveClients = props?.clients;
    currentActiveClients = currentActiveClients.map((customer) => {
      return {
        ...customer,
        customer_name: customer?.names?.fantasy_name || customer?.names?.name,
        customer_id: customer?.identifications?.itsm_id.toString(),
      };
    });
    currentActiveClients.forEach(async (c) => {
      const foundTicketIn30Days = allTickets30Days.find(
        (t) => t.customer_id === c.customer_id
      );

      const foundTicketIn60Days = allTickets60Days.find(
        (t) => t.customer_id === c.customer_id
      );
      const foundTicketIn90Days = allTickets90Days.find(
        (t) => t.customer_id === c.customer_id
      );

      if (foundTicketIn30Days) {
        return true;
      }
      if (foundTicketIn60Days) {
        clientsThirtyDays.push(c);
        return true;
      }
      if (foundTicketIn90Days) {
        clientsSixtyDays.push(c);
        return true;
      }
      if (
        !foundTicketIn30Days &&
        !foundTicketIn60Days &&
        !foundTicketIn90Days
      ) {
        clientsNinetyDays.push(c);
        return true;
      }
    });

    const dataTemp = [
      {
        name: "30 dias",
        clientes: clientsThirtyDays.length,
      },
      {
        name: "60 dias",
        clientes: clientsSixtyDays.length,
      },
      {
        name: "90 dias",
        clientes: clientsNinetyDays.length,
      },
    ];
    setThirtyDaysClients(clientsThirtyDays);
    setSixtyDaysClients(clientsSixtyDays);
    setNinetyDaysClients(clientsNinetyDays);
    setData(dataTemp);
    setLoading(false);
  }

  function checkIsVisible() {
    let localStorageData = JSON.parse(localStorage.getItem("favorites"));
    if (localStorageData) {
      var index = localStorageData.findIndex(
        (value) => value.component === GRAPH_NAME
      );

      return index !== -1;
    } else {
      return false;
    }
  }

  function closeModal() {
    setModalVisible(false);
  }

  function showModal(event) {
    if (event?.activeTooltipIndex === 0) {
      setTableData(thirtyDaysClients);
    }

    if (event?.activeTooltipIndex === 1) {
      setTableData(sixtyDaysClients);
    }

    if (event?.activeTooltipIndex === 2) {
      setTableData(ninetyDaysClients);
    }

    setModalVisible(true);
  }

  useEffect(() => {
    setLoading(true);
    getGraphColor();
    getContractType();
    setDataChart();
    // getContracts();
  }, [props.contractType, props.contracts, props.clients]);

  useEffect(() => {
    setIsFavorite(checkIsVisible());
  }, [props.favorites]);

  function handleVisibleChange() {
    setPopoverVisibility(!popoverVisibility);
  }

  return (
    <Card
      bordered={false}
      style={{ borderRadius: "20px", height: "100%", minWidth: "300px" }}
    >
      {loading ? (
        <Row
          align="middle"
          justify="center"
          style={{ height: "250px", minWidth: "275px" }}
        >
          <Col style={{ display: "flex", flexDirection: "column" }}>
            <Spin />
            <Text>Carregando...</Text>
          </Col>
        </Row>
      ) : (
        <>
          <CockpitsHeader
            title={
              <Col span={18}>
                <Title level={4} style={{ fontWeight: 400, margin: "0px" }}>
                  Sem abrir tickets
                </Title>
              </Col>
            }
            isFavorite={isFavorite}
            updateFavorite={updateFavorite}
            colorPicker={
              <Popover
                placement="left"
                title={"Personalização de cor"}
                content={() => {
                  return (
                    <ColorPicker
                      changeColors={changeColors}
                      options={[1]}
                      state={colors}
                    />
                  );
                }}
              >
                <BgColorsOutlined className="star-icon" />
              </Popover>
            }
          />
          <Row>
            <Col>
              <MainBarChart
                data={data}
                showModalFunction={showModal}
                colors={colors}
                dataKey="clientes"
              />
            </Col>
          </Row>
          <Modal
            closable={false}
            footer={null}
            width={"85vw"}
            okButtonProps={{
              style: {
                display: "none",
                backgroundColor: "#43914F",
                color: "white",
                fontWeight: "bold",
                borderRadius: "5px",
              },
            }}
            open={modalVisible}
          >
            <UnopenedTickets
              closeModal={closeModal}
              tableData={tableData}
              contractTypes={props.contractType}
              popoverVisibility={popoverVisibility}
            />
          </Modal>
        </>
      )}
    </Card>
  );
}
