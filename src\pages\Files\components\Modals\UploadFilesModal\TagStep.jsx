import { Typo<PERSON>, Row, Col, Select, Tooltip } from "antd";
import { useSelector, shallowEqual } from "react-redux";
import {
  setAllTagsFilesState,
  setUploadFilesState,
  setUploadReadyFilesState,
} from "../../../../../store/actions/files-action";

import { useEffect } from "react";
import { dynamoGet } from "../../../../../service/apiDsmDynamo";

export const TagStep = () => {
  const { Option } = Select;
  const { Text } = Typography;
  const allTags = useSelector((state) => state.files.allTags, shallowEqual);
  const tags = useSelector((state) => state.files.tags, shallowEqual);

  useEffect(() => {
    async function getTags() {
      const tableTags = await dynamoGet(
        `${process.env.REACT_APP_STAGE}-files-tags`
      );
      setAllTagsFilesState(tableTags);
    }
    getTags();
  }, []);

  return (
    <>
      <Row style={{ paddingBottom: "8px" }}>
        <Text>
          Selecione as{" "}
          <strong>
            Tags
            <Tooltip title={"Campo obrigatório"}>
              <span style={{ color: "red" }}>*</span>
            </Tooltip>
          </strong>
        </Text>
      </Row>
      <Row align={"middle"} style={{ paddingBottom: "50px" }}>
        <Col span={24}>
          <Select
            onChange={(e) => {
              setUploadFilesState({ field: "tags", value: e });
              setUploadReadyFilesState(true);
            }}
            placeholder={"Selecione as tags..."}
            value={tags}
            style={{ width: "100%" }}
            mode={"multiple"}
          >
            {allTags?.map((tag, key) => (
              <Option value={tag.name} key={key}>
                {tag.name}
              </Option>
            ))}
          </Select>
        </Col>
      </Row>
    </>
  );
};
