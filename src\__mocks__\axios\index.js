const axiosMock = jest.genMockFromModule("axios");

let mockResponse = {};
let mockError = null;
// Mock the Axios request function
axiosMock.request.mockImplementation((config) => {
  return new Promise((resolve, reject) => {
    if (mockError) {
      reject(mockError);
    } else {
      resolve(mockResponse);
    }
  });
});

// Helper function to set the mock response
axiosMock.__setMockResponse = (response) => {
  mockResponse = response;
};

// Helper function to set the mock error
axiosMock.__setMockError = (error) => {
  mockError = error;
};

// Reset the mock response and error
axiosMock.__resetMock = () => {
  mockResponse = {};
  mockError = null;
};

module.exports = axiosMock;
