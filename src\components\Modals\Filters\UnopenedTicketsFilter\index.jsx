import { React, useEffect, useState } from "react";
import { Select, DatePicker, Button, Form, Row } from "antd";
import { CloseCircleOutlined } from "@ant-design/icons";
import { otrsGet } from "../../../../service/apiOtrs";

function UnopenedTickersFilter(props) {
  const [form] = Form.useForm();
  const { Option } = Select;
  const [contractType, setContractType] = useState([]);
  console.log(props);
  function onFilterFinish(values) {
    console.log(values);
    props.filter(values);
    props.handleVisibleChange();
  }

  function onFilterFailed() {}
  console.log(props);

  function getContractTypes() {
    setContractType(props.contractType);
  }

  function clear() {
    form.resetFields();
  }

  useEffect(() => {
    getContractTypes();
  }, [props.contractType]);

  return (
    <Form
      form={form}
      style={{ display: "flex", flexDirection: "column" }}
      name="basic"
      initialValues={{
        remember: true,
      }}
      onFinish={onFilterFinish}
      onFinishFailed={onFilterFailed}
      autoComplete="off"
    >
      <div style={{ display: "flex", justifyContent: "space-between" }}>
        <h2>Sem abrir tickets</h2>
        <Button type="text" onClick={() => props.handleVisibleChange()}>
        <CloseCircleOutlined />
        </Button>
      </div>
      <Form.Item name="contractType">
        <Select
          showSearch
          loading={contractType?.length === 0}
          placeholder="Tipo de contratos"
          optionFilterProp="children"
          filterOption={(input, option) =>
            option.props.children.toLowerCase().indexOf(input.toLowerCase()) >=
              0 ||
            option.props.value.toLowerCase().indexOf(input.toLowerCase()) >= 0
          }
        >
          {contractType?.map((value) => {
            return (
              <Option key={value.name} value={value.name}>
                {value.name}
              </Option>
            );
          })}
        </Select>
      </Form.Item>

      <Row justify="space-between">
        <Button type="default" onClick={clear}>
          Limpar
        </Button>
        <Button htmlType="submit" type="primary">
          Aplicar
        </Button>
      </Row>
    </Form>
  );
}

export default UnopenedTickersFilter;
