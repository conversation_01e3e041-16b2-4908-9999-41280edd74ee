import React, { useState } from "react";
import { Row, Col, Select, Typography } from "antd";
import { useEffect } from "react";
import { setTechnicalProposalState } from "../../../store/actions/technical-proposal-action";
import { useSelector, shallowEqual } from "react-redux";
import { dynamoGet } from "../../../service/apiDsmDynamo";
import Cookies from "js-cookie";

const { Title } = Typography;
const { Option } = Select;

export const TypeField = () => {
  const type = useSelector(
    (state) => state.technicalProposal.type,
    shallowEqual
  );

  const [types, setTypes] = useState([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    getTypes();
  }, []);

  async function getTypes() {
    try {
      setLoading(true);

      let response = await dynamoGet("hml-crm-custom-fields");
      let labels = [];

      if (response.length > 0) {
        const consumeTypes = response[0].deal.find(
          (deal) => deal.name === "Tipo de Consumo"
        );
        labels = consumeTypes.options.map((option) => option.label);
      }

      setTypes(labels);
      setLoading(false);
    } catch (error) {
      setLoading(false);
    }
  }

  return (
    <Col sm={24} md={24} lg={12}>
      <Row>
        <Title level={5} style={{ fontWeight: "400" }}>
          Tipo
        </Title>
      </Row>
      <Row>
        <Col span={23}>
          <Select
            id="type"
            loading={loading}
            showSearch
            placeholder="Tipo da proposta"
            optionFilterProp="children"
            value={type ? type : Cookies.get("type") ? type : null}
            filterOption={(input, option) =>
              option?.children?.toLowerCase().includes(input?.toLowerCase())
            }
            filterSort={(optionA, optionB) =>
              optionA.children
                .toLowerCase()
                .localeCompare(optionB.children.toLowerCase())
            }
            onSelect={(e) =>
              setTechnicalProposalState({ field: "type", value: e })
            }
            style={{ width: "100%" }}
          >
            {types.map((value, key) => (
              <Option key={key} value={value}>
                {value}
              </Option>
            ))}
          </Select>
        </Col>
      </Row>
    </Col>
  );
};
