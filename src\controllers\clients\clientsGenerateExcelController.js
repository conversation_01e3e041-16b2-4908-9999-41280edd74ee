import ExcelJS from "exceljs";
import { dynamoGet } from "../../service/apiDsmDynamo";
import { format, isValid } from "date-fns";

const headersCustomer = [
  { label: "DSM ID", key: "dsm_id" },
  { label: "Data de Criação", key: "created_at" },
  { label: "CRM do cliente", key: "crm_id" },
  { label: "ITSM do cliente", key: "itsm_id" },
  { label: "Nome do cliente", key: "name" },
  { label: "Nome fantasia", key: "fantasy_name" },
  { label: "CNPJ", key: "cnpj" },
];

const dataToExtractCustomer = (data) =>
  data.map((item) => {
    return {
      dsm_id: item.dsm_id,
      created_at: item.created_at,
      crm_id: item.identifications.crm_id,
      itsm_id: item.identifications.itsm_id,
      name: item.names?.name,
      fantasy_name: item.names?.fantasy_name,
      cnpj: item.cnpj,
    };
  });

const headersContacts = [
  { label: "DSM ID do cliente", key: "customerID" },
  { label: "Nome do cliente", key: "customer_name" },
  { label: "DSM ID do contato", key: "dsm_id" },
  { label: "CRM do contato", key: "crm_id" },
  { label: "ITSM do contato", key: "itsm_id" },
  { label: "Primeiro nome", key: "first_name" },
  { label: "Último nome", key: "last_name" },
  { label: "Telefone", key: "phone" },
  { label: "Email", key: "email" },
];

const dataToExtractContacts = (data) =>
  data.flatMap((item) => {
    if (Array.isArray(item.contacts)) {
      return item.contacts.map((contact) => {
        return {
          customerID: item.dsm_id,
          customer_name: item.names.name
            ? item.names.name
            : item.names.fantasy_name,
          dsm_id: contact.dsm_id,
          crm_id: contact.identifications.crm_id,
          itsm_id: contact.identifications.itsm_id,
          first_name: contact.names.first_name,
          last_name: contact.names.last_name,
          phone: contact.phone,
          email: contact.email,
        };
      });
    } else {
      return [];
    }
  });

const headersContracts = [
  { label: "DSM ID do cliente", key: "customerID" },
  { label: "Nome do cliente", key: "customer_name" },
  { label: "DSM ID do contrato", key: "dsm_id" },
  { label: "CRM do contrato", key: "crm_id" },
  { label: "ITSM do contrato", key: "itsm_id" },
  { label: "Nome do contrato", key: "contract_name" },
  { label: "Data início", key: "start_date" },
  { label: "Data fim", key: "end_date" },
  { label: "Valor do contrato", key: "value" },
  { label: "Valor da Calculadora AWS", key: "aws_value" },
  { label: "Escopo", key: "scope" },
  { label: "Tipo de Horas", key: "type_hours" },
  { label: "ID do Contrato", key: "contract_id" },
  { label: "Total de Horas", key: "total_hours" },
];

const dataToExtractContracts = async (data) => {
  const allContracts = await dynamoGet(
    `${process.env.REACT_APP_STAGE}-contracts`
  );
  const customerContracts = data.map((item) => {
    const currentCustomerContracts = allContracts.filter(
      (c) => c.customer_id === item.id
    );
    return currentCustomerContracts.map((contract) => {
      return {
        customerID: item.dsm_id,
        customer_name: item.names.name
          ? item.names.name
          : item.names.fantasy_name,
        dsm_id: contract.dsm_id,
        crm_id: contract.identifications.crm_id,
        itsm_id: contract.identifications.itsm_id,
        contract_name: contract.name,
        start_date: contract.target_dates.expected_start_date,
        end_date: contract.target_dates.expected_close_date,
        value: contract.values?.value,
        aws_value: contract.values?.aws_value,
        scope: contract.scope,
        type_hours: contract.type_hours,
        contract_id: contract.id,
        total_hours: contract.total_hours,
      };
    });
  });
  return customerContracts.flat();
};

const headersAccounts = [
  { label: "DSM ID do cliente", key: "customerID" },
  { label: "Nome do cliente", key: "customer_name" },
  { label: "Perfil", key: "profile" },
  { label: "Conta", key: "account_id" },
];

const dataToExtractAccounts = (data) =>
  data.flatMap((item) => {
    if (Array.isArray(item.accounts)) {
      return item.accounts.map((account) => {
        return {
          customerID: item.dsm_id,
          customer_name: item.names.name
            ? item.names.name
            : item.names.fantasy_name,
          profile: account.profile,
          account_id: account.account_id,
        };
      });
    } else {
      return [];
    }
  });

const headersAddress = [
  { label: "DSM ID do cliente", key: "customerID" },
  { label: "Nome do cliente", key: "customer_name" },
  { label: "Tipo de logradouro", key: "tipo_logradouro" },
  { label: "Logradouro", key: "logradouro" },
  { label: "Número", key: "numero" },
  { label: "Complemento", key: "complemento" },
  { label: "Bairro", key: "bairro" },
  { label: "Cep", key: "cep" },
  { label: "Cidade", key: "cidade" },
  { label: "País", key: "pais" },
];

const dataToExtractAddress = (data, customersInfo) => {
  const address = data.map((customer) => {
    const customerInfo = customersInfo?.find((c) => c.id === customer.id);
    if (customerInfo.address) {
      return {
        customerID: customer.dsm_id,
        customer_name: customer.names.name
          ? customer.names.name
          : customer.names.fantasy_name,
        tipo_logradouro: customerInfo.address.tipo_logradouro,
        logradouro: customerInfo.address.logradouro,
        numero: customerInfo.address.numero,
        complemento: customerInfo.address.complemento,
        bairro: customerInfo.address.bairro,
        cep: customerInfo.address.cep,
        cidade: customerInfo.address.cidade,
        pais: customerInfo.address.pais,
      };
    } else {
      return [];
    }
  });
  return address.flat();
};

const headersMembers = [
  { label: "DSM ID do cliente", key: "customerID" },
  { label: "Nome do cliente", key: "customer_name" },
  { label: "Nome do sócio", key: "name" },
  { label: "Data de Entrada", key: "data_entrada" },
  { label: "Qualificação do Sócio", key: "qualificacao_socio" },
  { label: "Faixa Etária", key: "faixa_etaria" },
  { label: "Tipo", key: "tipo" },
];

const dataToExtractMembers = (data, customersInfo) => {
  const members = data.map((customer) => {
    const customerInfo = customersInfo?.find((c) => c.id === customer.id);
    if (Array.isArray(customerInfo?.members)) {
      return customerInfo.members.map((member) => {
        return {
          customerID: customer.dsm_id,
          customer_name: customer.names.name
            ? customer.names.name
            : customer.names.fantasy_name,
          name: member.nome,
          data_entrada: member.data_entrada,
          qualificacao_socio: member.qualificacao_socio.descricao,
          faixa_etaria: member.faixa_etaria,
          tipo: member.tipo,
        };
      });
    } else {
      return [];
    }
  });
  return members.flat();
};

export const generateExcel = async (data, selectedFields) => {
  const workbook = new ExcelJS.Workbook();
  const customersInfo =
    selectedFields.some((f) => f.subItems.includes("Endereço")) ||
    selectedFields.some((f) => f.subItems.includes("Sócios"))
      ? await dynamoGet(`${process.env.REACT_APP_STAGE}-customers`)
      : [];

  const abas = [
    {
      name: "Clientes",
      headers: headersCustomer,
      dataToExtract: dataToExtractCustomer(data),
    },
    {
      name: "Contatos",
      headers: headersContacts,
      dataToExtract:
        selectedFields.some((f) => f.label === "Contatos") &&
        dataToExtractContacts(data),
    },
    {
      name: "Contratos",
      headers: headersContracts,
      dataToExtract:
        selectedFields.some((f) => f.label === "Contratos") &&
        (await dataToExtractContracts(data)),
    },
    {
      name: "Contas",
      headers: headersAccounts,
      dataToExtract:
        selectedFields.some((f) => f.label === "Contas") &&
        dataToExtractAccounts(data),
    },
    {
      name: "Endereço",
      headers: headersAddress,
      dataToExtract:
        selectedFields.some((f) => f.subItems.includes("Endereço")) &&
        dataToExtractAddress(data, customersInfo),
    },
    {
      name: "Sócios",
      headers: headersMembers,
      dataToExtract:
        selectedFields.some((f) => f.subItems.includes("Sócios")) &&
        dataToExtractMembers(data, customersInfo),
    },
  ];

  abas.forEach((aba) => {
    if (
      selectedFields.some((f) => f.label === aba.name) ||
      selectedFields.some((f) => f.subItems.includes(aba.name))
    ) {
      const worksheet = workbook.addWorksheet(aba.name);

      const headerRow = worksheet.addRow([]);
      let columnIndex = 1;
      aba.headers.forEach((header) => {
        if (
          selectedFields.some((f) => f.subItems.includes(header.label)) ||
          header.key === "customer_name" ||
          header.key === "customerID" ||
          aba.name === "Endereço" ||
          aba.name === "Sócios"
        ) {
          headerRow.getCell(columnIndex).value = header.label;
          columnIndex += 1;
        }
      });

      aba.dataToExtract?.forEach((item) => {
        const row = worksheet.addRow([]);
        let columnIndex = 1;
        aba.headers.forEach((header) => {
          if (
            selectedFields.some((f) => f.subItems.includes(header.label)) ||
            header.key === "customer_name" ||
            header.key === "customerID" ||
            aba.name === "Endereço" ||
            aba.name === "Sócios"
          ) {
            const colData =
              header.label.toLowerCase().includes("data") &&
              aba.name !== "Sócios"
                ? formatDateFields(item[header.key], aba.name)
                : item[header.key];
            row.getCell(columnIndex).value = colData;
            columnIndex += 1;
          }
        });
      });
    }
  });

  const buffer = await workbook.xlsx.writeBuffer();
  const blob = new Blob([buffer], {
    type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
  });

  return blob;
};

const formatDateFields = (date) => {
  const checkValidDate = isValid(new Date(date));
  if (checkValidDate) {
    const formattedData = format(new Date(date), "dd/MM/yyyy");
    return formattedData;
  } else {
    return "";
  }
};

export const addFieldInSelectedFields = (field, fields, selectedFields) => {
  let updatedFields = [...selectedFields];

  if (field.includes("Todos")) {
    const parts = field.split("-");
    const label = parts[1];
    updatedFields = updatedFields.filter((item) => item.label !== label);
    const findFieldByLabel = fields.find((f) => f.label === label);
    findFieldByLabel.subItems.push(field);
    updatedFields.push(findFieldByLabel);
  } else {
    const findFieldBySubItem = fields.find((f) => f.subItems?.includes(field));

    const isLabelInSelectedField = selectedFields.find(
      (f) => f.label == findFieldBySubItem.label
    );
    if (isLabelInSelectedField) {
      isLabelInSelectedField.subItems.push(field);

      updatedFields = [...selectedFields];
    } else {
      updatedFields = [
        ...selectedFields,
        { label: findFieldBySubItem.label, subItems: [field] },
      ];
    }
  }
  return updatedFields;
};

export const removeFieldInSelectedFields = (field, selectedFields) => {
  let updatedFields = [...selectedFields];

  if (field.includes("Todos")) {
    const parts = field.split("-");
    const label = parts[1];

    updatedFields = updatedFields.filter((item) => item.label !== label);
    return updatedFields;
  } else {
    const findFieldBySubItem = updatedFields.find((f) =>
      f.subItems?.includes(field)
    );

    if (findFieldBySubItem) {
      findFieldBySubItem.subItems = findFieldBySubItem.subItems.filter(
        (subItem) => subItem !== field
      );

      if (findFieldBySubItem.subItems.length === 0) {
        updatedFields = updatedFields.filter(
          (item) => item.label !== findFieldBySubItem.label
        );
      }
    }
  }

  return updatedFields;
};

export const downloadExcel = (blob, fileName) => {
  const url = URL.createObjectURL(blob);
  const a = document.createElement("a");
  a.href = url;
  a.download = fileName;
  a.click();

  URL.revokeObjectURL(url);
};
