import axios from "axios";

const transport = axios.create({
  baseURL: "https://publica.cnpj.ws/cnpj/",
});

export const formatCNPJ = (cnpj) => {
  if (!cnpj || cnpj === "" || cnpj === null)
    throw new Error("CNPJ is not defined");

  console.log({ cnpj });
  return cnpj.toString().replace(/\D/g, "");
};

export async function getCNPJ(cnpj) {
  try {
    const { data } = await transport.get(formatCNPJ(cnpj));
    return data;
  } catch (error) {
    console.log(error);
    new Error(error);
    return error.response;
  }
}
