import React, { useState } from "react";
import { Row, Col, Typography, Select, Form } from "antd";
import { useSelector, shallowEqual } from "react-redux";
import { dynamoGet } from "../../../service/apiDsmDynamo";
import { setTechnicalProposalState } from "../../../store/actions/technical-proposal-action";
import { useEffect } from "react";

const { Title } = Typography;
const { Option } = Select;

export const BusField = () => {
    const bus = useSelector(state => state.technicalProposal.bus, shallowEqual)

    const [busList, setBusList] = useState([])
    const [loading, setLoading] = useState(false)

    useEffect(() => {
        getBus()
    }, [])

    async function getBus() {
        try {
            setLoading(true)

            const response = await dynamoGet(`${process.env.REACT_APP_STAGE}-wallets`);
            const buNames = response.map(bu => bu.name)
            setBusList(buNames);

            setLoading(false)
        } catch (error) {
            setLoading(false)
        }
    }

    return (
        <Col sm={24} md={12} lg={6}>
            <Row>
                <Title level={5} style={{ fontWeight: "400" }}>
                    BUs
                </Title>
            </Row>
            <Row>
                <Col span={24}>
                        <Select
                            id="bus"
                            loading={loading}
                            mode="multiple"
                            value={bus}
                            showSearch
                            placeholder={"Nome da unidade de negócio"}
                            optionFilterProp="children"
                            filterOption={(input, option) =>
                                option?.children
                                    ?.toLowerCase()
                                    .includes(input?.toLowerCase())
                            }
                            filterSort={(optionA, optionB) =>
                                optionA?.children
                                    ?.toLowerCase()
                                    .localeCompare(optionB.children?.toLowerCase())
                            }
                            onChange={(e) => setTechnicalProposalState({ field: 'bus', value: e })}
                            style={{ width: '100%' }}
                        >

                            {
                                busList.map((value, key) =>
                                    <Option value={value} key={key}>{value}</Option>
                                )
                            }
                            

                        </Select>
                </Col>
                {/* <Col span={4}>
            {selectedBU ? (
              <AddBU busParent={bus} setBus={(bus) => setBus(bus)} />
            ) : (
              <AddBU busParent={bus} setBus={(bus) => setBus(bus)} />
            )}
          </Col> */}
            </Row>
        </Col>
    )
}