import { Row, Col, Form, Card, Typography, InputNumber } from "antd";
import { useContext } from "react";
import { formatHourName } from "../../controllers/Proposals/formatHourName";
import { ProposalContext } from "../../contexts/totalValuesProposals";
import { TotalValuesProposalType } from "../../contexts/reducers/totalValuesProposal";

const sumTotalOfHours = async (array) => {
  let result = array.reduce((acc, curr) => {
    return acc + curr.totalValue;
  }, 0);
  result = Number(result);
  if (result < 0) {
    result = 0;
  }
  return result.toFixed(2);
};

async function updateProperty(
  prop,
  value,
  totalValuesState,
  hourClass,
  month,
  formName
) {
  let newObj = totalValuesState;
  let newObjTotalValues = newObj.totalValues;

  let newFilteredTotalValues = totalValuesState?.totalValues?.find(
    (v) => v.hourClass === hourClass
  );

  let newFilteredTotalValuesByMonth = newFilteredTotalValues?.values?.find(
    (v) => v.month === month
  );

  newFilteredTotalValuesByMonth?.values?.map((v) => {
    if (v.formName === formName) {
      v["burst"] = Number(value);

      let totalHours = v["hours"] + Number(value);
      if (totalHours < 0) {
        totalHours = 0;
      }

      v["totalHours"] = totalHours;

      const newHourValueWithDiscount =
        v["hourValue"] - (v["hourValue"] * v["discountValue"]) / 100;

      if (v["discountUnit"] === "percentage") {
        v["totalValue"] = v["totalHours"] * newHourValueWithDiscount;
      } else {
        v["totalValue"] =
          v["totalHours"] * (v["hourValue"] - v["discountValue"]);
      }
    }
  });

  for (let i = 0; i < newObjTotalValues.length; i++) {
    if (newObjTotalValues[i].hourClass === hourClass) {
      newObjTotalValues[i] = newFilteredTotalValues;
    }
  }
  return newObjTotalValues;
}

export const HourBurstCard = ({ hourClass, month }) => {
  const { Text } = Typography;
  const { proposalState, setProposalState } = useContext(ProposalContext);

  const filteredTotalValues = proposalState?.totalValues?.find(
    (v) => v.hourClass === hourClass
  );

  const filteredTotalValuesByMonth = filteredTotalValues?.values?.find(
    (v) => v.month === month
  );

  const handleInputValue = async (value, hourType) => {
    const newState = await updateProperty(
      "burst",
      value,
      proposalState,
      hourClass,
      month,
      hourType.formName
    );
    setProposalState({
      type: TotalValuesProposalType.SET_TOTAL_VALUES,
      value: newState,
    });

    let newSpread = [...proposalState?.spreadSetup];

    const filteredTotalValues = proposalState?.totalValues?.filter(
      (v) => v.hourClass === "hourValue"
    )[0].values;

    let allSetupHours = filteredTotalValues
      .filter((v) => v.month === month)[0]
      .values.filter((i) => i.type === "SETUP");

    let allSustHours = filteredTotalValues
      .filter((v) => v.month === month)[0]
      .values.filter((i) => i.type === "SUST");

    const sumSetupValue = await sumTotalOfHours(allSetupHours);
    const sumSustValue = await sumTotalOfHours(allSustHours);

    if (proposalState?.spreadSetup.find((s) => s.month === month).value > 0) {
      newSpread.forEach((s) => {
        if (s.month === month) {
          s.value = sumSustValue + sumSetupValue / Number(month);
        }
      });

      setProposalState({
        type: TotalValuesProposalType.SET_SPREAD_SETUP,
        value: newSpread,
      });
    }
  };

  return (
    <Col xxl={8} xl={24} sm={24}>
      <Form.Item style={{ textAlign: "center", marginTop: "1rem" }}>
        <Text>Burst De Horas {`(${month} meses)`}</Text>
        <Card
          style={{
            width: "100%",
            backgroundColor: "#D9D9D9",
            borderRadius: "5px",
            marginTop: "1rem",
          }}
        >
          <Row justify="space-between" gutter={[4, 16]}>
            {filteredTotalValuesByMonth?.values?.map((t) => {
              return (
                <Col span={12} style={{ textAlign: "center" }}>
                  <Row justify={"center"}>
                    <Text>{formatHourName(t.formName)}</Text>
                  </Row>
                  <Row justify={"center"}>
                    <InputNumber
                      min={t.type === "SUST" ? undefined : 0}
                      defaultValue={t.burst}
                      style={{ textAlign: "center", width: "90%" }}
                      onChange={(e) => handleInputValue(e, t)}
                    />
                  </Row>
                </Col>
              );
            })}
          </Row>
        </Card>
      </Form.Item>
    </Col>
  );
};
