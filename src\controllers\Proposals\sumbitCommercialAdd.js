import { message } from "antd";
import { generatePDF } from "./generatePDF";
import { dynamoPut, dynamoGet } from "../../service/apiDsmDynamo";
import { v4 } from "uuid";
import { verifyEmptyCostsFields } from "../../utils/verifyEmptyCostsFields";
import { verifySustMinHours } from "../../utils/verifySustMinHours";
import { logNewAuditAction } from "../audit/logNewAuditAction";

export const submitCommercialController = async (
  add_proposal,
  draftItems,
  currentContacts,
  services,
  proposalState,
  customerBrand,
  mainSearchId,
  user
) => {
  const formData = add_proposal.getFieldsValue();

  const awsCostsArr = formData["main_aws_investments"];
  const addCosts = formData["aditional_costs"];

  const emptyRequiredFields = await verifyEmptyCostsFields(
    awsCostsArr,
    addCosts,
    formData
  );

  if (emptyRequiredFields.length > 0) {
    return false;
  }

  const fileNames = proposalState.fileNames;
  const architectureFileNames = proposalState.architectureFileNames;
  const scenarioFileNames = proposalState.scenarioFileNames;

  const res = await dynamoGet(`${process.env.REACT_APP_STAGE}-cost-management`);

  const paymentSelected = res[0].paymentMethods.find(
    (p) => p.id === proposalState.paymentMethod
  );

  const customerCNPJ = proposalState.customer?.cnpj
    ? proposalState.customer?.cnpj
    : currentContacts?.cnpj
    ? currentContacts?.cnpj
    : "";

  const customerNamesName = proposalState?.customer?.names?.name;

  const customerNamesFantasyName = proposalState?.customer?.names?.fantasy_name;

  const customerData = {
    names: {
      name: customerNamesName,
      fantasy_name: customerNamesFantasyName,
    },
    cnpj: customerCNPJ,
    contacts: currentContacts?.contacts,
    mainContact: formData?.project_contacts,
  };

  const totalValues = proposalState.totalValues.find(
    (v) => v.hourClass === "hourValue"
  ).values;

  const consideredMonth =
    proposalState.month === "" || proposalState.month === []
      ? ["12"]
      : proposalState.month;

  const sustMinHoursVerified = await verifySustMinHours(
    totalValues,
    consideredMonth
  );

  // if (!sustMinHoursVerified) {
  //   message.error("Verifique o valor das horas recorrentes");
  //   return false;
  // }

  try {
    const proposalData = {
      searchId: mainSearchId ? mainSearchId : v4(),
      name: formData?.project_name,
      type: formData?.project_type,
      status: formData?.project_status
        ? formData?.project_status === "revisão técnica"
          ? "revisão técnica"
          : "concluída"
        : "concluída",
      commercialStatus: proposalState.status,
      active: true,
      mainOportunity: proposalState.selectedOportunity,
      opportunity: proposalState.oportunities,
      architects: formData?.project_architects,
      customer: customerData,
      bus: formData?.project_bu,
      services: services,
      specialFields: [
        { name: "Desafios", value: draftItems.challenges },
        { name: "Cenário apresentado", value: draftItems.scenarios },
        { name: "Premissas", value: draftItems.premisses },
        {
          name: "Pontos não contemplados",
          value: draftItems.extra_points,
        },
        { name: "Arquitetura", value: draftItems.architecture },
        {
          name: "Fatores influenciadores",
          value: draftItems.main_factors,
        },
        {
          name: "Custos adicionais",
          value: formData.aditional_costs,
        },
        {
          name: "Resultados esperados",
          value: draftItems.expected_results,
        },
        { name: "Observações", value: draftItems.observations },
        { name: "Notas Internas", value: draftItems.internal_notes },
      ],
      awsCosts: formData?.main_aws_investments,
      totalValues: proposalState.totalValues,
      monthSelected: proposalState.month ? proposalState.month : ["12"],
      paymentMethod: proposalState.paymentMethod,
      paymentSelected: paymentSelected,
      fileNames: [...fileNames],
      architectureFileNames: [...architectureFileNames],
      scenarioFileNames: [...scenarioFileNames],
      spreadSetup: proposalState.spreadSetup,
      ticketData: {},
      pipedriveCreatedAt: new Date(),
      s3FolderNotes: `${process.env.REACT_APP_CRM_API}/proposal/${proposalState.selectedOportunity}`,
    };

    let customerName = proposalState?.customer?.names?.name
      ? proposalState?.customer?.names?.name
      : "";

    await dynamoPut(
      `${process.env.REACT_APP_STAGE}-proposals`,
      v4(),
      proposalData
    );

    await generatePDF(
      proposalData,
      customerName,
      customerBrand,
      customerData,
      user
    );

    const username = localStorage.getItem("@dsm/username");
    const title = "Cadastro de Proposta Comercial";
    const description = `${username} cadastrou a proposta: ${proposalData.name}`;
    logNewAuditAction(username, title, description);

    message.success("Proposta adicionada com sucesso!");

    return true;
  } catch (err) {
    return false;
  }
};
