function calculateEstimatesFromService(service) {
  let estimatesService = [
    { name: "setup8x5", value: 0 },
    { name: "setup24x7", value: 0 },
    { name: "sust8x5", value: 0 },
    { name: "sust24x7", value: 0 },
    { name: "dbasec8x5", value: 0 },
    { name: "dbasec24x7", value: 0 },
  ];
  service.tasks.map((task) => {
    estimatesService[0].value += task.estimates["8x5setup"];
    estimatesService[1].value += task.estimates["24x7setup"];
    estimatesService[2].value += task.estimates["8x5sust"];
    estimatesService[3].value += task.estimates["24x7sust"];
    estimatesService[4].value += task.estimates["8x5dbasec"];
    estimatesService[5].value += task.estimates["24x7dbasec"];
  });

  return estimatesService;
}

function calculateEstimatesFromManagementService(service, allEstimates) {
  let estimatesService = [
    { name: "setup8x5", value: 0 },
    { name: "setup24x7", value: 0 },
    { name: "sust8x5", value: 0 },
    { name: "sust24x7", value: 0 },
  ];
  service.tasks.map((task) => {
    if (task.taskTime.type !== "percentage") {
      estimatesService[0].value += task.estimates["8x5setup"];
      estimatesService[1].value += task.estimates["24x7setup"];
    } else {
      estimatesService[0].value +=
        (allEstimates.find((e) => e.name === estimatesService[0].name).value *
          task.taskTime.time) /
        100;
      estimatesService[0].value +=
        (allEstimates.find((e) => e.name === estimatesService[1].name).value *
          task.taskTime.time) /
        100;
    }
  });

  return estimatesService;
}

export async function calculateManagementServices(services) {
  let newServices = [];
  let allEstimatesSum = [
    { name: "setup8x5", value: 0 },
    { name: "setup24x7", value: 0 },
    { name: "sust8x5", value: 0 },
    { name: "sust24x7", value: 0 },
    { name: "dbasec8x5", value: 0 },
    { name: "dbasec24x7", value: 0 },
  ];

  for (let i = 0; i < services.length; i++) {
    const estimates = calculateEstimatesFromService(services[i]);
    if (!services[i].management) {
      allEstimatesSum.forEach((e, i) => {
        e.value += estimates[i].value;
      });
    }
  }

  for (let i = 0; i < services.length; i++) {
    const newServiceStr = JSON.stringify(services[i]);
    let newService = JSON.parse(newServiceStr);
    if (services[i].management) {
      let estimateManagement = calculateEstimatesFromManagementService(
        services[i],
        allEstimatesSum
      );
      estimateManagement[0].value = Math.ceil(estimateManagement[0].value);

      newService.tasks.forEach((task) => {
        if (task.taskTime.type === "percentage")
          task.estimates["8x5setup"] = estimateManagement[0].value;
      });
    }
    newServices.push(newService);
  }
  return newServices;
}
