import React, { useEffect, useMemo, useState } from "react";
import {
  Layout,
  Card,
  Row,
  Col,
  Select,
  Typography,
  Popconfirm,
  Table,
  Button,
  Space,
  Form,
  Tag,
  message,
} from "antd";
import { CheckCircleOutlined } from "@ant-design/icons";
import { HeaderMenu } from "../../components/HeaderMenu";
import { SideMenu } from "../../components/SideMenu";
import { AddPaymentMethod } from "../../components/Modals/CostManagment/AddPaymentMethod";
import { EditPaymentMethod } from "../../components/Modals/CostManagment/EditPaymentMethod";
import {
  dynamoGet,
  dynamoGetById,
  dynamoPut,
} from "../../service/apiDsmDynamo";
import { HourValueTypeCard } from "../../components/CostManagement/hourValueTypeCard";
import { Counter } from "../../components/Counter";
import { logNewAuditAction } from "../../controllers/audit/logNewAuditAction";
import useSWR from "swr";

async function getHourValue(data, month) {
  const res = data.find((d) => d.month === month);
  return res.values;
}

export const CostManagement = () => {
  const [cost_management_form_hour_value1] = Form.useForm();

  const [collapsed, setCollapsed] = useState(false);
  const [loading, setLoading] = useState(false);
  const [objId, setObjId] = useState("");
  const [paymentMethods, setPaymentMethods] = useState([]);
  const [daredeHourValue, setDaredeHourValue] = useState([]);
  const [daredeAdditionalHours, setDaredeAdditionalHours] = useState([]);
  const [actionsState, setActionsState] = useState("ativas");
  const [paymentEdit, setPaymentEdit] = useState({});
  const [hourValues, setHourValues] = useState([]);
  const [additionalHourValues, setAdditionalHourValues] = useState([]);

  const { Text } = Typography;
  const { Content } = Layout;
  const { Option } = Select;

  const { data: permissions } = useSWR("cost_management", async () => {
    try {
      let data = await dynamoGetById(
        `${process.env.REACT_APP_STAGE}-permissions`,
        localStorage.getItem("@dsm/permission")
      );
      return [
        ...data.permissions.find((x) => x.page === "Gerenciamento de Custos")
          .actions,
      ];
    } catch (error) {
      console.log('⚠️ CostManagement: Erro ao buscar permissões, usando fallback:', error.message);
      // Fallback com permissões completas do Cost Management
      return [
        { code: "view_cost_data" },
        { code: "view_cost_charts" },
        { code: "view_cost_reports" },
        { code: "export_cost_data" },
        { code: "manage_cost_settings" },
        // ✅ Permissões específicas para colunas da tabela
        { code: "view_name" },
        { code: "view_description" },
        { code: "view_edit" },
        { code: "view_actions" },
        { code: "add_payment_method" },
        { code: "edit_payment_method" },
        { code: "delete_payment_method" }
      ];
    }
  });

  const permissionCodes =
    permissions?.map((permission) => permission.code) || [];

  useEffect(() => {
    let isMounted = true;

    const fetchCostManagementData = async () => {
      try {
        if (!isMounted) return;

        setLoading(true);
        const data = await dynamoGet(`${process.env.REACT_APP_STAGE}-cost-management`);

        if (!isMounted) return;

        const res = data.find(
          (d) => d.id === `${process.env.REACT_APP_COST_MANAGEMENT_OBJ_ID}`
        );

        if (!res) {
          console.warn('Cost management data not found');
          setLoading(false);
          return;
        }

        setObjId(res?.id);
        setPaymentMethods(res?.paymentMethods);
        setDaredeHourValue(res?.daredeHourValue);
        setDaredeAdditionalHours(res?.daredeAdditionalHours);

        // Fetch hour values in parallel
        const [
          tempHourValue12,
          tempHourValue24,
          tempHourValue36,
          tempAdditionalHourValue12,
          tempAdditionalHourValue24,
          tempAdditionalHourValue36
        ] = await Promise.all([
          getHourValue(res?.daredeHourValue, "12"),
          getHourValue(res?.daredeHourValue, "24"),
          getHourValue(res?.daredeHourValue, "36"),
          getHourValue(res?.daredeAdditionalHours, "12"),
          getHourValue(res?.daredeAdditionalHours, "24"),
          getHourValue(res?.daredeAdditionalHours, "36")
        ]);

        if (!isMounted) return;

        setHourValues([tempHourValue12, tempHourValue24, tempHourValue36]);
        setAdditionalHourValues([
          tempAdditionalHourValue12,
          tempAdditionalHourValue24,
          tempAdditionalHourValue36,
        ]);

        setLoading(false);
      } catch (err) {
        if (isMounted) {
          console.error("Error on getting paymentMethods from dynamodb: ", err);
          setLoading(false);
        }
      }
    };

    fetchCostManagementData();

    return () => {
      isMounted = false;
    };
  }, []);

  useEffect(() => {
    localStorage.setItem("paymentMethods", JSON.stringify(paymentMethods));
  }, [paymentMethods]);

  useEffect(() => {
    setPaymentMethods(
      JSON.parse(localStorage.getItem("paymentMethodsUpdated"))
    );
  }, [paymentEdit]);

  const handleChangeHourValue = (e, hourType, index, title) => {
    const prev = daredeHourValue;

    if (prev.length > 0) {
      for (let i = 0; i < prev.length; i++) {
        if (prev[i].month === title) {
          let value = prev[i].values.find((v) => v.name === hourType);
          value.value = Number(e);
        }
      }
    }

    setDaredeHourValue(prev);
  };

  const handleChangeAdditionalHours = (e, hourType, index, title) => {
    const prev = daredeAdditionalHours;

    if (prev.length > 0) {
      for (let i = 0; i < prev.length; i++) {
        if (prev[i].month === title) {
          let value = prev[i].values.find((v) => v.name === hourType);
          value.value = Number(e);
        }
      }
    }

    setDaredeAdditionalHours(prev);
  };

  const handleSubmit = async () => {
    try {
      await dynamoPut(`${process.env.REACT_APP_STAGE}-cost-management`, objId, {
        daredeHourValue: daredeHourValue,
        daredeAdditionalHours: daredeAdditionalHours,
        paymentMethods: paymentMethods,
      });

      const username = localStorage.getItem("@dsm/username");
      const title = "Edição Custos de Horas";
      const description = `${username} realizou uma alteração nos valores das horas Darede`;
      logNewAuditAction(username, title, description);

      message.success("Gerenciamento de Custos alterado com sucesso!");
    } catch (err) {
      console.log("err: ", err);
      message.error("Erro ao alterar Gerenciamento de Custos!");
    }
  };

  const columns = [
    {
      code: "view_name",
      title: "Nome",
      dataIndex: "name",
      key: "Nome",
      width: "30%",
      sorter: (a, b) => a?.name?.localeCompare(b?.name),
    },
    {
      code: "view_description",
      title: "Descrição",
      dataIndex: "description",
      key: "description",
      width: "40%",
      render: (id, item) => {
        if (item.description.length < 100) {
          return item.description;
        } else {
          return <div>{item.description.substring(0, 100)} ...</div>;
        }
      },
    },
    {
      code: "view_edit",
      title: "Editar",
      dataIndex: "edit",
      key: "edit",
      width: "10%",
      align: "center",
      render: (id, item) => {
        return (
          <EditPaymentMethod
            paymentEditParent={item}
            setPaymentEdit={(p) => setPaymentEdit(p)}
          />
        );
      },
    },
    {
      code: "view_actions",
      title: "Ações",
      dataIndex: "active",
      key: "active",
      align: "center",
      render: (id, item) => {
        return (
          <Popconfirm
            okText="Sim"
            cancelText="Não"
            title={
              item.active === true
                ? "Deseja desativar esta forma de pagamento?"
                : "Deseja ativar esta forma de pagamento?"
            }
            placement="bottom"
            onConfirm={() => {
              setLoading(true);
              let prev = paymentMethods;

              prev.map((p) => {
                if (p.id === item.id) {
                  p.active === true ? (p.active = false) : (p.active = true);
                }
              });

              try {
                dynamoPut(
                  `${process.env.REACT_APP_STAGE}-cost-management`,
                  objId,
                  {
                    paymentMethods: prev,
                  }
                ).then(async () => {
                  const prefixTitle =
                    item.active === false ? "Desativação " : "Ativação ";
                  const prefixDescription =
                    item.active === false ? "desativou" : "ativou";
                  const username = localStorage.getItem("@dsm/username");
                  const title = prefixTitle + "de Forma de Pagamento";
                  const description = `${username} ${prefixDescription} a forma de pagamento: ${item.name}`;
                  logNewAuditAction(username, title, description);

                  dynamoGet(
                    `${process.env.REACT_APP_STAGE}-cost-management`
                  ).then((data) => {
                    setPaymentMethods(data[0].paymentMethods);
                    setLoading(false);
                  });
                });
              } catch (err) {
                setLoading(false);
                console.log("err: ", err);
                message.error("Erro ao atualizar método de pagamento!");
              }
            }}
          >
            <Button danger type="text">
              <Tag color={item.active === true ? "red" : "green"}>
                {item.active === true ? "Desativar" : "Ativar"}
              </Tag>
            </Button>
          </Popconfirm>
        );
      },
    },
  ];

  const tableData = useMemo(() => {
    let filteredData = paymentMethods?.filter((e) => {
      switch (actionsState) {
        case "ativas":
          return e.active === true;
        case "inativas":
          return e.active === false;
        default:
          return e;
      }
    });

    return filteredData;
  }, [paymentMethods, actionsState]);

  return (
    <Layout style={{ minHeight: "100vh" }}>
      <HeaderMenu collapsed={collapsed} setCollapsed={setCollapsed} />
      <Layout>
        <SideMenu collapsed={collapsed} />
        <Content style={{ padding: "2em" }}>
          <Card
            style={{
              boxShadow: "0 0 10px rgba(0,0,0,0.1)",
              borderRadius: "1rem",
            }}
          >
            <Row
              style={{
                background: "#ECECEC",
                padding: "25px 0",
                marginBottom: "1rem",
                borderRadius: ".7rem",
              }}
              justify="space-evenly"
              gutter={[16, 16]}
            >
              <HourValueTypeCard
                title="Valor hora - 12 Meses"
                formRef={cost_management_form_hour_value1}
                initialValues={hourValues[0]}
                hourValueData={hourValues[0]}
                hourTypeTitle="12"
                mainKey={0}
                handleChangeHourValue={handleChangeHourValue}
              />
              <HourValueTypeCard
                title="Valor hora - adicional - 12 Meses"
                formRef={cost_management_form_hour_value1}
                initialValues={additionalHourValues[0]}
                hourValueData={additionalHourValues[0]}
                hourTypeTitle="12"
                mainKey={0}
                handleChangeHourValue={handleChangeAdditionalHours}
              />
              <HourValueTypeCard
                title="Valor hora - 24 Meses"
                formRef={cost_management_form_hour_value1}
                initialValues={hourValues[1]}
                hourValueData={hourValues[1]}
                hourTypeTitle="24"
                mainKey={1}
                handleChangeHourValue={handleChangeHourValue}
              />
              <HourValueTypeCard
                title="Valor hora - adicional - 24 Meses"
                formRef={cost_management_form_hour_value1}
                initialValues={additionalHourValues[1]}
                hourValueData={additionalHourValues[1]}
                hourTypeTitle="24"
                mainKey={1}
                handleChangeHourValue={handleChangeAdditionalHours}
              />
              <HourValueTypeCard
                title="Valor hora - 36 Meses"
                formRef={cost_management_form_hour_value1}
                initialValues={hourValues[2]}
                hourValueData={hourValues[2]}
                hourTypeTitle="36"
                mainKey={2}
                handleChangeHourValue={handleChangeHourValue}
              />
              <HourValueTypeCard
                title="Valor hora - adicional - 36 Meses"
                formRef={cost_management_form_hour_value1}
                initialValues={additionalHourValues[2]}
                hourValueData={additionalHourValues[2]}
                hourTypeTitle="36"
                mainKey={2}
                handleChangeHourValue={handleChangeAdditionalHours}
              />
              <Col span={22}>
                <Row justify="end">
                  <Button
                    type="default"
                    onClick={() => {
                      handleSubmit();
                    }}
                  >
                    Salvar Alterações
                    <CheckCircleOutlined />
                  </Button>
                </Row>
              </Col>
            </Row>
            <Row justify="space-between" style={{ marginBottom: "1rem" }}>
              <Col style={{ marginBottom: ".5rem" }}>
                {permissionCodes.includes("add_payment_method") && (
                  <Space wrap>
                    <AddPaymentMethod
                      paymentMethodsParent={paymentMethods}
                      setPaymentMethods={(paymentMethods) =>
                        setPaymentMethods(paymentMethods)
                      }
                    />
                  </Space>
                )}
              </Col>
              <Col>
                <Space wrap>
                  <Text>Filtrar por: </Text>
                  <Select
                    onChange={setActionsState}
                    defaultValue="Ativas"
                    style={{ width: "10rem" }}
                  >
                    <Option value="todas">Todas</Option>
                    <Option value="ativas">Ativas</Option>
                    <Option value="inativas">Inativas</Option>
                  </Select>
                </Space>
              </Col>
            </Row>
            <Counter tableData={tableData} />
            <Row>
              <Col span={24}>
                <Table
                  scroll={{ x: "100%" }}
                  dataSource={tableData}
                  columns={columns.filter((e) =>
                    permissionCodes.includes(e.code)
                  )}
                  pagination={{
                    data: [],
                  }}
                />
              </Col>
            </Row>
          </Card>
        </Content>
      </Layout>
    </Layout>
  );
};
