import {
  PaperClipOutlined,
  DownloadOutlined,
  CheckOutlined,
  PlusOutlined,
  DeleteOutlined,
} from "@ant-design/icons";
import { useLocation } from "react-router-dom";
import { useEffect, useState } from "react";
import { Button, Modal, Row, Col, Table, message, Progress, Empty } from "antd";
import axios from "axios";

export const ArchitectureDocuments = (proposalParent) => {
  const [showModal, setShowModal] = useState();
  const [childProposal, setChildProposal] = useState({});
  const [loading, setLoading] = useState(false);
  const FileDownload = require("js-file-download");

  // console.log('archi proposalParent: ', proposalParent.proposalParent)

  const handleCloseModal = () => {
    setShowModal(false);
  };

  const handleGet = async (file) => {
    setLoading(true);
    setShowModal(true);
    let name = `${proposalParent.proposalParent.searchId}/documents/architecture/${file}`;

    const {
      data: { data },
    } = await axios.post(
      `${process.env.REACT_APP_API_PERMISSION}s3/link`,
      {
        operation: "get",
        key: name,
        bucket: `${process.env.REACT_APP_STAGE}-proposals-documents`,
      },
      {
        headers: {
          authorization: localStorage.getItem("jwt"),
        },
      }
    );

    axios
      .get(data, {
        responseType: "blob",
      })
      .then((response) => {
        FileDownload(response.data, `${file}`);
        setLoading(false);
      })
      .catch((err) => {
        setLoading(false);
        message.error("Erro ao tentar fazer download");
      });

    setShowModal(false);
  };

  const columns = [
    {
      title: "ID",
      dataIndex: "id",
      key: "id",
    },
    {
      title: "Nome do arquivo",
      dataIndex: "file_name",
      key: "file_name",
    },
    {
      title: "Tipo do Arquivo",
      dataIndex: "file_type",
      key: "file_type",
      align: "center",
    },
    {
      title: "Download",
      dataIndex: "download",
      key: "download",
      align: "center",
      render: (id, item) => {
        return (
          <Button
            type="text"
            onClick={() => {
              handleGet(`${item.file_name}.${item.file_type}`);
            }}
          >
            <DownloadOutlined />
          </Button>
        );
      },
    },
  ];

  useEffect(() => {
    setChildProposal(proposalParent.proposalParent);
  }, [proposalParent.proposalParent]);

  // console.log('proposalParent: ', proposalParent)

  return (
    <>
      <Button
        type="link"
        onClick={() => {
          setShowModal(true);
        }}
      >
        Visualizar Anexos
        {/* <PaperClipOutlined /> */}
      </Button>
      <Modal
        title="Fazer Upload"
        open={showModal}
        closable={false}
        onOk={() => setShowModal(false)}
        footer={[
          <Row justify="end">
            <Button type="primary" onClick={handleCloseModal}>
              Ok <CheckOutlined />
            </Button>
          </Row>,
        ]}
      >
        <Row
          style={{ marginTop: "2em", display: "flex", flexDirection: "column" }}
          justify="center"
        >
          {childProposal.architectureFileNames ? (
            <Table
              dataSource={childProposal.architectureFileNames?.map(
                (file, fileKey) => {
                  return {
                    id: fileKey + 1,
                    file_name: file
                      ? file?.replace(
                          `.${file?.split(".")[file?.split(".").length - 1]}`,
                          ""
                        )
                      : "",
                    file_type: file ? file?.split(".").pop() : "",
                    download: fileKey,
                  };
                }
              )}
              columns={columns}
              scroll={{ x: 100 }}
              pagination={false}
              loading={loading}
            ></Table>
          ) : (
            <Empty>Nenhum arquivo encontrado</Empty>
          )}
        </Row>
      </Modal>
    </>
  );
};
