export const inputValueController = async (
  value,
  inputType,
  hourType,
  totalHours
) => {
  if (inputType === "discountType") {
    // totalDaredeInvestment.map((curr) => {
    //   return setTotalDaredeInvestment((prev) => {
    //     if (curr.title === hourType.title.split(" ").pop()) {
    //       curr.hours.map((hour) => {
    //         if (hour.type === hourType.title.split(" ").shift()) {
    //           hourType.title.split(" ").shift() === "8x5"
    //             ? (hour.discountType8x5 = value)
    //             : (hour.discountType24x7 = value);
    //         }
    //       });
    //     }
    //     return prev && setTotalDaredeInvestment(prev);
    //   });
    // });
  } else if (inputType === "discountValue") {
    // if it is the inputType values we are going to manipulate totalDiscountValue values
    // totalDaredeInvestment.map((curr) => {
    //   return setTotalDaredeInvestment((prev) => {
    //     if (curr.title === hourType.title.split(" ").pop()) {
    //       curr.hours.map((hour) => {
    //         if (hour.type === hourType.title.split(" ").shift()) {
    //           hourType.title.split(" ").shift() === "8x5"
    //             ? (hour.discountValue8x5 = Number(value))
    //             : (hour.discountValue24x7 = Number(value));
    //         }
    //       });
    //     }
    //     return prev && setTotalDaredeInvestment(prev);
    //   });
    // });
  } else if (inputType === "additionalHourValue") {
    // totalDaredeInvestment.map((curr) => {
    //   return setTotalDaredeInvestment((prev) => {
    //     if (curr.title === hourType.title.split(" ").pop()) {
    //       curr.hours.map((hour) => {
    //         if (hour.type === hourType.title.split(" ").shift()) {
    //           hourType.title.split(" ").shift() === "8x5"
    //             ? (hour.totalAditional8x5 = Number(value))
    //             : (hour.totalAditional24x7 = Number(value));
    //         }
    //       });
    //     }
    //     return prev && setTotalDaredeInvestment(prev);
    //   });
    // });
  } else if (inputType === "additionalDiscountValue") {
    // totalDaredeInvestment.map((curr) => {
    //   return setTotalDaredeInvestment((prev) => {
    //     if (curr.title === hourType.title.split(" ").pop()) {
    //       curr.hours.map((hour) => {
    //         if (hour.type === hourType.title.split(" ").shift()) {
    //           hourType.title.split(" ").shift() === "8x5"
    //             ? (hour.discountAditionalValue8x5 = Number(value))
    //             : (hour.discountAditionalValue24x7 = Number(value));
    //         }
    //       });
    //     }
    //     return prev && setTotalDaredeInvestment(prev);
    //   });
    // });
  } else if (inputType === "additionalDiscountType") {
    // totalDaredeInvestment.map((curr) => {
    //   return setTotalDaredeInvestment((prev) => {
    //     if (curr.title === hourType.title.split(" ").pop()) {
    //       curr.hours.map((hour) => {
    //         if (hour.type === hourType.title.split(" ").shift()) {
    //           hourType.title.split(" ").shift() === "8x5"
    //             ? (hour.discountAditionalType8x5 = value)
    //             : (hour.discountAditionalType24x7 = value);
    //         }
    //       });
    //     }
    //     return prev && setTotalDaredeInvestment(prev);
    //   });
    // });
  } else if (inputType === "hourValue") {
    // if it is not the inputType values we are going to manipulate the totalValue values
    // totalDaredeInvestment.map((curr) => {
    //   return setTotalDaredeInvestment((prev) => {
    //     if (curr.title === hourType.title.split(" ").pop()) {
    //       curr.hours.map((hour) => {
    //         if (hour.type === hourType.title.split(" ").shift()) {
    //           hourType.title.split(" ").shift() === "8x5"
    //             ? (hour.totalValue8x5 = Number(value))
    //             : (hour.totalValue24x7 = Number(value));
    //         }
    //       });
    //     }
    //     return prev && setTotalDaredeInvestment(prev);
    //   });
    // });
  } else if (inputType === "burst") {
    let temp = totalHours;
    temp.map((i) => {
      if (i.formName === hourType.formName) {
        i.burst = Number(value);
        i.total = Number(value) + i.value;
      }
    });
    return temp;
  } else {
    console.log("error on handling totalDaredeInvestments");
  }
};
