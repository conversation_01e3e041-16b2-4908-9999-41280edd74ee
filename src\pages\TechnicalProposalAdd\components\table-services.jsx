import { useState, useEffect, useMemo } from "react";
import { shallowEqual, useSelector } from "react-redux";
import { NavLink } from "react-router-dom";
import {
  Row,
  Col,
  Input,
  Table,
  Button,
  Empty,
  Popconfirm,
  Space,
  Tooltip,
} from "antd";
import { DeleteOutlined, EditOutlined } from "@ant-design/icons";

import { AddService } from "../../../components/Modals/TechnicalProposals/AddNewService";
import { AddManagementService } from "../../../components/Modals/TechnicalProposals/AddNewManagementService";

import { TextColor } from "../../../hooks/ColorInverter";
import { useDebounce } from "../../../hooks/useDebouce";

import { dynamoGet } from "../../../service/apiDsmDynamo";

import { store } from "../../../store/store";
import { setTechnicalProposalState } from "../../../store/actions/technical-proposal-action";
import { calculateTotalValues } from "../../../controllers/Proposals/proposal-controller";

export const TableServices = (servicesInfo) => {
  const serviceList = useSelector(
    (state) => state?.technicalProposal?.serviceList,
    shallowEqual
  );
  const selectedServices = useSelector(
    (state) => state?.technicalProposal?.selectedServices,
    shallowEqual
  );
  const loadingPersist = useSelector(
    (state) => state.globalPersist.technicalProposal.loadingPersist,
    shallowEqual
  );

  const [search, setSearch] = useState("");
  const [loading, setLoading] = useState(false);
  const [costManagementData, setCostManagementData] = useState(null);

  const debouncedSearchTerm = useDebounce(search, 300);

  const selectedServicesInfo = useMemo(() => {
    return serviceList.filter((service) =>
      selectedServices.includes(service.id)
    );
  }, [serviceList, selectedServices]);

  useEffect(() => {
    dynamoGet(`${process.env.REACT_APP_STAGE}-cost-management`).then((data) => {
      const res = data.find(
        (d) => d.id === `${process.env.REACT_APP_COST_MANAGEMENT_OBJ_ID}`
      );

      setCostManagementData(res);
    });
  }, []);
  useEffect(() => {
    calculateTotalValues(selectedServicesInfo, costManagementData);
  }, [selectedServicesInfo, costManagementData]);

  useEffect(() => {
    try {
      setLoading(true);

      const { serviceList } = store.getState().technicalProposal;
      const serviceListStr = JSON.stringify(serviceList);
      const services = JSON.parse(serviceListStr);

      selectedServices.forEach((serviceID) => {
        const localStorageName = `proposal-service-${serviceID}`;
        const localChangeService = localStorage.getItem(localStorageName);

        if (localChangeService) {
          const changeService = JSON.parse(localChangeService);
          const index = services.findIndex((d) => d.id === serviceID);
          if (index >= 0) services[index] = changeService;
        }
      });

      setTechnicalProposalState({
        field: "serviceList",
        value: services,
      });

      setLoading(false);
    } catch (error) {
      console.log(error);
      setLoading(false);
    }
  }, [selectedServices]);

  function handleRemoveService(serviceID) {
    localStorage.removeItem(`proposal-service-${serviceID}`);

    const { selectedServices } = store.getState().technicalProposal;

    setTechnicalProposalState({
      field: "selectedServices",
      value: selectedServices.filter((service) => service !== serviceID),
    });
  }

  const handleAddServices = (data) => {
    servicesInfo.setServices(data);
  };

  const columns = [
    {
      title: "Nome",
      dataIndex: "name",
      key: "name",
      sorter: (a, b) => a?.name.localeCompare(b?.name),
    },
    {
      title: "Descrição",
      dataIndex: "description",
      key: "description",
      render: (id, item) => {
        if (item?.description?.length < 100) {
          return item?.description ? item?.description : "-";
        } else {
          return (
            <div>
              {item?.description ? (
                <>{item?.description?.substring(0, 100)}...</>
              ) : (
                <Row justify="center">-</Row>
              )}
            </div>
          );
        }
      },
    },
    {
      title: "Tag",
      dataIndex: "tag",
      key: "tag",
      align: "center",
      sorter: (a, b) => a?.tag?.name.localeCompare(b?.tag?.name),
      render: (id, item) => {
        return TextColor(item?.tag?.rgb, item?.tag?.name);
      },
    },
    {
      title: "8x5 SETUP",
      dataIndex: "SETUP85",
      key: "SETUP85",
      align: "center",
      render: (id, item) => {
        let percentageBruteValue = 0;
        if (item.management === true) {
          let totalManagementHours = 0;
          let supportManagementHours8x5 = [];
          let supportManagementHours24x7 = [];
          selectedServicesInfo.map((s) => {
            if (s.management === false) {
              s.tasks.map((t) => {
                supportManagementHours8x5.push(t.estimates["8x5setup"]);
                supportManagementHours24x7.push(t.estimates["24x7setup"]);
              });
            }
          });

          let sumHours8x5 = supportManagementHours8x5.reduce(
            (a, b) => a + b,
            0
          );
          let sumHours24x7 = supportManagementHours24x7.reduce(
            (a, b) => a + b,
            0
          );

          totalManagementHours = sumHours8x5 + sumHours24x7;

          item.tasks.map((cur) => {
            if (cur.taskTime.type === "percentage") {
              percentageBruteValue += Math.ceil(
                (cur.taskTime.time * totalManagementHours) / 100
              );
            }
          });
        }
        let SETUP85Support = [];
        item.tasks.map((task) => {
          if (!task.taskTime) {
            SETUP85Support.push(task.estimates["8x5setup"]);
          } else if (task.taskTime.type === "hour") {
            SETUP85Support.push(task.taskTime.time);
          }
        });
        return item.management === false ? (
          <>{SETUP85Support.reduce((a, b) => a + b, 0)}</>
        ) : (
          <>
            {SETUP85Support.reduce((a, b) => a + b, 0) + percentageBruteValue}
          </>
        );
      },
    },
    {
      title: "24x7 SETUP",
      dataIndex: "SETUP247",
      key: "SETUP247",
      align: "center",
      render: (id, item) => {
        let SETUP247Support = [];
        item.tasks.map((task) => {
          SETUP247Support.push(task.estimates["24x7setup"]);
        });
        return <>{SETUP247Support.reduce((a, b) => a + b, 0)}</>;
      },
    },
    {
      title: "Editar",
      dataIndex: "edit",
      key: "edit",
      align: "center",
      render: (id, item) => {
        if (servicesInfo.state[servicesInfo.state.length - 1] === "technical") {
          let editDataFormat = servicesInfo.state[1];
          let allData = [
            {
              item,
              editDataFormat,
              allServicesData: [...serviceList]
            },
          ];

          return loadingPersist ? (
            <Tooltip title="Persistindo informações">
              <Button type="text" disabled={true} loading />
            </Tooltip>
          ) : (
            <NavLink
              to={
                item.management === false
                  ? "/technical-proposals-services/edit"
                  : "/service-management/edit"
              }
              state={item.management === false ? [item, serviceList] : allData}
            >
              <Button type="text" disabled={serviceList.length === 0}>
                <EditOutlined />
              </Button>
            </NavLink>
          );
        }
      },
    },
    {
      title: "Remover",
      dataIndex: "id",
      key: "remove",
      align: "center",
      render: (id, item) => {
        return (
          <Popconfirm
            title="Você tem certeza que deseja remover este serviço?"
            onConfirm={() => handleRemoveService(id)}
            okText="Sim"
            cancelText="Não"
          >
            <Button type="text" danger>
              <DeleteOutlined />
            </Button>
          </Popconfirm>
        );
      },
    },
  ];

  const filteredServices = useMemo(() => {
    let sortedServices = [];
    selectedServices.forEach((s) => {
      const serviceData = serviceList.find(
        (serviceInList) => serviceInList.id === s
      );
      if (serviceData) sortedServices.push(serviceData);
    });

    let selectedServiceList = sortedServices.filter((item) => {
      let includes = [];

      const description = item.description
        ? item.description.toUpperCase()
        : "";
      const name = item.name ? item.name.toUpperCase() : "";

      const tag = item.tag.name ? item.tag.name.toUpperCase() : "";

      const searchTerm = debouncedSearchTerm.toUpperCase();

      if (
        description.includes(searchTerm) ||
        name.includes(searchTerm) ||
        tag.includes(searchTerm)
      )
        includes.push(true);
      else includes.push(false);

      return !includes.includes(false);
    });

    return selectedServiceList;
  }, [selectedServices, serviceList, debouncedSearchTerm]);

  return (
    <>
      <Row
        justify="space-between"
        style={{ marginBlock: "1rem" }}
        gutter={[8, 8]}
      >
        {servicesInfo.state[servicesInfo.state.length - 1] === "technical" ? (
          <>
            <Col>
              <Space wrap>
                <>
                  <AddService
                    services={serviceList}
                    setServices={(data) => handleAddServices(data)}
                    loading={loading}
                  />
                  <AddManagementService
                    services={serviceList}
                    setServices={(data) => handleAddServices(data)}
                    loading={loading}
                  />
                </>
              </Space>
            </Col>
            <Col>
              <Input
                style={{
                  width: "300px",
                  height: "35px",
                  borderRadius: "7px",
                }}
                placeholder="Buscar por Nome, Descrição, Tag..."
                onChange={(e) => setSearch(e.target.value)}
              />
            </Col>
          </>
        ) : (
          <></>
        )}
      </Row>
      <Row justify="center" align="middle" style={{ marginBottom: "15px" }}>
        <Col span={24}>
          {filteredServices.length > 0 || loading ? (
            <Table
              loading={loading}
              scroll={{ x: 100 }}
              columns={columns}
              pagination={{
                data: [],
                total: filteredServices.length,
              }}
              dataSource={filteredServices}
            />
          ) : (
            <Empty
              description="Nenhum serviço encontrado"
              style={{ margin: "30px 0" }}
            />
          )}
        </Col>
      </Row>
    </>
  );
};
