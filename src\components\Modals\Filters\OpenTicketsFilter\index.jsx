import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Typography, Row, Col } from "antd";
import React, { useEffect, useState } from "react";
import { CloseCircleOutlined } from "@ant-design/icons";
import { otrsGet } from "../../../../service/apiOtrs";
import { filterByOrderOfInput } from "../../../../utils/filterByOrderOfInput";

function OpenTicketsFilter(props) {
  const { Title } = Typography;
  const { allCustomers } = props;
  const [allUsers, setAllUsers] = useState([]);

  const [form] = Form.useForm();
  const { Option } = Select;

  function onFilterFinish(values) {
    props.filter(values);
    props.hiddenPopover();
  }

  function onFilterFailed() {}

  function clear() {
    form.resetFields();
  }

  async function getAllUsers() {
    const { data } = await otrsGet("read/user/all/0");
    const userNames = [];

    Object.entries(data).forEach((item) => {
      const user = item[1];
      userNames.push({
        id: user.id,
        login: user.login,
        name: `${user.first_name}  ${user.last_name}`,
      });
    });

    setAllUsers(userNames);
  }

  const sortAlpha = (array) => {
    // Verificar se array é válido e é realmente um array
    if (!array || !Array.isArray(array)) {
      return [];
    }

    const sortedArray = array.sort((a, b) => {
      if (a.name > b.name) {
        return 1;
      }
      if (a.name < b.name) {
        return -1;
      }
      return 0;
    });

    return sortedArray;
  };

  useEffect(() => {
    getAllUsers();
  }, []);

  return (
    <Form
      form={form}
      style={{ display: "flex", flexDirection: "column", width: "230px" }}
      name="basic"
      initialValues={{ remember: true }}
      onFinish={onFilterFinish}
      onFinishFailed={onFilterFailed}
      autoComplete="off"
    >
      <Row
        justify="space-between"
        align="middle"
        style={{ marginBottom: "10px" }}
      >
        <Col span={18}>
          <Title
            level={5}
            style={{ fontWeight: 400, margin: "0px", marginRight: "10px" }}
          >
            Tickets abertos
          </Title>
        </Col>
        <Col span={4}>
          <Button
            type="text"
            onClick={() => props.hiddenPopover()}
            style={{ width: "25px", height: "25px", padding: "0px" }}
          >
            {" "}
            <CloseCircleOutlined style={{ color: "#868383" }} />{" "}
          </Button>
        </Col>
      </Row>
      <Form.Item
        labelAlign="top"
        labelCol={{ span: 24 }}
        label="Proprietário"
        name="owner"
        rules={[{ required: true, message: "Por favor, preencha esse campo" }]}
      >
        <Select
          showSearch
          style={{ width: "100%" }}
          placeholder="Selecione o proprietário"
          optionFilterProp="children"
          loading={allUsers.length === 0}
          filterOption={(input, option) =>
            filterByOrderOfInput(input, option.props.children)
          }
          filterSort={(optionA, optionB) =>
            optionA.children
              .toLowerCase()
              .localeCompare(optionB.children.toLowerCase())
          }
        >
          {sortAlpha(allUsers).map((user) => {
            return (
              <Option key={user.id} value={user.login}>
                {user.name}
              </Option>
            );
          })}
        </Select>
      </Form.Item>

      <Form.Item name="customer">
        <Select
          showSearch
          style={{ width: "100%" }}
          placeholder="Selecione o cliente"
          optionFilterProp="children"
          loading={allCustomers.length === 0}
          filterOption={(input, option) => {
            return filterByOrderOfInput(input, option?.props?.children);
          }}
          filterSort={(optionA, optionB) => {
            return optionA?.children
              ?.toLowerCase()
              .localeCompare(optionB?.children?.toLowerCase());
          }}
        >
          {allCustomers.map((customer) => {
            return (
              customer.name !== "" && (
                <Option
                  key={customer.id}
                  value={customer?.names?.name || customer?.names?.fantasy_name}
                >
                  {customer?.names?.name || customer?.names?.fantasy_name}
                </Option>
              )
            );
          })}
        </Select>
      </Form.Item>
      <Form.Item name="date">
        <DatePicker style={{ width: "100%" }} placeholder="Data de abertura" />
      </Form.Item>

      <Row justify="space-between" align="middle">
        <Button type="text" onClick={clear}>
          Limpar
        </Button>
        <Button type="primary" htmlType="submit" className="filter-submit-btn">
          Aplicar
        </Button>
      </Row>
    </Form>
  );
}

export default OpenTicketsFilter;
