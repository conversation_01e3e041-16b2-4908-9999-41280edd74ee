import { useState, useEffect, useCallback, createContext, useContext } from 'react';
import { authService } from '../services/authService';

// Create Auth Context
const AuthContext = createContext(null);

/**
 * Auth Provider Component
 */
export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  // Initialize authentication state
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        setIsLoading(true);
        
        // Check if user has legacy localStorage data and migrate
        await authService.migrateFromLocalStorage();
        
        // Check current authentication status
        const authenticated = await authService.isAuthenticated();
        
        if (authenticated) {
          const userInfo = authService.getUserInfo();
          setUser(userInfo);
          setIsAuthenticated(true);
        } else {
          setUser(null);
          setIsAuthenticated(false);
        }
      } catch (error) {
        console.error('Error initializing auth:', error);
        setUser(null);
        setIsAuthenticated(false);
      } finally {
        setIsLoading(false);
      }
    };

    initializeAuth();
  }, []);

  // Login function
  const login = useCallback(async (token, userInfo) => {
    try {
      setIsLoading(true);
      const success = await authService.setAuthToken(token, userInfo);
      
      if (success) {
        setUser(userInfo);
        setIsAuthenticated(true);
        return { success: true };
      } else {
        return { success: false, error: 'Failed to set authentication' };
      }
    } catch (error) {
      console.error('Login error:', error);
      return { success: false, error: error.message };
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Logout function
  const logout = useCallback(async () => {
    try {
      setIsLoading(true);
      await authService.logout();
      setUser(null);
      setIsAuthenticated(false);
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Update user info
  const updateUser = useCallback((newUserInfo) => {
    setUser(prevUser => ({ ...prevUser, ...newUserInfo }));
    authService.setUserInfo({ ...user, ...newUserInfo });
  }, [user]);

  // Check if user has specific permission
  const hasPermission = useCallback((requiredPermission) => {
    if (!user || !user.permission) return false;
    
    // Add your permission logic here
    return user.permission === requiredPermission || user.permission === 'admin';
  }, [user]);

  // Refresh authentication status
  const refreshAuth = useCallback(async () => {
    try {
      const authenticated = await authService.isAuthenticated();
      
      if (authenticated) {
        const userInfo = authService.getUserInfo();
        setUser(userInfo);
        setIsAuthenticated(true);
      } else {
        setUser(null);
        setIsAuthenticated(false);
      }
      
      return authenticated;
    } catch (error) {
      console.error('Error refreshing auth:', error);
      return false;
    }
  }, []);

  const value = {
    user,
    isLoading,
    isAuthenticated,
    login,
    logout,
    updateUser,
    hasPermission,
    refreshAuth,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

/**
 * Custom hook to use authentication
 */
export const useAuth = () => {
  const context = useContext(AuthContext);
  
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  
  return context;
};

/**
 * HOC for components that require authentication
 */
export const withAuth = (WrappedComponent) => {
  return function AuthenticatedComponent(props) {
    const { isAuthenticated, isLoading } = useAuth();
    
    if (isLoading) {
      return <div>Loading...</div>; // Replace with your loading component
    }
    
    if (!isAuthenticated) {
      return <div>Unauthorized</div>; // Replace with redirect to login
    }
    
    return <WrappedComponent {...props} />;
  };
};

/**
 * Hook for creating authenticated API calls
 */
export const useAuthenticatedApi = () => {
  const { logout } = useAuth();
  
  const createApiInstance = useCallback(() => {
    const instance = authService.createAuthenticatedAxios();
    
    // Add additional interceptors if needed
    instance.interceptors.response.use(
      (response) => response,
      async (error) => {
        if (error.response?.status === 401) {
          await logout();
          window.location.href = '/login';
        }
        return Promise.reject(error);
      }
    );
    
    return instance;
  }, [logout]);
  
  return { createApiInstance };
};

export default useAuth;
