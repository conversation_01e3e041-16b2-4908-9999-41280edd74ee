import { PlusOutlined } from '@ant-design/icons'
import { useState, useEffect } from 'react'
import {
  Button,
  Checkbox,
  Modal,
  Form,
  Row,
  Col,
  Table,
  Input,
  message,
  Tag,
  Space,
  Typography,
  Select
} from 'antd'
import { setTechnicalProposalState } from '../../../store/actions/technical-proposal-action'
import { shallowEqual, useSelector } from 'react-redux'
import { store } from '../../../store/store'

const { Text } = Typography
const { Option } = Select

export const AddService = props => {
  const serviceList = useSelector(state => state.technicalProposal.serviceList, shallowEqual)
  const selectedServices = useSelector(state => state.technicalProposal.selectedServices, shallowEqual)

  const { loading } = props
  const [add_service] = Form.useForm()
  const [showModal, setShowModal] = useState()

  const [actionsState, setActionsState] = useState('')
  const [search, setSearch] = useState('')

  const handleCancel = () => {
    add_service.resetFields()
    setShowModal(false)
  }

  const handleOk = () => {
    add_service.resetFields()
    setShowModal(false)
  }

  const columns = [
    {
      title: 'Nome',
      dataIndex: 'name',
      key: 'name',
      sorter: (a, b) => {
        return a.name.localeCompare(b.name)
      }
    },
    {
      title: 'Tag',
      dataIndex: 'tag',
      key: 'tag',
      align: 'center',
      sorter: (a, b) => {
        return a.name.localeCompare(b.name)
      },
      render: (id, props) => {
        return <Tag color={props.tag.rgb}>{props.tag.name}</Tag>
      }
    },
    {
      title: 'Selecionar',
      dataIndex: 'id',
      key: 'id',
      align: 'center',
      render: (id, props) => {
        return (
          <Space align='center' style={{ marginTop: '20px' }}>
            <Form.Item name="checkbox-group">
              <Checkbox
                checked={selectedServices.includes(id)}
                value={props.id}
                onChange={event => handleSelectService(event.target.checked, id)}
              />
            </Form.Item>
          </Space>
        )
      }
    }
  ]

  function handleSelectService(checked, id) {
    const { selectedServices } = store.getState().technicalProposal

    const listSTR = JSON.stringify(selectedServices)
    let list = JSON.parse(listSTR)

    if (checked)
      list.push(id)
    else
      list = list.filter(item => item !== id)

    setTechnicalProposalState({
      field: 'selectedServices',
      value: list
    })
  }

  return (
    <>
      <Button type="primary" onClick={() => setShowModal(true)}>
        Adicionar Serviço
        <PlusOutlined />
      </Button>
      <Modal
        title="Adicionar Serviço"
        open={showModal}
        closable={false}
        width={"60%"}
        onOk={handleOk}
        okText="Selecionar"
        cancelText="Cancelar"
        onCancel={handleCancel}
      >
        <Row
          justify="space-between"
          align="bottom"
          style={{ marginBottom: '1rem' }}
        >
          <Col span={12}>
            <Space wrap>
              <Input
                placeholder="Buscar por Nome ou Tag..."
                onChange={e => setSearch(e.target.value)}
              />
            </Space>
          </Col>
          <Col>
            <Space>
              <Text>Filtrar por: </Text>
              <Select onChange={setActionsState} defaultValue="ativos">
                <Option value="todos">Todos</Option>
                <Option value="ativos">Ativos</Option>
                <Option value="inativos">Inativos</Option>
              </Select>
            </Space>
          </Col>
        </Row>
        <Form
          form={add_service}
          layout="vertical"
          name="add_service"
          initialValues={{ 'checkbox-group': false }}
        >
          <Table
            loading={loading}
            defaultPageSize={5}
            columns={columns}
            scroll={{ x: 100 }}
            pagination={true}
            dataSource={serviceList?.filter(item => item.management !== true).filter(e => {
              const data = [
                e?.name
                  ? e?.name?.toString().includes(search?.toLowerCase())
                  : false,
                e?.tag.name
                  ? e?.tag.name?.toString().includes(search?.toLowerCase())
                  : false,
                e?.description.toLowerCase()
                  ? e?.description
                    .toLowerCase()
                    ?.toString()
                    .includes(search?.toLowerCase())
                  : false,
                e?.name
                  ? e?.name?.toLowerCase().includes(search?.toLowerCase())
                  : false
              ]

              if (actionsState === 'ativos' || actionsState === '') {
                for (let i = 0; i < data.length; i++) {
                  if (data[i] === true) {
                    return e?.active === true
                  }
                }
              } else if (actionsState === 'inativos') {
                for (let i = 0; i < data.length; i++) {
                  if (data[i] === true) {
                    return e?.active === false
                  }
                }
              } else if (actionsState === 'todos') {
                for (let i = 0; i < data.length; i++) {
                  if (data[i] === true) {
                    return e
                  }
                }
              }

              for (let i = 0; i < data.length; i++) {
                if (data[i] === true) {
                  return e
                }
              }
              if (search === '') return e

              return null
            })}
          />
        </Form>
      </Modal>
    </>
  )
}
