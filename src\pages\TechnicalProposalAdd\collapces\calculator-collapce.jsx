import React, { useState, useEffect } from "react";
import { Card, Row, Col, Input, Button, Form, message, Select } from "antd";
import { DeleteOutlined, PlusOutlined } from "@ant-design/icons";
import { shallowEqual, useSelector } from "react-redux";

import { MainCollapse } from "../../../components/Collapse/CommercialProposal";

import { invalidInputNumberChars } from "../../../constants/invalidCharsInputNumber";

import { realMask } from "../../../utils/masks";

import {
  setCalculatorItemTechnicalProposalState,
  setTechnicalProposalState,
} from "../../../store/actions/technical-proposal-action";
import { awsRegionsArr } from "../../../constants/awsRegionNames";

const { Option } = Select;

export const CalculatorCollapce = ({ add_proposal }) => {
  const reduxCalculator = useSelector(
    (state) => state.technicalProposal.calculator,
    shallowEqual
  );

  function addCalculator() {
    const item = {
      title: "",
      month_value: "",
      year_value: "",
      upfront_value: "",
      calculator_link: "",
      regions: [],
    };

    setTechnicalProposalState({
      field: "calculator",
      value: [...reduxCalculator, item],
    });
  }

  function removeItem(position) {
    const newList = reduxCalculator.filter((item, index) => index !== position);

    setTechnicalProposalState({
      field: "calculator",
      value: newList,
    });
  }

  return (
    <>
      <Row>
        <Col span={24} key={0} id="calculate">
          <MainCollapse
            title={"Calculadora"}
            editor={
              <Form.List name="main_aws_investments">
                {(main_aws_investments, { add, remove }) => (
                  <>
                    {reduxCalculator.map((value, index) => (
                      <Form.Item required={false} key={value.key}>
                        <CalculatorItem
                          index={index}
                          value={value}
                          removeItem={removeItem}
                        />
                      </Form.Item>
                    ))}
                    <Button type="text" onClick={() => addCalculator()}>
                      Adicionar Custo AWS
                      <PlusOutlined />
                    </Button>
                  </>
                )}
              </Form.List>
            }
          ></MainCollapse>
        </Col>
      </Row>
    </>
  );
};

const CalculatorItem = ({ index, value, removeItem }) => {
  return (
    <>
      <Row gutter={24}>
        <Col span={24}>
          <Card
            style={{
              backgroundColor: "#F6F6F6",
              boxShadow: "0 0 5px rgba(0,0,0,0.1)",
              borderRadius: "5px",
              marginRight: "10px",
            }}
          >
            <Row gutter={24}>
              <Col span={24}>
                <Form.Item label="Título" required={true}>
                  <Input
                    value={value.title}
                    onChange={(e) => {
                      setCalculatorItemTechnicalProposalState({
                        index: index,
                        value: {
                          ...value,
                          title: e.currentTarget.value,
                        },
                      });
                    }}
                  />
                </Form.Item>
              </Col>
              <Col lg={8} md={24}>
                <Form.Item label="Valor mensal" required={true}>
                  <Input
                    value={value.month_value}
                    onKeyDown={(e) => {
                      invalidInputNumberChars.find(
                        (keyPressed) => keyPressed === e.key
                      ) && e.preventDefault();
                    }}
                    addonAfter={
                      <Select
                        defaultValue={
                          value.month_unit ? value.month_unit : "dolar"
                        }
                        onSelect={(e) => {
                          setCalculatorItemTechnicalProposalState({
                            index: index,
                            value: {
                              ...value,
                              month_unit: e,
                            },
                          });
                        }}
                      >
                        <Option value="dolar">$</Option>
                        <Option value="reais">R$</Option>
                      </Select>
                    }
                    type="text"
                    maxLength={14}
                    onChange={(e) => {
                      setCalculatorItemTechnicalProposalState({
                        index: index,
                        value: {
                          ...value,
                          month_value: realMask(e.currentTarget.value),
                        },
                      });
                    }}
                  />
                </Form.Item>
              </Col>
              <Col lg={8} md={24}>
                <Form.Item label="Valor anual" required={true}>
                  <Input
                    value={value.year_value}
                    onKeyDown={(e) => {
                      invalidInputNumberChars.find(
                        (keyPressed) => keyPressed === e.key
                      ) && e.preventDefault();
                    }}
                    addonAfter={
                      <Select
                        defaultValue={
                          value.year_unit ? value.year_unit : "dolar"
                        }
                        onSelect={(e) => {
                          setCalculatorItemTechnicalProposalState({
                            index: index,
                            value: {
                              ...value,
                              year_unit: e,
                            },
                          });
                        }}
                      >
                        <Option value="dolar">$</Option>
                        <Option value="reais">R$</Option>
                      </Select>
                    }
                    type="text"
                    maxLength={14}
                    onChange={(e) => {
                      setCalculatorItemTechnicalProposalState({
                        index: index,
                        value: {
                          ...value,
                          year_value: realMask(e.currentTarget.value),
                        },
                      });
                    }}
                  />
                </Form.Item>
              </Col>
              <Col lg={8} md={24}>
                <Form.Item label="Valor Upfront">
                  <Input
                    value={value.upfront_value}
                    onKeyDown={(e) => {
                      invalidInputNumberChars.find(
                        (keyPressed) => keyPressed === e.key
                      ) && e.preventDefault();
                    }}
                    addonAfter={
                      <Select
                        defaultValue={
                          value.upfront_unit ? value.upfront_unit : "dolar"
                        }
                        onSelect={(e) => {
                          setCalculatorItemTechnicalProposalState({
                            index: index,
                            value: {
                              ...value,
                              upfront_unit: e,
                            },
                          });
                        }}
                      >
                        <Option value="dolar">$</Option>
                        <Option value="reais">R$</Option>
                      </Select>
                    }
                    type="text"
                    maxLength={14}
                    onChange={(e) => {
                      setCalculatorItemTechnicalProposalState({
                        index: index,
                        value: {
                          ...value,
                          upfront_value: realMask(e.currentTarget.value),
                        },
                      });
                    }}
                  />
                </Form.Item>
              </Col>
            </Row>
            <Row>
              <Col span={24}>
                <Form.Item label="Link Público" required={true}>
                  <Input
                    value={value.calculator_link}
                    type="url"
                    onChange={(e) => {
                      setCalculatorItemTechnicalProposalState({
                        index: index,
                        value: {
                          ...value,
                          calculator_link: e.currentTarget.value,
                        },
                      });
                    }}
                  />
                </Form.Item>
              </Col>
            </Row>
            <Row>
              <Col span={24}>
                <Form.Item label="Regiões">
                  <Select
                    // loading={loading}
                    mode="multiple"
                    value={value?.regions || []}
                    showSearch
                    placeholder={"Regiões consideradas na estimativa..."}
                    optionFilterProp="children"
                    filterOption={(input, option) =>
                      option?.children
                        ?.toLowerCase()
                        .includes(input?.toLowerCase())
                    }
                    filterSort={(optionA, optionB) =>
                      optionA?.children
                        ?.toLowerCase()
                        .localeCompare(optionB.children?.toLowerCase())
                    }
                    onChange={(e) => {
                      setCalculatorItemTechnicalProposalState({
                        index: index,
                        value: {
                          ...value,
                          regions: e,
                        },
                      });
                    }}
                    style={{ width: "100%" }}
                  >
                    {awsRegionsArr.map((value, key) => (
                      <Option value={value.code} key={key}>
                        {`${value.name} / ${value.code}`}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
            </Row>
            <Row justify="end">
              <Col>
                <Button
                  danger
                  type="text"
                  onClick={() => {
                    removeItem(index);
                    message.warning("Custo AWS removido");
                  }}
                >
                  Remover item
                  <DeleteOutlined />
                </Button>
              </Col>
            </Row>
          </Card>
        </Col>
      </Row>
    </>
  );
};
