import React, { useEffect, useState, useRef, useContext } from "react";
import Cookies from "js-cookie";
import {
  Layout,
  Card,
  Row,
  Col,
  Input,
  Popconfirm,
  Button,
  Select,
  Form,
} from "antd";
import {
  DeleteOutlined,
  PlusOutlined,
  ArrowLeftOutlined,
  LoadingOutlined,
} from "@ant-design/icons";
import { HeaderMenu } from "../../components/HeaderMenu";
import { SideMenu } from "../../components/SideMenu";
import { useLocation, useNavigate } from "react-router-dom";
import { ProposalHeader } from "../../components/ProposalHeader";
import { AddDocument } from "../../components/Modals/TechnicalProposals/AddDocument";
import { AddArchitectureDocument } from "../../components/Modals/TechnicalProposals/AddArchitectureDocument";
import { MainCollapse } from "../../components/Collapse/CommercialProposal";
import { v4 } from "uuid";
import RichTextEditor from "../../components/RichTextEditor/RichTextEditor";
import { TableProposals } from "../../components/Table/Proposals/TableProposals";
import { dynamoGet, dynamoPut } from "../../service/apiDsmDynamo";
import { DisableIncrementScroll } from "../../hooks/DisableIncrementScroll";
import { otrsPost } from "../../service/apiOtrs";
import { ConfirmTicket } from "../../components/Modals/TechnicalProposals/ConfirmTickets";
import { invalidInputNumberChars } from "../../constants/invalidCharsInputNumber";
import { ProposalsCards } from "../../components/Proposals/ProposalsCards";
import { AdditionalCostsContent } from "../../components/Proposals/AdditionalCostsContent";
import { realMask } from "../../utils/masks";
import { getTicketByNumber } from "../../service/getTicketByNumber";
import { submitEditTechnicalController } from "../../controllers/Proposals/sumbitTechnical";
import { TotalValuesProposalType } from "../../contexts/reducers/totalValuesProposal";
import { totalValuesController } from "../../controllers/Proposals/totalValuesTechnical";
import { renderSettingsProposal } from "../../controllers/Proposals/renderSettingsProposal";
import {
  ProposalProvider,
  ProposalContext,
} from "../../contexts/totalValuesProposals";
import {
  DataCookieContext,
  DataCookieProvider,
} from "../../contexts/data-cookie";

export const TechnicalProposalRfsEdit = () => {
  return (
    <DataCookieProvider>
      <TechnicalProposalEditPage />
    </DataCookieProvider>
  );
};

export const TechnicalProposal = () => {
  return (
    <ProposalProvider>
      <TechnicalProposalEditPage />
    </ProposalProvider>
  );
};

export const TechnicalProposalEditPage = () => {
  let mainSearchId = v4();
  const premisseRef = useRef(null);
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const { proposalState, setProposalState } = useContext(ProposalContext);
  const { cookieState, setCookieState } = useContext(DataCookieContext);
  const { Content } = Layout;
  const { state } = useLocation();
  const { Option } = Select;
  const [currentContacts, setCurrentContacts] = useState([]);
  const allHeaderData = (state, currentContacts);
  const [edit_proposal] = Form.useForm();
  const [collapsed, setCollapsed] = useState(false);
  const [recognizingFormList, setRecognizingFormList] = useState(false);
  const [recongnizingHeaderChange, setRecongnizingHeaderChange] =
    useState(false);
  const [initializedServicesState, setInitializedServicesState] =
    useState(false);
  const [ticketInfo, setTicketInfo] = useState(state?.ticketData);
  const [ticketName, setTicketName] = useState("");
  const [draftItems, setDraftItems] = useState({
    challenges: state?.specialFields ? state?.specialFields[0]?.value : "",
    scenarios: state?.specialFields ? state?.specialFields[1]?.value : "",
    premisses: state?.specialFields ? state?.specialFields[2]?.value : "",
    extra_points: state?.specialFields ? state?.specialFields[3]?.value : "",
    architecture: state?.specialFields ? state?.specialFields[4]?.value : "",
    main_factors: state?.specialFields ? state?.specialFields[5]?.value : "",
    expected_results: state?.specialFields
      ? state?.specialFields[7]?.value
      : "",
    observations: state?.specialFields ? state?.specialFields[8]?.value : "",
    internal_notes: state?.specialFields ? state?.specialFields[9]?.value : "",
  });
  const [costManagementData, setCostManagementData] = useState([]);
  const [services, setServices] = useState(state.services);

  const [customerBrand, setCustomerBrand] = useState("");

  async function geTicketName(ticketInfo) {
    const res = await getTicketByNumber(ticketInfo.TicketNumber);
    console.log("getTicketByNumber: ", res[0].subject);
    setTicketName(res[0].subject);
  }

  const handleDraftItems = (itemName, value) => {
    const items = { ...draftItems };
    items[itemName] = value;
    setDraftItems(items);
  };

  const selectAfter = (
    <Select defaultValue="dolar">
      <Option value="dolar">$</Option>
      <Option value="reais">R$</Option>
    </Select>
  );

  const handleLocalStorageCollapseItems = () => {
    if (
      mainSearchId &&
      !localStorage.getItem("technical-proposal/mainSearchId")
    ) {
      localStorage.setItem("technical-proposal/mainSearchId", mainSearchId);
    }

    if (localStorage.getItem("technical-proposal/services")) {
      setServices(
        JSON.parse(localStorage.getItem("technical-proposal/services"))
      );
    }

    if (localStorage.getItem("technical-proposal/architectureFileList")) {
      console.log(
        "setou",
        localStorage.getItem("technical-proposal/architectureFileList"),
        JSON.parse(
          localStorage.getItem("technical-proposal/architectureFileList")
        )
      );
      edit_proposal.setFieldsValue(
        JSON.parse(
          localStorage.getItem("technical-proposal/architectureFileList")
        )
      );
    }
  };

  const handleTotalValues = async () => {
    const newTotalValues = await totalValuesController(
      proposalState,
      costManagementData,
      services
    );

    setProposalState({
      type: TotalValuesProposalType.SET_TOTAL_VALUES,
      value: newTotalValues,
    });
  };

  const collapseContent = [
    {
      name: "Desafios",
      itemName: "challenges",
      content: (
        <Form.Item name="proposal_challenge">
          <RichTextEditor
            value={draftItems.challenges}
            handleDraftItems={handleDraftItems}
            itemName="challenges"
          />
        </Form.Item>
      ),
      formName: "proposal_challenge",
    },
    {
      name: "Cenário apresentado",
      itemName: "scenarios",
      content: (
        <Form.Item name="proposal_scenario">
          <RichTextEditor
            value={draftItems.scenarios}
            handleDraftItems={handleDraftItems}
            itemName="scenarios"
          />
        </Form.Item>
      ),
      formName: "proposal_scenario",
    },
    {
      name: "Premissas",
      itemName: "premisses",
      content: (
        <Form.Item name="proposal_premisses">
          <div className="preview-container" id="premisses-container"></div>
          {!loading ? (
            <RichTextEditor
              value={draftItems.premisses}
              handleDraftItems={handleDraftItems}
              itemName="premisses"
            />
          ) : (
            <Row justify="center">
              <LoadingOutlined />
            </Row>
          )}
        </Form.Item>
      ),
    },
    {
      name: "Pontos não contemplados",
      itemName: "extra_points",
      content: (
        <Form.Item name="proposal_extra_points">
          <div className="preview-container" id="no-covered-container"></div>
          {!loading ? (
            <RichTextEditor
              value={draftItems.extra_points}
              handleDraftItems={handleDraftItems}
              itemName="extra_points"
            />
          ) : (
            <Row justify="center">
              <LoadingOutlined />
            </Row>
          )}
        </Form.Item>
      ),
      formName: "proposal_extra_points",
    },
    {
      name: "Arquitetura",
      itemName: "architecture",
      content: (
        <>
          <Form.Item name="proposal_architecture">
            <>
              <RichTextEditor
                value={draftItems.architecture}
                handleDraftItems={handleDraftItems}
                itemName="architecture"
              />
            </>
          </Form.Item>
          <Row justify="center">
            <AddArchitectureDocument />
          </Row>
        </>
      ),
      formName: "proposal_architecture",
    },
    {
      name: "Calculadora",
      content: (
        <Form.List name="main_aws_investments">
          {(fields, { add, remove }) => (
            <>
              {fields.map((field, index) => (
                <Form.Item required={false} key={field.key}>
                  <Row gutter={24}>
                    <Col span={24}>
                      <Card
                        style={{
                          backgroundColor: "#F6F6F6",
                          boxShadow: "0 0 5px rgba(0,0,0,0.1)",
                          borderRadius: "5px",
                          marginRight: "10px",
                        }}
                      >
                        <Row gutter={24}>
                          <Col span={24}>
                            <Form.Item
                              label="Título"
                              {...field}
                              name={[field.name, "title"]}
                              required={true}
                            >
                              <Input
                                onChange={() =>
                                  setRecognizingFormList(!recognizingFormList)
                                }
                              />
                            </Form.Item>
                          </Col>
                          <Col lg={8} md={24}>
                            <Form.Item
                              label="Valor mensal"
                              {...field}
                              name={[field.name, "month_value"]}
                              required={true}
                            >
                              <Input
                                onKeyDown={(e) => {
                                  invalidInputNumberChars.find(
                                    (keyPressed) => keyPressed === e.key
                                  ) && e.preventDefault();
                                }}
                                maxLength={14}
                                addonAfter={selectAfter}
                                type="text"
                                onChange={(e) => {
                                  const newValue = realMask(
                                    e.currentTarget.value
                                  );

                                  const formValue = edit_proposal.getFieldValue(
                                    "main_aws_investments"
                                  );
                                  formValue[field.key].month_value = newValue;
                                  edit_proposal.getFieldValue(
                                    "main_aws_investments",
                                    formValue
                                  );

                                  setRecognizingFormList(!recognizingFormList);
                                }}
                              />
                            </Form.Item>
                          </Col>
                          <Col lg={8} md={24}>
                            <Form.Item
                              label="Valor anual"
                              {...field}
                              name={[field.name, "year_value"]}
                              required={true}
                            >
                              <Input
                                onKeyDown={(e) => {
                                  invalidInputNumberChars.find(
                                    (keyPressed) => keyPressed === e.key
                                  ) && e.preventDefault();
                                }}
                                maxLength={14}
                                addonAfter={selectAfter}
                                type="text"
                                onChange={(e) => {
                                  const newValue = realMask(
                                    e.currentTarget.value
                                  );

                                  const formValue = edit_proposal.getFieldValue(
                                    "main_aws_investments"
                                  );
                                  formValue[field.key].year_value = newValue;
                                  edit_proposal.getFieldValue(
                                    "main_aws_investments",
                                    formValue
                                  );

                                  setRecognizingFormList(!recognizingFormList);
                                }}
                              />
                            </Form.Item>
                          </Col>
                          <Col lg={8} md={24}>
                            <Form.Item
                              label="Valor Upfront"
                              {...field}
                              validateTrigger={["onChange", "onBlur"]}
                              name={[field.name, "upfront_value"]}
                            >
                              <Input
                                onKeyDown={(e) => {
                                  invalidInputNumberChars.find(
                                    (keyPressed) => keyPressed === e.key
                                  ) && e.preventDefault();
                                }}
                                addonAfter={selectAfter}
                                type="text"
                                maxLength={14}
                                onChange={(e) => {
                                  const newValue = realMask(
                                    e.currentTarget.value
                                  );

                                  const formValue = edit_proposal.getFieldValue(
                                    "main_aws_investments"
                                  );
                                  formValue[field.key].upfront_value = newValue;
                                  edit_proposal.getFieldValue(
                                    "main_aws_investments",
                                    formValue
                                  );

                                  setRecognizingFormList(!recognizingFormList);
                                }}
                              />
                            </Form.Item>
                          </Col>
                        </Row>
                        <Row>
                          <Col span={24}>
                            <Form.Item
                              label="Link Público"
                              {...field}
                              name={[field.name, "calculator_link"]}
                              required={true}
                              rules={[
                                {
                                  type: "url",
                                  message: "Insira um link válido",
                                },
                              ]}
                            >
                              <Input type="url" />
                            </Form.Item>
                          </Col>
                        </Row>
                        <Row justify="end">
                          <Col>
                            {fields.length > 1 ? (
                              <Button
                                danger
                                type="text"
                                onClick={() => remove(field.name)}
                              >
                                Remover item
                                <DeleteOutlined />
                              </Button>
                            ) : null}
                          </Col>
                        </Row>
                      </Card>
                    </Col>
                  </Row>
                </Form.Item>
              ))}
              <Button
                type="text"
                onClick={() => add(state.main_aws_investments)}
              >
                Adicionar Custo AWS
                <PlusOutlined />
              </Button>
            </>
          )}
        </Form.List>
      ),
      formName: "main_aws_investments",
    },
    {
      name: "Fatores influenciadores",
      itemName: "main_factors",
      content: (
        <Form.Item name="proposal_main_factors">
          <div className="preview-container" id="factor-container"></div>
          {!loading ? (
            <RichTextEditor
              value={draftItems.main_factors}
              handleDraftItems={handleDraftItems}
              itemName="main_factors"
            />
          ) : (
            <Row justify="center">
              <LoadingOutlined />
            </Row>
          )}
        </Form.Item>
      ),
      formName: "proposal_main_factors",
    },
    {
      name: "Custos adicionais",
      content: <AdditionalCostsContent add_proposal={edit_proposal} />,
      formName: "proposal_additional_costs",
    },
    {
      name: "Resultados esperados",
      itemName: "expected_results",
      content: (
        <Form.Item name="proposal_expected_results">
          <RichTextEditor
            value={draftItems.expected_results}
            handleDraftItems={handleDraftItems}
            itemName="expected_results"
          />
        </Form.Item>
      ),
      formName: "proposal_expected_results",
    },
    {
      name: "Observações",
      itemName: "observations",
      content: (
        <Form.Item name="proposal_observations">
          <RichTextEditor
            value={draftItems.observations}
            handleDraftItems={handleDraftItems}
            itemName="observations"
          />
        </Form.Item>
      ),
      formName: "proposal_observations",
    },
    {
      name: "Notas Internas",
      itemName: "internal_notes",
      content: (
        <Form.Item name="proposal_internal_notes">
          <RichTextEditor
            value={draftItems.internal_notes}
            handleDraftItems={handleDraftItems}
            itemName="internal_notes"
          />
        </Form.Item>
      ),
      formName: "proposal_internal_notes",
    },
  ];

  const Submit = async () => {
    setLoading(true);
    const user = localStorage.getItem("@dsm/username");
    await submitEditTechnicalController(
      edit_proposal,
      collapseContent,
      draftItems,
      currentContacts,
      services,
      proposalState,
      customerBrand,
      proposalState.idFixed,
      user,
      ticketInfo,
      state
    );
    const cleanArr = [];

    setProposalState({
      type: TotalValuesProposalType.SET_FILE_NAMES,
      value: cleanArr,
    });
    setProposalState({
      type: TotalValuesProposalType.SET_ARCHITECTURE_FILE_NAMES,
      value: cleanArr,
    });
    setProposalState({
      type: TotalValuesProposalType.SET_SCENARIO_FILE_NAMES,
      value: cleanArr,
    });
    setProposalState({
      type: TotalValuesProposalType.SET_ID_FIXED,
      value: "",
    });
    console.log("state depois: ", { proposalState });
    setLoading(false);
    navigate(-1);
  };

  const getDraftItems = async () => {
    setLoading(true);
    let res;
    if (state.specialFields) {
      res = await renderSettingsProposal(state, premisseRef);
      setTimeout(() => {}, 500);
      setLoading(false);

      setDraftItems({
        ...draftItems,
        ...res,
      });
      console.log("draft 2: ", { draftItems });
    }
    setLoading(false);
    // if (
    //   Cookies.get("premisses_cookie") ||
    //   Cookies.get("extra_points_cookie") ||
    //   Cookies.get("main_factors_cookie")
    // ) {
    //   const cookiesValues = {
    //     premisses: Cookies.get("premisses_cookie"),
    //     extra_points: Cookies.get("extra_points_cookie"),
    //     main_factors: Cookies.get("main_factors_cookie"),
    //   };

    //   setDraftItems({
    //     ...draftItems,
    //     ...cookiesValues,
    //   });
    //   console.log("draft 1: ", { draftItems });
    // } else {
    //   setLoading(true);
    //   let res;
    //   if (state.specialFields) {
    //     res = await renderSettingsProposal(state, premisseRef);
    //     setTimeout(() => {}, 500);
    //     setLoading(false);

    //     setDraftItems({
    //       ...draftItems,
    //       ...res,
    //     });
    //     console.log("draft 2: ", { draftItems });
    //   }
    //   setLoading(false);
    // }
  };

  // const updateCookies = () => {
  //   setInterval(() => {
  //     let allInputFieldName = [
  //       "project_name",
  //       "client_name",
  //       "project_type",
  //       "project_status",
  //       "project_contacts",
  //       "project_architects",
  //       "project_bu",
  //       "project_opportunity",
  //     ];

  //     let challenges = draftItems.challenges;
  //     let scenarios = draftItems.scenarios;
  //     let premisses = draftItems.premisses;
  //     let extra_points = draftItems.extra_points;
  //     let architecture = draftItems.architecture;
  //     let main_factors = draftItems.main_factors;
  //     let expected_results = draftItems.expected_results;
  //     let observations = draftItems.observations;
  //     let internal_notes = draftItems.internal_notes;

  //     Cookies.set("challenges_cookie", challenges, {
  //       expires: 1,
  //     });
  //     Cookies.set("scenarios_cookie", scenarios, {
  //       expires: 1,
  //     });

  //     Cookies.set("premisses_cookie", premisses, {
  //       expires: 1,
  //     });

  //     Cookies.set("extra_points_cookie", extra_points, {
  //       expires: 1,
  //     });

  //     Cookies.set("architecture_cookie", architecture, {
  //       expires: 1,
  //     });
  //     Cookies.set("main_factors_cookie", main_factors, {
  //       expires: 1,
  //     });
  //     Cookies.set("expected_results_cookie", expected_results, {
  //       expires: 1,
  //     });
  //     Cookies.set("observations_cookie", observations, {
  //       expires: 1,
  //     });
  //     Cookies.set("internal_notes_cookie", internal_notes, {
  //       expires: 1,
  //     });

  //     allInputFieldName.map((item) => {
  //       let newValue = edit_proposal.getFieldValue(item);
  //       setCookieState({
  //         type: item,
  //         value: newValue,
  //       });
  //     });
  //   }, 30000);
  // };

  useEffect(() => {
    getDraftItems();

    dynamoGet(`${process.env.REACT_APP_STAGE}-cost-management`).then((data) => {
      const res = data.find(
        (d) => d.id === `${process.env.REACT_APP_COST_MANAGEMENT_OBJ_ID}`
      );

      setCostManagementData(res);
    });

    geTicketName(ticketInfo);
    handleLocalStorageCollapseItems();
    DisableIncrementScroll();

    setLoading(false);
  }, []);

  // useEffect(() => {
  //   const setHeaderCookie = () => {
  //     let allHeaderInputs = {
  //       projectName: cookieState.project_name,
  //       projectClient: cookieState.client_name,
  //       projectType: cookieState.project_type,
  //       projectStatus: cookieState.project_status,
  //       projectContacts: cookieState.project_contacts,
  //       projectArchitects: cookieState.project_architects,
  //       projectBUs: cookieState.project_bu,
  //       projectOpportunity: cookieState.project_opportunity,
  //     };

  //     Cookies.set("header_cookie", allHeaderInputs, { expires: 1 });
  //   };

  //   setHeaderCookie();
  // }, [cookieState]);

  useEffect(() => {
    if (initializedServicesState === false) {
      setInitializedServicesState(true);
    } else {
      handleTotalValues();
    }
  }, [services]);

  useEffect(() => {
    setProposalState({
      type: TotalValuesProposalType.SET_ID_FIXED,
      value: state.searchId,
    });
    setProposalState({
      type: TotalValuesProposalType.SET_TOTAL_VALUES,
      value: state.totalValues,
    });
    setProposalState({
      type: TotalValuesProposalType.SET_MONTH,
      value: state.monthSelected,
    });
    setProposalState({
      type: TotalValuesProposalType.SET_ARCHITECTURE_FILE_NAMES,
      value: state.architectureFileNames,
    });
    setProposalState({
      type: TotalValuesProposalType.SET_SCENARIO_FILE_NAMES,
      value: state.scenarioFileNames,
    });
    setProposalState({
      type: TotalValuesProposalType.SET_FILE_NAMES,
      value: state.fileNames,
    });
    setProposalState({
      type: TotalValuesProposalType.SET_CUSTOMER,
      value: state.customer,
    });
    setProposalState({
      type: TotalValuesProposalType.SET_STATUS,
      value: state.status,
    });
    if (state.spreadSetup) {
      setProposalState({
        type: TotalValuesProposalType.SET_SPREAD_SETUP,
        value: state.spreadSetup,
      });
    }
    if (state.awsCosts) {
      edit_proposal.setFieldValue("main_aws_investments", state.awsCosts);
    }
    if (state.specialFields) {
      const stateValue = state.specialFields.find(
        (i) => i.name === "Custos adicionais"
      );
      edit_proposal.setFieldValue("aditional_costs", stateValue.value);
    }
    if (state.mainOportunity) {
      setProposalState({
        type: TotalValuesProposalType.SET_SELECTED_OPORTUNITY,
        value: state.mainOportunity,
      });
    }
  }, [state]);

  console.log({ proposalState }, { state });

  return (
    <Layout style={{ minHeight: "100vh" }}>
      <HeaderMenu collapsed={collapsed} setCollapsed={setCollapsed} />
      <Layout>
        <SideMenu collapsed={collapsed} />
        <Content style={{ padding: "2em" }}>
          <Card
            style={{
              boxShadow: "0 0 10px rgba(0,0,0,0.1)",
              borderRadius: "20px",
            }}
            className="technicalProposalCard"
          >
            <Popconfirm
              title="Ao voltar você perderá os dados do formulário, tem certeza?"
              onConfirm={() => {
                navigate(-1);
                localStorage.setItem("technical-proposal/services", "");
                // localStorage.setItem("technical-proposal/form", "");
                localStorage.setItem("technical-proposal/customer", "");
                localStorage.setItem("technical-proposal/fileList", "");
                localStorage.setItem(
                  "technical-proposal/architectureFileList",
                  ""
                );
                localStorage.setItem("technical-proposal/main-contact", "");
                localStorage.setItem("CollapseValidator", "");
              }}
              okText="Voltar"
              cancelText="Não"
            >
              <Button type="text" style={{ marginBottom: "15px" }}>
                <ArrowLeftOutlined />
                Editar proposta
              </Button>
            </Popconfirm>
            <Row justify="center">
              <Col span={24}>
                <Card
                  style={{
                    backgroundColor: "#f4f4f4",
                    boxShadow: "0px 4px 4px rgba(0, 0, 0, .5)",
                    borderRadius: "5px",
                  }}
                >
                  <Form
                    Layout="vertical"
                    style={{ marginBottom: "2rem" }}
                    form={edit_proposal}
                    // initialValues={{
                    //   main_aws_investments: state?.awsCosts,
                    //   aditional_costs:
                    //     state?.specialFields[6]?.value !== undefined
                    //       ? state?.specialFields[6].value
                    //       : [],
                    //   proposal_challenge: state?.specialFields[0]?.value,
                    //   proposal_scenario: state?.specialFields[1]?.value,
                    //   proposal_premisses: state?.specialFields[2]?.value,
                    //   proposal_extra_points: state?.specialFields[3]?.value,
                    //   proposal_architecture: state?.specialFields[4]?.value,
                    //   proposal_main_factors: state?.specialFields[5]?.value,
                    //   proposal_expected_results: state?.specialFields[7]?.value,
                    //   proposal_observations: state?.specialFields[8]?.value,
                    //   proposal_internal_notes: state?.specialFields[9]?.value,
                    // }}
                    onFinish={() => {
                      Submit();
                    }}
                  >
                    <ProposalHeader
                      currentContacts={currentContacts}
                      setCurrentContacts={(e) => setCurrentContacts(e)}
                      mainForm={edit_proposal}
                      setBrand={(image) => setCustomerBrand(image)}
                      recongnizingHeaderChange={recongnizingHeaderChange}
                      setRecongnizingHeaderChange={(e) =>
                        setRecongnizingHeaderChange(e)
                      }
                    />

                    {collapseContent.map((collapseItem, collapseItemKey) => (
                      <Row>
                        <Col span={24} key={collapseItemKey}>
                          <MainCollapse
                            title={collapseItem.name}
                            editor={collapseItem.content}
                          ></MainCollapse>
                        </Col>
                      </Row>
                    ))}
                  </Form>

                  <Row justify="space-between">
                    <ProposalsCards hourClass="hourValue" />
                  </Row>
                </Card>
              </Col>
            </Row>
            <TableProposals
              state={[
                services,
                edit_proposal.getFieldValue("client_name"),
                "technical",
              ]}
              setServices={(services) => setServices(services)}
            />
            <Row justify="space-between">
              <Col>
                <AddDocument />
              </Col>
              <Col>
                <ConfirmTicket
                  loading={loading}
                  ticketInfo={ticketInfo}
                  ticketName={ticketName}
                  setTicketInfo={(ticketInfo) => setTicketInfo(ticketInfo)}
                  submitFunction={() => Submit()}
                />
              </Col>
            </Row>
          </Card>
        </Content>
      </Layout>
    </Layout>
  );
};
