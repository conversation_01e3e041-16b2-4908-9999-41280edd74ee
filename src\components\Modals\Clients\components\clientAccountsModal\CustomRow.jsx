import { Tooltip } from "antd";

const currentTooltipTitle = (selectedAccounts, currentUserAccesses, props) => {
  let tooltipTitle = "";
  if (
    selectedAccounts?.length >= 10 ||
    currentUserAccesses?.length + selectedAccounts?.length >= 10
  ) {
    tooltipTitle =
      "Limite de 10 acessos atingido caso você necessite do acesso a esta conta, remova algum acesso já solicitado";
  }
  if (
    currentUserAccesses?.find((e) => e.account_id === props["data-row-key"])
  ) {
    tooltipTitle = "Você já solicitou acesso para esta conta";
  }
  return tooltipTitle;
};

export const CustomRow = ({ props, selectedAccounts, currentUserAccesses }) => {
  return (
    <Tooltip
      style={{
        width: "5em",
      }}
      title={currentTooltipTitle(selectedAccounts, currentUserAccesses, props)}
    >
      <tr {...props} />
    </Tooltip>
  );
};
