export function filterBySearch(data, search, actionState, dateRange) {
  let filteredData = data;

  if (search !== "") {
    const searchLower = search.toLowerCase();

    filteredData = filteredData.filter((item) => {
      if (
        item.name &&
        item.name.toString().toLowerCase().includes(searchLower)
      ) {
        return true;
      }

      if (
        item.type &&
        item.type.toString().toLowerCase().includes(searchLower)
      ) {
        return true;
      }

      if (
        item.customerName &&
        item.customerName.toLowerCase().includes(searchLower)
      ) {
        return true;
      }

      for (let i = 0; i < item.tags.length; i++) {
        if (item.tags[i].toLocaleLowerCase().includes(searchLower)) {
          return true;
        }
      }

      return false;
    });
  }

  return filteredData || [];
}
