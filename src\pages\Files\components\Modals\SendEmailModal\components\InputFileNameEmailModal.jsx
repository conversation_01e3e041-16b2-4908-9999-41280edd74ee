import { useSelector, shallowEqual } from "react-redux";

import { Col, Row, Typography, Input } from "antd";
import { setSelectedFileFieldState } from "../../../../../../store/actions/files-action";

const { Text } = Typography;
export const InputFileNameEmailModal = () => {
  const fileName = useSelector(
    (state) => state.files.selectedFile.name,
    shallowEqual
  );

  return (
    <Col span={24}>
      <Row>
        <Text>Nome do arquivo: </Text>
      </Row>
      <Row>
        <Input
          onChange={(e) => {
            setSelectedFileFieldState({ field: "name", value: e.target.value });
          }}
          value={fileName}
          style={{ width: "100%" }}
          status={fileName ? "" : "error"}
        />
        {!fileName ? (
          <Text type={"danger"}>Preenchimento Obrigatório</Text>
        ) : null}
      </Row>
    </Col>
  );
};
