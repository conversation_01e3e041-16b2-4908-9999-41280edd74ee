import React, { useMemo, useState } from "react";
import { BsBoxArrowRight } from "react-icons/bs";
import {
  Row,
  Col,
  Typography,
  Input,
  Modal,
  But<PERSON>,
  DatePicker,
  Divider,
  Tabs,
} from "antd";
import { LoadingOutlined } from "@ant-design/icons";
import * as controller from "../../../pages/Billing/controllers/complianceControllers";
import moment from "moment";
import { filterTableData } from "../../../utils/filterTableData";
import { SearchInput } from "../../SearchInput";

export const DetailsBilling = (props) => {
  const { contract, client } = props;
  const [show, setShow] = useState(false);
  const [billing, setBilling] = useState([]);
  const [loading, setLoading] = useState(false);
  const [currenTab, setCurrentTab] = useState("1");
  const { Text } = Typography;

  const showModal = async () => {
    setShow(true);
  };

  const handleOk = () => {
    setShow(false);
  };

  const filteredBilling = useMemo(() => {
    let billingData = billing || [];
    billingData = billingData?.filter((e) => {
      return (
        e?.accounts.length > 0 &&
        client?.accounts?.find((a) => a.account_id === e?.payer_account)
      );
    });
    return billingData;
  }, [billing]);

  const invoicesTabs = [
    {
      key: "1",
      label: "Agrupado por PA",
      children: (
        <DetailsBilingList
          billingList={filteredBilling}
          client={client}
          contract={contract}
        />
      ),
    },
    {
      key: "2",
      label: "Visão Geral",
      children: (
        <DetailsBilingListByAccount
          billingList={filteredBilling}
          client={client}
          contract={contract}
        />
      ),
    },
  ];

  return (
    <>
      <Button
        type="text"
        onClick={async () => {
          await showModal();
        }}
      >
        <BsBoxArrowRight />
      </Button>
      <Modal
        width="60vw"
        title="Invoices"
        open={show}
        onCancel={handleOk}
        footer={[
          <Button
            type="submit"
            onClick={() => {
              handleOk();
            }}
          >
            Fechar
          </Button>,
        ]}
      >
        <>
          <Row
            style={{ marginBottom: 20 }}
            justify="space-between"
            align="middle"
          >
            <Col span={24}>
              <Input.Group>
                <DatePicker
                  style={{ width: "100%" }}
                  bordered={false}
                  placeholder="Selecione o mês para analisar"
                  picker="month"
                  onChange={async (d) => {
                    await controller.handleDateSelection(
                      moment(d).format("MM-YYYY"),
                      setBilling,
                      setLoading
                    );
                  }}
                />
              </Input.Group>
            </Col>
          </Row>
          {loading ? (
            <Row justify="center">
              <LoadingOutlined />
            </Row>
          ) : filteredBilling?.length ? (
            <Tabs
              defaultActiveKey="1"
              items={invoicesTabs}
              onChange={(key) => {
                setCurrentTab(key);
              }}
            />
          ) : (
            <Text>Não há billing registrado para este mês.</Text>
          )}
        </>
      </Modal>
    </>
  );
};

const DetailsBilingList = (props) => {
  const { billingList, client, contract } = props;

  return (
    <>
      <ContentBillingItem
        accounts={billingList}
        client={client}
        contract={contract}
      />
    </>
  );
};

const DetailsBilingListByAccount = (props) => {
  const { billingList, client, contract } = props;
  const [search, setSearch] = useState("");

  const groupBySubAccount = (data) => {
    let groupedValues = [];
    data.forEach((item) => {
      let existingGroup = groupedValues.find(
        (group) => group.sub_account === item.sub_account
      );
      if (!existingGroup) {
        groupedValues.push({
          payer_account_id: item.payer_account_id,
          sub_account: item.sub_account,
          cost: item.cost,
          bundled_discount: item.bundled_discount,
          credit: item.credit,
          edp_discount: item.edp_discount,
          spp_discount: item.spp_discount,
          savingPlans: item.savingPlans,
          total_discount: item.total_discount,
        });
      } else {
        existingGroup.cost += item.cost;
        existingGroup.bundled_discount += item.bundled_discount;
        existingGroup.credit += item.credit;
        existingGroup.edp_discount += item.edp_discount;
        existingGroup.spp_discount += item.spp_discount;
        existingGroup.savingPlans += item.savingPlans;
        existingGroup.total_discount += item.total_discount;
      }
    });
    return groupedValues;
  };

  const filteredAccounts = useMemo(() => {
    let accounts = [];
    billingList?.map((billing, index) => {
      accounts.push(...billing.accounts);
    });

    accounts = groupBySubAccount(accounts);
    const searchFields = ["sub_account", "payer_account_id"];
    accounts = filterTableData({ searchFields, search, data: accounts });
    return accounts;
  }, [billingList, search]);

  return (
    <>
      <SearchInput
        placeholder="Pesquisar"
        onChange={(e) => {
          setSearch(e);
        }}
      />
      <ContentBillingItem
        accounts={filteredAccounts}
        client={client}
        contract={contract}
      />
    </>
  );
};

const ContentBillingItem = (props) => {
  const { accounts, client, contract } = props;
  const { Text } = Typography;

  return (
    <Row justify="center">
      {accounts?.map((account, index) => {
        return (
          <Col span={12} key={index}>
            <Divider />
            <Row>
              <Col>
                <Text style={{ fontWeight: "700" }}>Cliente: </Text>
                <Text>
                  {client?.names?.name || client?.names?.fantasy_name}
                </Text>
              </Col>
            </Row>
            <Row>
              <Col>
                <Text style={{ fontWeight: "700" }}>Título do Contrato: </Text>
                <Text>{contract?.name}</Text>
              </Col>
            </Row>
            <Row>
              <Col>
                <Text style={{ fontWeight: "700" }}>Conta: </Text>
                {
                  <Text>
                    {account?.payer_account
                      ? account?.payer_account
                      : account?.sub_account
                      ? account?.sub_account
                      : account?.account_id}
                  </Text>
                }
              </Col>
            </Row>
            <Row>
              <Col>
                <Text style={{ fontWeight: "700" }}>Total: </Text>
                <Text>
                  ${" "}
                  {account?.total_pos
                    ? parseFloat(
                        account?.total_pos + account?.total_neg
                      ).toFixed(2)
                    : account?.cost
                    ? parseFloat(account?.cost).toFixed(2)
                    : "0.00"}
                </Text>
              </Col>
            </Row>
            <Row>
              <Col>
                <Text style={{ fontWeight: "700" }}>Saving Plan: </Text>
                <Text>
                  $ -
                  {account?.total_saving_plans
                    ? Math.abs(account?.total_saving_plans).toFixed(2)
                    : account?.savingPlans
                    ? Math.abs(account?.savingPlans).toFixed(2)
                    : "0.00"}
                </Text>
              </Col>
            </Row>
            <Row>
              <Col>
                <Text style={{ fontWeight: "700" }}>Bundled Discount: </Text>
                <Text>
                  $ -
                  {account?.total_bundled_discount
                    ? Math.abs(account?.total_bundled_discount).toFixed(2)
                    : account?.bundled_discount
                    ? Math.abs(account?.bundled_discount).toFixed(2)
                    : "0.00"}
                </Text>
              </Col>
            </Row>
            <Row>
              <Col>
                <Text style={{ fontWeight: "700" }}>SPP: </Text>
                <Text>
                  $ -
                  {account?.total_spp_discount
                    ? Math.abs(account?.total_spp_discount).toFixed(2)
                    : account?.spp_discount
                    ? Math.abs(account?.spp_discount).toFixed(2)
                    : "0.00"}
                </Text>
              </Col>
            </Row>
            <Row>
              <Col>
                <Text style={{ fontWeight: "700" }}>EDP: </Text>
                <Text>
                  $ -
                  {account?.total_edp_discount
                    ? Math.abs(account?.total_edp_discount).toFixed(2)
                    : account?.edp_discount
                    ? Math.abs(account?.edp_discount).toFixed(2)
                    : "0.00"}
                </Text>
              </Col>
            </Row>
            <Row>
              <Col>
                <Text style={{ fontWeight: "700" }}>Créditos: </Text>
                <Text>
                  $ -
                  {account?.total_credit
                    ? Math.abs(account?.total_credit).toFixed(2)
                    : account?.credit
                    ? Math.abs(account?.credit).toFixed(2)
                    : "0.00"}
                </Text>
              </Col>
            </Row>
            {!account?.payer_account ? (
              <Row>
                <Col>
                  <Text style={{ fontWeight: "700" }}>Payer Account: </Text>
                  <Text>{account?.payer_account_id}</Text>
                </Col>
              </Row>
            ) : null}
          </Col>
        );
      })}
    </Row>
  );
};
