import { DeleteOutlined, EditOutlined, CheckOutlined } from "@ant-design/icons";
import { useEffect, useState } from "react";
import {
  Button,
  Modal,
  Row,
  Col,
  Input,
  Typography,
  Form,
  message,
} from "antd";
import { dynamoGet, dynamoPut } from "../../../service/apiDsmDynamo";
import { logNewAuditAction } from "../../../controllers/audit/logNewAuditAction";

export const EditPaymentMethod = (paymentEditParent) => {
  const { Title } = Typography;
  const [paymentMethodForm] = Form.useForm();
  const [showModal, setShowModal] = useState();
  const { paymentEdit, setPaymentEdit } = paymentEditParent;
  const [childPaymentMethod, setChildPaymentMethod] = useState([]);

  useEffect(() => {
    setChildPaymentMethod(paymentEditParent.paymentEditParent);
  }, [paymentEditParent.paymentEditParent]);

  const handleSubmit = (e) => {
    e.preventDefault();
    setPaymentEdit(childPaymentMethod);

    const prev = JSON.parse(localStorage.getItem("paymentMethods"));
    const i = prev.map((obj) => obj.id).indexOf(childPaymentMethod.id);
    prev[i] = {
      id: childPaymentMethod.id,
      active: childPaymentMethod.active,
      name: childPaymentMethod.name,
      description: childPaymentMethod.description,
    };
    localStorage.setItem("paymentMethodsUpdated", JSON.stringify(prev));

    try {
      dynamoGet(`${process.env.REACT_APP_STAGE}-cost-management`)
        .then(async (data) => {
          try {
            dynamoPut(
              `${process.env.REACT_APP_STAGE}-cost-management`,
              data[0].id,
              {
                paymentMethods: prev,
              }
            );
            const username = localStorage.getItem("@dsm/username");
            const title = "Edição de Forma de Pagamento";
            const description = `${username} editou a forma de pagamento: ${childPaymentMethod.name}`;
            logNewAuditAction(username, title, description);
          } catch (err) {
            console.log("err: ", err);
            message.error("Erro ao alterar Gerenciamento de Custos!");
          }
        })
        .catch((err) => {
          console.log("Error on getting paymentMethods from dynamoDB: ", err);
        });
    } catch (err) {
      console.log("err: ", err);
    }

    setShowModal(false);
  };

  const handleCloseModal = () => {
    setShowModal(false);
  };

  return (
    <>
      <Button
        type="text"
        onClick={() => {
          setChildPaymentMethod(paymentEditParent.paymentEditParent);
          setShowModal(true);
        }}
        icon={<EditOutlined />}
      ></Button>
      <Modal
        title="Editar Forma de Pagamento"
        visible={showModal}
        width={"80%"}
        closable={false}
        destroyOnClose={true}
        footer={null}
        onCancel={() => {
          setShowModal(false);
        }}
      >
        <Form
          layout="horizontal"
          form={paymentMethodForm}
          onFinish={handleSubmit}
        >
          <Row gutter={[16, 16]} justify="center">
            <Col span={24}>
              <Title level={5} style={{ fontWeight: "400", fontSize: "14px" }}>
                Nome
              </Title>
              <Form.Item>
                <Input
                  required
                  defaultValue={childPaymentMethod?.name}
                  onChange={(e) => {
                    setChildPaymentMethod({
                      ...childPaymentMethod,
                      name: e.target.value,
                    });
                  }}
                />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={[16, 16]} justify="center">
            <Col span={24}>
              <Title level={5} style={{ fontWeight: "400", fontSize: "14px" }}>
                Descrição
              </Title>
              <Input
                required
                defaultValue={childPaymentMethod?.description}
                onChange={(e) => {
                  setChildPaymentMethod({
                    ...childPaymentMethod,
                    description: e.target.value,
                  });
                }}
              />
            </Col>
          </Row>
        </Form>

        <Row
          gutter={[16, 16]}
          justify={"space-between"}
          style={{ marginTop: "5rem" }}
        >
          <Button type="text-color" onClick={handleCloseModal}>
            Cancelar
          </Button>
          <Button
            type="primary"
            onClick={handleSubmit}
            disabled={
              !childPaymentMethod.name || !childPaymentMethod.description
            }
          >
            Salvar <CheckOutlined />
          </Button>
        </Row>
      </Modal>
    </>
  );
};
