import "moment/locale/pt-br";
import locale from "antd/es/date-picker/locale/pt_BR";
import { useEffect, useState } from "react";
import { DatePicker, Typography, Col, Space } from "antd";
import { setContractFieldState } from "../../../store/actions/contract-action";

export const DateFilters = () => {
  const [date, setDate] = useState({ dtStart: null, dtEnd: null });
  const { RangePicker } = DatePicker;
  const { Text } = Typography;

  useEffect(() => {
    setContractFieldState({ field: "dtStart", value: date.dtStart });
    setContractFieldState({ field: "dtEnd", value: date.dtEnd });
  }, [date]);

  const handleOnChange = (moment) => {
    if (moment !== null) {
      setDate({ dtStart: moment[0], dtEnd: moment[1] });
    } else {
      setDate({ dtStart: null, dtEnd: null });
    }
  };

  return (
    <Col span={7}>
      <Text>Filtrar por período:</Text>
      <RangePicker
        picker="month"
        placeholder={["Inicio", "Fim"]}
        defaultValue={[null, null]}
        locale={locale}
        style={{
          height: "32px",
          width: "100%",
        }}
        onChange={(moment) => {
          handleOnChange(moment);
        }}
      />
    </Col>
  );
};
