.table-container {
    position: relative;
    margin-bottom: 20px;

    .table-row-options-container {
        display: flex!important;
        transition: 0.7s;
        justify-content: center;
        align-items: center;
        margin-top: 6px;

        span {
            width: 25px;
            height: 25px;
            display: flex!important;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            transition: 0.4s;

            &:hover{
                opacity: 0.7;
            }
        }
    }
}

.row-item {
    position: relative;
    background-color: white;
    display: flex!important;

    td {
        flex: 1;
    }

    .table-options-container {
        position: absolute;
        display: flex!important;
        right: -20px; 
        transition: 0.7s;
        opacity: 0;
        z-index: -3;
        height: 100%;
        justify-content: center;
        align-items: center;

        span {
            position: relative;
            width: 25px;
            height: 25px;
            display: flex!important;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            transition: 0.4s;

            &:hover{
                opacity: 0.7;
            }
        }
    }
}

.row-item:hover {
    .table-options-container {
        right: -75px;
        opacity: 1;
        z-index: 1;
    }
}