import { produce } from "immer";

export const TotalValuesProposalType = {
  SET_TOTAL_VALUES: "SET_TOTAL_VALUES",
  SET_DISCOUNT_UNIT: "SET_DISCOUNT_UNIT",
  SET_DISCOUNT_VALUE: "SET_DISCOUNT_VALUE",
  SET_MONTH: "SET_MONTH",
  SET_GREATEST_MONTH: "SET_GREATEST_MONTH",
  SET_SPREAD_SETUP: "SET_SPREAD_SETUP",
  SET_FILE_NAMES: "SET_FILE_NAMES",
  SET_ARCHITECTURE_FILE_NAMES: "SET_ARCHITECTURE_FILE_NAMES",
  SET_SCENARIO_FILE_NAMES: "SET_SCENARIO_FILE_NAMES",
  SET_ID_FIXED: "SET_ID_FIXED",
  SET_CUSTOMER: "SET_CUSTOMER",
  SET_OPORTUNITIES: "SET_OPORTUNITIES",
  SET_STATUS: "SET_STATUS",
  SET_PAYMENT_METHOD: "SET_PAYMENT_METHOD",
  SET_SELECTED_OPORTUNITY: "SET_SELECTED_OPORTUNITY",
  SET_PIPEDRIVE_DEAL_OWNER: "SET_PIPEDRIVE_DEAL_OWNER",
};

export const reducer = (state, action) => {
  switch (action.type) {
    case TotalValuesProposalType.SET_TOTAL_VALUES:
      return {
        ...state,
        totalValues: action.value,
      };
    case TotalValuesProposalType.SET_PAYMENT_METHOD:
      return {
        ...state,
        paymentMethod: action.value,
      };
    case TotalValuesProposalType.SET_PIPEDRIVE_DEAL_OWNER:
      return {
        ...state,
        pipedriveDealOwner: action.value,
      };
    case TotalValuesProposalType.SET_MONTH:
      return {
        ...state,
        month: action.value,
      };
    case TotalValuesProposalType.SET_GREATEST_MONTH:
      return {
        ...state,
        greatestMonth: action.value,
      };
    case TotalValuesProposalType.SET_SPREAD_SETUP:
      return {
        ...state,
        spreadSetup: action.value,
      };
    case TotalValuesProposalType.SET_FILE_NAMES:
      return {
        ...state,
        fileNames: action.value,
      };
    case TotalValuesProposalType.SET_ARCHITECTURE_FILE_NAMES:
      return {
        ...state,
        architectureFileNames: action.value,
      };
    case TotalValuesProposalType.SET_SCENARIO_FILE_NAMES:
      return {
        ...state,
        scenarioFileNames: action.value,
      };
    case TotalValuesProposalType.SET_ID_FIXED:
      return {
        ...state,
        idFixed: action.value,
      };
    case TotalValuesProposalType.SET_CUSTOMER:
      return {
        ...state,
        customer: action.value,
      };
    case TotalValuesProposalType.SET_STATUS:
      return {
        ...state,
        status: action.value,
      };
    case TotalValuesProposalType.SET_OPORTUNITIES:
      return {
        ...state,
        oportunities: action.value,
      };
    case TotalValuesProposalType.SET_SELECTED_OPORTUNITY:
      return {
        ...state,
        selectedOportunity: action.value,
      };
    case TotalValuesProposalType.SET_DISCOUNT_UNIT:
      return produce(state, (draft) => {
        const objectHourValue = draft?.totalValues?.find(
          (i) => i.hourClass === action?.hourClass
        );
        const arrayToUpdate = objectHourValue?.values?.find(
          (i) => i.month === state.month
        );
        const objectToUpdate = arrayToUpdate?.values?.find(
          (i) => i.formName === action?.formName
        );
        if (objectToUpdate) {
          objectToUpdate.discountUnit = action?.discountUnit;
        }
      });
    case TotalValuesProposalType.SET_DISCOUNT_VALUE:
      return produce(state, (draft) => {
        const objectHourValue = draft?.totalValues?.find(
          (i) => i.hourClass === action?.hourClass
        );
        const arrayToUpdate = objectHourValue?.values?.find(
          (i) => i.month === state.month
        );
        const objectToUpdate = arrayToUpdate?.values?.find(
          (i) => i.formName === action?.formName
        );
        if (objectToUpdate) {
          objectToUpdate.discountValue = Number(action?.discountValue);
        }
      });
  }
};
