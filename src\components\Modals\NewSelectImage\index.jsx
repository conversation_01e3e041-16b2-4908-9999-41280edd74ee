import { useState, useRef, useEffect } from "react";
import { shallowEqual, useSelector } from "react-redux";
import { Spin } from "antd";
import { CameraOutlined } from "@ant-design/icons";

import styledSheet from "./style.module.scss";
import { apiProposals, getHeader } from "../../utils/api";

export const SelectImage = () => {
  const clientName = useSelector(
    (state) => state.technicalProposal.clientName,
    shallowEqual
  );

  const imageRef = useRef(null);
  const inputImageRef = useRef(null);
  const [loading, setLoading] = useState(false);
  const [image, setImage] = useState("");

  useEffect(() => {
    if (clientName) getCustomerBrand(clientName);
  }, [clientName]);

  function getCustomerBrand(customerName) {
    try {
      const headers = getHeader();
      setLoading(true);

      apiProposals
        .get(`/brand/${customerName}`, { headers })
        .then((response) => {
          const { url } = response.data;
          setImage(url);
        })
        .catch((error) => {
          console.log(error);
          setImage("");
        });
    } finally {
      setLoading(false);
    }
  }

  async function saveCustomerBrand(brandImage) {
    const headers = getHeader();
    if (brandImage && clientName) {
      let formData = new FormData();
      formData.append("image", brandImage);

      const name = clientName.replace(/\/|\\/gm, "-");

      await apiProposals
        .put(`/brand/${name}`, formData, { headers })
        .catch((error) => {
          console.log(error);
        });
    }
  }

  function selectNewBackgroundImage() {
    if (inputImageRef.current) inputImageRef.current.click();
  }

  async function changeBackgroundImage() {
    try {
      if (inputImageRef.current) {
        const { files } = inputImageRef.current;

        if (imageRef.current) imageRef.current.src = "";

        if (files) {
          const file = files[0];

          setLoading(true);
          const image = new Image();
          image.onload = async () => {
            const canvas = document.createElement("canvas");

            canvas.width = image.naturalWidth;
            canvas.height = image.naturalHeight;
            canvas.getContext("2d").drawImage(image, 0, 0);

            const blob = await resizeImage(canvas);

            const imageURL = new File([blob], "my-new-name.webp", {
              type: blob.type,
            });
            var reader = new FileReader();
            reader.readAsDataURL(imageURL);
            reader.onload = function () {
              if (imageRef.current) imageRef.current.src = reader.result;

              setImage(reader.result);
              saveCustomerBrand(reader.result);

              setLoading(false);
            };
          };
          image.src = URL.createObjectURL(file);
        }
      }
    } catch (error) {
      setLoading(false);
    }
  }

  async function resizeImage(canvas) {
    let blob = "";
    let maxSize = 100000;
    let imageQuality = 1;

    blob = await new Promise((resolve) => {
      canvas.toBlob((blob) => {
        resolve(blob);
      }, "image/webp");
    });

    if (blob.size > maxSize) {
      while (blob.size > maxSize && imageQuality > 0) {
        imageQuality = imageQuality - 0.1;
        blob = await new Promise((resolve) => {
          canvas.toBlob(
            (blob) => {
              resolve(blob);
            },
            "image/webp",
            imageQuality
          );
        });
      }
    }

    return blob;
  }

  return (
    <div>
      {image ? null : (
        <>
          <span className={styledSheet["no-image-text"]}>
            Selecione a logo do cliente <br /> (tamanho máx: 10MB)
          </span>
        </>
      )}

      <div
        className={styledSheet["image-containt"]}
        onClick={selectNewBackgroundImage}
      >
        <img ref={imageRef} className={styledSheet["image"]} src={image} />

        <CameraOutlined className={styledSheet["icon-camera"]} />
      </div>

      {loading ? (
        <div
          style={{
            position: "absolute",
            width: "100%",
            height: "100%",
            top: 0,
            justifyContent: "center",
            alignItems: "center",
            display: "flex",
            flexDirection: "column",
          }}
        >
          <Spin />
          <label>Carregando . . .</label>
        </div>
      ) : null}

      <div className={styledSheet["image-container"]}>
        <input
          type="file"
          accept="image/*"
          ref={inputImageRef}
          onChange={changeBackgroundImage}
        />
      </div>
    </div>
  );
};
