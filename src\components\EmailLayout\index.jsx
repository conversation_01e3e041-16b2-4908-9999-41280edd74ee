import { useContext } from 'react'
import { PlusOutlined } from '@ant-design/icons'
import { Row, Button } from 'antd'

import { EmailHeader } from "./EmailHeader"
import { EmailFooter } from "./EmailFooter"
import { EmailAutomationContext } from '../../contexts/email-automation'

import { EmailItem } from './EmailItem'
import { EmailTable } from './EmailTable'
import { EmailTimeline } from './EmailTimeline'

import styledSheet from './style.module.scss'

const EmailLayout = ({ children, onAddText, onAddTimeline, onAddTable }) => {
    const { emailAutomationState } = useContext(EmailAutomationContext)
    const { showFooter, showHeader } = emailAutomationState

    return (
        <div id="template-email-automation">
            <table
                style={{
                    margin: 'auto'
                }}
                cellPadding="0"
                cellSpacing="0"
                align="center"
                width="600"
            >
                <tbody>
                    <tr>
                        <td>

                            {
                                showHeader ?
                                    <EmailHeader />
                                    :
                                    null
                            }

                            <br />

                            <table border={0} cellPadding="0" cellSpacing="0" width="100%">
                                <tbody>
                                    <tr>
                                        <td
                                            style={{
                                                color: '#4a423c',
                                                fontFamily: 'arial, helvetica, sans-serif',
                                                fontSize: 16
                                            }}
                                        >
                                            {children}
                                        </td>
                                    </tr>
                                </tbody>
                            </table>

                            <Row 
                                style={{ justifyContent: 'space-between' }} 
                                className={styledSheet['buttons-container']}
                            >
                                <Button
                                    type="primary"
                                    onClick={onAddText}
                                    style={{ display: 'none' }}
                                >
                                    <PlusOutlined />
                                    Texto
                                </Button>

                                <Button
                                    type="primary"
                                    onClick={onAddTimeline}
                                    style={{ display: 'none' }}
                                >
                                    <PlusOutlined />
                                    Timeline
                                </Button>

                                <Button
                                    type="primary"
                                    onClick={onAddTable}
                                    style={{ display: 'none' }}
                                >
                                    <PlusOutlined />
                                    Tabela
                                </Button>
                            </Row>

                            <br />

                            {
                                showFooter ?
                                    <EmailFooter />
                                    :
                                    null
                            }
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    )
}

EmailLayout.Item = EmailItem;
EmailLayout.Table = EmailTable;
EmailLayout.Timeline = EmailTimeline;

export { EmailLayout }