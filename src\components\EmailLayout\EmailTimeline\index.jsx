import styledSheet from './style.module.scss'
import { useContext, useState } from 'react'

import { Tag, Tooltip } from 'antd'
import { PlusOutlined } from '@ant-design/icons'

import { EmailAutomationContext } from '../../../contexts/email-automation'
import { EmailAutomationReducerType } from '../../../contexts/reducers/email-automation-reducer'
import { useToggle } from '../../../hooks/useToggle'

import { TimelineItem } from './timeline-item'
import { CreateTimelineItemModal } from '../../Modals/EmailAutomation/CreateTimelinetemModal'

export const TimelineColors = {
    TAG: '#018f69'
}

export const EmailTimeline = ({ items, index }) => {
    const [isOpen, toggle] = useToggle()
    const { emailAutomationState, setEmailAutomationState } = useContext(EmailAutomationContext)
    const { templateList } = emailAutomationState
    const [selectedItemTimeline, setSelectedItemTimeline] = useState(null)

    function onCloseModal() {
        setSelectedItemTimeline(null)
        toggle()
    }

    function onConfirmModal({ description, startDate, tag, position }) {
        let timelineData = generateTimelineData()
        timelineData.text = description
        timelineData.date = startDate
        timelineData.category.tag = tag
        timelineData.category.color = TimelineColors.TAG

        const newTemplateListStr = JSON.stringify(templateList)
        const newTemplateList = JSON.parse(newTemplateListStr)

        if (position !== null) {
            newTemplateList[index].value[position] = timelineData
        } else {
            newTemplateList[index].value.push(timelineData)
        }

        setEmailAutomationState({
            type: EmailAutomationReducerType.SET_TEMPLATE_LIST,
            value: newTemplateList
        })

        onCloseModal()

    }

    function generateTimelineData() {
        return {
            text: '',
            date: '',
            category: {
                tag: '',
                color: ''
            },
            position: null
        }
    }

    function remove(positionItem) {
        const newTemplateListStr = JSON.stringify(templateList)
        let newTemplateList = JSON.parse(newTemplateListStr)

        let timeline = newTemplateList[index]
        let timelineItems = timeline.value
        timelineItems = timelineItems.filter((item, index) => index !== positionItem)

        if (timelineItems.length === 0) {
            delete newTemplateList[index]
        } else {
            timeline.value = timelineItems
            newTemplateList[index] = timeline
        }

        setEmailAutomationState({
            type: EmailAutomationReducerType.SET_TEMPLATE_LIST,
            value: newTemplateList.filter(template => template !== null)
        })
    }

    function onEditItem(positionItem, data) {
        setSelectedItemTimeline({
            ...data,
            position: positionItem
        })

        toggle()
    }

    return (
        <div className={styledSheet['timeline-container']} id={`timeline-${index}`}>
            {
                items.map((item, index) =>
                    <TimelineItem
                        data={item}
                        key={index}
                        onRemoveItem={() => remove(index)}
                        onEditItem={() => onEditItem(index, item)}
                    />
                )
            }

            <div className={styledSheet['timeline-add-button']}>
                <Tooltip title="Adicionar Item" >
                    <Tag color="green" onClick={toggle}>
                        <PlusOutlined />
                    </Tag>
                </Tooltip>
            </div>

            {
                isOpen ?
                    <CreateTimelineItemModal
                        isOpen={isOpen}
                        toggle={onCloseModal}
                        onConfirm={onConfirmModal}
                        data={selectedItemTimeline}
                    />
                    :
                    null
            }

        </div>
    )
}