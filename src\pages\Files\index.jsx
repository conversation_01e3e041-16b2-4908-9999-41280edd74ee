import React, { useEffect, useState } from "react";
import { Col, Layout, Row } from "antd";

import { FilesCard } from "./components/FilesCard";
import { SideMenu } from "../../components/SideMenu";
import { HeaderMenu } from "../../components/HeaderMenu";
import { UploadFilesCardNew } from "./components/UploadFilesCardNew";

import { cleanAllState } from "../../store/actions/files-action";

import * as controller from "../../controllers/files/filesController";

export const Files = () => {
  const { Content } = Layout;
  const [collapsed, setCollapsed] = useState(false);

  useEffect(() => {
    window.scrollTo(0, 0);
    controller.getCustomers();

    return () => {
      cleanAllState();
    };
  }, []);

  return (
    <Layout style={{ minHeight: "100vh" }}>
      <HeaderMenu collapsed={collapsed} setCollapsed={setCollapsed} />
      <Layout>
        <SideMenu collapsed={collapsed} />
        <Content style={{ padding: "2em" }}>
          <Row gutter={[8, 16]}>
            <Col span={24}>
              <UploadFilesCardNew />
            </Col>
            <Col span={24}>
              <FilesCard />
            </Col>
          </Row>
        </Content>
      </Layout>
    </Layout>
  );
};
