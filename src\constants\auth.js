/**
 * Constantes relacionadas à autenticação
 */

// Status de autenticação
export const AUTH_STATUS = {
  LOADING: 'loading',
  AUTHENTICATED: 'authenticated',
  UNAUTHENTICATED: 'unauthenticated',
  ERROR: 'error',
  INITIALIZING: 'initializing',
  REFRESHING: 'refreshing'
};

// Rotas de autenticação
export const AUTH_ROUTES = {
  LOGIN: '/login',
  LOGOUT: '/logoff',
  MFA: '/mfa',
  HOME: '/',
  UNAUTHORIZED: '/unauthorized',
  FORBIDDEN: '/forbidden'
};

// Erros de autenticação
export const AUTH_ERRORS = {
  INVALID_TOKEN: 'Token inválido',
  EXPIRED_TOKEN: 'Token expirado',
  NETWORK_ERROR: 'Erro de rede',
  INVALID_CREDENTIALS: 'Credenciais inválidas',
  PERMISSION_DENIED: 'Permissão negada',
  SESSION_EXPIRED: 'Sessão expirada',
  AMPLIFY_NOT_CONFIGURED: 'AWS Amplify não configurado',
  COGNITO_ERROR: 'Erro no Amazon Cognito',
  // REMOVIDO: HTTPONLY_NOT_SUPPORTED
  REFRESH_FAILED: 'Falha no refresh do token',
  LOGOUT_FAILED: 'Falha no logout',
  USER_NOT_FOUND: 'Usuário não encontrado',
  INVALID_GRANT: 'invalid_grant',
  INVALID_CODE: 'invalid_code',
  MISSING_CONFIG: 'missing_config'
};

// Métodos de autenticação - REMOVIDO HTTPONLY
export const AUTH_METHODS = {
  COGNITO: 'cognito',
  // REMOVIDO: HTTPONLY para evitar HttpOnly cookies
  MANUAL: 'manual',
  OAUTH: 'oauth'
};

// Timeouts
export const AUTH_TIMEOUTS = {
  TOKEN_MIGRATION_DELAY: 5000, // 5 segundos
  REQUEST_TIMEOUT: 30000, // 30 segundos
  CACHE_CLEANUP_INTERVAL: 5 * 60 * 1000, // 5 minutos
  SESSION_CHECK_INTERVAL: 60000, // 1 minuto
  INACTIVITY_TIMEOUT: 25 * 60 * 1000, // 25 minutos
  WARNING_TIMEOUT: 23 * 60 * 1000, // 23 minutos
  REFRESH_THRESHOLD: 5 * 60 * 1000 // 5 minutos antes da expiração
};

// Cookies
export const COOKIE_CONFIG = {
  TOKEN_NAME: 'dsm_token',
  MAX_AGE: 8 * 60 * 60 * 1000, // 8 horas
  SAME_SITE: 'lax',
  SECURE: true,
  PATH: '/',
};

// LocalStorage Keys (para migração)
export const STORAGE_KEYS = {
  JWT: 'jwt',
  DSM_TOKEN: '@dsm/token',
  DSM_NAME: '@dsm/name',
  DSM_EMAIL: '@dsm/mail', // Corrigido para '@dsm/mail'
  DSM_PERMISSION: '@dsm/permission',
  DSM_USERNAME: '@dsm/username',
  DSM_TIME: '@dsm/time',
  VERSION: 'version',
};

// Tokens conhecidos para limpeza
export const LEGACY_TOKEN_KEYS = [
  'jwt',
  '@dsm/token',
  'token',
  'authToken',
  'access_token',
  'id_token',
  'refresh_token',
];

// Headers HTTP
export const HTTP_HEADERS = {
  AUTHORIZATION: 'Authorization',
  CONTENT_TYPE: 'Content-Type',
  ACCEPT: 'Accept',
  USER_AGENT: 'User-Agent',
};

// Valores padrão
export const DEFAULT_VALUES = {
  CONTENT_TYPE: 'application/json',
  ACCEPT: 'application/json',
  BEARER_PREFIX: 'Bearer ',
};

// Configurações de retry
export const RETRY_CONFIG = {
  MAX_ATTEMPTS: 3,
  DELAY_MS: 1000,
  BACKOFF_MULTIPLIER: 2,
};

// Validação
export const VALIDATION = {
  MIN_CODE_LENGTH: 10,
  MIN_TOKEN_LENGTH: 20,
  EMAIL_REGEX: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
};

// Configurações do Cognito
export const COGNITO_CONFIG = {
  USER_POOL_ID: 'us-east-1_VCf8aHRIZ',
  REGION: 'us-east-1',
  DOMAIN: 'dsm-application.auth.us-east-1.amazoncognito.com',
  SCOPES: [
    'email',
    'openid',
    'phone',
    'profile',
    'aws.cognito.signin.user.admin'
  ],
  RESPONSE_TYPE: 'code',
  IDENTITY_PROVIDER: 'AzureAD' // Para produção
};

// URLs por ambiente
export const ENVIRONMENT_URLS = {
  development: {
    CLIENT_ID: '3no5aulnqvut73n2hq93tqdrq0',
    REDIRECT_URI: 'http://localhost:3000/mfa',
    LOGOUT_URI: 'http://localhost:3000/login',
    API_BASE: 'http://localhost:3000/api'
  },
  dev: {
    CLIENT_ID: 'a612mhbbrvrh45ec6n8amqf5i',
    REDIRECT_URI: 'https://dev.dsm.darede.com.br/mfa',
    LOGOUT_URI: 'https://dev.dsm.darede.com.br/login',
    API_BASE: 'https://api.dsm.darede.com.br/dev'
  },
  hml: {
    CLIENT_ID: '74k0l615eeerd40t2rb8tthffb',
    REDIRECT_URI: 'https://hml.dsm.darede.com.br/mfa',
    LOGOUT_URI: 'https://hml.dsm.darede.com.br/login',
    API_BASE: 'https://api.dsm.darede.com.br/hml'
  },
  production: {
    CLIENT_ID: '56abme2ar7cabfnep9919ijbed',
    REDIRECT_URI: 'https://dsm.darede.com.br/mfa',
    LOGOUT_URI: 'https://dsm.darede.com.br/login',
    API_BASE: 'https://api.dsm.darede.com.br/prod'
  }
};

// Permissões padrão
export const DEFAULT_PERMISSIONS = {
  ADMIN: 'admin',
  MANAGER: 'manager',
  USER: 'user',
  READ: 'read',
  WRITE: 'write',
  DELETE: 'delete',
  EDIT: 'edit',
  CREATE: 'create'
};

export default {
  AUTH_TIMEOUTS,
  COOKIE_CONFIG,
  STORAGE_KEYS,
  LEGACY_TOKEN_KEYS,
  AUTH_ROUTES,
  AUTH_STATUS,
  AUTH_ERRORS,
  AUTH_METHODS,
  HTTP_HEADERS,
  DEFAULT_VALUES,
  RETRY_CONFIG,
  VALIDATION,
  COGNITO_CONFIG,
  ENVIRONMENT_URLS,
  DEFAULT_PERMISSIONS
};
