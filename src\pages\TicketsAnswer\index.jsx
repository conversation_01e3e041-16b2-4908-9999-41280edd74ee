import { useState, useRef, useEffect } from "react";
import Jo<PERSON>Editor from "jodit-react";
import { SideMenu } from "../../components/SideMenu";
import { HeaderMenu } from "../../components/HeaderMenu";
import {
  ArrowLeftOutlined,
  UploadOutlined,
  FormOutlined,
  MailOutlined,
  NotificationOutlined,
  LoadingOutlined,
} from "@ant-design/icons";
import moment from "moment";
import {
  Layout,
  Card,
  Row,
  Col,
  Typography,
  Input,
  Upload,
  Button,
  Form,
  Select,
  DatePicker,
  Checkbox,
  TimePicker,
  Space,
  message,
  Spin,
} from "antd";
import { AddDocument } from "../../components/Modals/Tickets/AddDocument";
import { useLocation, useNavigate } from "react-router-dom";
import axios from "axios";
import { UsersSelect } from "../../components/UserSelect";
import { dynamoPost, dynamoPut } from "../../service/apiDsmDynamo";
import { Documents } from "../../components/Modals/Tickets/Documents";
import { otrsGet, otrsPut } from "../../service/apiOtrs";
import RichTextEditor from "../../components/RichTextEditor/RichTextEditor";
import { invalidInputNumberChars } from "../../constants/invalidCharsInputNumber";
import { authService } from "../../services/authService";

const { Content } = Layout;
const { Title, Text } = Typography;
const { Option } = Select;
const editorConfig = {
  readonly: false,
  placeholder: "Escreva sua mensagem...",
  toolbar: true,
  spellcheck: true,
  toolbarButtonSize: "medium",
  toolbarAdaptive: false,
  showCharsCounter: true,
  showWordsCounter: true,
  showXPathInStatusbar: false,
  askBeforePasteHTML: true,
  askBeforePasteFromWord: true,
  disablePlugins: "powered-by-jodit",
  //defaultActionOnPaste: "insert_clear_html",
  uploader: {
    insertImageAsBase64URI: true,
  },
};

export const TicketsAnswer = (props) => {
  const { state } = useLocation();
  const navigate = useNavigate();
  const [collapsed, setCollapsed] = useState(false);
  const [loading, setLoading] = useState(false);
  const [customerVisible, setCustomerVisible] = useState(1);
  const [awsCost, setAwsCost] = useState("");
  const [queueFilter, setQueueFilter] = useState("");
  const [queue, setQueue] = useState(
    state.drafts ? state?.drafts[0]?.queue : ""
  );
  const [queueItems, setQueueItems] = useState([]);
  const [sender, setSender] = useState("");
  const [receiver, setReceiver] = useState("");
  const [form] = Form.useForm();
  const [architects, setArchitects] = useState([]);
  const [text, setText] = useState(``);
  const [services, setServices] = useState([]);
  const [summary, setSummary] = useState(``);
  const [fileList, setFileList] = useState([]);
  const [contracts, setContracts] = useState([]);
  const [contractLoading, setContractLoading] = useState(false);
  const [servicesLoading, setServicesLoading] = useState(false);
  const [stageDateFilter, setStageDateFilter] = useState(
    state?.drafts ? state?.drafts[0]?.next_stage : "open"
  );
  const [ortsLoading, setOrtsLoading] = useState(false);
  const [ticketTypes, setTicketTypes] = useState([]);
  const [queueLoading, setQueueLoading] = useState(false);
  const [typesLoading, setTypesLoading] = useState(false);

  const handleTicketUpdate = async () => {
    try {
      await otrsPut(`update/ticket/${localStorage.getItem("@ticket-number")}`, {
        email: JSON.parse(localStorage.getItem("@ticket/data")).owner_ticket,
        queue: form.getFieldValue("queue"),
        //type: form.getFieldValue("type"),
        service: form.getFieldValue("service"),
      }).then((res) => {
        message.success("Ticket atualizado com sucesso!");
      });
    } catch (err) {
      console.log(err);
      message.error("Erro ao atualizar ticket!");
    }
  };

  const handleTicketResponse = async (communication) => {
    setOrtsLoading(true);
    handleTicketUpdate();
    try {
      await otrsPut("close/ticket", {
        communicationType: communication,
        sender: sender !== "" ? sender : form.getFieldValue("sender"),
        receiver: receiver !== "" ? receiver : form.getFieldValue("receiver"),
        title: form.getFieldValue("subject"),
        closed: form.getFieldValue("next_stage"),
        pendingTime: form.getFieldValue("next_stage")?.includes("pending")
          ? {
              year: parseInt(
                moment(form.getFieldValue("stage_date")).format("YYYY")
              ),
              month: parseInt(
                moment(form.getFieldValue("stage_date")).format("MM")
              ),
              day: parseInt(
                moment(form.getFieldValue("stage_date")).format("DD")
              ),
              hour: parseInt(
                moment(form.getFieldValue("stage_date")).format("HH")
              ),
              minute: parseInt(
                moment(form.getFieldValue("stage_date")).format("mm")
              ),
            }
          : {
              year: 0,
              month: 0,
              day: 0,
              hour: 0,
              minute: 0,
            },
        time_unit: parseInt(form.getFieldValue("stage_time")),
        description: form.getFieldValue("text"),
        tktId: parseInt(state.id),
        tktNumber: localStorage.getItem("@ticket-number"),
        isVisibleForCustomer: customerVisible === true ? 1 : 0,
        visit: form.getFieldValue("visit"),
        contract: form.getFieldValue("contract"),
        has_aws_costs: form.getFieldValue("aws_costs"),
        aws_calculator_link: form.getFieldValue("aws_calculator"),
        aws_time_unit: form.getFieldValue("time_unit_aws"),
      }).then((res) => {
        setOrtsLoading(false);
        if (res.data.Error) {
          if (
            res.data.Error.ErrorMessage ===
            "TicketUpdate: User does not have access to the ticket!"
          ) {
            message.error(
              "Erro ao responder o ticket, verifique se suas permissões estão de acordo"
            );
          } else {
            message.warning("Por favor, preencha os campos obrigatórios");
          }
        } else {
          message.success("Ticket respondido com sucesso!");
          navigate("/tickets");
        }
      });
    } catch (err) {
      console.log(err);
      setOrtsLoading(false);
      navigate(-1);
      message.error("Erro ao responder o ticket, tente novamente");
    }
  };

  const handleSubmit = async (data) => {
    data["ticket_id"] = state !== null ? state.id : state[0];
    data["queue"] = queue;
    data["visibility"] = customerVisible === true ? 1 : 0;
    try {
      !state.id
        ? await dynamoPost(`${process.env.REACT_APP_STAGE}-tickets`, {
            id: state.toString(),
            currentUser: localStorage.getItem("@dsm/name"),
            drafts: [
              {
                //type: data.type,
                queue: data.queue !== "" ? data.queue : state.drafts[0].queue,
                sender: sender ? sender : state.drafts[0].sender,
                receiver: receiver ? receiver : state.drafts[0].receiver,
                service: data.service,
                visit: data.visit,
                activity_time: data.activity_time,
                activity_time_unit: data.activity_time_unit,
                contract: data.contract,
                summary: data.summary,
                subject: data.subject,
                text: data.text,
                visibility: data.visibility === 1 ? true : false,
                attachments: fileList,
                next_stage: data.next_stage,
                stage_date: data.stage_date,
                stage_time: moment(data.stage_time, "HH:mm:ss").format(
                  "HH:mm:ss"
                ),
                aws_cost: data.aws_cost,
                aws_calculator:
                  data.aws_cost === "yes" ? data.aws_calculator : "",
                time_unit_aws:
                  data.aws_cost === "yes" ? data.time_unit_aws : "",
              },
            ],
          })
        : await dynamoPut(
            `${process.env.REACT_APP_STAGE}-tickets`,
            data.ticket_id.toString(),
            {
              currentUser: localStorage.getItem("@dsm/name"),
              drafts: [
                {
                  type: data.type,
                  queue: data.queue,
                  sender: sender ? sender : state.drafts[0].sender,
                  receiver: receiver ? receiver : state.drafts[0].receiver,
                  service: data.service,
                  visit: data.visit,
                  activity_time: data.activity_time,
                  activity_time_unit: data.activity_time_unit,
                  contract: data.contract,
                  summary: data.summary,
                  subject: data.subject,
                  visibility: data.visibility === 1 ? true : false,
                  text: data.text,
                  attachments: fileList,
                  next_stage: data.next_stage,
                  stage_date: data.stage_date,
                  stage_time: moment(data.stage_time, "HH:mm:ss").format(
                    "HH:mm:ss"
                  ),
                  aws_cost: data.aws_cost,
                  aws_calculator:
                    data.aws_cost === "yes" ? data.aws_calculator : "",
                  time_unit_aws:
                    data.aws_cost === "yes" ? data.time_unit_aws : "",
                },
              ],
            }
          );
      localStorage.setItem("@ticket-number", "");
      localStorage.setItem("@ticket-client", "");
    } catch (err) {
      console.log(err);
    }
  };

  // const templates = (e) => {
  //   console.log(e);
  //   if (e === "tema") {
  //     form.setFieldsValue({
  //       text: `
  //     <p>TRY COPY CONTENT FROM A WORD AND PASTE HERE.</p><hr>
  //     <p><strong>Email Inspection Form (From Inspection Details)<img src="data:image/png;base64,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" style="float: right;"></strong>
  //     </p><p><br></p><p>Subject: May 23 2019 Compliance Inspection for&nbsp;{{FacilityName}}</p><p>
  //     (ie. {{InspectionCompleteDate}} {{InspectionEventType}} “Inspection”)
  //     </p><p><br></p><p>An inspection of Carl's Jr (FSE-0004) located at 65 Rogers Road Patterson, CA 95363 was completed on May 23 2019.
  //     </p><p><br></p><p>A copy of the inspection report is available here:&nbsp;<a href="www.open.linkoonline.com/asdfajjhafjasdfajsjfdjhafd">www.open.linkoonline.com/asdfajjhafjasdfajsjfdjhafd</a></p><p><br></p><p>
  //     If you have any questions or concerns, please contact us.
  //     </p><p><br></p><p>Sincerely,
  //     </p><p>Mark Gentry
  //     </p><p>Lead FOG Inspector
  //     </p><p>City of Patterson Environmental Compliance Department
  //     </p><p><a href="mailto:<EMAIL>"><EMAIL></a></p><p>(209) 895-8060</p>
  //     `,
  //     });
  //   } else {
  //     form.setFieldsValue({
  //       text: ``,
  //     });
  //   }
  // };

  const allFormData = [
    // {
    //   title: "Tipo",
    //   name: "type",
    //   rules: [{ required: true, message: "Selecione o tipo do chamdo" }],
    //   content: (
    //     <Select
    //       loading={typesLoading}
    //       placeholder="Selecione o tipo de ticket"
    //       showSearch
    //       filterOption={(input, option) =>
    //         option?.children
    //             ?.toLowerCase()
    //             .includes(input?.toLowerCase())
    //         }
    //         filterSort={(optionA, optionB) =>
    //         optionA.children
    //             ?.toLowerCase()
    //             .localeCompare(optionB.children?.toLowerCase())
    //         }
    //     >
    //       {ticketTypes.map((t) => (
    //         <Option value={t.id}>{t.name}</Option>
    //       ))}
    //     </Select>
    //   ),
    // },
    {
      title: "Fila",
      name: "queue",
      rules: [],
      content: (
        <UsersSelect
          loading={queueLoading}
          placeholder="Selecione a fila"
          currentValue={queue}
          name={"queue"}
          queueItems={queueItems}
          setQueue={(queue) => setQueue(queue)}
        />
      ),
    },
    {
      title: "De",
      name: "sender",
      rules: [],
      content: (
        <UsersSelect
          loading={loading}
          placeholder="Ex.: <EMAIL>"
          currentValue={sender}
          name={"sender"}
          sender={architects.filter((i) => i.email !== receiver)}
          setSender={(sender) => setSender(sender)}
        />
      ),
    },
    {
      title: "Para",
      name: "receiver",
      rules: [],
      content: (
        <UsersSelect
          loading={loading}
          placeholder="Ex.: <EMAIL>"
          currentValue={receiver}
          name={"receiver"}
          onSelect={(e) => form.setFieldsValue({ receiver: e })}
          receiver={architects.filter((i) => i.email !== sender)}
          setReceiver={(receiver) => setReceiver(receiver)}
        />
      ),
    },
    {
      title: "Serviço",
      name: "service",
      placeholder: "Ex.: migração de banco de dados",
      rules: [{ required: true, message: "Selecione o serviço" }],
      content: (
        <Select
          loading={servicesLoading}
          showSearch
          placeholder="Selecione o serviço"
          optionFilterProp="children"
          onSelect={(e) => form.setFieldsValue({ service: e })}
          filterOption={(input, option) =>
            option?.children?.toLowerCase().includes(input?.toLowerCase())
          }
          filterSort={(optionA, optionB) =>
            optionA.children
              ?.toLowerCase()
              .localeCompare(optionB.children?.toLowerCase())
          }
        >
          {services?.map((item, index) => {
            return (
              <Option key={index} value={item?.id}>
                {item?.name}
              </Option>
            );
          })}
        </Select>
      ),
    },
    {
      title: "Visita",
      name: "visit",
      rules: [{ required: true, message: "Selecione se há visita" }],
      content: (
        <Select defaultValue="no" placeholder="Selecione se há visita">
          <Option value="yes">Sim</Option>
          <Option value="no">Não</Option>
        </Select>
      ),
    },
    {
      title: "Contrato",
      name: "contract",
      rules: [{ required: true, message: "Selecione o contrato" }],
      content: (
        <Select
          loading={contractLoading}
          showSearch
          placeholder="Selecione o contrato"
          optionFilterProp="children"
          onSelect={(e) => form.setFieldsValue({ contract: e })}
          filterOption={(input, option) =>
            option?.children?.toLowerCase().includes(input?.toLowerCase())
          }
          filterSort={(optionA, optionB) =>
            optionA.children
              ?.toLowerCase()
              .localeCompare(optionB.children?.toLowerCase())
          }
        >
          {Object.values(contracts)
            ?.filter((nullTotalHours) => nullTotalHours.total_hours !== 0)
            .map((item, index) => {
              return (
                <Option key={index} value={item?.id}>
                  {item?.name}
                </Option>
              );
            })}
        </Select>
      ),
    },
    {
      title: "Assunto",
      name: "subject",
      rules: [{ required: true, message: "Selecione o assunto" }],
      content: (
        <Input type="text" placeholder="Ex.: migração de banco de dados" />
      ),
    },
    {
      title: "Resumo do Ticket",
      name: "summary",
      rules: [{ required: true, message: "Insira o resumo do ticket" }],
      content: (
        <JoditEditor
          value={summary}
          config={editorConfig}
          onChange={(value) => setSummary(value)}
        />
      ),
    },
    // {
    //   title: 'Tema',
    //   name: 'theme',
    //   content:
    //   <Select
    //     defaultValue="empty"
    //     onChange={(e) => {
    //       templates(e);
    //     }}
    //   >
    //     <Option value="empty">Vazio</Option>
    //     <Option value="tema">Tema 1</Option>
    //   </Select>
    // },

    {
      title: "Texto",
      name: "text",
      rules: [{ required: true, message: "Insira o texto do ticket" }],
      content: (
        <JoditEditor
          value={text}
          config={editorConfig}
          onChange={(value) => setText(value)}
        />
      ),
    },
    {
      title: "Próximo estado",
      name: "next_stage",
      rules: [{ required: true, message: "Selecione o próximo estado" }],
      content: (
        <Select
          defaultValue="open"
          placeholder="Selecione o próximo estado"
          onSelect={(e) => setStageDateFilter(e)}
        >
          <Option value="closed successful">Fechado com sucesso</Option>
          <Option value="open">Aberto</Option>
          <Option value="pending auto close+">Fechamento pendente</Option>
          <Option value="pending reminder">Lembrete pendente</Option>
          <Option value="pending user">Usuário pendente</Option>
        </Select>
      ),
    },
    {
      title: "Data & Hora do estado:",
      name: "stage_date",
      rules: [
        {
          required: true,
          message: "Selecione a data e hora do próximo estado",
        },
      ],
      content: (
        <DatePicker
          format="YYYY-MM-DD HH:mm:ss"
          defaultValue={
            state[0]?.drafts ? moment(state[0]?.drafts[0].stage_date) : moment()
          }
          showTime={{
            defaultValue: moment("00:00:00", "HH:mm:ss"),
          }}
        />
      ),
    },
    {
      title: "Tempo (em minutos)",
      name: "stage_time",
      rules: [
        { required: true, message: "Selecione o tempo do próximo estado" },
        { oninput: (e) => Math.abs(e.target.value) },
      ],
      content: (
        <Input
          onKeyDown={(e) => {
            invalidInputNumberChars.find(
              (keyPressed) => keyPressed === e.key
            ) && e.preventDefault();
          }}
          type="number"
          placeholder="Insira o tempo utilizado em minutos"
          onInput={(e) => Math.abs(e.target.value)}
          style={{
            width: "70%",
          }}
          min={0}
        />
      ),
    },
    {
      title: "Visível para o cliente",
      name: "visibility",
      rules: [],
      content: (
        <Checkbox
          // checked={state?.drafts[0].visibility === true ? true : false}
          onChange={(e) => setCustomerVisible(e.target.checked)}
        >
          Visível para o cliente
        </Checkbox>
      ),
    },
    {
      title: "Custos AWS",
      name: "aws_cost",
      rules: [],
      content: (
        <Select
          defaultValue="no"
          onSelect={(e) => setAwsCost(e)}
          placeholder="Ex.: Sim"
        >
          <Option value="yes">Sim</Option>
          <Option value="no">Não</Option>
        </Select>
      ),
    },
    {
      title: "Calculadora AWS",
      name: "aws_calculator",
      rules: [],
      content: <Input type="text" placeholder="Ex.: https://calculator.aws" />,
    },
    {
      title: "Unidades de tempo (mins)",
      name: "time_unit_aws",
      rules: [],
      content: <Input type="number" placeholder="Ex.: 123456" />,
    },
    // {
    //   title: 'Anexos',
    //   name: 'attachments',
    //   content:
    // }
  ];

  useEffect(() => {
    if (queueItems.length > 0) {
      setQueueFilter(queueItems?.find((item) => item.id === queue)?.name);
    }
  }, [queue, queueItems]);

  useEffect(() => {
    if (queue !== "") {
      form.setFieldValue("queue", queue);
    }
    if (sender !== "") {
      form.setFieldValue("sender", sender);
    }
    if (receiver !== "") {
      form.setFieldValue("receiver", receiver);
    }
  }, [queue, sender, receiver]);

  useEffect(() => {
    setLoading(true);
    setContractLoading(true);
    setQueueLoading(true);
    setTypesLoading(true);
    async function getContractsByCustomer(id) {
      const response = await otrsGet(
        `read/contract/customer/${localStorage.getItem("@ticket-client-id")}`
      );
      setContracts(response.data);
      setContractLoading(false);
    }
    async function getUsers() {
      try {
        const { data } = await axios.get(
          `${process.env.REACT_APP_API_PERMISSION}/cognito/read`,
          {
            headers: authService.getAuthHeaders(),
          }
        );
        let users = [];
        // ramos
        const items = data.data.Items || data.data || [];
        for (let i = 0; i < items.length; i++) {
          users.push(items[i]);
        }
        setArchitects(users);
      } catch (error) {
        console.error('Erro ao buscar usuários:', error);
        setArchitects([]);
      }
    }
    async function getQueue() {
      let queue = await otrsGet(
        `read/ticket/queues/allByUser/${localStorage.getItem("@otrs/userId")}`
      );
      setQueueItems(queue.data);
      setQueueLoading(false);
    }
    async function getServices() {
      setServicesLoading(true);
      let services = await otrsGet(`read/services`)
        .then((res) => setServices(res.data))
        .finally(() => setServicesLoading(false));
    }
    async function getAllData() {
      await getUsers();
      await getQueue();
      await getServices();
      await getContractsByCustomer();
    }
    getAllData();
    if (
      state?.drafts &&
      state.currentUser === localStorage.getItem("@dsm/username")
    ) {
      form.setFieldsValue({
        type: state?.drafts[0].type,
        queue: state?.drafts[0].queue,
        sender: state?.drafts[0].sender,
        receiver: state?.drafts[0].receiver,
        service: state?.drafts[0].service,
        visit: state?.drafts[0].visit,
        activity_time: state?.drafts[0].activity_time,
        contract: state?.drafts[0].contract,
        summary: state?.drafts[0].summary,
        subject: state?.drafts[0].subject,
        text: state?.drafts[0].text,
        visibility: state?.drafts[0].visibility,
        stage_date: moment(state?.drafts[0].stage_date),
        stage_time: moment(state?.drafts[0].stage_time, "HH:mm:ss"),
        next_stage: state?.drafts[0].next_stage,
      });
    }
  }, []);

  return (
    <Layout style={{ minHeight: "100vh" }}>
      <HeaderMenu collapsed={collapsed} setCollapsed={setCollapsed} />
      <Layout>
        <SideMenu collapsed={collapsed} />
        <Content style={{ padding: "2em" }}>
          {ortsLoading === true ? (
            <Card
              style={{
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
                height: "40%",
                borderRadius: "20px",
              }}
            >
              <Row justify="center">
                <Title level={5}>
                  Realizando validação das informações de tickets
                </Title>
              </Row>
              <Row justify="center">
                <Spin size="large" />
              </Row>
            </Card>
          ) : (
            <Card
              style={{
                boxShadow: "0 0 10px rgba(0,0,0,0.1)",
                borderRadius: "20px",
              }}
            >
              <Row gutter={16}>
                <Col span={10}>
                  <Button
                    style={{ paddingLeft: "0" }}
                    type="text"
                    onClick={() => navigate(-1)}
                    icon={<ArrowLeftOutlined />}
                  >
                    Responder ticket
                  </Button>
                </Col>
              </Row>
              <Form
                layout="vertical"
                onFinish={handleSubmit}
                form={form}
                initialValues={{
                  visit: state?.drafts ? state?.drafts[0]?.visit : "no",
                  next_stage: state?.drafts
                    ? state?.drafts[0]?.next_stage
                    : "open",
                  visibility: state?.drafts
                    ? state?.drafts[0]?.visibility === true
                      ? "checked"
                      : ""
                    : "",
                }}
              >
                <Row style={{ marginTop: "1rem" }}>
                  {allFormData.map((formItem, formItemKey) => {
                    if (
                      !formItem.name.includes("stage") &&
                      formItem.name !== "stage_date"
                    ) {
                      if (
                        formItem.name !== "contract" &&
                        formItem.name !== "summary" &&
                        !formItem.name.includes("aws") &&
                        formItem.name !== "time_unit"
                      ) {
                        return (
                          <Col
                            span={formItem.name !== "activity_time" ? 24 : 8}
                          >
                            <Form.Item
                              label={formItem.title}
                              name={formItem.name}
                              rules={formItem.rules}
                            >
                              {formItem.content}
                            </Form.Item>
                          </Col>
                        );
                      } else if (
                        !queueFilter?.includes("DCQ") &&
                        formItem.name === "contract"
                      ) {
                        return (
                          <Col span={24}>
                            <Form.Item
                              label={formItem.title}
                              name={formItem.name}
                              rules={formItem.rules}
                            >
                              {formItem.content}
                            </Form.Item>
                          </Col>
                        );
                      } else if (
                        !queueFilter?.includes("FilaPrevendas") &&
                        formItem.name === "summary"
                      ) {
                        return (
                          <Col span={24}>
                            <Form.Item
                              label={formItem.title}
                              name={formItem.name}
                              rules={formItem.rules}
                            >
                              {formItem.content}
                            </Form.Item>
                          </Col>
                        );
                      } else if (
                        (queueFilter?.includes("Squad") ||
                          queueFilter?.includes("DCQ") ||
                          queueFilter?.includes("Prevendas")) &&
                        formItem.name === "aws_cost"
                      ) {
                        return (
                          <Col span={24}>
                            <Form.Item
                              label={formItem.title}
                              name={formItem.name}
                              rules={formItem.rules}
                            >
                              {formItem.content}
                            </Form.Item>
                          </Col>
                        );
                      } else if (
                        awsCost === "yes" &&
                        formItem.name.includes("aws")
                      ) {
                        return (
                          <Col span={24}>
                            <Form.Item
                              label={formItem.title}
                              name={formItem.name}
                              rules={formItem.rules}
                            >
                              {formItem.content}
                            </Form.Item>
                          </Col>
                        );
                      }
                    } else if (formItem.name !== "stage_date") {
                      return (
                        <Col span={8}>
                          <Form.Item
                            style={{
                              width:
                                formItem.name === "next_stage" ? "70%" : "100%",
                            }}
                            label={formItem.title}
                            name={formItem.name}
                            rules={formItem.rules}
                          >
                            {formItem.content}
                          </Form.Item>
                        </Col>
                      );
                    } else if (
                      formItem.name === "stage_date" &&
                      stageDateFilter !== "open" &&
                      stageDateFilter !== "closed successful"
                    ) {
                      return (
                        <Col span={8}>
                          <Form.Item
                            style={{ width: "100%" }}
                            label={formItem.title}
                            name={formItem.name}
                            rules={formItem.rules}
                          >
                            {formItem.content}
                          </Form.Item>
                        </Col>
                      );
                    }
                  })}
                </Row>
              </Form>
              <Row justify="space-between">
                <Space>
                  <Col>
                    <Button
                      type="primary"
                      style={{ borderRadius: "5px" }}
                      icon={<MailOutlined />}
                      onClick={() => {
                        form.submit();
                        handleTicketResponse("Email");
                      }}
                    >
                      Enviar email!
                    </Button>
                  </Col>
                  <Col style={{ margin: "5px 20px" }}>
                    <Button
                      type="default"
                      style={{ borderRadius: "5px" }}
                      icon={<NotificationOutlined />}
                      onClick={() => {
                        handleTicketResponse("Internal");
                        form.submit();
                      }}
                    >
                      Enviar como nota
                    </Button>
                  </Col>
                  <Col>
                    <Button
                      type="primary"
                      style={{
                        borderRadius: "5px",
                        backgroundColor: "#A6B0B7",
                        border: "#A6B0B7",
                      }}
                      icon={<FormOutlined />}
                      onClick={form.submit}
                    >
                      Salvar como novo rascunho!
                    </Button>
                  </Col>
                </Space>
                <Space wrap>
                  <Col>
                    <AddDocument
                      state={state[0]?.id ? state[0]?.id : state[0]}
                      fileList={fileList}
                      setFileList={(fileList) => setFileList(fileList)}
                    />
                  </Col>
                  {state?.id && (
                    <Col>
                      <Documents state={state[0]} />
                    </Col>
                  )}
                </Space>
              </Row>
            </Card>
          )}
        </Content>
      </Layout>
    </Layout>
  );
};
