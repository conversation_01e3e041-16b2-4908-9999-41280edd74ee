import { Select, Row, Col, message } from "antd";
import { useSelector, shallowEqual } from "react-redux";

import { UpdateEmailsModal } from "../../UpdateEmailsModal/UpdateEmailsModal";
import { setSelectedFileFieldState } from "../../../../../../store/actions/files-action";
import { validateEmail } from "../../../../../../utils/validators";

const { Option } = Select;

export const EditEmailsSelect = () => {
  const emails = useSelector(
    (state) => state.files.selectedFile.emails,
    shallowEqual
  );

  return (
    <Col span={24}>
      <Row gutter={[8, 8]} justify={"space-between"}>
        <Col span={21}>
          <Row>
            <Select
              style={{ width: "100%" }}
              mode={"multiple"}
              placeholder={"Selecione os emails do arquivo..."}
              value={emails}
              onChange={(e) => {
                let allValid = true;
                for (let i = 0; i < e.length; i++) {
                  if (!validateEmail(e[i])) {
                    message.warning("Insira um email válido", 3);
                    allValid = false;
                    break;
                  }
                }
                if (allValid) {
                  setSelectedFileFieldState({ field: "emails", value: e });
                }
              }}
            >
              {emails.map((option, key) => (
                <Option value={option} key={key}>
                  {option}
                </Option>
              ))}
            </Select>
          </Row>
        </Col>
        <Col span={3}>
          <Row justify={"end"}>
            <UpdateEmailsModal />
          </Row>
        </Col>
      </Row>
    </Col>
  );
};
