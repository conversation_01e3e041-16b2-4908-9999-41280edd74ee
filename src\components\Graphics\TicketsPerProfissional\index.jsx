import { React, useCallback, useEffect, useMemo, useState } from "react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Radial<PERSON>ar<PERSON><PERSON>,
  RadialBar,
  Sector,
  Legend,
  PolarAngleAxis,
  Cell,
  Tooltip,
} from "recharts";
import "./TicketsPerProfissional.css";
import { Select, Popover, Modal, Spin, Card, Typography, Row, Col } from "antd";
import { FilterFilled, StarFilled, StarOutlined } from "@ant-design/icons";
import FilterTicketsPerProfissional from "../../Modals/Filters/TicketsPerProfissionalFilter";
import TableTicketsPerProfissional from "../../Table/TicketsPerProfissional";
import { otrsGet, otrsPost } from "../../../service/apiOtrs";
import moment from "moment";
import { generateRandomColor } from "../../../hooks/generateRandomColor";

const renderActiveShape = (props) => {
  const {
    cx,
    cy,
    innerRadius,
    outerRadius,
    startAngle,
    endAngle,
    fill,
    payload,
  } = props;

  return (
    <g>
      <Sector
        cx={cx}
        cy={cy}
        innerRadius={innerRadius}
        outerRadius={outerRadius}
        startAngle={startAngle}
        endAngle={endAngle}
        fill={fill}
      />
    </g>
  );
};

function TicketsPerProfissional(props) {
  const { Title, Text } = Typography;
  const [activeIndex, setActiveIndex] = useState(0);
  const [isFavorite, setIsFavorite] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [colors, setColors] = useState([]);
  const [popoverVisible, setPopoverVisible] = useState(false);
  const [data, setData] = useState(false | []);
  const [singleData, setSingleData] = useState(false);
  const [loading, setLoading] = useState(false);
  const [windowWidth, setWindowWidth] = useState(window.innerWidth);
  const [responsiveMargin, setResponsiveMargin] = useState({
    top: 0,
    right: 0,
    left: 0,
    bottom: 0,
  });

  const GRAPH_NAME = "TicketsPerProfissional";
  const closedTicketStates = ["closed successful", "closed unsuccessful"];
  const MAX_TICKETS = 100;

  useEffect(() => {
    setIsFavorite(checkIsVisible());
    getGraphColor();
  }, [props.favorites, props.contracts, props.wallets, props.professionals]);
  const updateWidth = () => {
    setWindowWidth(window.innerWidth);
  };
  useEffect(() => {
    window.addEventListener("resize", updateWidth);
    return () => window.removeEventListener("resize", updateWidth);
  }, [windowWidth]);

  useMemo(() => {
    if (992 < windowWidth && windowWidth < 1200) {
      setResponsiveMargin({
        top: 0,
        right: -(windowWidth * 0.1),
        left: -(windowWidth * 0.02),
        bottom: 0,
      });
    } else if (windowWidth > 1600) {
      setResponsiveMargin({
        top: 0,
        right: -(windowWidth * 0.1),
        left: -(windowWidth * 0.02),
        bottom: 0,
      });
    } else {
      setResponsiveMargin({
        top: 0,
        right: -(windowWidth * 0.05),
        left: -(windowWidth * 0.02),
        bottom: 0,
      });
    }
  }, [windowWidth]);

  function showModal() {
    setModalVisible(!modalVisible);
  }

  function handleOk() {
    setModalVisible(false);
  }

  function handleCancel() {
    setModalVisible(false);
  }

  function checkIsVisible() {
    let localStorageData = JSON.parse(localStorage.getItem("favorites"));
    if (localStorageData) {
      var index = localStorageData.findIndex(
        (value) => value.component === GRAPH_NAME
      );

      return index !== -1;
    } else {
      return false;
    }
  }

  function getGraphColor() {
    let storage_color = JSON.parse(localStorage.getItem("colors"));
    if (storage_color) {
      // setColors(storage_color[GRAPH_NAME]);
    }
  }

  async function updateFavorite() {
    await props?.setVisionFavorite(GRAPH_NAME);
    setIsFavorite(checkIsVisible());
  }

  const onPieEnter = useCallback(
    (_, index) => {
      setActiveIndex(index);
    },
    [setActiveIndex]
  );

  function hiddenPopover() {
    setPopoverVisible(!popoverVisible);
  }

  function swapVisible() {
    let object = structuredClone(props.componentVisibles);
    object.tableTicketsPerProfissional = !object.tableTicketsPerProfissional;
    props.setComponentVisibles(object);
  }

  function getUsername(userId) {
    let fullUsername =
      props.professionals.find((p) => p.id === userId).first_name +
      " " +
      props.professionals.find((p) => p.id === userId).last_name;
    return fullUsername;
  }

  async function getFilteredChartData(filters) {
    const { professionals, wallet, queue, monthSelected } = filters;
    let getFirstDayOfMonth = moment(monthSelected)
      .startOf("month")
      .format("YYYY-MM-DD HH:mm:ss");
    let getLastDayOfMonth = moment(monthSelected)
      .endOf("month")
      .format("YYYY-MM-DD HH:mm:ss");
    const filteredData = [];
    setLoading(true);
    for (let i = 0; i < professionals.length; i++) {
      const allTicketsPerProfissionalInTimeRange = await otrsPost(
        "read/tickets/allTicketsByPDMInTimeRange",
        {
          params: {
            pdmId: professionals[i],
            wallet: wallet,
            queue: queue,
            start: getFirstDayOfMonth,
            end: getLastDayOfMonth,
          },
        }
      );
      filteredData.push({
        name: getUsername(professionals[i]),
        value: allTicketsPerProfissionalInTimeRange.data.length,
        ticketsData: allTicketsPerProfissionalInTimeRange.data,
        colors: generateRandomColor(),
      });
    }
    setColors(filteredData.map((c) => c.colors));
    setData(filteredData);
    setLoading(false);
  }

  const handleClickLegend = (e) => {
    const foundedObject = data.findIndex((c) => c.name === e.value);
    setActiveIndex(foundedObject);
  };

  const responseNameForNullGraphData = "max";
  const checkingValidGraphData =
    data &&
    data.some(
      (item) => item.name !== responseNameForNullGraphData && item.value > 0
    );
  const CustomTooltip = ({ payload }) => {
    return (
      <Card style={{ padding: "0", borderRadius: "20px" }}>
        <Row>
          <Text>{payload?.[0]?.payload?.name}</Text>
        </Row>
        <span>
          <Text>
            <small>
              Tickets associados: {payload?.[0]?.payload?.ticketsData?.length}
            </small>
          </Text>
        </span>
      </Card>
    );
  };

  return (
    <Card
      bordered="false"
      style={{
        borderRadius: "20px",
        height: "100%",
        minWidth: "476px",
        minHeight: "400px",
      }}
    >
      <Row style={{ height: "100%" }}>
        <Col span={18}>
          <Title level={4} style={{ fontWeight: 400, margin: "0px" }}>
            Tickets por profissional
          </Title>
        </Col>

        <Col span={2} style={{ alignSelf: "center" }}>
          <Popover
            open={popoverVisible}
            onOpenChange={hiddenPopover}
            placement="left"
            content={() => {
              return (
                <FilterTicketsPerProfissional
                  wallets={props.wallets}
                  queues={props.queues}
                  professionals={props.professionals}
                  hiddenPopover={hiddenPopover}
                  filter={getFilteredChartData}
                />
              );
            }}
            trigger="click"
          >
            <div
              style={{
                display: "flex",
                textAlign: "center",
                cursor: "pointer",
                gap: "5px",
              }}
              className="filter"
            >
              <FilterFilled className="filter-icon" />
            </div>
          </Popover>
        </Col>
        <Col span={2} style={{ alignSelf: "center" }}>
          {!isFavorite ? (
            <StarOutlined onClick={updateFavorite} className="star-icon" />
          ) : (
            <StarFilled onClick={updateFavorite} className="star-icon" />
          )}
        </Col>
      </Row>
      {loading ? (
        <Row
          justify="center"
          style={{ width: "100%", height: "250px", alignItems: "center" }}
        >
          <Spin />
        </Row>
      ) : (
        <>
          <Row style={{ justifyContent: data !== 0 ? "" : "center" }}>
            {data == 0 ? (
              <Row align="middle" justify="center" style={{ height: "250px" }}>
                <Col>
                  <Text>Selecione um profissional*</Text>
                </Col>
              </Row>
            ) : (
              <Col style={{ alignSelf: "flex-start" }}>
                {checkingValidGraphData ? (
                  <PieChart
                    onClick={() =>
                      props.isModal ? showModal() : swapVisible()
                    }
                    margin={responsiveMargin}
                    width={380}
                    height={250}
                  >
                    <Pie
                      activeIndex={activeIndex}
                      data={data}
                      cx={150}
                      cy={120}
                      dataKey="value"
                      outerRadius={95}
                      onMouseEnter={onPieEnter}
                      too
                    >
                      {data.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={colors[index]} />
                      ))}
                    </Pie>
                    <Tooltip content={<CustomTooltip />} />
                    <Legend
                      formatter={(value, entry, index) => <span>{value}</span>}
                      onClick={(e) => handleClickLegend(e)}
                      className="text-xs"
                      layout="vetical"
                      verticalAlign="middle"
                      align="right"
                    />
                  </PieChart>
                ) : (
                  <Col
                    style={{
                      height: "250px",
                      display: "flex",
                      justifyContent: "center",
                      alignItems: "center",
                    }}
                  >
                    <Text>Não há dados para o filtro selecionado</Text>
                  </Col>
                )}
              </Col>
            )}
          </Row>
          <Modal
            closable={false}
            width={"90vw"}
            style={{ display: "flex", justifyContent: "center" }}
            bodyStyle={{ backgroundColor: "transparent" }}
            cancelButtonProps={{ style: { display: "none" } }}
            footer={null}
            open={modalVisible}
            onOk={handleOk}
            onCancel={handleCancel}
          >
            <TableTicketsPerProfissional
              users={props.professionals}
              showModal={showModal}
              tableData={data[activeIndex]}
            />
          </Modal>
        </>
      )}
    </Card>
  );
}

export default TicketsPerProfissional;
