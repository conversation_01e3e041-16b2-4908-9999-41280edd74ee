import {
  setActiveCustomersWithActiveContractsReduce,
  setActiveCustomersWithInactiveContractsReduce,
  setInactiveCustomersReduce,
  setProspectsReduce,
  setAllCustomersReduce,
  setStateReduce,
  setSearchReduce,
  setDateReduce,
} from "../reducers/customers-reducer";

import { store } from "../store";

export const setActiveCustomersWithActiveContractState = (payload) => {
  store.dispatch(setActiveCustomersWithActiveContractsReduce(payload));
};

export const setActiveCustomersWithInactiveContractState = (payload) => {
  store.dispatch(setActiveCustomersWithInactiveContractsReduce(payload));
};

export const setInactivesCustomersState = (payload) => {
  store.dispatch(setInactiveCustomersReduce(payload));
};

export const setProspectsState = (payload) => {
  store.dispatch(setProspectsReduce(payload));
};

export const setAllCustomersState = (payload) => {
  store.dispatch(setAllCustomersReduce(payload));
};

export const setCustomersState = (payload) => {
  store.dispatch(setStateReduce(payload));
};

export const setSearchState = (payload) => {
  store.dispatch(setSearchReduce(payload));
};

export const setDateState = ({ field, value }) => {
  store.dispatch(setDateReduce({ field, value }));
};
