import { Tag } from "antd";

export const TextColor = (hex, name) => {
  if(hex) {
    let shorthandRegex = /^#?([a-f\d])([a-f\d])([a-f\d])$/i
    hex = hex?.replace(shorthandRegex, (m, r, g, b) => {
      return r + r + g + g + b + b
    })
    let rgb = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
    rgb = rgb
      ? {
          r: parseInt(rgb[1], 16),
          g: parseInt(rgb[2], 16),
          b: parseInt(rgb[3], 16)
        }
      : { r: 0, g: 0, b: 0 }

    return Math.round(
      (parseInt(rgb.r) * 299 + parseInt(rgb.g) * 587 + parseInt(rgb.b) * 114) /
        1000
    ) > 150 ? (
      <Tag style={{ color: '#555' }} color={hex}>
        {name}
      </Tag>
    ) : (
      <Tag style={{ color: '#fff' }} color={hex}>
        {name}
      </Tag>
    )
  }
  else{
    return null
  }
}