import { Button, Modal } from "antd";
import { useState } from "react";
import { SendOutlined } from "@ant-design/icons";
export const SendFilesModal = ({ form }) => {
  const [showModal, setShowModal] = useState(false);

  const handleModalVisible = () => {
    setShowModal(!showModal);
  };

  const submitForm = () => {
    form.submit();
  };

  return (
    <>
      <Button
        type="primary"
        onClick={handleModalVisible}
        icon={<SendOutlined />}
      >
        Enviar
      </Button>
      <Modal open={showModal} onCancel={handleModalVisible} onOk={submitForm}>
        Teste
      </Modal>
    </>
  );
};
