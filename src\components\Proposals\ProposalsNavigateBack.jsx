import { useNavigate } from "react-router-dom";
import {
    ArrowLeftOutlined,
  } from "@ant-design/icons";
import { But<PERSON>, Popconfirm } from "antd";


export const NavigateBackButton = ({collapseContent}) => {
    const navigate = useNavigate();
    const handleBackClick = () => {
        navigate(-1)
        localStorage.setItem("technical-proposal/services", "");
        localStorage.setItem("technical-proposal/form", "");
        localStorage.setItem("technical-proposal/customer", "");
        localStorage.setItem(
            "technical-proposal/architectureFileList",
            ""
        );
        localStorage.setItem("technical-proposal/fileList", "");
        localStorage.setItem("CollapseValidator", "");
        localStorage.setItem("technical-proposal/mainSearchId", "");
        collapseContent.map((item) => {
            return item.itemName
            ? localStorage.setItem(item.itemName, "")
            : null;
        });
    };

    return(
        <Popconfirm
              title="Ao voltar você perderá os dados do formulário, tem certeza?"
              onConfirm={handleBackClick}
              okText="Voltar"
              cancelText="Não"
            >
              <Button
                type="text"
                style={{ marginBottom: "15px" }}
                icon={<ArrowLeftOutlined />}
              >
                Criar proposta
              </Button>
            </Popconfirm>
    )
}