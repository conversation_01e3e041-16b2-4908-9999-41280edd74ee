import { Card, Row, Col, Typography, Form, Button } from "antd";
import { UploadFilesForm } from "./UploadFilesForm";
import { CloudUploadOutlined } from "@ant-design/icons";
import { SendFilesModal } from "../../../components/Modals/Files/SendFiles";

export const UploadFilesCard = () => {
  const { Title, Text } = Typography;
  const { filesForm } = Form;
  return (
    <Card
      style={{
        boxShadow: "0 0 10px rgba(0,0,0,0.1)",
        borderRadius: "20px",
      }}
    >
      <Row>
        <Title level={4}>Upload de arquivos</Title>
      </Row>
      <Row>
        <Col span={24}>
          <UploadFilesForm form={filesForm} />
        </Col>
      </Row>
      <Row justify="end">
        <Col span={3}>
          <Button type="default" icon={<CloudUploadOutlined />}>
            Enviar
          </Button>
        </Col>
        <Col>
          <SendFilesModal form={filesForm} />
        </Col>
      </Row>
    </Card>
  );
};
