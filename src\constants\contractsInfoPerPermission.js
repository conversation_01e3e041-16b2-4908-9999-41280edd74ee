export const contractsView = [
  {
    fieldName: "Data de início da vigência",
    key: "expected_start_date",
    content: "",
    permissionCode: "info_modal_view_start_date",
  },
  {
    fieldName: "Data de fim da vigência",
    key: "expected_close_date",
    content: "",
    permissionCode: "info_modal_view_end_date",
  },
  {
    fieldName: "Escopo",
    content: "",
    permissionCode: "info_modal_view_scope",
    key: "scope",
  },
  {
    fieldName: "Nome do contrato",
    key: "name",
    content: "",
    permissionCode: "info_modal_view_contract_name",
  },
  {
    fieldName: "ID do contrato",
    key: "itsm_id",
    content: "",
    permissionCode: "info_modal_view_contract_id",
  },
  {
    fieldName: "DSM ID",
    key: "dsm_id",
    content: "",
    permissionCode: "info_modal_view_dsm_id",
  },
  {
    fieldName: "Total de horas",
    key: "total_hours",
    content: "",
    permissionCode: "info_modal_view_total_hours",
  },
  {
    fieldName: "Squad",
    key: "squad",
    content: "",
    permissionCode: "info_modal_view_squad",
  },
  {
    fieldName: "Porcentagem de bloqueio",
    key: "block",
    content: "",
    permissionCode: "info_modal_view_block_percentage",
  },
  {
    fieldName: "Porcentagem de notificação",
    key: "freezing",
    content: "",
    permissionCode: "info_modal_view_freezing_percentage",
  },
  {
    fieldName: "Tipo de consumo",
    key: "type_hours",
    content: "",
    permissionCode: "info_modal_view_type_hours",
  },
  {
    fieldName: "Data de Criação",
    key: "created_at",
    content: "",
    permissionCode: "info_modal_view_creation_date",
  },
  {
    fieldName: "Data de Atualização",
    key: "updated_at",
    content: "",
    permissionCode: "info_modal_view_update_date",
  },
  {
    fieldName: "CRM",
    key: "crm_id",
    content: "",
    permissionCode: "info_modal_view_crm",
  },
  {
    fieldName: "ITSM do Cliente",
    key: "customer_itsm",
    content: "",
    permissionCode: "info_modal_view_customer_id",
  },
  {
    fieldName: "Nome do Cliente",
    key: "customer_name",
    content: "",
    permissionCode: "info_modal_view_customer_name",
  },
  {
    fieldName: "CNPJ",
    key: "customer_cnpj",
    content: "",
    permissionCode: "info_modal_view_cnpj",
  },
  // {
  //   fieldName: "TAX ID",
  //   key: "customer.tax_id",
  //   content: "",
  //   permissionCode: "info_modal_view_tax_id",
  // },
  {
    fieldName: "Valor/Hora",
    key: "excess_cost",
    content: "",
    permissionCode: "info_modal_view_excess_cost",
  },
  {
    fieldName: "Emails de bloqueio",
    key: "block_emails",
    content: "",
    permissionCode: "info_modal_view_notification_emails",
  },
  {
    fieldName: "Tipo de horas",
    key: "pool_type",
    content: "",
    permissionCode: "info_modal_view_pool_hours",
  },
  {
    fieldName: "Status",
    key: "active",
    content: "",
    permissionCode: "info_modal_view_status",
  },
  {
    fieldName: "Valor do contrato",
    key: "value",
    content: "",
    permissionCode: "info_modal_view_value",
  },
  {
    fieldName: "Valor da Calculadora AWS",
    key: "aws_value",
    content: "",
    permissionCode: "info_modal_view_aws_value",
  },
  {
    fieldName: "Valor faturado",
    key: "billed_value",
    content: "",
    permissionCode: "info_modal_view_billed_value",
  },
  {
    fieldName: "Valor hora excedente",
    key: "excess_hour_value",
    content: "",
    permissionCode: "info_modal_view_excess_hour_value",
  },
  {
    fieldName: "Índice de reajuste",
    key: "reajustment_index",
    content: "",
    permissionCode: "info_modal_view_reajustment_index",
  },
  {
    fieldName: "Percentual reajustado",
    key: "reajustment_percentage",
    content: "",
    permissionCode: "info_modal_view_reajustment_percentage",
  },
  {
    fieldName: "Renovação automática",
    key: "automatic_renewal",
    content: "",
    permissionCode: "info_modal_view_automatic_renewal",
  },
  {
    fieldName: "Assinado",
    key: "signed",
    content: "",
    permissionCode: "info_modal_view_signed",
  },
  {
    fieldName: "Data de assinatura",
    key: "signature_date",
    content: "",
    permissionCode: "info_modal_view_signature_date",
  },
  {
    fieldName: "Multa rescisão",
    key: "rescission_fine",
    content: "",
    permissionCode: "info_modal_view_rescission_fine",
  },
  {
    fieldName: "Multa SLA",
    key: "sla_fine",
    content: "",
    permissionCode: "info_modal_view_sla_fine",
  },
  {
    fieldName: "Data de cancelamento/rescisão",
    key: "cancellation_date",
    content: "",
    permissionCode: "info_modal_view_cancellation_date",
  },
  {
    fieldName: "Rescisão Aviso Prévio",
    key: "notice_rescission",
    content: "",
    permissionCode: "info_modal_view_notice_rescission",
  },
  {
    fieldName: "Invoice",
    key: "invoice",
    content: "",
    permissionCode: "info_modal_view_invoice",
  },
  {
    fieldName: "Ordem de compra",
    key: "purchase_order",
    content: "",
    permissionCode: "info_modal_view_purchase_order",
  },
  {
    fieldName: "Valor do invoice",
    key: "invoice_amount",
    content: "",
    permissionCode: "info_modal_view_invoice_amount",
  },
  {
    fieldName: "Status do invoice",
    key: "invoice_status",
    content: "",
    permissionCode: "info_modal_view_invoice_status",
  },
  {
    fieldName: "Período de reajuste",
    key: "reajustment_period",
    content: "",
    permissionCode: "info_modal_view_reajustment_period",
  },
  {
    fieldName: "Target",
    key: "target",
    content: "",
    permissionCode: "info_modal_view_target",
  },
  {
    fieldName: "Período performado",
    key: "performed_period",
    content: "",
    permissionCode: "info_modal_view_performed_period",
  },
  {
    fieldName: "Data inicial de serviço",
    key: "service_start_date",
    content: "",
    permissionCode: "info_modal_view_service_start_date",
  },
  {
    fieldName: "Contrato",
    key: "contract_bond",
    content: "",
    permissionCode: "info_modal_view_contract_bond",
  },
  {
    fieldName: "Prazo do contrato",
    key: "contract_deadline",
    content: "",
    permissionCode: "info_modal_view_contract_deadline",
  },
  {
    fieldName: "Valor previsto",
    key: "expected_value",
    content: "",
    permissionCode: "info_modal_view_expected_value",
  },
  {
    fieldName: "Número de Parcelas",
    key: "installments",
    content: "",
    permissionCode: "info_modal_view_value",
  },
  {
    fieldName: "Valor da Parcela",
    key: "installmentsValue",
    content: "",
    permissionCode: "info_modal_view_value",
  },
];
