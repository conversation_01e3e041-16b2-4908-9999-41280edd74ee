import { <PERSON><PERSON>, <PERSON>, Col, Form, Input, Row, Select } from "antd";
import {
    DeleteOutlined,
    PlusOutlined,
    ArrowLeftOutlined,
    SaveOutlined,
  } from "@ant-design/icons";
import { useState } from "react";
import { invalidInputNumberChars } from "../../constants/invalidCharsInputNumber";


export const CalculatorContent = () => {

    const { Option } = Select;
    const [recognizingFormList, setRecognizingFormList] = useState(false);


    const selectAfter = (
        <Select defaultValue="dolar">
          <Option value="dolar">$</Option>
          <Option value="reais">R$</Option>
        </Select>
    );
    
    return(
        
        <Form.List name="main_aws_investments">
          {(fields, { add, remove }) => (
            <>
              {fields.map((field, index) => (
                <Form.Item required={false} key={field.key}>
                  <Row gutter={24}>
                    <Col span={24}>
                      <Card
                        style={{
                          backgroundColor: "#F6F6F6",
                          boxShadow: "0 0 5px rgba(0,0,0,0.1)",
                          borderRadius: "5px",
                          marginRight: "10px",
                        }}
                      >
                        <Row gutter={24}>
                          <Col span={24}>
                            <Form.Item
                              label="Título"
                              {...field}
                              name={[field.name, "title"]}
                              required={true}
                            >
                              <Input
                                onChange={() =>
                                  setRecognizingFormList(!recognizingFormList)
                                }
                              />
                            </Form.Item>
                          </Col>
                          <Col lg={8} md={24}>
                            <Form.Item
                              label="Valor mensal"
                              {...field}
                              name={[field.name, "month_value"]}
                              required={true}
                            >
                              <Input
                                onKeyDown={(e) => {
                                  invalidInputNumberChars.find(
                                    (keyPressed) => keyPressed === e.key
                                  ) && e.preventDefault();
                                }}
                                addonAfter={selectAfter}
                                type="number"
                                min={0}
                                onChange={() =>
                                  setRecognizingFormList(!recognizingFormList)
                                }
                              />
                            </Form.Item>
                          </Col>
                          <Col lg={8} md={24}>
                            <Form.Item
                              label="Valor anual"
                              {...field}
                              name={[field.name, "year_value"]}
                              required={true}
                            >
                              <Input
                                onKeyDown={(e) => {
                                  invalidInputNumberChars.find(
                                    (keyPressed) => keyPressed === e.key
                                  ) && e.preventDefault();
                                }}
                                addonAfter={selectAfter}
                                type="number"
                                min={0}
                                onChange={() =>
                                  setRecognizingFormList(!recognizingFormList)
                                }
                              />
                            </Form.Item>
                          </Col>
                          <Col lg={8} md={24}>
                            <Form.Item
                              label="Valor Upfront"
                              {...field}
                              validateTrigger={["onChange", "onBlur"]}
                              name={[field.name, "upfront_value"]}
                            >
                              <Input
                                onKeyDown={(e) => {
                                  invalidInputNumberChars.find(
                                    (keyPressed) => keyPressed === e.key
                                  ) && e.preventDefault();
                                }}
                                addonAfter={selectAfter}
                                type="number"
                                min={0}
                                onChange={() =>
                                  setRecognizingFormList(!recognizingFormList)
                                }
                              />
                            </Form.Item>
                          </Col>
                        </Row>
                        <Row>
                          <Col span={24}>
                            <Form.Item
                              label="Link Público"
                              {...field}
                              name={[field.name, "calculator_link"]}
                              required={true}
                              rules={[
                                {
                                  type: "url",
                                  message: "Insira um link válido",
                                },
                              ]}
                            >
                              <Input type="url" />
                            </Form.Item>
                          </Col>
                        </Row>
                        <Row justify="end">
                          <Col>
                            {fields.length > 1 ? (
                              <Button
                                danger
                                type="text"
                                onClick={() => remove(field.name)}
                              >
                                Remover item
                                <DeleteOutlined />
                              </Button>
                            ) : null}
                          </Col>
                        </Row>
                      </Card>
                    </Col>
                  </Row>
                </Form.Item>
              ))}
              <Button
                type="text"
                onClick={() => add()}
              >
                Adicionar Custo AWS
                <PlusOutlined />
              </Button>
            </>
          )}
        </Form.List>
      
    );
};