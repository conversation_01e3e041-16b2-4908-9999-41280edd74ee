import {
  Row,
  Col,
  Select,
  Typography,
  Input,
  Tooltip,
  Card,
  Space,
  Form,
} from "antd";
import { useEffect, useState, useContext } from "react";
import { invalidInputNumberChars } from "../../constants/invalidCharsInputNumber";
import { formatHourName } from "../../controllers/Proposals/formatHourName";
import { ProposalContext } from "../../contexts/totalValuesProposals";
import { TotalValuesProposalType } from "../../contexts/reducers/totalValuesProposal";
import { totalValuesController } from "../../controllers/Proposals/totalValues";

async function updateProperty(
  prop,
  value,
  totalValuesState,
  hourClass,
  month,
  formName
) {
  let newObj = totalValuesState;
  let newObjTotalValues = newObj.totalValues;

  let newFilteredTotalValues = totalValuesState?.totalValues?.find(
    (v) => v.hourClass === hourClass
  );

  let newFilteredTotalValuesByMonth = newFilteredTotalValues?.values?.find(
    (v) => v.month === month
  );

  newFilteredTotalValuesByMonth?.values?.map((v) => {
    if (v.formName === formName) {
      if (value === "reais" || value === "percentage") {
        v[prop] = value;
      } else {
        v[prop] = Number(value);
      }
    }
  });

  console.log(
    { newFilteredTotalValues },
    { newFilteredTotalValuesByMonth },
    { totalValuesState }
  );

  for (let i = 0; i < newObjTotalValues.length; i++) {
    console.log("newObjTotalValues[i]: ", newObjTotalValues[i]);
    if (newObjTotalValues[i].hourClass === hourClass) {
      newObjTotalValues[i] = newFilteredTotalValues;
    }
  }
  return newObjTotalValues;
}

export const AdditionalHourValueCardDaredeInvestments = ({
  title, // 8x5 SETUP,
  hourClass,
  costManagementData,
  services,
  state,
}) => {
  console.log({ title });
  const { Text } = Typography;
  const { Option } = Select;
  const { proposalState, setProposalState } = useContext(ProposalContext);

  const filteredTotalValues = proposalState?.totalValues?.find(
    (v) => v.hourClass === hourClass
  );

  const filteredTotalValuesByMonth = filteredTotalValues?.values?.find(
    (v) => v.month === proposalState?.month
  );

  console.log({ hourClass }, { filteredTotalValuesByMonth });

  const handleInputValue = async (value, hourClass, hourType) => {
    console.log({ value }, hourClass);
    if (value < 0) value = 0;
    if (value === "reais" || value === "percentage") {
      const newState = await updateProperty(
        "discountUnit",
        value,
        proposalState,
        hourClass,
        proposalState?.month,
        hourType.formName
      );
      console.log({ newState });
      const calculatedValue = await totalValuesController(
        newState,
        proposalState.month,
        costManagementData,
        services,
        state,
        hourClass
      );
      // const calculatedValue = await totalValuesController(
      //   proposalState,
      //   costManagementData,
      //   services
      // );
      setProposalState({
        type: TotalValuesProposalType.SET_TOTAL_VALUES,
        value: calculatedValue,
      });
    } else {
      const newState = await updateProperty(
        "discountValue",
        value,
        proposalState,
        hourClass,
        proposalState?.month,
        hourType.formName
      );
      console.log({ newState });

      const calculatedValue = await totalValuesController(
        newState,
        proposalState.month,
        costManagementData,
        services,
        state,
        hourClass
      );
      setProposalState({
        type: TotalValuesProposalType.SET_TOTAL_VALUES,
        value: calculatedValue,
      });
    }
  };

  const getDiscountDefaultValue = (t) => {
    console.log({ t });
    return t.discountValue;
  };

  const getDiscountDefaultUnit = (t) => {
    return t.discountUnit;
  };

  return (
    <Col xxl={8} xl={24} sm={24} style={{ textAlign: "center" }}>
      <Text>{title}</Text>
      <Card
        style={{
          width: "100%",
          backgroundColor: title.includes("Adicional") ? "#fff" : "#0F9347",
          borderColor: title.includes("Adicional") ? "#000" : "none",
          borderRadius: "5px",
        }}
      >
        <Row justify="space-between" gutter={[4, 16]}>
          {filteredTotalValuesByMonth
            ? filteredTotalValuesByMonth?.values?.map((t, i) => {
                return (
                  <Col span={12} style={{ textAlign: "center" }}>
                    <Text
                      style={{
                        color: title.includes("Adicional") ? "#000" : "#fff",
                      }}
                    >
                      {formatHourName(t.formName)}
                    </Text>
                    <Space size={"small"}>
                      <Tooltip title="Valor hora">
                        <Input
                          disabled
                          placeholder={`Valor em Gerenciamento de Custos`}
                          style={{ textAlign: "center", color: "#000" }}
                          type="number"
                          value={t.hourValue}
                        />
                      </Tooltip>
                      <Tooltip title="Valor do desconto">
                        <Input
                          style={{ textAlign: "center" }}
                          placeholder="Valor do desconto"
                          type="number"
                          defaultValue={getDiscountDefaultValue(t)}
                          onKeyDown={(e) => {
                            invalidInputNumberChars.find(
                              (keyPressed) => keyPressed === e.key
                            ) && e.preventDefault();
                          }}
                          onChange={(e) =>
                            handleInputValue(
                              e.target.value,
                              hourClass === "additionalHourValue"
                                ? "additionalHourValue"
                                : "hourValue", // since it is not discount values
                              t
                            )
                          }
                          addonAfter={
                            <Select
                              style={{ padding: 0 }}
                              defaultValue={getDiscountDefaultUnit(t)}
                              onChange={(value) => {
                                handleInputValue(
                                  value,
                                  hourClass === "additionalHourValue"
                                    ? "additionalHourValue"
                                    : "hourValue", // since it is not discount values
                                  t
                                );
                              }}
                            >
                              <Option value="reais">R$</Option>
                              <Option value="percentage">%</Option>
                            </Select>
                          }
                        />
                      </Tooltip>
                    </Space>
                  </Col>
                );
              })
            : null}
        </Row>
      </Card>
    </Col>
  );
};
