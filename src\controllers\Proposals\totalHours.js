export const totalHoursController = async (services) => {
  let supportMainHours85Setup = [];
  let supportMainHours85Sust = [];
  let supportMainHours247Setup = [];
  let supportMainHours247Sust = [];
  let supportMainHours85SEC = [];
  let supportMainHours247SEC = [];
  let percentages = [];

  services.map((service) => {
    if (service.management === true) {
      service.tasks.map((task) => {
        if (task.taskTime.type === "percentage") {
          percentages.push(task.taskTime.time);
        } else {
          supportMainHours85Setup.push(task.taskTime.time);
        }
      });
    } else {
      service.tasks.map((task) => {
        supportMainHours85Setup.push(task.estimates["8x5setup"]);
        supportMainHours85Sust.push(task.estimates["8x5sust"]);
        supportMainHours247Setup.push(task.estimates["24x7setup"]);
        supportMainHours247Sust.push(task.estimates["24x7sust"]);
        supportMainHours85SEC.push(task.estimates["8x5dbasec"]);
        supportMainHours247SEC.push(task.estimates["24x7dbasec"]);
      });
    }
  });

  let newTotalHours = [];

  let value = 0;

  let value8x5 = supportMainHours85Setup.reduce((a, b) => a + b, 0);
  let value24x7 = supportMainHours247Setup.reduce((a, b) => a + b, 0);
  value8x5 =
    value8x5 +
    Math.ceil(
      ((value8x5 + value24x7) * percentages.reduce((a, b) => a + b, 0)) / 100
    );
  newTotalHours.push({
    type: "SETUP",
    formName: "setup8x5",
    value: value8x5,
  });

  newTotalHours.push({
    type: "SETUP",
    formName: "setup24x7",
    value: value24x7,
  });

  value = supportMainHours85Sust.reduce((a, b) => a + b, 0);
  newTotalHours.push({
    type: "SUST",
    formName: "sust8x5",
    value: value,
  });

  value = supportMainHours247Sust.reduce((a, b) => a + b, 0);
  newTotalHours.push({
    type: "SUST",
    formName: "sust24x7",
    value: value,
  });

  value = supportMainHours85SEC.reduce((a, b) => a + b, 0);
  newTotalHours.push({
    type: "SEC",
    formName: "dbasec8x5",
    value: value,
  });

  value = supportMainHours247SEC.reduce((a, b) => a + b, 0);
  newTotalHours.push({
    type: "SEC",
    formName: "dbasec24x7",
    value: value,
  });

  return newTotalHours;
};
