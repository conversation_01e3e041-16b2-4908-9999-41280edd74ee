import ExcelJS from "exceljs";
import {
  generateExcel,
  getContractInfoByPermission,
} from "../../../controllers/contracts/contract-controller";
import {
  contractsViewMock,
  permissionsMock,
  filteredContractsMock,
  allContractsMock,
  contractInfoByPermissionMock,
} from "../../../__mocks__/contracts";

describe("getContractInfoByPermission", () => {
  test("should return the contracts correctly formatted", async () => {
    const result = await getContractInfoByPermission(
      permissionsMock,
      filteredContractsMock,
      allContractsMock,
      contractsViewMock
    );

    const expected = [
      {
        fieldName: "ID do contrato",
        content: 55,
        permissionCode: "info_modal_view_contract_id",
        key: "itsm_id",
      },

      {
        fieldName: "Data de início da vigência",
        content: "07/07/2022",
        permissionCode: "info_modal_view_start_date",
        key: "expected_start_date",
      },

      {
        fieldName: "Data de fim da vigência",
        content: "07/07/2023",
        permissionCode: "info_modal_view_end_date",
        key: "expected_close_date",
      },

      {
        fieldName: "Data de Criação",
        content: "31/10/2022",
        permissionCode: "info_modal_view_creation_date",
        key: "created_at",
      },

      {
        fieldName: "Nome do contrato",
        content: "Contract1",
        permissionCode: "info_modal_view_contract_name",
        key: "name",
      },

      {
        fieldName: "Escopo",
        content: "Scope Contract1",
        permissionCode: "info_modal_view_scope",
        key: "scope",
      },

      {
        fieldName: "Squad",
        content: "SUST-E2",
        permissionCode: "info_modal_view_squad",
        key: "squad",
      },

      {
        fieldName: "Total de horas",
        content: "0",
        permissionCode: "info_modal_view_total_hours",
        key: "total_hours",
      },

      {
        fieldName: "Tipo de consumo",
        content: "Billing",
        permissionCode: "info_modal_view_type_hours",
        key: "type_hours",
      },
    ];

    expect(result.length).toEqual(4);
    expect(result[0]).toEqual(expected);
  });
});

describe("generateExcel", () => {
  it("should return a Blob with the correct 'xlsx' type", async () => {
    const blob = await generateExcel(contractInfoByPermissionMock);
    expect(blob instanceof Blob).toBeTruthy();
    expect(blob.type).toBe(
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    );
  });

  it("should verify the presence of 'Contratos' tabs in the generated Excel file", async () => {
    const blob = await generateExcel(contractInfoByPermissionMock);
    const workbook = new ExcelJS.Workbook();
    await workbook.xlsx.load(blob);

    const constractsSheet = workbook.getWorksheet("Contratos");

    expect(constractsSheet).toBeDefined();
  });

  it("should verify the correctness of cell values in the generated Excel file", async () => {
    const blob = await generateExcel(contractInfoByPermissionMock);
    const workbook = new ExcelJS.Workbook();
    await workbook.xlsx.load(blob);
    const constractsSheet = workbook.getWorksheet("Contratos");

    const expected = [
      [
        "ID do contrato",
        "Data de início da vigência",
        "Data de fim da vigência",
        "Data de Criação",
        "Nome do contrato",
        "Escopo",
        "Squad",
        "Total de horas",
        "Tipo de consumo",
      ],
      [
        55,
        "07/07/2022",
        "07/07/2023",
        "31/10/2022",
        "Contract1",
        "Scope Contract1",
        "SUST-E2",
        "0",
        "Billing",
      ],
      [
        57,
        "07/07/2022",
        "07/07/2023",
        "31/10/2022",
        "Contract3",
        "Scope Contract3",
        "SUST-E2",
        "0",
        "Billing",
      ],
      [
        58,
        "07/07/2022",
        "07/07/2023",
        "31/10/2022",
        "Contract4",
        "Scope Contract4",
        "SUST-E2",
        "0",
        "Billing",
      ],
      [
        59,
        "07/07/2022",
        "07/07/2023",
        "31/10/2022",
        "Contract5",
        "Scope Contract5",
        "SUST-E2",
        "0",
        "Billing",
      ],
    ];

    constractsSheet.eachRow((row, rowNumber) => {
      const actualRowData = [];
      row.eachCell((cell) => {
        actualRowData.push(cell.value);
      });
      expect(actualRowData).toEqual(expected[rowNumber - 1]);
    });
  });
});
