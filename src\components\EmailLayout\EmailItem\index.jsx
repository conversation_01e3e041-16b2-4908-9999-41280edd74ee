import { useContext, useState } from 'react'
import { EmailAutomationContext } from '../../../contexts/email-automation'
import { EmailAutomationReducerType } from '../../../contexts/reducers/email-automation-reducer'
// import { removeItemAlert } from '../../shared/alerts/remove-item-alert'
import { EmailText } from '../EmailText'
import { TextEditor } from '../EmailTextEditor'


export const EmailItem = ({ value, positionItem }) => {
    const { emailAutomationState, setEmailAutomationState } = useContext(EmailAutomationContext)
    const { templateList } = emailAutomationState
    const [isEdit, setIsEdit] = useState(false)
    const [text, setText] = useState("")

    function removeItemMessage() {
        // removeItemAlert({
        //     onSuccessCallback: onRemoveItem
        // })
    }

    return (
        isEdit ?
            <TextEditor 
                setIsEdit={setIsEdit}
                value={value} 
                positionItem={positionItem}
            />
            :
            <EmailText 
                setIsEdit={setIsEdit}
                value={value} 
                positionItem={positionItem}
            />
    )
}