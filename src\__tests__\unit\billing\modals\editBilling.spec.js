import { formattedCommitments } from "../../../../components/Modals/Billing/controllers/editBilling";

describe("formattedCommitments", () => {
  it("Should only keep commitments with commitmentRange as an array and format the remaining commitments", () => {
    const commitments = [
      {
        id: 1,
        commitmentRange: ["2023-01", "2023-12"],
        commitmentValue: "30000",
      },
      { id: 2, commitmentRange: [], commitmentValue: "" },
      {
        id: 3,
        commitmentRange: ["2024-01", "2024-12"],
        commitmentValue: "40000",
      },
      { id: 4 },
    ];

    const formatted = formattedCommitments(commitments);

    expect(formatted).not.toContainEqual({ id: 2, commitmentRange: [] });
    expect(formatted).not.toContainEqual({ id: 4 });

    expect(formatted).toContainEqual({
      id: 1,
      commitmentRange: { startDate: "2023-01", endDate: "2023-12" },
      commitmentValue: "30000"
    });
    expect(formatted).toContainEqual({
      id: 3,
      commitmentRange: { startDate: "2024-01", endDate: "2024-12" },
      commitmentValue: "40000"
    });
  });
});
