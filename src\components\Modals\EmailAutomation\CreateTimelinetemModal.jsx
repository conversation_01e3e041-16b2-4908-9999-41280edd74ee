import React, { useEffect } from "react";
import { InfoCircleOutlined } from "@ant-design/icons";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import {
  Button,
  Form,
  Input,
  Modal,
  Tooltip,
  Row,
  Col
} from "antd";

export const CreateTimelineItemModal = ({ isOpen, toggle, onConfirm, data }) => {
  const [form] = Form.useForm();

  useEffect(() => {
    if (data) {
      const { date, text, category } = data

      form.setFieldsValue({
        startDate: format(
          new Date(date),
          "yyyy-MM-dd",
          { locale: ptBR }
        ),
        tag: category.tag,
        description: text
      });

      return
    }

    form.resetFields()

  }, [form, data])

  function handleConfirm() {

    const formData = form.getFieldsValue()

    let newData = {
      description: formData.description,
      position: formData.position,
      tag: formData.tag,
      startDate: `${formData.startDate} 03:00:00`,
      position: data ? data.position : null
    }

    onConfirm(newData)
  }

  return (
    <Modal
      title="Adicionar Item"
      visible={isOpen}
      onOk={handleConfirm}
      okText="Adicionar"
      onCancel={toggle}
      cancelText="Cancelar"
    >
      <Form
        layout="vertical"
        onFinish={toggle}
        requiredMark={false}
        form={form}
      >

        <Row style={{ justifyContent: 'space-between' }} >
          <Col span={8}>
            <FormItem
              type="date"
              label="Data de Início:"
              name="startDate"
              tooltip="Data de início atividade"
              required={true}
              messageRequired="Prencha este campo."
            />
            {/* <Form.Item
              rules={[{ required: true, message: "Prencha este campo." }]}
              name="startDate"
              label={
                <>
                  Data de Início
                  <Tooltip title="Data de início atividade">
                    <Button type="text">
                      <InfoCircleOutlined />
                    </Button>
                  </Tooltip>
                </>
              }
            >
              <MaskedInput
                placeholder="Data de Início"
                mask="00/00/0000"
              />
            </Form.Item> */}

          </Col>
          <Col span={15}>
            <FormItem
              type="text"
              label="Tag:"
              name="tag"
              tooltip="Tag de indicação da atividade"
              required={true}
              messageRequired="Prencha este campo."
            />
          </Col>
        </Row>

        <Row>
          <Col span={24}>
            <FormItem
              type="text"
              label="Descrição:"
              required={true}
              messageRequired="Prencha este campo."
              tooltip="Descrição da atividade"
              name="description"
            />
          </Col>
        </Row>
      </Form>
    </Modal>
  );
};


const FormItem = React.forwardRef(({ required, messageRequired, label, tooltip, type, name }, ref) => {
  return (
    <Form.Item
      rules={[{ required, message: messageRequired }]}
      label={
        <>
          {label}
          <Tooltip title={tooltip}>
            <Button type="text">
              <InfoCircleOutlined />
            </Button>
          </Tooltip>
        </>
      }
      name={name}
    >
      <Input
        type={type}
        ref={ref}
      />
    </Form.Item>
  )
})