import {
  PlusOutlined,
  DeleteOutlined,
  ContactsOutlined,
  CheckOutlined,
} from "@ant-design/icons";
import { useEffect, useState } from "react";
import {
  Button,
  Modal,
  Row,
  Col,
  Table,
  Typography,
  Input,
  Form,
  message,
  Popconfirm,
} from "antd";
import { v4 } from "uuid";
import { maskPhone } from "../../../utils/masks";
import { validateEmail } from "../../../utils/validators";

export const AddContact = (contactsParent) => {
  const { Text, Title } = Typography;
  const [contactForm] = Form.useForm();
  const [showModal, setShowModal] = useState();
  const { contacts, setContacts } = contactsParent;
  const [childContacts, setChildContacts] = useState([]);
  const [currentContact, setCurrentContact] = useState({
    id: "",
    name: "",
    email: "",
    phone: "",
  });

  useEffect(() => {
    setChildContacts(contactsParent.contactsParent);
  }, [contactsParent.contactsParent]);

  const handleSubmit = (e) => {
    e.preventDefault();
    setContacts(childContacts);
    setShowModal(false);
  };

  const handleRemoveItem = (props) => {
    const prev = childContacts;
    setChildContacts(prev.filter((contact) => contact.email !== props.email));
    message.success(`Contato "${props.email}" removido com sucesso!`);
  };

  const handleCloseModal = () => {
    setCurrentContact({});
    contactForm.resetFields(["name", "lastName", "email", "phone"]);
    setShowModal(false);
  };

  const handleAddItem = (e) => {
    if (contactsParent.contactsParent === undefined) {
      message.error(`Nenhum Cliente foi selecionado!`);
      handleCloseModal();
    } else if (
      childContacts?.find((contact) => contact?.email === currentContact?.email)
    ) {
      message.error(`Contato "${currentContact?.email}" já existe!`);
    } else {
      let index = 0;

      const addedItem = {
        contact_id: v4(),
        customer_id: childContacts[0]?.customer_id,
        identifications: childContacts[0]?.identifications,
        responsability: childContacts[0]?.responsability,
        contact_type: null,
        active: 1,
        names: {
          first_name: currentContact?.first_name,
          last_name: currentContact?.last_name,
        },
        email: currentContact?.email,
        phone: currentContact?.phone.replace(/\D/g, ""),
      };

      if (!validateEmail(addedItem.email)) {
        message.error("Email inválido");
        return;
      }

      if (addedItem.phone.length < 10) {
        message.error("Telefone inválido");
        return;
      }

      setChildContacts((prev) => [
        ...prev.slice(0, index + 1),
        addedItem,
        ...prev.slice(index + 1),
      ]);
      setCurrentContact({});
      contactForm.resetFields(["name", "lastName", "email", "phone"]);
      message.success(
        `Contato "${currentContact.email}" adicionado com sucesso!`
      );
    }
  };

  const columns = [
    {
      title: "Nome",
      dataIndex: "name",
      key: "name",
    },
    {
      title: "Email",
      dataIndex: "email",
      key: "email",
    },
    {
      title: "Telefone",
      dataIndex: "phone",
      key: "phone",
      align: "center",
      render: (phone) => {
        return <span>{maskPhone(phone)}</span>;
      },
    },
    {
      title: "Remover",
      dataIndex: "remove",
      key: "remove",
      align: "center",
      render: (id, props) => {
        return (
          <Popconfirm
            title={"Tem certeza que deseja remover este contato?"}
            okText={"Sim"}
            cancelText={"Não"}
            onConfirm={(e) => {
              handleRemoveItem(props);
            }}
          >
            <Button danger type="text" icon={<DeleteOutlined />}></Button>
          </Popconfirm>
        );
      },
    },
  ];

  return (
    <>
      <Button
        type="text"
        onClick={() => {
          setChildContacts(contactsParent.contactsParent);
          setShowModal(true);
        }}
      >
        <PlusOutlined />
      </Button>
      <Modal
        title="Adicionar Contato"
        visible={showModal}
        width={"80%"}
        closable={false}
        destroyOnClose={true}
        footer={null}
        onCancel={() => {
          setShowModal(false);
          setCurrentContact({});
          contactForm.resetFields(["name", "lastName", "email", "phone"]);
          setContacts(childContacts);
        }}
      >
        <Form layout="horizontal" form={contactForm} onFinish={handleSubmit}>
          <Row justify="center" gutter={[16, 16]}>
            <Col span={6}>
              <Title level={5} style={{ fontWeight: "400", fontSize: "14px" }}>
                Nome
              </Title>
              <Form.Item name={"name"}>
                <Input
                  required
                  onChange={(e) => {
                    setCurrentContact({
                      ...currentContact,
                      first_name: e.target.value,
                    });
                  }}
                />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Title level={5} style={{ fontWeight: "400", fontSize: "14px" }}>
                Sobrenome
              </Title>
              <Form.Item name={"lastName"}>
                <Input
                  required
                  onChange={(e) => {
                    setCurrentContact({
                      ...currentContact,
                      last_name: e.target.value,
                    });
                  }}
                />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Title level={5} style={{ fontWeight: "400", fontSize: "14px" }}>
                Email
              </Title>
              <Form.Item name={"email"}>
                <Input
                  required
                  onChange={(e) => {
                    setCurrentContact({
                      ...currentContact,
                      email: e.target.value,
                    });
                  }}
                />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Title level={5} style={{ fontWeight: "400", fontSize: "14px" }}>
                Telefone
              </Title>
              <Input
                type="text"
                required
                value={maskPhone(currentContact.phone)}
                maxLength={15}
                onChange={(e) => {
                  setCurrentContact({
                    ...currentContact,
                    phone: e.target.value,
                  });
                }}
              />
            </Col>
          </Row>

          <Row justify="center">
            <Form.Item>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={handleAddItem}
                disabled={
                  !currentContact.first_name ||
                  !currentContact.last_name ||
                  !currentContact.email ||
                  !currentContact.phone
                }
              >
                Adicionar
              </Button>
            </Form.Item>
          </Row>
        </Form>

        <Row style={{ marginBottom: "1rem" }}>
          <Col span={24}>
            {childContacts?.length > 0 ? (
              <Table
                dataSource={childContacts.map((item, index) => {
                  return {
                    id: index,
                    name: `${item?.names?.first_name} ${item?.names?.last_name}`,
                    email: item?.email,
                    phone: item?.phone ? item.phone : "",
                  };
                })}
                columns={columns}
                scroll={{ x: 100 }}
                pagination={true}
              ></Table>
            ) : (
              <Row justify="center" gutter={[24, 16]}>
                <Col span={24}>
                  <Row justify="center">
                    <ContactsOutlined style={{ fontSize: "5rem" }} />
                  </Row>
                  <Row justify="center">
                    <Title level={5} style={{ fontWeight: "400" }}>
                      Não existem outros contatos inseridos selecionados
                    </Title>
                  </Row>
                </Col>
              </Row>
            )}
          </Col>
        </Row>

        <Row gutter={[16, 16]} justify={"space-between"}>
          <Button type="text-color" onClick={handleCloseModal}>
            Cancelar <DeleteOutlined />
          </Button>
          <Button type="primary" onClick={handleSubmit}>
            Salvar <CheckOutlined />
          </Button>
        </Row>
      </Modal>
    </>
  );
};
