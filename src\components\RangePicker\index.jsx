import React from "react";
import { DatePicker } from "antd";
import moment from "moment";

export const RangePickerStandard = ({
  onChange,
  value,
  placeholder = ["Selecione um período", "Selecione um período"],
}) => {
  const format = "DD/MM/YYYY";
  const { RangePicker } = DatePicker;

  let firstDayOfCurrentMonth = new Date(
    new Date().getFullYear(),
    new Date().getMonth(),
    1
  );
  let lastDayOfCurrentMonth = new Date(
    new Date().getFullYear(),
    new Date().getMonth() + 1,
    0
  );
 
  return (
    <RangePicker
      style={{ width: "100%" }}
      onChange={onChange}
      defaultValue={[
        moment(firstDayOfCurrentMonth, format),
        moment(lastDayOfCurrentMonth, format),
      ]}
      value={
        value.length === 0
          ? [
              moment(firstDayOfCurrentMonth, format),
              moment(lastDayOfCurrentMonth, format),
            ]
          : [moment(value[0], format), moment(value[1], format)]
      }
      allowClear={false}
      placeholder={placeholder}
      format={format}
    />
  );
};
