import { useState, useEffect, useCallback, useRef, useMemo } from "react";
import { Chart as ChartJS, ArcElement, Tooltip } from "chart.js";
import { <PERSON><PERSON><PERSON>, <PERSON>, Legend, Sector } from "recharts";
import { Pie as Pie2, getElementsAtEvent } from "react-chartjs-2";
import { ArrowLeftOutlined, StarFilled, StarOutlined } from "@ant-design/icons";
import { Card, Col, Modal, Select, Spin, Typography, Row, message } from "antd";
import ExpirationOfContractsTable from "../../Table/ExpirationOfContracts";
import "./TicketsOfWalletManages.css";
import { otrsGet, otrsPost } from "../../../service/apiOtrs";
import moment from "moment";
import { Radio } from "antd";
import TicketsOfWallerManagesTable from "../../Table/TicketsOfWalletManages";
import { filterByOrderOfInput } from "../../../utils/filterByOrderOfInput";
import { CockpitsHeader } from "../../CockpitsHeader";

const MAIN_PAGE = 0;
const OPEN_PAGE = 1;
const PENDING_PAGE = 2;
const REOPENED_PAGE = 3;

export function MainGraph(props) {
  const { Title, Text } = Typography;
  const [isFavorite, setIsFavorite] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [allContracts, setAllContracts] = useState([]);
  const [allClients, setAllClients] = useState([]);
  const [allTickets, setAllTickets] = useState([]);
  const [open, setOpen] = useState([]);
  const [pending, setPending] = useState([]);
  const [reopened, setReopened] = useState([]);
  const [currentCustomer, setCurrentCustomer] = useState("");
  const [currentContract, setCurrentContract] = useState("");
  const [selectedIndex, setSelectedIndex] = useState();
  const [loading, setLoading] = useState(false);
  const chartRef = useRef();
  const { Option } = Select;
  const GRAPH_NAME = "TicketWalletManagement";

  const data = {
    labels: [" Abertos", " Pendentes", " Reabertos"],
    datasets: [
      {
        label: "# of Votes",
        data: [open.length, pending.length, reopened.length],
        backgroundColor: [
          "rgba(65, 142, 77, 1)",
          "rgba(112, 211, 63, 1)",
          "rgba(17, 53, 22, 1)",
        ],
        borderColor: [
          "rgba(255, 255, 255, 1)",
          "rgba(255, 255, 255, 1)",
          "rgba(255, 255, 255, 1)",
        ],
        borderWidth: 1,
      },
    ],
  };

  let amountTickets = useMemo(() => {
    if (data) {
      let sum = data?.datasets[0].data.reduce((prev, curr) => {
        return prev + curr;
      }, 0);
      return sum;
    }
    return 0, [data];
  });

  let isHoverable = useMemo(() => {
    if (currentCustomer && amountTickets > 0) {
      return true;
    }

    return false;
  }, [currentCustomer, amountTickets]);

  function getClientTickets(event) {
    getContracts(event);
    setCurrentCustomer(event);

    otrsPost(`read/tickets/customer`, { params: event }).then((resp) => {
      let openArray = [];
      let reopenedArray = [];
      let pendingArray = [];
      resp.data.forEach((ticket) => {
        if (ticket.ticket_state === "open") {
          openArray.push(ticket);
        } else if (ticket.ticket_state === "reopened") {
          reopenedArray.push(ticket);
        } else if (
          ticket.ticket_state.toLowerCase().includes("pending") ||
          ticket.ticket_state.toLowerCase().includes("pendente")
        ) {
          pendingArray.push(ticket);
        }
      });
      setAllTickets(resp.data);
      setOpen(openArray);
      setReopened(reopenedArray);
      setPending(pendingArray);
    });
  }

  async function getContracts(clientName) {
    setLoading(true);
    try {
      const { data } = await otrsGet(`read/customer/name/${clientName}`);
      let customerId = data[0].customer_id;
      try {
        await otrsGet(`read/contract/customer/${customerId}`).then(
          async (clientContracts) => {
            let arrayNames = [];
            Object.entries(clientContracts.data).forEach((val) => {
              arrayNames.push(val[1].name);
            });
            setAllContracts(arrayNames);
            setLoading(false);
          }
        );
      } catch (err) {
        setLoading(false);
        message.error("Erro ao carregar contratos");
        console.log(err);
      }
    } catch (err) {
      setLoading(false);
      message.error("Erro ao carregar contratos");
      console.log(err);
    }
  }

  function filterByContracts(event) {
    let open = [];
    let reopened = [];
    let pending = [];
    const [wordToFind, ...rest] = event.split(" ");
    const filteredTickets = allTickets.filter(
      (i) => i?.contract_name?.toLowerCase() === event?.toLowerCase()
    );
    filteredTickets.forEach((ticket) => {
      if (ticket.ticket_state === "open") {
        open.push(ticket);
      } else if (ticket.ticket_state === "reopened") {
        reopened.push(ticket);
      } else if (!ticket.ticket_state.includes("closed")) {
        pending.push(ticket);
      }
    });

    setOpen(open);
    setReopened(reopened);
    setPending(pending);
  }

  function showModal(event) {
    const graphic = getElementsAtEvent(chartRef.current, event);
    if (graphic[0]) {
      switch (graphic[0].index) {
        case 0:
          setSelectedIndex(0);
          setModalVisible(true);
          break;
        case 1:
          setSelectedIndex(1);
          setModalVisible(true);
          break;
        case 2:
          setSelectedIndex(2);
          setModalVisible(true);
          break;
        default:
          setSelectedIndex(undefined);
          setModalVisible(false);
          break;
      }
    }
  }

  function closeModal() {
    setModalVisible(false);
  }

  function checkIsVisible() {
    let localStorageData = JSON.parse(localStorage.getItem("favorites"));
    if (localStorageData) {
      let index = localStorageData.findIndex(
        (value) => value.component === GRAPH_NAME
      );

      return index !== -1;
    } else {
      return false;
    }
  }

  async function updateFavorite() {
    await props?.setVisionFavorite(GRAPH_NAME);
    setIsFavorite(checkIsVisible());
  }

  useEffect(() => {
    setIsFavorite(checkIsVisible());
  }, [props.favorites]);

  function getSelectedTickets() {
    if (selectedIndex === 0) {
      return open;
    } else if (selectedIndex === 1) {
      return pending;
    } else if (selectedIndex === 2) {
      return reopened;
    }
  }

  const removeDuplicates = (array) => {
    let uniqueArray = [];
    for (let i = 0; i < array.length; i++) {
      if (uniqueArray.indexOf(array[i]) === -1) {
        uniqueArray.push(array[i]);
      }
    }
    return uniqueArray;
  };

  const sortAlpha = (array) => {
    const sortedArray = array.sort((a, b) => {
      if (a.toLowerCase() > b.toLowerCase()) {
        return 1;
      }
      if (a.toLowerCase() < b.toLowerCase()) {
        return -1;
      }
      return 0;
    });
    return removeDuplicates(sortedArray);
  };

  const cardsContent = [
    {
      title: "Abertos",
      value: open.length,
      color: "#74CF49",
    },
    {
      title: "Pendentes",
      value: pending.length,
      color: "rgb(197,225,3)",
    },
    {
      title: "Reabertos",
      value: reopened.length,
      color: "#F5A623",
    },
  ];

  useEffect(() => {
    if (props.allClients.length > 0) setAllClients(props.allClients);
  }, [props.allClients]);

  const handleCardClick = (index, numOfTickets, typeOfTicket) => {
    if (numOfTickets === 0) {
      const formatedTicketName = typeOfTicket
        .substring(0, typeOfTicket.length - 1)
        .toLowerCase();
      message.warning(`Nenhum ticket ${formatedTicketName}`);
      return;
    }

    if (index >= 0 && index <= 3) {
      if (currentCustomer && amountTickets > 0) {
        setSelectedIndex(index);
        setModalVisible(true);
      }
    }
  };
  return (
    <Card
      bordered="false"
      style={{
        borderRadius: "20px",
        height: "100%",
        boxShadow: "0 0 10px rgba(0,0,0,0.1)",
        minWidth: "365px",
        minHeight: "483px",
        alignItems: loading ? "center" : "",
        display: loading ? "flex" : "",
        justifyContent: loading ? "center" : "",
      }}
    >
      {loading ? (
        <Row>
          <Spin style={{ marginRight: "5px" }} />
          <Text>Carregando...</Text>
        </Row>
      ) : (
        <>
          <CockpitsHeader
            title={
              <Col span={22}>
                <Title level={4} style={{ fontWeight: "400" }}>
                  Gerenciamento de tickets por contrato
                </Title>
              </Col>
            }
            isFavorite={isFavorite}
            updateFavorite={updateFavorite}
          />
          <Row justify="space-between">
            <Col span={12}>
              <Select
                placeholder="Selecionar cliente"
                value={currentCustomer ? currentCustomer : undefined}
                loading={allClients.length === 0}
                mode="default"
                optionFilterProp="children"
                showSearch
                filterOption={(input, option) =>
                  filterByOrderOfInput(input, option.props.children)
                }
                onChange={(event) => getClientTickets(event)}
                style={{ width: "90%" }}
                filterSort={(optionA, optionB) => {
                  return optionA.children
                    .toLowerCase()
                    .localeCompare(optionB.children.toLowerCase());
                }}
              >
                {allClients.map(
                  (val, index) =>
                    val.name !== "" && (
                      <Option
                        key={index}
                        value={val?.names?.name || val?.names?.fantasy_name}
                      >
                        {val?.names?.name || val?.names?.fantasy_name}
                      </Option>
                    )
                )}
              </Select>
            </Col>
            <Col span={12}>
              <Select
                id="contract-select"
                mode="default"
                loading={allContracts.length === 0 && allTickets.length !== 0}
                disabled={allTickets.length === 0}
                placeholder="Selecionar contrato"
                onChange={(event) => filterByContracts(event)}
                style={{ width: "100%" }}
                optionFilterProp="children"
                showSearch
                filterOption={(input, option) =>
                  option.props.children
                    .toLowerCase()
                    .indexOf(input.toLowerCase()) >= 0 ||
                  option.props.value
                    .toLowerCase()
                    .indexOf(input.toLowerCase()) >= 0
                }
              >
                {sortAlpha(allContracts).map((val, index) => (
                  <Option key={index} value={val}>
                    {val}
                  </Option>
                ))}
              </Select>
            </Col>
          </Row>
          <Row align="middle">
            <Col span={12}>
              {cardsContent.map((card, index) => {
                return (
                  <Col span={20} key={index}>
                    <Card
                      hoverable={isHoverable}
                      style={{
                        margin: "8px 0",
                        borderRadius: "8px",
                        cursor: "pointer",
                        borderColor: card.color,
                      }}
                      bordered={true}
                      onClick={() =>
                        handleCardClick(index, card.value, card.title)
                      }
                    >
                      <Title level={5} style={{ fontWeight: 400 }}>
                        {card.title}
                      </Title>
                      <Text>{card.value}</Text>
                    </Card>
                  </Col>
                );
              })}
            </Col>
            <Col span={12}>
              {currentCustomer && amountTickets > 0 ? (
                <Pie2
                  ref={chartRef}
                  onClick={(event) => showModal(event)}
                  data={data}
                  options={{
                    responsive: true,
                    cutoutPercentage: 80,
                    plugins: {
                      legend: {
                        display: false,
                      },
                    },
                  }}
                />
              ) : (
                <div
                  style={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    width: "100%",
                    height: "100%",
                  }}
                >
                  {currentCustomer
                    ? "Nenhum ticket vinculado a este contrato*"
                    : "Selecione um cliente*"}
                </div>
              )}
            </Col>
          </Row>
          <Modal
            width={"90vw"}
            className="flex justify-center"
            bodyStyle={{ backgroundColor: "transparent" }}
            cancelButtonProps={{ style: { display: "none" } }}
            onCancel={closeModal}
            centered
            footer={null}
            okButtonProps={{
              style: {
                display: "none",
                backgroundColor: "#43914F",
                color: "white",
                fontWeight: "bold",
                borderRadius: "5px",
              },
            }}
            open={modalVisible}
          >
            <TicketsOfWallerManagesTable
              tickets={getSelectedTickets()}
              closeModal={closeModal}
            />
          </Modal>
        </>
      )}
    </Card>
  );
}

export function OpenedDonutThree(props) {
  const { tickets } = props;

  const [activeIndex, setActiveIndex] = useState(0);
  const [period, setPeriod] = useState(3);
  const [data, setData] = useState([]);
  const [modalVisible, setModalVisible] = useState(false);
  const [tableTickets, setTableTickets] = useState([]);

  useEffect(() => {
    getData();
  }, [period]);

  const renderActiveShape = (props) => {
    const {
      cx,
      cy,
      innerRadius,
      outerRadius,
      startAngle,
      endAngle,
      fill,
      payload,
    } = props;
    return (
      <g>
        <text x={cx} y={cy + 10} dy={-5} textAnchor="middle" fill={"black"}>
          {payload.value} Ticket
        </text>

        <Sector
          cx={cx}
          cy={cy}
          innerRadius={innerRadius}
          outerRadius={outerRadius}
          startAngle={startAngle}
          endAngle={endAngle}
          fill={fill}
        />
      </g>
    );
  };

  const onPieEnter = useCallback(
    (_, index) => {
      setActiveIndex(index);
    },
    [setActiveIndex]
  );

  function getData() {
    const dataPeriod =
      period === 3
        ? [
            {
              name: "Menor 7D",
              value: getThreePeriodValue(null, 7, tickets),
              fill: "#74CF49",
            },
            {
              name: "Maior 7D e Menor 30D",
              value: getThreePeriodValue(7, 30, tickets),
              fill: "rgb(197,225,3)",
            },
            {
              name: "Maior 30D",
              value: getThreePeriodValue(30, null, tickets),
              fill: "#FF3C3C",
            },
          ]
        : [
            {
              name: "Menor ou igual 14D",
              value: getTwoPeriodValue(true, tickets),
              fill: "#74CF49",
            },
            {
              name: "Maior 14D",
              value: getTwoPeriodValue(false, tickets),
              fill: "#FF3C3C",
            },
          ];

    setData(dataPeriod);
  }

  function getTwoPeriodValue(moreThanFourteen, tickets) {
    let ticketCounter = 0;

    tickets.forEach((ticket) => {
      if (moreThanFourteen) {
        if (
          moment(
            Date.now() - new Date(ticket.ticket_create_time).getTime()
          ).days() <= 14
        ) {
          ticketCounter++;
        }
      } else {
        if (
          moment(
            Date.now() - new Date(ticket.ticket_create_time).getTime()
          ).days() > 14
        ) {
          ticketCounter++;
        }
      }
    });

    return ticketCounter;
  }

  function getThreePeriodValue(min, max, tickets) {
    let ticketCounter = 0;

    tickets.forEach((ticket) => {
      if (min === null) {
        if (
          moment(
            Date.now() - new Date(ticket.ticket_create_time).getTime()
          ).days() <= 7
        ) {
          ticketCounter++;
        }
      }

      if (min !== null && max !== null) {
        if (
          moment(
            Date.now() - new Date(ticket.ticket_create_time).getTime()
          ).days() > 7 &&
          moment(
            Date.now() - new Date(ticket.ticket_create_time).getTime()
          ).days() <= 30
        ) {
          ticketCounter++;
        }
      }

      if (max === null) {
        if (
          moment(
            Date.now() - new Date(ticket.ticket_create_time).getTime()
          ).days() > 30
        ) {
          ticketCounter++;
        }
      }
    });

    return ticketCounter;
  }

  function onPeriod(e) {
    setPeriod(e.target.value);
  }

  function showModal(event) {
    setTableTickets(getTickets(event.name));
    setModalVisible(true);
  }

  function getTickets(name) {
    const ticketArray = [];

    tickets.forEach((ticket) => {
      if (name === "Menor 7D") {
        if (
          moment(
            Date.now() - new Date(ticket.ticket_create_time).getTime()
          ).days() <= 7
        ) {
          ticketArray.push(ticket);
        }
      }

      if (name === "Maior 7D e Menor 30D") {
        if (
          moment(
            Date.now() - new Date(ticket.ticket_create_time).getTime()
          ).days() > 7 &&
          moment(
            Date.now() - new Date(ticket.ticket_create_time).getTime()
          ).days() <= 30
        ) {
          ticketArray.push(ticket);
        }
      }

      if (name === "Maior 30D") {
        if (
          moment(
            Date.now() - new Date(ticket.ticket_create_time).getTime()
          ).days() > 30
        ) {
          ticketArray.push(ticket);
        }
      }

      if (name === "Menor ou igual 14D") {
        if (
          moment(
            Date.now() - new Date(ticket.ticket_create_time).getTime()
          ).days() <= 14
        ) {
          ticketArray.push(ticket);
        }
      }

      if (name === "Maior 14D") {
        if (
          moment(
            Date.now() - new Date(ticket.ticket_create_time).getTime()
          ).days() > 14
        ) {
          ticketArray.push(ticket);
        }
      }
    });

    return ticketArray;
  }

  function closeModal() {
    setModalVisible(false);
  }

  return (
    <div className="card">
      <div className="flex justify-between px-5 py-7 items-center mb-1">
        <div className="info-graph-header">
          <ArrowLeftOutlined
            className=""
            onClick={() => {
              props.closeCardPage();
            }}
          />
          <h3 className="bold">{props.title}</h3>
        </div>
      </div>

      <div className="info-graph">
        <PieChart width={500} height={260}>
          <Pie
            activeIndex={activeIndex}
            activeShape={renderActiveShape}
            data={data}
            startAngle={360}
            endAngle={0}
            cx={160}
            cy={120}
            innerRadius={60}
            outerRadius={100}
            fill="#43914F"
            dataKey="value"
            onClick={(event) => showModal(event)}
            onMouseEnter={onPieEnter}
          ></Pie>
          <Legend
            formatter={(value, entry, index) => (
              <span className="text-lg text-black">{value}</span>
            )}
            className="text-xs"
            layout="vetical"
            verticalAlign="middle"
            align="right"
          />
        </PieChart>
      </div>
      <div className="radio-group">
        <Radio.Group onChange={onPeriod} value={period}>
          <Radio value={3}>3 Períodos</Radio>
          <Radio value={2}>2 períodos</Radio>
        </Radio.Group>
      </div>
      <Modal
        closable={false}
        width={"90vw"}
        className="flex justify-center"
        bodyStyle={{ backgroundColor: "transparent" }}
        cancelButtonProps={{ style: { display: "none" } }}
        open={modalVisible}
      >
        <TicketsOfWallerManagesTable
          tickets={tableTickets}
          closeModal={closeModal}
        />
      </Modal>
    </div>
  );
}

ChartJS.register(ArcElement, Tooltip);
export const options = {
  responsive: true,
  plugins: {
    legend: {
      position: "top",
    },
    title: {
      display: true,
    },
  },
};

function TicketsOfWalletManages(props) {
  const [page, setPage] = useState(MAIN_PAGE);
  const [content, setContent] = useState([]);
  const [allClients, setAllClients] = useState([]);

  const openCardPage = (page, content) => {
    setPage(page);
    setContent(content);
  };

  useEffect(() => {
    if (props?.allClients) setAllClients(props.allClients);
  }, [props.allClients]);

  const closeCardPage = () => {
    setPage(MAIN_PAGE);
  };
  if (page === MAIN_PAGE) {
    return (
      <div>
        <MainGraph
          allClients={allClients}
          openCardPage={openCardPage}
          favorites={props.favorites}
          componentVisibles={props.componentVisibles}
          setVisionFavorite={props?.setVisionFavorite}
        />
      </div>
    );
  } else if (page === OPEN_PAGE) {
    return (
      <OpenedDonutThree
        title={"Abertos"}
        tickets={content}
        closeCardPage={closeCardPage}
      />
    );
  } else if (page === PENDING_PAGE) {
    return (
      <OpenedDonutThree
        title={"Pendentes"}
        tickets={content}
        closeCardPage={closeCardPage}
      />
    );
  } else if (page === REOPENED_PAGE) {
    return (
      <OpenedDonutThree
        title={"Reabertos"}
        tickets={content}
        closeCardPage={closeCardPage}
      />
    );
  }
}

export default TicketsOfWalletManages;
