export async function getExecutiveNameFromEmail(userName) {
  let name = userName.split(".");

  const capitalizedFirstName =
    name[0].charAt(0).toUpperCase() + name[0].slice(1);
  let capitalizedSecondName = "";
  if (name.length > 1) {
    if (name[1].includes("@")) {
      const tempName = name[1].split("@");
      name[1] = tempName[0];
    }
    capitalizedSecondName = name[1].charAt(0).toUpperCase() + name[1].slice(1);
  }

  const response =
    capitalizedSecondName !== ""
      ? `${capitalizedFirstName} ${capitalizedSecondName}`
      : capitalizedFirstName;

  return response;
}
