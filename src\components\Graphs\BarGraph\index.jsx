import React from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>Axi<PERSON> } from "recharts";

export const MainBarChart = ({
  data,
  colors,
  dataKey,
  bars,
  axis,
  margin,
  tooltip,
  showModalFunction,
}) => {
  const [width, setWidth] = React.useState(window.innerWidth);
  let secondaryProportion = window.innerWidth;

  const updateWidth = () => {
    setWidth(window.innerWidth);
  };
  React.useEffect(() => {
    window.addEventListener("resize", updateWidth);

    return () => window.removeEventListener("resize", updateWidth);
  });

  const graphWidth = React.useMemo(() => {
    if (width > 1800) {
      const proportion = 1850 / 800;
      return width / proportion;
    } else if (width > 1200 && width < 1800) {
      const proportion = 1850 / 500;
      return width / proportion;
    } else if (width < 1200) {
      const proportion = 1850 / 500;
      return width / proportion;
    }
  }, [width]);

  const secondaryGraphWidth = React.useMemo(() => {
    if (width > 1800) {
      const proportion = 1850 / 800;
      return width / proportion;
    } else if (width > 1200 && width < 1800) {
      const proportion = 1850 / 850;
      return width / proportion;
    } else if (width < 1200) {
      const proportion = 1850 / 1550;
      return width / proportion;
    }
  }, [width]);

  return (
    <BarChart
      onClick={showModalFunction ? showModalFunction : null}
      width={
        secondaryProportion
          ? width < 1600
            ? secondaryGraphWidth - 300
            : secondaryGraphWidth - 150
          : width < 1600
          ? graphWidth - 300
          : graphWidth - 150
      }
      height={300}
      data={data}
      margin={{ top: 10, right: 30, left: 20, bottom: 5 }}
    >
      {axis ? axis : <XAxis dataKey="name" />}
      <YAxis />
      <Tooltip content={tooltip} />
      {bars ? (
        bars
      ) : (
        <Bar dataKey={dataKey} fill={colors ? colors[1].color : "#8884d8"} />
      )}
    </BarChart>
  );
};
