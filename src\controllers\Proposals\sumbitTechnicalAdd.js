import { message } from "antd";
import { dynamoPost, dynamoGet } from "../../service/apiDsmDynamo";
import { otrsPost } from "../../service/apiOtrs";
import { saveCustomerBrand } from "./saveCustomerBrand";
import { otrsPut } from "../../service/apiOtrs";
import { store } from "../../store/store";
import { calculateManagementServices } from "./calculateManagementServices";
import { totalValuesController } from "./newTotalValuesTechnical";
import { generatePDF } from "./generatePDF";
import { logNewAuditAction } from "../audit/logNewAuditAction";

async function cleanLocalStorage(collapseContent) {
  localStorage.setItem("technical-proposal/services", "");
  localStorage.setItem("technical-proposal/form", "");
  localStorage.setItem("technical-proposal/customer", "");
  localStorage.setItem("technical-proposal/fileList", "");
  localStorage.setItem("technical-proposal/architectureFileList", "");
  localStorage.setItem("CollapseValidator", "");

  collapseContent.map((item) => {
    return item.itemName ? localStorage.setItem(item.itemName, "") : null;
  });
}

const spreadSetupInitialValue = [
  {
    month: "12",
    value: 0,
  },
  {
    month: "24",
    value: 0,
  },
  {
    month: "36",
    value: 0,
  },
];

export const submitAddTechnicalController = async (
  add_proposal,
  collapseContent,
  currentContacts,
  servicess,
  proposalState,
  customerBrand,
  mainSearchId,
  user
) => {
  const technicalProposal = store.getState().technicalProposal;

  try {
    const res = await dynamoGet(
      `${process.env.REACT_APP_STAGE}-cost-management`
    ).then(async (data) => {
      const costs = data.find(
        (d) => d.id === `${process.env.REACT_APP_COST_MANAGEMENT_OBJ_ID}`
      );

      let sortedServices = [];
      technicalProposal.selectedServices.forEach((s) => {
        const serviceData = technicalProposal.serviceList.find(
          (serviceInList) => serviceInList.id === s
        );
        if (serviceData) sortedServices.push(serviceData);
      });

      const services = sortedServices;

      const servicesWithManagementCalculated =
        await calculateManagementServices(services);

      const newTotalValues = await totalValuesController(
        technicalProposal,
        costs,
        servicesWithManagementCalculated
      );

      const customerNamesName = technicalProposal.selectedCustomer.names?.name
        ? technicalProposal.selectedCustomer.names?.name
        : "";

      const customerNamesFantasyName = technicalProposal.selectedCustomer.names
        ?.fantasy_name
        ? technicalProposal.selectedCustomer.names?.fantasy_name
        : technicalProposal.clientName;

      const customerData = {
        names: {
          name: customerNamesName,
          fantasy_name: customerNamesFantasyName,
        },
        cnpj: technicalProposal.selectedCustomer.cnpj,
        contacts: technicalProposal.selectedCustomer.contacts,
        mainContact: technicalProposal.selectedContact,
      };

      const proposalData = {
        searchId: technicalProposal.persistID,
        name: technicalProposal.projectName,
        type: technicalProposal.type || "",
        status: technicalProposal.status || "",
        commercialStatus: "não inicializada",
        active: true,
        mainOportunity: technicalProposal.selectedOpportunity,
        opportunity: technicalProposal.opportunities,
        architects: technicalProposal.architects,
        customer: customerData,
        bus: technicalProposal.bus,
        services: servicesWithManagementCalculated,
        specialFields: [
          { name: "Desafios", value: technicalProposal.challenges },
          { name: "Cenário apresentado", value: technicalProposal.scenarios },
          { name: "Premissas", value: technicalProposal.premisses },
          {
            name: "Pontos não contemplados",
            value: technicalProposal.extra_points,
          },
          { name: "Arquitetura", value: technicalProposal.architecture },
          {
            name: "Fatores influenciadores",
            value: technicalProposal.main_factors,
          },
          {
            name: "Custos adicionais",
            value: technicalProposal.additional_costs,
          },
          {
            name: "Resultados esperados",
            value: technicalProposal.expected_results,
          },
          { name: "Observações", value: technicalProposal.observations },
          { name: "Notas Internas", value: technicalProposal.internal_notes },
        ],
        awsCosts: technicalProposal.calculator,
        totalValues: newTotalValues,
        monthSelected: ["12"],
        spreadSetup: spreadSetupInitialValue,
        fileNames: technicalProposal.fileNames,
        architectureFileNames: technicalProposal.architectureFileNames,
        scenarioFileNames: technicalProposal.scenarioFileNames,
        ticketData: technicalProposal.ticketData,
        pipedriveCreatedAt: new Date(),
        s3FolderNotes: `${process.env.REACT_APP_CRM_API}/proposal/${technicalProposal.selectedOpportunity}`,
      };

      await dynamoPost(
        `${process.env.REACT_APP_STAGE}-proposals`,
        proposalData
      );

      const customerName = technicalProposal.selectedCustomer.names
        ?.fantasy_name
        ? technicalProposal.selectedCustomer.names?.fantasy_name
        : technicalProposal.clientName;

      await generatePDF(
        proposalData,
        customerName,
        customerBrand,
        customerData,
        user
      );

      const username = localStorage.getItem("@dsm/username");
      const title = "Cadastro de Proposta Técnica";
      const description = `${username} cadastrou a proposta: ${technicalProposal.projectName}`;
      logNewAuditAction(username, title, description);

      message.success("Proposta adicionada com sucesso!");
      await cleanLocalStorage(collapseContent);

      return true;
    });
    return res;
  } catch (err) {

    message.error("Erro ao adicionar a proposta");
    return false;
  }
};
