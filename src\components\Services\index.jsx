import React from "react";
import {
  <PERSON>,
  Card,
  Row,
  Col,
  Input,
  Button,
  Typography,
  Tooltip,
  Select,
} from "antd";
import { DeleteOutlined, PlusOutlined } from "@ant-design/icons";
import { invalidInputNumberChars } from "../../constants/invalidCharsInputNumber";
import { estimatedActivities } from "../../constants/servicesFixedData";
import {
  handleActivityChange,
  handleAddActivity,
  handleRemoveActivity,
  handleSubActivityChange,
  handleAddSubActivity,
  handleRemoveSubActivity,
} from "../../controllers/services/index";
import { CheckOutlined } from "@ant-design/icons";
import { TextColor } from "../../hooks/ColorInverter";

export const SubTasksContent = (props) => {
  const { Text } = Typography;
  const { container, containerKey, activities, setActivities } = props;
  return (
    <Row>
      <Col span={24}>
        <Text>Sub Atividades</Text>
        <Form.Item>
          {container.subtasks.map((item, index) => (
            <Row justify="space-between">
              <Col span={20} style={{ marginTop: ".5rem" }}>
                <Input
                  rows={1}
                  placeholder="Descrição"
                  disabled={
                    !activities[containerKey].description ||
                    !activities[containerKey].name
                  }
                  key={index}
                  value={item.value}
                  onChange={(e) =>
                    handleSubActivityChange(
                      e.target.value,
                      item.index,
                      container.id,
                      setActivities
                    )
                  }
                />
              </Col>
              <Col span={4}>
                <Row>
                  <Col span={12} style={{ marginTop: ".5rem" }}>
                    <Tooltip
                      title={
                        container.subtasks.some((item) => item.value === "")
                          ? "Preencha a descrição da sub atividade"
                          : ""
                      }
                    >
                      <Button
                        disabled={container.subtasks.some(
                          (item) => item.value === ""
                        )}
                        type="text"
                        onClick={() => {
                          handleAddSubActivity(container.id, setActivities);
                        }}
                      >
                        <PlusOutlined />
                      </Button>
                    </Tooltip>
                  </Col>
                  <Col span={12} style={{ marginTop: ".5rem" }}>
                    {container.subtasks.length > 1 && (
                      <Button
                        danger
                        type="text"
                        onClick={() => {
                          handleRemoveSubActivity(
                            container.id,
                            item.index,
                            false,
                            setActivities
                          );
                        }}
                      >
                        <DeleteOutlined />
                      </Button>
                    )}
                  </Col>
                </Row>
              </Col>
            </Row>
          ))}
        </Form.Item>
      </Col>
    </Row>
  );
};

export const ActivitiesContent = (props) => {
  const { Text } = Typography;
  const { TextArea } = Input;
  const { activities, setActivities } = props;
  return (
    <>
      {activities.map((container, containerKey) => (
        <Card
          key={containerKey}
          style={{
            backgroundColor: "#f4f4f4",
            margin: "25px 0",
            boxShadow: "0px 4px 4px rgba(0, 0, 0, .5)",
            borderRadius: "5px",
          }}
        >
          <Row justify="space-between" gutter={[16, 16]}>
            <Col lg={14} sm={24}>
              <Row>
                <Col span={24}>
                  <Form.Item>
                    <Text>
                      Nome{" "}
                      <span
                        style={{
                          color: "#ff4d4f",
                          fontWeight: "bold",
                        }}
                      >
                        *
                      </span>
                    </Text>
                    <Input
                      placeholder="Nome"
                      value={activities[containerKey].name}
                      style={{
                        borderColor: !activities[containerKey].name
                          ? "#ff4d4f"
                          : "",
                      }}
                      onChange={(e) =>
                        handleActivityChange(
                          e.target.value,
                          container.id,
                          "name",
                          setActivities
                        )
                      }
                    />
                    {!activities[containerKey].name && (
                      <span
                        style={{
                          color: "#ff4d4f",
                          fontSize: 13,
                        }}
                      >
                        * valor não informado, campo obrigatório.
                      </span>
                    )}
                  </Form.Item>
                </Col>
              </Row>
              <Row>
                <Col span={24}>
                  <Form.Item
                    rules={[
                      {
                        required: true,
                        message: "Preencha a descrição da atividade.",
                      },
                    ]}
                  >
                    <Text>
                      Descrição{" "}
                      <span
                        style={{
                          color: "#ff4d4f",
                          fontWeight: "bold",
                        }}
                      >
                        *
                      </span>
                    </Text>
                    <TextArea
                      rows={1}
                      value={activities[containerKey].description}
                      style={{
                        borderColor: !activities[containerKey].description
                          ? "#ff4d4f"
                          : "",
                      }}
                      onChange={(e) =>
                        handleActivityChange(
                          e.target.value,
                          container.id,
                          "description",
                          setActivities
                        )
                      }
                      placeholder="Digite a descrição da atividade"
                    />
                    {!activities[containerKey].description && (
                      <span
                        style={{
                          color: "#ff4d4f",
                          fontSize: 13,
                        }}
                      >
                        * valor não informado, campo obrigatório.
                      </span>
                    )}
                  </Form.Item>
                </Col>
              </Row>

              <SubTasksContent
                container={container}
                containerKey={containerKey}
                activities={activities}
                setActivities={setActivities}
              />
            </Col>

            <Col offset={1} lg={9} sm={24}>
              <Row justify="center" gutter={[16, 16]}>
                {estimatedActivities.map(
                  (estimatedActivity, estimatedActivityKey) => {
                    return (
                      <Col span={12}>
                        <Form.Item>
                          <Row justify="center">{estimatedActivity.title}</Row>
                          <Input
                            className="no-scroll"
                            type="number"
                            min="0"
                            placeholder={estimatedActivity.placeholder}
                            onKeyDown={(e) => {
                              invalidInputNumberChars.find(
                                (keyPressed) => keyPressed === e.key
                              ) && e.preventDefault();
                            }}
                            value={
                              activities[containerKey].estimates[
                                estimatedActivity.title
                                  .replace("/", "")
                                  .split(" ")
                                  .join("")
                                  .toLocaleLowerCase()
                              ]
                                ? activities[containerKey].estimates[
                                    estimatedActivity.title
                                      .replace("/", "")
                                      .split(" ")
                                      .join("")
                                      .toLocaleLowerCase()
                                  ]
                                : ""
                            }
                            onChange={(e) => {
                              handleActivityChange(
                                e.target.value,
                                container.id,
                                estimatedActivity.form_title,
                                setActivities
                              );
                            }}
                          ></Input>
                        </Form.Item>
                      </Col>
                    );
                  }
                )}
              </Row>
            </Col>
          </Row>

          <Row style={{ width: "100%" }} justify="space-between">
            <Button
              type="link"
              style={{ margin: "15px 0" }}
              onClick={() => {
                handleAddActivity(setActivities);
              }}
            >
              Adicionar Atividade
              <PlusOutlined />
            </Button>
            {activities.length > 1 && (
              <Button
                danger
                type="text"
                style={{ margin: "15px 0" }}
                onClick={() => {
                  handleRemoveActivity(container.id, setActivities);
                }}
              >
                Remover Atividade
                <DeleteOutlined />
              </Button>
            )}
          </Row>
        </Card>
      ))}
    </>
  );
};

export const TagInput = (props) => {
  const { Option } = Select;
  const {
    selectTagToggle,
    setSelectTagToggle,
    tagName,
    setTagName,
    state,
    mainTagColorInput,
    setMainTagColorInput,
    arr,
  } = props;
  return (
    <Col span={11}>
      <Tooltip
        placement="top"
        title={
          <>
            {selectTagToggle === false
              ? "Deseja utilizar uma tag existente?"
              : "Deseja criar uma nova tag"}
            <Row justify="end">
              <Col>
                <Button
                  type="primary"
                  icon={<CheckOutlined />}
                  onClick={() => setSelectTagToggle(!selectTagToggle)}
                ></Button>
              </Col>
            </Row>
          </>
        }
      >
        <TagInputLabel />
        {selectTagToggle === false ? (
          <Row justify="start" align="middle">
            <Col lg={22}>
              <Form.Item name="tag_name">
                <Input
                  placeholder="Nome da tag"
                  value={tagName}
                  style={{
                    borderColor: !tagName ? "#ff4d4f" : "",
                    marginTop: 8,
                  }}
                  onChange={(event) => {
                    setTagName(event.target.value);
                  }}
                />
              </Form.Item>
            </Col>

            <Col lg={1}>
              <Form.Item name="tag_color" initialValue={mainTagColorInput}>
                <Input
                  type="color"
                  value={mainTagColorInput}
                  onChange={(event) => {
                    setMainTagColorInput(event.target.value);
                  }}
                  style={{
                    width: "2rem",
                    padding: "0",
                    clipPath: "circle(32% at center)",
                    border: "none",
                    outline: "none",
                    boxShadow: "none",
                    marginTop: 5,
                  }}
                />
              </Form.Item>
            </Col>
          </Row>
        ) : (
          <Form.Item
            name="tag_name"
            rules={[
              {
                required: true,
                message: "Preencha o nome da tag.",
              },
            ]}
          >
            <Select
              placeholder="Nome da tag"
              style={{
                marginTop: 8,
              }}
              onChange={(e, i) => {
                console.log(e);
                setTagName(e);
                state?.map((i) =>
                  i.tag.name === e ? setMainTagColorInput(i.tag.rgb) : []
                );
              }}
            >
              {arr.map((item) => {
                return (
                  <Option value={item.tag.name}>
                    {TextColor(item?.tag.rgb, item?.tag.name)}
                  </Option>
                );
              })}
            </Select>
          </Form.Item>
        )}
      </Tooltip>
    </Col>
  );
};

const TagInputLabel = () => {
  const { Text } = Typography;
  return (
    <Text>
      Nome da Tag{" "}
      <span
        style={{
          color: "#ff4d4f",
          fontWeight: "bold",
        }}
      >
        *
      </span>
    </Text>
  );
};
