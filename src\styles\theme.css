/* ✅ Tema DSM - Cores Globais */

/* Variáveis CSS para cores do DSM */
:root {
  --dsm-primary-color: #00B050;
  --dsm-primary-hover: rgba(0, 176, 80, 0.8);
  --dsm-primary-light: rgba(0, 176, 80, 0.2);
  --dsm-text-color: #404040;
  --dsm-white: #ffffff;
  --dsm-success: #00B050;
  --dsm-error: #eb2f96;
}

/* ✅ Botões primários */
.ant-btn-primary {
  background-color: var(--dsm-primary-color) !important;
  border-color: var(--dsm-primary-color) !important;
}

.ant-btn-primary:hover,
.ant-btn-primary:focus {
  background-color: var(--dsm-primary-hover) !important;
  border-color: var(--dsm-primary-hover) !important;
}

/* ✅ Links */
a {
  color: var(--dsm-primary-color) !important;
}

a:hover {
  color: var(--dsm-primary-hover) !important;
}

/* ✅ Checkboxes */
.ant-checkbox-checked .ant-checkbox-inner {
  background-color: var(--dsm-primary-color) !important;
  border-color: var(--dsm-primary-color) !important;
}

.ant-checkbox-wrapper:hover .ant-checkbox-inner,
.ant-checkbox:hover .ant-checkbox-inner,
.ant-checkbox-input:focus + .ant-checkbox-inner {
  border-color: var(--dsm-primary-color) !important;
}

/* ✅ Radio buttons */
.ant-radio-checked .ant-radio-inner {
  border-color: var(--dsm-primary-color) !important;
}

.ant-radio-checked .ant-radio-inner::after {
  background-color: var(--dsm-primary-color) !important;
}

.ant-radio-wrapper:hover .ant-radio,
.ant-radio:hover .ant-radio-inner,
.ant-radio-input:focus + .ant-radio-inner {
  border-color: var(--dsm-primary-color) !important;
}

/* ✅ Inputs com foco */
.ant-input:focus,
.ant-input-focused {
  border-color: var(--dsm-primary-color) !important;
  box-shadow: 0 0 0 2px var(--dsm-primary-light) !important;
}

/* ✅ Select com foco */
.ant-select:not(.ant-select-disabled):hover .ant-select-selector,
.ant-select-focused .ant-select-selector {
  border-color: var(--dsm-primary-color) !important;
  box-shadow: 0 0 0 2px var(--dsm-primary-light) !important;
}

/* ✅ Tabs ativas */
.ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
  color: var(--dsm-primary-color) !important;
}

.ant-tabs-ink-bar {
  background-color: var(--dsm-primary-color) !important;
}

/* ✅ Progress bars */
.ant-progress-bg {
  background-color: var(--dsm-primary-color) !important;
}

/* ✅ Switch */
.ant-switch-checked {
  background-color: var(--dsm-primary-color) !important;
}

/* ✅ Slider */
.ant-slider-track {
  background-color: var(--dsm-primary-color) !important;
}

.ant-slider-handle {
  border-color: var(--dsm-primary-color) !important;
}

.ant-slider-handle:hover,
.ant-slider-handle:focus {
  border-color: var(--dsm-primary-color) !important;
  box-shadow: 0 0 0 5px var(--dsm-primary-light) !important;
}

/* ✅ Rate (estrelas) */
.ant-rate-star-full .ant-rate-star-second {
  color: var(--dsm-primary-color) !important;
}

/* ✅ Pagination - Estilo DSM completo */
.ant-pagination-item-active {
  background-color: var(--dsm-primary-color) !important;
  border-color: var(--dsm-primary-color) !important;
}

.ant-pagination-item-active a {
  color: var(--dsm-white) !important;
}

.ant-pagination-item:hover:not(.ant-pagination-item-active) {
  border-color: var(--dsm-primary-color) !important;
}

.ant-pagination-item:hover:not(.ant-pagination-item-active) a {
  color: var(--dsm-primary-color) !important;
}

/* ✅ Botões de navegação da paginação */
.ant-pagination-prev:hover .ant-pagination-item-link,
.ant-pagination-next:hover .ant-pagination-item-link {
  border-color: var(--dsm-primary-color) !important;
  color: var(--dsm-primary-color) !important;
}

/* ✅ Jump to page */
.ant-pagination-jump-prev:hover::after,
.ant-pagination-jump-next:hover::after {
  color: var(--dsm-primary-color) !important;
}

/* ✅ Page size selector */
.ant-pagination-options-size-changer .ant-select-selector:hover {
  border-color: var(--dsm-primary-color) !important;
}

/* ✅ Quick jumper */
.ant-pagination-options-quick-jumper input:hover,
.ant-pagination-options-quick-jumper input:focus {
  border-color: var(--dsm-primary-color) !important;
  box-shadow: 0 0 0 2px var(--dsm-primary-light) !important;
}

/* ✅ Steps */
.ant-steps-item-finish .ant-steps-item-icon {
  background-color: var(--dsm-primary-color) !important;
  border-color: var(--dsm-primary-color) !important;
}

.ant-steps-item-process .ant-steps-item-icon {
  background-color: var(--dsm-primary-color) !important;
  border-color: var(--dsm-primary-color) !important;
}

/* ✅ Spin (loading) */
.ant-spin-dot-item {
  background-color: var(--dsm-primary-color) !important;
}

/* ✅ Badge */
.ant-badge-count {
  background-color: var(--dsm-primary-color) !important;
}

/* ✅ Tag */
.ant-tag-green {
  background-color: var(--dsm-primary-light) !important;
  border-color: var(--dsm-primary-color) !important;
  color: var(--dsm-primary-color) !important;
}

/* ✅ Alert success */
.ant-alert-success {
  background-color: var(--dsm-primary-light) !important;
  border-color: var(--dsm-primary-color) !important;
}

.ant-alert-success .ant-alert-icon {
  color: var(--dsm-primary-color) !important;
}

/* ✅ Message success */
.ant-message-success .anticon {
  color: var(--dsm-primary-color) !important;
}

/* ✅ Notification success */
.ant-notification-notice-success {
  border-left-color: var(--dsm-primary-color) !important;
}

.ant-notification-notice-success .ant-notification-notice-icon {
  color: var(--dsm-primary-color) !important;
}

/* ✅ Calendar */
.ant-picker-calendar-date-selected .ant-picker-calendar-date-value,
.ant-picker-calendar-date-selected:hover .ant-picker-calendar-date-value {
  background-color: var(--dsm-primary-color) !important;
  color: var(--dsm-white) !important;
}

/* ✅ DatePicker */
.ant-picker-cell-selected .ant-picker-cell-inner,
.ant-picker-cell-selected:hover .ant-picker-cell-inner {
  background-color: var(--dsm-primary-color) !important;
  color: var(--dsm-white) !important;
}

.ant-picker-today-btn {
  color: var(--dsm-primary-color) !important;
}

/* ✅ TimePicker */
.ant-picker-time-panel-column > li.ant-picker-time-panel-cell-selected .ant-picker-time-panel-cell-inner {
  background-color: var(--dsm-primary-color) !important;
}

/* ✅ Transfer */
.ant-transfer-list-header .ant-checkbox-checked .ant-checkbox-inner {
  background-color: var(--dsm-primary-color) !important;
  border-color: var(--dsm-primary-color) !important;
}

/* ✅ Tree */
.ant-tree-node-selected {
  background-color: var(--dsm-primary-light) !important;
}

.ant-tree-checkbox-checked .ant-tree-checkbox-inner {
  background-color: var(--dsm-primary-color) !important;
  border-color: var(--dsm-primary-color) !important;
}

/* ✅ Upload */
.ant-upload.ant-upload-drag:hover {
  border-color: var(--dsm-primary-color) !important;
}

.ant-upload-list-item-done .ant-upload-list-item-name {
  color: var(--dsm-primary-color) !important;
}

/* ✅ Anchor */
.ant-anchor-link-active > .ant-anchor-link-title {
  color: var(--dsm-primary-color) !important;
}

.ant-anchor-ink::before {
  background-color: var(--dsm-primary-color) !important;
}

/* ✅ BackTop */
.ant-back-top {
  background-color: var(--dsm-primary-color) !important;
}

/* ✅ Affix */
.ant-affix {
  background-color: var(--dsm-white) !important;
}

/* ✅ ÍCONES E BOTÕES ESPECÍFICOS - PADRÃO DSM VERDE */

/* ✅ Botões tipo link - devem ser verdes */
.ant-btn-link {
  color: var(--dsm-primary-color) !important;
}

.ant-btn-link:hover,
.ant-btn-link:focus {
  color: var(--dsm-primary-hover) !important;
}

/* ✅ REMOVIDO: Regras genéricas que aplicavam verde a todos os ícones */
/* Apenas ícones específicos devem ser verdes conforme o projeto original */

/* ✅ COMPONENTES ESPECÍFICOS QUE DEVEM SER VERDES */

/* ✅ ResetMFAButton - Modal Contatos (ícone de cadeado para reset MFA) */
.ant-btn[data-testid*="reset-mfa"] .anticon-lock,
.ant-tooltip .ant-btn .anticon-lock {
  color: var(--dsm-primary-color) !important;
}

.ant-btn[data-testid*="reset-mfa"]:hover .anticon-lock,
.ant-tooltip .ant-btn:hover .anticon-lock {
  color: var(--dsm-primary-hover) !important;
}

/* ✅ CustomerPortalButton */
.ant-btn .ant-tag[color="mediumseagreen"] {
  background-color: var(--dsm-primary-color) !important;
  border-color: var(--dsm-primary-color) !important;
  color: var(--dsm-white) !important;
}

/* ✅ Botões de visualizar contatos */
.ant-btn[data-testid="open-contacts-modal"] {
  color: var(--dsm-primary-color) !important;
}

.ant-btn[data-testid="open-contacts-modal"]:hover {
  color: var(--dsm-primary-hover) !important;
}

/* ✅ Tags verdes específicas */
.ant-tag[color="green"] {
  background-color: var(--dsm-primary-light) !important;
  border-color: var(--dsm-primary-color) !important;
  color: var(--dsm-primary-color) !important;
}

/* ✅ REMOVIDO: Regras genéricas para ícones em modais, tooltips e popovers */
/* Mantendo apenas regras específicas para componentes que realmente devem ser verdes */
