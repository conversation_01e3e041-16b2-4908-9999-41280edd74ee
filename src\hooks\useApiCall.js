import { useState, useEffect, useCallback, useRef } from 'react';

/**
 * Custom hook for making API calls with proper cleanup and error handling
 * Prevents memory leaks and race conditions
 */
export const useApiCall = (apiFunction, dependencies = [], options = {}) => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const abortControllerRef = useRef(null);
  const isMountedRef = useRef(true);

  const {
    immediate = true,
    onSuccess,
    onError,
    retryCount = 0,
    retryDelay = 1000,
  } = options;

  // Cleanup function
  const cleanup = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }
  }, []);

  // Execute API call
  const execute = useCallback(async (...args) => {
    if (!isMountedRef.current) return;

    // Cancel previous request
    cleanup();

    // Create new abort controller
    abortControllerRef.current = new AbortController();
    
    setLoading(true);
    setError(null);

    let attempts = 0;
    const maxAttempts = retryCount + 1;

    while (attempts < maxAttempts && isMountedRef.current) {
      try {
        const result = await apiFunction(...args, {
          signal: abortControllerRef.current.signal
        });

        if (!isMountedRef.current) return;

        setData(result);
        setLoading(false);
        
        if (onSuccess) {
          onSuccess(result);
        }
        
        return result;
      } catch (err) {
        attempts++;
        
        // If aborted, don't retry
        if (err.name === 'AbortError') {
          return;
        }

        // If last attempt or component unmounted
        if (attempts >= maxAttempts || !isMountedRef.current) {
          if (isMountedRef.current) {
            setError(err);
            setLoading(false);
            
            if (onError) {
              onError(err);
            }
          }
          return;
        }

        // Wait before retry
        await new Promise(resolve => setTimeout(resolve, retryDelay));
      }
    }
  }, [apiFunction, cleanup, onSuccess, onError, retryCount, retryDelay]);

  // Auto-execute on mount if immediate is true
  useEffect(() => {
    if (immediate) {
      execute();
    }

    return () => {
      isMountedRef.current = false;
      cleanup();
    };
  }, dependencies); // eslint-disable-line react-hooks/exhaustive-deps

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
      cleanup();
    };
  }, [cleanup]);

  return {
    data,
    loading,
    error,
    execute,
    cancel: cleanup,
  };
};

/**
 * Hook for multiple parallel API calls
 */
export const useParallelApiCalls = (apiCalls, dependencies = []) => {
  const [results, setResults] = useState([]);
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState([]);
  const isMountedRef = useRef(true);

  useEffect(() => {
    let isMounted = true;
    isMountedRef.current = true;

    const executeParallelCalls = async () => {
      if (!isMounted) return;

      setLoading(true);
      setErrors([]);
      setResults([]);

      try {
        const promises = apiCalls.map(async (apiCall, index) => {
          try {
            const result = await apiCall();
            return { index, result, error: null };
          } catch (error) {
            return { index, result: null, error };
          }
        });

        const responses = await Promise.allSettled(promises);
        
        if (!isMounted) return;

        const newResults = [];
        const newErrors = [];

        responses.forEach((response, index) => {
          if (response.status === 'fulfilled') {
            const { result, error } = response.value;
            newResults[index] = result;
            newErrors[index] = error;
          } else {
            newResults[index] = null;
            newErrors[index] = response.reason;
          }
        });

        setResults(newResults);
        setErrors(newErrors);
      } catch (error) {
        if (isMounted) {
          console.error('Error in parallel API calls:', error);
        }
      } finally {
        if (isMounted) {
          setLoading(false);
        }
      }
    };

    executeParallelCalls();

    return () => {
      isMounted = false;
      isMountedRef.current = false;
    };
  }, dependencies); // eslint-disable-line react-hooks/exhaustive-deps

  useEffect(() => {
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  return { results, loading, errors };
};

/**
 * Hook for paginated API calls
 */
export const usePaginatedApiCall = (apiFunction, pageSize = 10) => {
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [hasMore, setHasMore] = useState(true);
  const [page, setPage] = useState(1);
  const isMountedRef = useRef(true);

  const loadMore = useCallback(async () => {
    if (loading || !hasMore || !isMountedRef.current) return;

    setLoading(true);
    setError(null);

    try {
      const result = await apiFunction(page, pageSize);
      
      if (!isMountedRef.current) return;

      if (result && result.length > 0) {
        setData(prevData => [...prevData, ...result]);
        setPage(prevPage => prevPage + 1);
        setHasMore(result.length === pageSize);
      } else {
        setHasMore(false);
      }
    } catch (err) {
      if (isMountedRef.current) {
        setError(err);
      }
    } finally {
      if (isMountedRef.current) {
        setLoading(false);
      }
    }
  }, [apiFunction, page, pageSize, loading, hasMore]);

  const reset = useCallback(() => {
    setData([]);
    setPage(1);
    setHasMore(true);
    setError(null);
  }, []);

  useEffect(() => {
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  return {
    data,
    loading,
    error,
    hasMore,
    loadMore,
    reset,
  };
};

export default useApiCall;
