import { React, useEffect, useState } from "react";
import { Select, Button, Form, Row, Typography } from "antd";
import { CloseCircleOutlined } from "@ant-design/icons";
import { dynamoGet } from "../../../../service/apiDsmDynamo";
import { filterByOrderOfInput } from "../../../../utils/filterByOrderOfInput";

function AverageConsumptionFilter(props) {
  const { Title } = Typography;
  const { Option } = Select;
  const [form] = Form.useForm();
  const [contractTypes, setContractTypes] = useState([]);

  function onFilterFinish(values) {
    props.filter(values);
    props.handleVisibleChange();
  }

  async function getContractTypes() {
    let contractTypes = await dynamoGet(
      `${process.env.REACT_APP_STAGE}-contract-type`
    );

    setContractTypes(contractTypes);
  }

  function onFilterFailed() {}

  function clear() {
    form.resetFields();
  }

  useEffect(() => {
    getContractTypes();
  }, []);

  return (
    <Form
      form={form}
      style={{ display: "flex", flexDirection: "column", width: "400px" }}
      name="basic"
      onFinish={onFilterFinish}
      onFinishFailed={onFilterFailed}
      autoComplete="off"
    >
      <div
        style={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          marginBottom: "10px",
        }}
      >
        <Title level={5} style={{ fontWeight: "normal" }}>
          Média de consumo
        </Title>
        <CloseCircleOutlined onClick={() => props.handleVisibleChange()} />
      </div>
      <Form.Item name="client">
        <Select
          showSearch
          style={{ width: "100%" }}
          placeholder="Selecionar cliente"
          optionFilterProp="children"
          loading={props?.allClients?.length === 0}
          filterOption={(input, option) =>
            filterByOrderOfInput(input, option.props.children)
          }
          filterSort={(optionA, optionB) => {
            return optionA.children
              .toLowerCase()
              .localeCompare(optionB.children.toLowerCase());
          }}
        >
          {props?.allClients?.map((val, index) => {
            return (
              <Option key={index} value={val?.identifications?.itsm_id}>
                {val?.names?.name || val?.names?.fantasy_name}
              </Option>
            );
          })}
        </Select>
      </Form.Item>
      <Row justify="space-between">
        <Button type="default" onClick={clear}>
          Limpar
        </Button>
        <Button htmlType="submit" type="primary">
          Aplicar
        </Button>
      </Row>
    </Form>
  );
}

export default AverageConsumptionFilter;
