.state {
  padding: 5px;
  background-color: #0053f4;
  color: white;
  border-radius: 5px;
  text-align: center;
}

.vacationSelect {
  width: 146px;
  margin-right: 10px;
}

.icon {
  color: #1ebd71;
}

.table-card {
  margin: 0 auto;
  padding: 16px;
  background-color: rgb(255, 255, 255);
  box-shadow: 0px 0px 10px 2px rgba(113, 113, 113, 0.25);
  border-radius: 24px;
}

.vacation-icons {
  display: flex;
}

.table-card-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.paginator {
  margin-top: 15px;
}

.button button {
  background-color: #43914f;
  border: none;
  color: white;
  border-radius: 5px;
  padding: 6px 24px;
  box-shadow: 0px 0px 10px 2px rgba(64, 64, 64, 0.25);
  font-weight: bold;
  font-size: 16px;
  line-height: 22px;
}

.table-filter p {
  padding-top: 16px;
}

.container-table {
  width: 100%;
  margin: 0 auto;
}
.priority-container {
  display: flex;
  align-items: center;
  gap: 16px;
}
.priority {
  height: 80px;
  width: 4px;
}
