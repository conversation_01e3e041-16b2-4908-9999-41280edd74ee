import { logNewAuditAction } from "../../../controllers/audit/logNewAuditAction";
import { dynamoGenericPost } from "../../../service/apiDynamoGeneric";

jest.mock("../../../service/apiDynamoGeneric");

describe("testing logNewAuditAction function", () => {
  it("should return an error message if the username is missing", async () => {
    const result = await logNewAuditAction(
      "",
      "Some title",
      "Some description"
    );
    expect(result.statusCode).toBe(501);
    expect(result.message).toContain("username");
  });

  it("should return an error message if the title is missing", async () => {
    const result = await logNewAuditAction("testUser", "", "Some description");
    expect(result.statusCode).toBe(502);
    expect(result.message).toContain("title");
  });

  it("should return an error message if the description is missing", async () => {
    const result = await logNewAuditAction("testUser", "Some title", "");
    expect(result.statusCode).toBe(503);
    expect(result.message).toContain("description");
  });

  it("should add an audit action log and return a success message", async () => {
    dynamoGenericPost.mockResolvedValue({
      data: "Audit log added successfully",
    });

    const result = await logNewAuditAction(
      "nice.user",
      "Login",
      "User logged in"
    );

    expect(result.statusCode).toBe(200);
    expect(result.message).toContain("Success adding audit action log: Login");
  });
});
