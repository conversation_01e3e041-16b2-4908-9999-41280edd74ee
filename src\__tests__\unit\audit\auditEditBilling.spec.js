import { logBillingEdit } from "../../../controllers/audit/logBillingEdit";

describe("testing logBillingEdit function", () => {
  it("should return an error message if the contractId is missing", () => {
    const result = logBillingEdit(null, "64802305-db22-4399-88bf-4d7cd5424a79");

    expect(result.statusCode).toBe(501);
    expect(result.message).toContain("contractId");
  });

  it("should return an error message if the customerId is missing", async () => {
    const result = logBillingEdit("b2c549ba-4ac6-4a1d-858e-743702baccd8", "");

    expect(result.statusCode).toBe(502);
    expect(result.message).toContain("customerId");
  });
});
