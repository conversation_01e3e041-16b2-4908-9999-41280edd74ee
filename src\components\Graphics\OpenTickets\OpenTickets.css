.container-graphic-card {
  display: flex;
  flex-direction: row-reverse;
  justify-content: space-around;
  line-height: inherit;
  align-items: center;
}

.container-cards-open-tickets {
  display: flex;
  gap: 5px;
  width: 45%;
}

.first-column {
  display: grid;
  width: 50%;
}

.second-column {
  display: grid;
  width: 50%;
}

.container-cards, .first-column, .second-column {
  gap: 5px;
}

.graphic-card-text-open-ticket > h3, p {
  font-family: sans-serif;
  font-size: 16px;
  font-weight: normal;
  color: rgba(65, 78, 57, 1);
}
.graphic-card-text-open-ticket > p {
  margin-bottom: 0.5em;
}

.container-bar {
  margin-top: 8px;
  margin-left: -4px;
}

.graphic-card{
  border-radius: 10px;
  align-items: center;
}

.bar {
  width: 15px;
  height: 8px;
  border-top-right-radius: 50px;
  border-bottom-right-radius: 50px;
}

.graphic{
  width: 40%;
  max-width: 350px;
  min-width: 180px;
}

.first-column > .graphic-card:nth-child(1) {
  border: 2px solid rgba(65, 142, 77, 1);
}
.first-column > .graphic-card:nth-child(2) {
  border: 2px solid rgba(17, 53, 22, 1);
}
.first-column > .graphic-card:nth-child(3) {
  border: 2px solid rgba(112, 211, 63, 1);
}

.second-column > .graphic-card:nth-child(1) {
  border: 2px solid rgba(94, 103, 21, 1);
}
.second-column > .graphic-card:nth-child(2) {
  border: 2px solid rgba(234, 36, 36, 1);
}

/* CSS das barras no topo de cada card pequeno */
.first-column > .graphic-card:nth-child(1) > .container-bar > .bar {
  background-color: rgba(65, 142, 77, 1);
}
.first-column > .graphic-card:nth-child(2) > .container-bar > .bar {
  background-color: rgba(17, 53, 22, 1);
}
.first-column > .graphic-card:nth-child(3) > .container-bar > .bar {
  background-color: rgba(112, 211, 63, 1);
}
.second-column > .graphic-card:nth-child(1) > .container-bar > .bar {
  background-color: rgba(94, 103, 21, 1);
}
.second-column > .graphic-card:nth-child(2) > .container-bar > .bar {
  background-color: rgba(234, 36, 36, 1);
}

.table {
  margin-top: 16px;
}

