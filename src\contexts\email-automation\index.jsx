import { createContext, useEffect, useReducer } from 'react'
import { EmailAutomationReducer } from '../reducers/email-automation-reducer'

export const EmailItemTypes = {
    TEXT: 'text',
    TIMELINE: 'timeline',
    TABLE: 'table'
}

const InitialState = {
    showHeader: {
        value: 1,
        label: 'Sim'
    },
    showFooter: {
        value: 1,
        label: 'Sim'
    },
    templateList: []
}

export const EmailAutomationContext = createContext({
    emailAutomationState: InitialState,
    setEmailAutomationState: () => { }
})