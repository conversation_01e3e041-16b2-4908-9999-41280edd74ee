import { dsmApi<PERSON>rovider } from "../../../provider/dsm-api-provider";
import { setSwitchRoleState } from "../../../store/actions/switch-role-action";
import { uniqByKeepLast } from "../../../utils/uniqueByKeepLast";

export const getAllSwitchRoleData = async (permissionSets) => {
  const provider = dsmApiProvider();
  let solicitations = [];
  try {
    let { data } = await provider.get("read/switch-role");

    let { Items } = data.data;

    solicitations = formatTableData(Items);

    solicitations.forEach((e) => {
      for (let account of permissionSets) {
        if (e.username === account.user) {
          e.arn = account.arn;
        }
      }
    });

    let tkt = [];
    let objectParatemers = [
      "username",
      "solicitations",
      "client_ticket",
      "client_name",
      "allowed",
    ];
    const newArr = uniqByKeepLast(solicitations, (i) => i.client_user);

    newArr.forEach((e) => {
      let obj = {};

      objectParatemers.forEach((parameter) => {
        if (parameter === "allowed")
          return (obj[parameter] = solicitations
            .filter((i) => i.client_user === e[0])
            .every((v) => v.allowed));
        if (parameter === "solicitations")
          return (obj[parameter] = solicitations.filter(
            (i) => i.client_user === e[0]
          ));
        return (obj[parameter] = solicitations.filter(
          (i) => i.client_user === e[0]
        )[0]?.[parameter]);
      });
      return tkt.push(obj);
    });
    setSwitchRoleState({ field: "switchRoleData", value: tkt });
  } catch (err) {
    console.log(err);
  }
};

const formatTableData = (data) => {
  let formattedData = [];
  data.forEach((e) => {
    formattedData.push({
      id: e.id,
      role: e.role,
      time: e.time ? e.time : e.requestedTime,
      arn: e.arn,
      active: e.active,
      allowed: e.allowed,
      username: e.username,
      ticket_id: e.ticket_id,
      account_id: e.account_id,
      updated_at: e.updated_at,
      client_name: e?.client_name?.replace(/(^\w{1})|(\s+\w{1})/g, (letter) =>
        letter.toUpperCase()
      ),
      client_ticket: e.client_ticket,
      client_user: e?.client_ticket?.toString() + e.username,
    });
  });
  return formattedData;
};
