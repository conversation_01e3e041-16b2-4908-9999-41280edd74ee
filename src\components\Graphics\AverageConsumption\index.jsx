import { FilterFilled, StarFilled, StarOutlined } from "@ant-design/icons";
import React, { useEffect, useCallback } from "react";
import { useState } from "react";
import { otrsPost } from "../../../service/apiOtrs";
import { <PERSON><PERSON>hart, Cell, Bar, XAxis, Tooltip, ReferenceLine } from "recharts";
import { Card, Col, Modal, Popover, Row, Spin, Typography } from "antd";
import moment from "moment";
import AverageConsumptionTable from "../../Table/AverageConsumption";
import AverageConsumptionFilter from "../../Modals/Filters/AverageConsumptionFilter";
import "./AverageConsumption.css";

export default function AverageConsumption(props) {
  const { Title, Text } = Typography;
  const [isFavorite, setIsFavorite] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [colors, setColors] = useState();
  const [graphIndex, setGraphIndex] = useState();
  const [activeIndex, setActiveIndex] = useState(0);
  const [data, setData] = useState([]);
  const [selectedClient, setSelectedClient] = useState("");
  const [loading, setLoading] = useState(false);
  const [allTickets, setAllTickets] = useState([]);
  const [allContracts, setAllContracts] = useState([]);
  const [averageHours, setAverageHours] = useState();
  const [popoverVisibility, setPopoverVisibility] = useState(false);
  const [width, setWidth] = React.useState(window.innerWidth);
  let secondaryProportion = window.innerWidth;

  const GRAPH_NAME = "AverageConsumption";

  function showModal() {
    setModalVisible(!modalVisible);
  }

  function handleOk() {
    setModalVisible(false);
  }

  function handleCancel() {
    setModalVisible(false);
  }

  async function updateFavorite() {
    await props?.setVisionFavorite(GRAPH_NAME);
    setIsFavorite(checkIsVisible());
  }

  function handlePopoverVisibility() {
    setPopoverVisibility(!popoverVisibility);
  }

  const monthsName = [
    "Jan",
    "Fev",
    "Mar",
    "Abr",
    "Mai",
    "Jun",
    "Jul",
    "Ago",
    "Set",
    "Out",
    "Nov",
    "Dez",
  ];

  async function getAllMonths(filter) {
    setLoading(true);

    const clientConsume = await generateClientTimeConsumed(filter);

    let formatedData = [];

    clientConsume.forEach((client, index) => {
      const currentMonth = monthsName[moment(client.create_time).month()];
      let monthAlreadyExists = formatedData.find(
        (i) => i.name === currentMonth
      );

      if (!monthAlreadyExists) {
        formatedData.push({
          horas: client.time_unit[0],
          minutos: client.time_unit[1],
          fill: "#D1F010",
          name: currentMonth,
          tickets: [client],
        });
      } else {
        if(client.time_unit[2] === true){
          monthAlreadyExists.horas -= client.time_unit[0]
          monthAlreadyExists.minutos -= client.time_unit[1]
        }else{
          monthAlreadyExists.horas += client.time_unit[0];
          monthAlreadyExists.minutos += client.time_unit[1];
        }
        monthAlreadyExists.tickets.push(client);
      }
    });
    
    const sumHoursMonth = (
      formatedData.reduce((sumHours, month) => sumHours + month.horas, 0) /
      formatedData.length
      ).toFixed(0);
      
      formatedData = formatedData.map((item) => {
        if(item.minutos >= 60){
          item.horas += Math.floor(item.minutos /60)
          item.minutos = item.minutos % 60
        }
        return item
      })


    if (filter.situation) {
      if (filter.situation === "-1") {
        formatedData = formatedData.filter((item) => item.fill === "#D1F010");
      } else if (filter.situation === "1") {
        formatedData = formatedData.filter((item) => item.fill === "#EA2C2C");
      } else {
        formatedData = formatedData.filter((item) => item.fill === "#43914F");
      }
    }
    setAverageHours(sumHoursMonth);
    setData(formatedData);
    setLoading(false);
  }

  async function generateClientTimeConsumed(filter) {
    const today = moment();

    let tickets = await otrsPost("read/tickets/customerInRange", {
      params: {
        start:
          moment().subtract(5, "months").format("YYYY-MM-DD") + " 00:00:00",
        end: today.format("YYYY-MM-DD") + " 00:00:00",
        customerId: filter.client,
      },
    }).then((response) => {
      setAllTickets(response.data);
      return response.data;
    });

    // if (filter.contractType) {
    //   console.log(allContracts[0]?.type_hours?.toLowerCase(), filter.contractType.toLowerCase() , "SOIDFHSLDF")
    //   const filteredContracts = allContracts.map(
    //     (item) => item.type_hours?.toLowerCase() === filter.contractType.toLowerCase()
    //   );
    //   tickets = tickets?.filter((ticket) =>
    //     filteredContracts?.some(
    //       (contract) => ticket?.contract_name === contract?.name
    //     )
    //   );
    // }

    const ticketsMergedInHours = transformMinutesInHours(tickets);
    return ticketsMergedInHours;
  }

  function transformMinutesInHours(tickesInMinutes) {
    tickesInMinutes.map((item) => {
      const totalConsumed = Number(item.ticket_total_consumed_min)
      let hours;
      let minutes;
      let isNegative = false
      if(totalConsumed < 0){
        isNegative=true
        const positiveNum = Math.abs(totalConsumed)
        hours = Math.floor(positiveNum / 60);
        minutes = positiveNum % 60
        
      }else{
        hours = Math.floor(totalConsumed / 60);
        minutes = totalConsumed % 60;
        
      }
      item.time_unit = [hours, minutes, isNegative];
    });
    return tickesInMinutes;
  }

  function getGraphColor() {
    let storage_color = JSON.parse(localStorage.getItem("colors"));
    if (storage_color) {
      setColors(storage_color[GRAPH_NAME]);
    }
  }

  function getContracts() {
    if (props.contracts) {
      const contracts = Object.entries(props.contracts).map((item) => item[1]);
      setAllContracts(contracts);
    }
  }

  function getFilteredChartData(filter) {
    if (filter.client) {
      setSelectedClient(filter.client);
    }
    getAllMonths(filter);
  }

  function checkIsVisible() {
    let localStorageData = JSON.parse(localStorage.getItem("favorites"));
    if (localStorageData) {
      var index = localStorageData.findIndex(
        (value) => value.component === GRAPH_NAME
      );
      return index !== -1;
    } else {
      return false;
    }
  }

  function swapVisible() {
    let object = structuredClone(props.componentVisibles);
    object.consumptionTable = !object.consumptionTable;
    props.setComponentVisibles(object);
  }

  const onEnter = useCallback(
    (_, index) => {
      setActiveIndex(index);
    },
    [setActiveIndex]
  );

  useEffect(() => {
    getGraphColor();
    getContracts();
  }, [props.contracts]);
  
  useEffect(() => {
    setIsFavorite(checkIsVisible());

  }, [props.favorites])

  const updateWidth = () => {
    setWidth(window.innerWidth);
  };
  React.useEffect(() => {
    window.addEventListener("resize", updateWidth);

    return () => window.removeEventListener("resize", updateWidth);
  });

  const graphWidth = React.useMemo(() => {
    if (width > 1800) {
      const proportion = 1850 / 750;
      return width / proportion;
    } else if (width > 1200 && width < 1800) {
      const proportion = 1850 / 500;
      return width / proportion;
    } else if (width < 1200) {
      const proportion = 1850 / 500;
      return width / proportion;
    }
  }, [width]);

  const secondaryGraphWidth = React.useMemo(() => {
    if (width > 1800) {
      const proportion = 1850 / 800;
      return width / proportion;
    } else if (width > 1200 && width < 1800) {
      const proportion = 1850 / 800;
      return width / proportion;
    } else if (width < 1200) {
      const proportion = 1850 / 1550;
      return width / proportion;
    }
  }, [width]);

  const TooltipContent = (e) => {
    return data.length > 0 ? (
      <div
        style={{
          backgroundColor: "white",
          padding: "10px",
          borderRadius: "8px",
        }}
      >
        <p style={{ marginBottom: "5px", color: "gray", fontSize: "14px" }}>
          Mês: {data[e.label]?.name}
        </p>
        <p style={{ color: "#435E91", fontSize: "14px" }}>
          Horas consumidas: {data[e.label]?.horas}h {data[e.label]?.minutos}m
        </p>
      </div>
    ) : (
      <></>
    );
  };

  return (
    <Card bordered={false} style={{ borderRadius: "20px", height: "100%" }}>
      {loading ? (
        <Row
          style={{
            height: "250px",
            minWidth: "275px",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
          }}
        >
          <Col style={{ display: "flex", flexDirection: "column" }}>
            <Spin />
            <Text>Carregando...</Text>
          </Col>
        </Row>
      ) : (
        <>
          <Row>
            <Col span={20}>
              <Title level={4} style={{ fontWeight: 400 }}>
                Média de consumo
              </Title>
            </Col>
            <Col span={2}>
              <Popover
                open={popoverVisibility}
                onOpenChange={handlePopoverVisibility}
                placement="left"
                content={() => {
                  return (
                    <AverageConsumptionFilter
                      handleVisibleChange={handlePopoverVisibility}
                      allClients={props?.clients}
                      filter={getFilteredChartData}
                    />
                  );
                }}
                trigger="click"
              >
                <div style={{ cursor: "pointer" }} className="filter">
                  <FilterFilled className="filter-icon" />
                </div>
              </Popover>
            </Col>
            <Col span={2}>
              {!isFavorite ? (
                <StarOutlined onClick={updateFavorite} className="star-icon" />
              ) : (
                <StarFilled onClick={updateFavorite} className="star-icon" />
              )}
            </Col>
          </Row>
          {selectedClient == 0 ? (
            <Row
              align="middle"
              justify="center"
              style={{ height: "250px", minWidth: "275px" }}
            >
              <Col>
                <Text>Selecione um Cliente*</Text>
              </Col>
            </Row>
          ) : data.length > 0 ? (
            <BarChart
              onClick={(index, _) => {
                setGraphIndex(index.activeTooltipIndex);
                return props.isModal ? showModal() : swapVisible();
              }}
              width={
                secondaryProportion
                  ? width < 1600 && width > 1200
                    ? secondaryGraphWidth - 180
                    : width < 1200
                    ? secondaryGraphWidth - 280
                    : secondaryGraphWidth - 160
                  : width < 1600
                  ? graphWidth - 300
                  : graphWidth - 150
              }
              height={265}
              data={data}
              setLoading={false}
              activeIndex={activeIndex}
              onMouseEnter={onEnter}
              margin={{
                top: 10,
                right: 14,
                left: 44,
                bottom: 5,
              }}
            >
              <XAxis />
              <Tooltip content={TooltipContent} />
              <ReferenceLine
                y={averageHours}
                label={{
                  position: "left",
                  value: averageHours,
                  fill: "#272D3E",
                  fontSize: 12,
                }}
                stroke="#40484F"
                strokeDasharray="3 3"
              />
              <Bar dataKey="horas" stackId="a" fill="#D1F010">
                {/* {data.map((entry, index) => {
                  return <Cell key={index} fill={entry.fill} />;
                })} */}
              </Bar>
            </BarChart>
          ) : (
            <Row style={{ height: "250px" }} align="middle" justify="center">
              <Col>
                <Text>Sem dados*</Text>
              </Col>
            </Row>
          )}
          <Modal
            closable={true}
            bodyStyle={{ backgroundColor: "transparent" }}
            cancelButtonProps={{ style: { display: "none" } }}
            open={modalVisible}
            onOk={handleOk}
            footer={null}
            centered
            onCancel={handleCancel}
          >
            <AverageConsumptionTable
              data={data}
              showModal={showModal}
              index={graphIndex}
            />
          </Modal>
        </>
      )}
    </Card>
  );
}
