import { Col, Input, Row, Select, Space, Typography } from "antd";

export const SwitchRolesHeader = (props) => {
  const { setSearch, setState } = props;
  const { Text } = Typography;
  const { Option } = Select;
  return (
    <Row justify="space-between">
      <Col span={8} style={{ marginBottom: "1em", borderRadius: "15px" }}>
        <Input
          placeholder="Faça uma busca"
          style={{
            height: "35px",
            borderRadius: "7px",
          }}
          onChange={(e) => setSearch(e.target.value)}
        />
      </Col>
      <Col>
        <Space>
          <Text>Filtrar por: </Text>
          <Select onChange={setState} defaultValue="todos">
            <Option value="todos">Todos</Option>
            <Option value="active">Permitidos</Option>
            <Option value="inactive">Não Permitidos</Option>
          </Select>
        </Space>
      </Col>
    </Row>
  );
};

export const OTRSTicketNumberRedirector = (props) => {
  const { ticket } = props;
  return (
    <a
      target="_blank"
      rel="noreferrer"
      style={{ color: "#0f9347" }}
      href={`https://${
        process.env.REACT_APP_STAGE !== "prod" ? "hml." : ""
      }tickets.darede.com.br/otrs/index.pl?Action=AgentTicketZoom;TicketNumber=${ticket}`}
    >
      {ticket}
    </a>
  );
};
