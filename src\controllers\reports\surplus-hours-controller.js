import pdfMake from "pdfmake/build/pdfmake";
import vfsFonts from "pdfmake/build/vfs_fonts";

import { Row, Typography } from "antd";
import { LoadingOutlined } from "@ant-design/icons";

import { config } from "../../utils/config";
import { apiDsm, apiReports, getHeader } from "../../utils/api";
import { realMask } from "../../utils/masks";
import {
  setConsumptionState,
  setConsumptionSearchState,
  setUpdatedHoursContractsState,
} from "../../store/actions/consumption-hours-action";
import { store } from "../../store/store";
import { message } from "antd";
import { otrsCalculateHoursProvider } from "../../provider/otrs-calculate-hours-provider";
import { setContractFieldState } from "../../store/actions/contract-action";
import { getItemsByDynamicIndex } from "../../service/apiDsmDynamo";

const { Text } = Typography;
export async function search(setLoading = null) {
  try {
    const { dtStart, dtEnd, selectedContract, contractTypes } =
      store.getState().consumption;

    const month = dtStart.month() + 1;
    const monthEnd = dtEnd.month() + 1;

    const startDate = dtStart.year() + "-" + month.toString().padStart(2, "0");
    const endDate = dtEnd.year() + "-" + monthEnd.toString().padStart(2, "0");

    let monthDiff = getDifferenceInMonthsBetweenDates(
      dtStart.toDate(),
      dtEnd.toDate()
    );

    if (monthDiff < 0) {
      message.warning("Período de pesquisa inválido");
      return;
    }

    const contractType = contractTypes.find(
      (c) => c.value === selectedContract
    );
    if (!contractType) {
      message.warning("Tipo de contrato inválido");
      return;
    }

    if (monthDiff > 12) {
      message.warning(
        "Não é possivel pesquisar um intervalo de datas maior do que 12 meses"
      );
      return;
    }

    if (setLoading) setLoading(true);

    const contracts = await getContracts();

    const consumptions = await getConsumptionContracts(startDate, endDate);

    const joinList = joinContractAndHour(
      contracts,
      consumptions,
      dtStart.toDate(),
      dtEnd.toDate()
    );

    joinList.sort(
      (a, b) => a.identifications.itsm_id - b.identifications.itsm_id
    );

    setConsumptionSearchState({
      contracts: joinList,
      pageSelected: 1,
    });

    if (setLoading) setLoading(false);
  } catch (error) {
    if (setLoading) setLoading(false);
  }
}

export function joinContractAndHour(contracts, consumptions, dtStart, dtEnd) {
  let monthDiff = getDifferenceInMonthsBetweenDates(dtStart, dtEnd);

  monthDiff += 1;

  const contractsHours = [];
  let period = dtStart;

  for (let index = 0; index < monthDiff; index++) {
    contracts.forEach((contract) => {
      const contractId = contract.identifications.itsm_id;

      let total_hours = contract.total_hours ? contract.total_hours : 0;
      if (typeof total_hours === "string") {
        total_hours = total_hours.replace(",", ".");
      }

      const totalHours = total_hours ? Number(total_hours) : 0;
      const totalHoursInMinutes = convertHoursToMinutes(totalHours);

      const hourValue = getHourValue(contract.excess_cost);
      let consumptionTotalPercentage = "0%";
      let consumptionTotalHours = 0;
      let consumptionTotalMinutes = 0;
      let accumulatedHoursMinutes = 0;
      let accumulatedHours = 0;
      let surplusHoursMinutes = 0;
      let surplusValue = 0;
      let averageConsumption = 0;
      let expiredHours = 0;
      let balanceHours = 0;
      let finalBalanceHours = 0;
      let totalExpiredHours = 0;
      let expirePeriod = "";

      contract.type_hours = contract.type_hours || "";
      const monthWithString = period.getMonth() + 1;
      const periodToString = `${period.getFullYear()}-${monthWithString
        .toString()
        .padStart(2, "0")}`;

      if (consumptions[contractId]) {
        const consumption = consumptions[contractId].find(
          (c) => c.period === periodToString
        );

        if (consumption) {
          const consumptionPercentage = consumption.consumed_percentage;

          if (consumption.previus_accumulated)
            accumulatedHoursMinutes = consumption.previus_accumulated * 60;

          if (consumption.previus_accumulated)
            accumulatedHours = consumption.previus_accumulated;

          if (consumptionPercentage)
            consumptionTotalPercentage = formatConsumptionTotalToNumber(
              consumptionPercentage
            );

          consumptionTotalHours = consumption.consumed_hours;
          consumptionTotalMinutes = consumption.consumed_minutes;

          expiredHours = consumption.accumulated_expire * 60 || 0;
          finalBalanceHours = consumption.final_balance * 60 || 0;

          balanceHours = consumption.balance * 60 || 0;
          averageConsumption = consumption.average_consumption * 60 || 0;
          totalExpiredHours = consumption.total_expired * 60 || 0;

          if (consumptionTotalMinutes > 0) {
            surplusHoursMinutes = consumption.overtime * -1 * 60;
          }

          expirePeriod = consumption.period_expire;

          surplusValue = calculateSurplusValue(hourValue, surplusHoursMinutes);
          contract.total_hours = consumption.total_hours;
          contract.totalHoursInMinutes = consumption.total_hours * 60;
          contract.excess_cost = hourValue;

          contractsHours.push({
            ...contract,
            consumptionTotalPercentage,
            consumptionTotalHours,
            consumptionTotalMinutes,
            surplusHoursMinutes,
            accumulatedHoursMinutes,
            accumulatedHours,
            surplusValue,
            averageConsumption,
            expiredHours,
            balanceHours,
            finalBalanceHours,
            expirePeriod,
            totalExpiredHours,
            period: periodToString,
          });
        } else {
          surplusValue = calculateSurplusValue(hourValue, surplusHoursMinutes);
          contract.total_hours = totalHours;
          contract.totalHoursInMinutes = totalHoursInMinutes;
          contract.excess_cost = hourValue;

          contractsHours.push({
            ...contract,
            consumptionTotalPercentage,
            consumptionTotalHours,
            consumptionTotalMinutes,
            surplusHoursMinutes,
            accumulatedHoursMinutes,
            accumulatedHours,
            surplusValue,
            averageConsumption,
            expiredHours,
            balanceHours,
            finalBalanceHours,
            expirePeriod,
            totalExpiredHours,
            period: periodToString,
          });
        }
      } else {
        surplusValue = calculateSurplusValue(hourValue, surplusHoursMinutes);
        contract.total_hours = totalHours;
        contract.totalHoursInMinutes = totalHoursInMinutes;
        contract.excess_cost = hourValue;

        contractsHours.push({
          ...contract,
          consumptionTotalPercentage,
          consumptionTotalHours,
          consumptionTotalMinutes,
          surplusHoursMinutes,
          accumulatedHoursMinutes,
          accumulatedHours,
          surplusValue,
          averageConsumption,
          expiredHours,
          balanceHours,
          finalBalanceHours,
          expirePeriod,
          totalExpiredHours,
          period: periodToString,
        });
      }
    });

    period.setMonth(period.getMonth() + 1);
  }

  return contractsHours;
}

function getHourValue(value) {
  let hourValue = 0;
  if (value) {
    if (typeof value !== "object") hourValue = Number(value);
  }

  return hourValue;
}

function calculateSurplusValue(hourValue, surplusHoursMinutes) {
  const surplusHour = convertMinutesToHours(surplusHoursMinutes);

  return hourValue * surplusHour;
}

function formatConsumptionTotalToNumber(consumptionPercentage) {
  let percentage = consumptionPercentage.replace("%", "");
  return Number(percentage);
}

function calculateSuplusHours(totalHours, consumptionTotalHours) {
  let surplusHours = totalHours - consumptionTotalHours;
  if (surplusHours < 0) surplusHours *= -1;
  else surplusHours = 0;

  return surplusHours;
}

export async function getConsumptionContracts(dtStart, dtEnd) {
  try {
    const params = [];
    params.push(`dateStart=${dtStart}`);
    params.push(`dateEnd=${dtEnd}`);

    const provider = otrsCalculateHoursProvider();

    const { data } = await provider.get(
      `/contract/consumptions?${params.join("&")}`
    );

    const consumptions = data.reduce((a, consumption) => {
      const contractID = consumption.contractid;

      if (consumption) {
        if (a[contractID]) a[contractID].push(consumption);
        else a[contractID] = [consumption];
      }

      return a;
    }, {});

    return consumptions;
  } catch (error) {
    console.log(error);
    return [];
  }
}

export async function getContracts() {
  return await getInfoApiDsm(`${config.ENVIROMENT}-contracts`);
}

export async function getInfoApiDsm(tableName) {
  try {
    const headers = {
      ...getHeader(),
      dynamodb: tableName,
    };

    const { data } = await apiDsm.get("read/all/0", { headers });

    return data.data.Items;
  } catch (error) {
    console.log(error);
    return [];
  }
}

export async function getSquads() {
  try {
    const tableName = `${config.ENVIROMENT}-wallets`;

    const response = await apiDsm.get("read/all/0", {
      headers: {
        dynamodb: tableName,
      }
    });

    const { data } = response.data;

    if (data?.Items) {
      data.Items.sort((a, b) => a.name.localeCompare(b.name));

      setConsumptionState({ field: "squadOptions", value: data.Items });
      setContractFieldState({ field: "squadOptions", value: data.Items });
    } else {
      setConsumptionState({ field: "squadOptions", value: [] });
      setContractFieldState({ field: "squadOptions", value: [] });
    }
  } catch (error) {
    // console.error("❌ Erro ao carregar squads:", {
    //   message: error.message,
    //   status: error.response?.status,
    //   statusText: error.response?.statusText,
    //   url: error.config?.url,
    //   headers: error.config?.headers
    // });
    setConsumptionState({ field: "squadOptions", value: [] });
    setContractFieldState({ field: "squadOptions", value: [] });
  }
}

export async function getCustomerInfo(customerID) {
  try {
    const tableName = `${config.ENVIROMENT}-customers`;
    const headers = { ...getHeader(), dynamodb: tableName };

    const { data } = await apiDsm.get(`read/id/${customerID}`, { headers });

    let customerInfos = [];
    if (data.data) {
      const customer = data.data.Item;

      if (customer.names)
        customerInfos.push({ name: "Nome", value: customer.names.name });

      if (customer.names)
        customerInfos.push({
          name: "Nome Fantasia",
          value: customer.names.fantasy_name,
        });

      if (customer.address) {
        const address = customer.address;
        customerInfos.push({
          name: "Endereço",
          value: `${address.ogradouro || ""}, ${address.numero || ""}, ${
            address.complemento || ""
          } - ${address.bairro || ""}`,
        });
      }

      if (customer.phones) {
        const phones = customer.phones;
        customerInfos.push({
          name: "Telefone 1",
          value: `(${phones.ddd1 || ""}) ${phones.telefone1 || ""}`,
        });
        customerInfos.push({
          name: "Telefone 2",
          value: `(${phones.ddd2 || ""}) ${phones.telefone2 || ""}`,
        });
      }
    }

    return customerInfos;
  } catch (error) {
    return [];
  }
}

export function filterContracts(search, contract) {
  const id = contract.identifications.itsm_id || "-";
  if (id.toString().toUpperCase().includes(search.toUpperCase())) return true;

  if (contract.name.toUpperCase().includes(search.toUpperCase())) return true;

  const typeHours = contract.type_hours ? contract.type_hours : "";
  if (typeHours.toUpperCase().includes(search.toUpperCase())) return true;

  const customer = contract.customer.itsm_id || "-";
  if (customer.toString().toUpperCase().includes(search.toUpperCase()))
    return true;

  const squad = contract.squad || "";
  if (squad.toUpperCase().includes(search.toUpperCase())) return true;

  const total_hours = contract.total_hours || "";
  if (total_hours.toString().toUpperCase().includes(search.toUpperCase()))
    return true;

  const hourValue = getHourValue(contract.excess_cost);
  if (hourValue.toString().toUpperCase().includes(search.toUpperCase()))
    return true;

  const totalHourSurplus = contract.surplusValue;
  if (totalHourSurplus.toString().toUpperCase().includes(search.toUpperCase()))
    return true;

  return false;
}

function convertHoursToMinutes(value) {
  return value * 60;
}

function convertMinutesToHours(value) {
  return value / 60;
}

export function formactMinutosToHours(minutes, isExcel = false) {
  let hours = Math.trunc(minutes / 60);
  let restMinutes = Math.ceil(minutes % 60);

  if (minutes < 0) restMinutes = Math.ceil((minutes * -1) % 60);
  if (minutes < 0 && hours === 0) hours = "-0";

  if (isExcel) return `${hours}:${restMinutes.toString().padStart(2, "0")}:00`;

  return `${hours}:${restMinutes.toString().padStart(2, "0")}`;
}

function getDifferenceInMonthsBetweenDates(date1, date2) {
  let months;
  months = (date2.getFullYear() - date1.getFullYear()) * 12;
  months -= date1.getMonth();
  months += date2.getMonth();
  return months;
}

export function generatePDF(contracts) {
  const { vfs } = vfsFonts.pdfMake;
  pdfMake.vfs = vfs;

  const { collumnsToShow } = store.getState().globalPersist.consumption;

  const dynamicSizeCollumns = collumnsToShow.map(() => "auto");
  const dynamicHeaderCollumns = buildDynamicHeaders();
  const dynamicCollumnValues = buildDynamicCollumnValues(contracts);

  const documentDefinition = {
    pageSize: "A4",
    pageOrientation: "landscape",
    header: function () {
      return {
        margin: [10, 10, 10, 10],
        layout: "noBorders",
        fillColor: "#04A467",
        table: {
          widths: ["*"],
          body: [
            [{ text: "DAREDE", fontSize: 14, style: "title", color: "#FFF" }],
          ],
        },
      };
    },
    content: [
      {
        text: "Relatório de Consumo de Horas x Contrato",
        style: "titleContent",
      },
      "\n",
      {
        style: "tableExample",
        margin: [-30, 0, 0, 0],
        table: {
          widths: dynamicSizeCollumns,
          body: [dynamicHeaderCollumns, ...dynamicCollumnValues],
        },
        layout: {
          fillColor: function (rowIndex, node, columnIndex) {
            if (rowIndex === 0) return rowIndex % 2 === 0 ? "#04A467" : null;
          },
        },
      },
    ],
    styles: {
      header: {
        fontSize: 10,
        bold: true,
      },
      title: {
        fontSize: 11,
        alignment: "center",
        bold: true,
      },
      titleContent: {
        fontSize: 11,
        bold: true,
        alignment: "center",
        margin: [-30, 0, 0, 8],
      },
      data: {
        fontSize: 10,
      },
      tableRow: {
        fontSize: 7,
        bold: true,
        alignment: "center",
        color: "#FFF",
        bold: true,
      },
    },
  };

  pdfMake.createPdf(documentDefinition).open();
}

function buildDynamicCollumnValues(constracts) {
  const { collumnsToShow } = store.getState().globalPersist.consumption;

  return constracts.map((item) => {
    let values = [];

    if (collumnsToShow.includes("DSM ID"))
      values.push({
        text: item.dsm_id,
        fontSize: 6,
        alignment: "center",
      });

    if (collumnsToShow.includes("Contrato"))
      values.push({ text: item.name, fontSize: 6 });

    if (collumnsToShow.includes("Tipo"))
      values.push({
        text: item.type_hours,
        fontSize: 6,
        alignment: "center",
      });

    if (collumnsToShow.includes("Mês"))
      values.push({ text: item.period, fontSize: 6, alignment: "center" });

    if (collumnsToShow.includes("Cliente"))
      values.push({
        text: item.customer.itsm_id,
        fontSize: 6,
        alignment: "center",
      });

    if (collumnsToShow.includes("Status"))
      values.push({
        text: item.active ? "Ativo" : "Inativo",
        fontSize: 6,
        alignment: "center",
      });

    if (collumnsToShow.includes("Squad"))
      values.push({
        text: item.squad || "",
        fontSize: 6,
        alignment: "center",
      });

    if (collumnsToShow.includes("Horas Contratadas"))
      values.push({
        text: formactMinutosToHours(item.totalHoursInMinutes),
        fontSize: 6,
        alignment: "right",
      });

    if (collumnsToShow.includes("Horas Expiradas"))
      values.push({
        text: formactMinutosToHours(item.expiredHours),
        fontSize: 6,
        alignment: "right",
      });

    if (collumnsToShow.includes("Horas Consumidas"))
      values.push({
        text: formactMinutosToHours(item.consumptionTotalMinutes),
        fontSize: 6,
        alignment: "right",
      });

    if (collumnsToShow.includes("Horas Acumuladas"))
      values.push({
        text: formactMinutosToHours(item.accumulatedHoursMinutes),
        fontSize: 6,
        alignment: "right",
      });

    if (collumnsToShow.includes("Saldo"))
      values.push({
        text: formactMinutosToHours(item.finalBalanceHours),
        fontSize: 6,
        alignment: "right",
      });

    if (collumnsToShow.includes("Horas Excedentes"))
      values.push({
        text: formactMinutosToHours(item.surplusHoursMinutes),
        fontSize: 6,
        alignment: "right",
      });

    if (collumnsToShow.includes("Valor Hora"))
      values.push({
        text: `R$ ${realMask(Number(item.excess_cost || 0).toFixed(2))}`,
        fontSize: 6,
        alignment: "right",
      });

    if (collumnsToShow.includes("Total Excedente"))
      values.push({
        text: `R$ ${realMask(item.surplusValue.toFixed(2))}`,
        fontSize: 6,
        alignment: "right",
      });

    if (collumnsToShow.includes("Consumo Médio"))
      values.push({
        text: formactMinutosToHours(item.averageConsumption),
        fontSize: 6,
        alignment: "right",
      });

    if (collumnsToShow.includes("Total Horas Expiradas"))
      values.push({
        text: formactMinutosToHours(item.totalExpiredHours),
        fontSize: 6,
        alignment: "right",
      });

    return values;
  });
}

function buildDynamicHeaders() {
  const { collumnsToShow } = store.getState().globalPersist.consumption;
  const values = [];

  if (collumnsToShow.includes("DSM ID"))
    values.push({ text: "DSM ID", style: "tableRow", fontSize: 7 });

  if (collumnsToShow.includes("Contrato"))
    values.push({ text: "Contrato", style: "tableRow", fontSize: 7 });

  if (collumnsToShow.includes("Tipo"))
    values.push({ text: "Tipo", style: "tableRow", fontSize: 7 });

  if (collumnsToShow.includes("Mês"))
    values.push({ text: "Mês", style: "tableRow", fontSize: 7 });

  if (collumnsToShow.includes("Cliente"))
    values.push({ text: "Cliente", style: "tableRow", fontSize: 7 });

  if (collumnsToShow.includes("Status"))
    values.push({ text: "Status", style: "tableRow", fontSize: 7 });

  if (collumnsToShow.includes("Squad"))
    values.push({ text: "Squad", style: "tableRow", fontSize: 7 });

  if (collumnsToShow.includes("Horas Contratadas"))
    values.push({
      text: "Horas Contratadas",
      style: "tableRow",
      fontSize: 7,
    });

  if (collumnsToShow.includes("Horas Expiradas"))
    values.push({ text: "Horas Expiradas", style: "tableRow", fontSize: 7 });

  if (collumnsToShow.includes("Horas Consumidas"))
    values.push({ text: "Horas Consumidas", style: "tableRow", fontSize: 7 });

  if (collumnsToShow.includes("Horas Acumuladas"))
    values.push({ text: "Horas Acumuladas", style: "tableRow", fontSize: 7 });

  if (collumnsToShow.includes("Saldo"))
    values.push({ text: "Saldo", style: "tableRow", fontSize: 7 });

  if (collumnsToShow.includes("Horas Excedentes"))
    values.push({ text: "Horas Excedentes", style: "tableRow", fontSize: 7 });

  if (collumnsToShow.includes("Valor Hora"))
    values.push({ text: "Valor Hora", style: "tableRow", fontSize: 7 });

  if (collumnsToShow.includes("Total Excedente"))
    values.push({ text: "Total Excedente", style: "tableRow", fontSize: 7 });

  if (collumnsToShow.includes("Consumo Médio"))
    values.push({ text: "Consumo Médio", style: "tableRow", fontSize: 7 });

  if (collumnsToShow.includes("Total Horas Expiradas"))
    values.push({
      text: "Total Horas Expiradas",
      style: "tableRow",
      fontSize: 7,
    });

  return values;
}

const removeDuplicates = (arr, property) => {
  const uniqueMap = new Map();
  return arr.reduce((uniqueArr, obj) => {
    if (!uniqueMap.has(obj[property])) {
      uniqueMap.set(obj[property], true);
      uniqueArr.push(obj);
    }
    return uniqueArr;
  }, []);
};

export const getUpdatedHours = async (contracts = [], previousState = []) => {
  let updatedHours = [];
  let promises = [];

  contracts.forEach((contract) => {
    if (contract.hasChangedHours) {
      promises.push(
        getItemsByDynamicIndex(
          `${process.env.REACT_APP_STAGE}-consumption-hours`,
          "contract_id",
          contract.id
        )
      );
    }
  });

  await Promise.all(promises).then((values) => {
    values.forEach((value) => {
      updatedHours = updatedHours.concat(value);
    });
  });

  let formatted = previousState.concat(updatedHours);

  setUpdatedHoursContractsState({
    updatedHoursContracts: removeDuplicates(formatted, "id"),
  });
};

export const renderTotalHours = (total, data, changedHoursState) => {
  if (data?.hasChangedHours) {
    if (changedHoursState) {
      let currentContract = changedHoursState?.find(
        (contract) => contract.contract_id === data.id
      );
      if (currentContract) {
        // ✅ Corrigido: usar find() em vez de map() para encontrar o contrato correto
        const contract = currentContract.find((contract) => {
          let dataPeriodDate = new Date(data.period);
          let updatedConsumptionDate = new Date(
            contract.updated_consumption_hours
          );
          return dataPeriodDate < updatedConsumptionDate || dataPeriodDate >= updatedConsumptionDate;
        });

        if (contract) {
          let dataPeriodDate = new Date(data.period);
          let updatedConsumptionDate = new Date(
            contract.updated_consumption_hours
          );

          if (dataPeriodDate < updatedConsumptionDate) {
            return (
              <Row justify="center">
                <Text>{formactMinutosToHours(total)}</Text>
              </Row>
            );
          } else if (dataPeriodDate >= updatedConsumptionDate) {
            return (
              <Row justify="center">
                <Text>{formactMinutosToHours(contract.new_hours)}</Text>
              </Row>
            );
          }
        }
      } else {
        return (
          <Row justify="center">
            <LoadingOutlined />
          </Row>
        );
      }
    }
  }
  return <Text>{formactMinutosToHours(total)}</Text>;
};
