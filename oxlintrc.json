{"rules": {"react/jsx-uses-react": "off", "react/react-in-jsx-scope": "off", "no-unused-vars": "error", "no-console": "warn", "prefer-const": "error", "no-var": "error", "eqeqeq": "error", "no-duplicate-imports": "error", "no-unreachable": "error", "no-undef": "error", "react/jsx-no-undef": "error", "react/jsx-uses-vars": "error", "react/no-unused-state": "warn", "react/prefer-stateless-function": "warn", "react/jsx-pascal-case": "error", "react/jsx-no-duplicate-props": "error", "react/no-direct-mutation-state": "error", "react/jsx-key": "error", "react/no-array-index-key": "warn"}, "env": {"browser": true, "es2022": true, "node": true, "jest": true}, "extends": ["eslint:recommended", "@typescript-eslint/recommended", "plugin:react/recommended", "plugin:react-hooks/recommended"], "ignorePatterns": ["build/", "node_modules/", "public/", "*.config.js", "*.setup.js"]}