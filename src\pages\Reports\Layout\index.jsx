import { useState } from 'react'
import styledSheet from './style.module.scss'

import { Layout, Card } from "antd";

import { SideMenu } from "../../../components/SideMenu";
import { HeaderMenu } from "../../../components/HeaderMenu";

export const LayoutReport = ({ children }) => {
    const [collapsed, setCollapsed] = useState(false);

    return (
        <Layout style={{ minHeight: "100vh" }}>
            <HeaderMenu collapsed={collapsed} setCollapsed={setCollapsed} />
            <Layout>
                <SideMenu collapsed={collapsed} />
                <Layout.Content style={{ padding: "1em" }}>
                    <Card className={styledSheet['container']} bodyStyle={{ flex: 1, width:"100%" }} style={{padding:"1em", width:"100%"}} >
                        {children}
                    </Card>
                </Layout.Content>
            </Layout>
        </Layout>
    )
}