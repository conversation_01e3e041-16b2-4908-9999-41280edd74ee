import { useState, useMemo } from "react";
import { useSelector, shallowEqual } from "react-redux";

import { Row, Col, Button, Modal, message, Input } from "antd";
import { MailOutlined, SendOutlined } from "@ant-design/icons";

import { EditFileTags } from "./components/EditFileTags";
import { EditEmailsSelect } from "./components/EditEmailsSelect";
import { InputFileNameEmailModal } from "./components/InputFileNameEmailModal";
import { SelectCategoryEmailModal } from "./components/SelectCategoryEmailModal";

import { useToggle } from "../../../../../hooks/useToggle";
import { dynamoPut } from "../../../../../service/apiDsmDynamo";

import { setUploadFilesState } from "../../../../../store/actions/files-action";

import * as controller from "../../../../../controllers/files/filesController";
import { INITIAL_STATE } from "../../../../../store/reducers/files-reducer";
import { logNewAuditAction } from "../../../../../controllers/audit/logNewAuditAction";

export const SendEmailModal = ({ file }) => {
  const allFiles = useSelector((state) => state.files.files, shallowEqual);
  const selectedFile = useSelector(
    (state) => state.files.selectedFile,
    shallowEqual
  );

  const [loading, setLoading] = useState(false);
  const [toggleModalValue, toggle] = useToggle();

  const handleSendEmail = async () => {
    const tableName = `${process.env.REACT_APP_STAGE}-files-archive`;

    let isError = false;

    setLoading(true);
    try {
      await dynamoPut(tableName, selectedFile.id, selectedFile);
      let newFilesStr = JSON.stringify(allFiles);
      let newFiles = JSON.parse(newFilesStr);

      const index = allFiles.findIndex((f) => f.id === selectedFile.id);
      newFiles[index] = selectedFile;

      setUploadFilesState({ field: "files", value: newFiles });
    } catch (error) {
      isError = true;
      console.log(error);
      message.error(
        "Ops um erro correu, não foi possivel atualizar os dados do arquivo!"
      );
    }

    if (!isError) {
      try {
        await controller.dispatchEmail([selectedFile]);
        message.success("Email enviado com sucesso!");

        const username = localStorage.getItem("@dsm/username");
        const title = "Notificação por Email - Arquivos";
        const description = `${username} realizou a notificação por email do seguinte arquivo: ${selectedFile.name}`;
        logNewAuditAction(username, title, description);
      } catch (error) {
        isError = true;
        console.log(error);
        message.error("Ops um erro correu, não foi possivel enviar o email!");
      }
    }

    setLoading(false);

    if (!isError) {
      setUploadFilesState({
        field: "selectedFile",
        value: INITIAL_STATE.selectedFile,
      });
      toggle();
    }
  };

  const isDisabled = useMemo(() => {
    const { category, name, tags, emails } = selectedFile;

    if (
      category.length === 0 ||
      name.length === 0 ||
      tags.length === 0 ||
      emails.length === 0
    )
      return true;

    return false;
  }, [selectedFile]);

  return (
    <>
      <Button
        type="text"
        icon={<MailOutlined />}
        onClick={() => {
          toggle();
          setUploadFilesState({
            field: "selectedFile",
            value: file,
          });
        }}
      />

      <Modal
        title={file.name || ""}
        open={toggleModalValue}
        width={"40%"}
        closable={false}
        footer={
          <Row justify={"space-between"}>
            <Button
              type="text-color"
              onClick={() => {
                setUploadFilesState({
                  field: "selectedFile",
                  value: INITIAL_STATE.selectedFile,
                });
                toggle();
              }}
            >
              Cancelar
            </Button>
            <Button
              type="primary"
              onClick={handleSendEmail}
              disabled={isDisabled || loading}
              loading={loading}
            >
              <SendOutlined /> Enviar
            </Button>
          </Row>
        }
      >
        <Col span={24}>
          <Row>Cliente:</Row>
          <Row>
            <Input disabled={true} value={file.customerName} />
          </Row>
          <Row style={{ marginTop: "16px" }}>
            <InputFileNameEmailModal />
          </Row>

          <Row style={{ marginTop: "16px" }}>
            <SelectCategoryEmailModal />
          </Row>

          <Row style={{ marginTop: "16px" }}>Tags:</Row>
          <Row>
            <EditFileTags />
          </Row>

          <Row style={{ marginTop: "16px" }}>Emails:</Row>
          <Row>
            <EditEmailsSelect />
          </Row>
        </Col>
      </Modal>
    </>
  );
};
