import { React, useEffect } from 'react'
import { Select, DatePicker, Button, Form } from 'antd'
import { CloseSquareFilled } from '@ant-design/icons'

function ContractsPerWalletFilter(props) {
    const { Option } = Select
    const [form] = Form.useForm();

    const clients = ['Todos', 'Logikee', 'Darede']

    function onFilterFinish(values) {
        props.filterIssues(values)
        props.handleVisibleChange()
    }

    function onFilterFailed() {}

    function clear() {
        form.resetFields();
    }

    useEffect(() => {

    }, [])

    return (
        <Form
            form={form}
            style={{ display: 'flex', flexDirection: 'column' }}
            name="basic"
            onFinish={onFilterFinish}
            onFinishFailed={onFilterFailed}
            autoComplete="off">
            <button>
                <CloseSquareFilled />
            </button>
            <Form.Item name="profissional">
                <Select
                    showSearch
                    style={{ width: '100%' }}
                    placeholder="Profissional"
                    optionFilterProp="children"
                    filterOption={(input, option) =>
                        option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0 ||
                        option.props.value.toLowerCase().indexOf(input.toLowerCase()) >= 0
                    }>
                    {clients.map((val) => (
                        <Option value={val}>{val}</Option>
                    ))}
                </Select>
            </Form.Item>

            <Form.Item name="wallet">
                <Select
                    showSearch
                    style={{ width: '100%' }}
                    placeholder="Carteira"
                    optionFilterProp="children"
                    filterOption={(input, option) =>
                        option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0 ||
                        option.props.value.toLowerCase().indexOf(input.toLowerCase()) >= 0
                    }>
                    {clients.map((val) => (
                        <Option value={val}>{val}</Option>
                    ))}
                </Select>
            </Form.Item>

            <Form.Item name="row">
                <Select
                    showSearch
                    style={{ width: '100%' }}
                    placeholder="Fila"
                    optionFilterProp="children"
                    filterOption={(input, option) =>
                        option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0 ||
                        option.props.value.toLowerCase().indexOf(input.toLowerCase()) >= 0
                    }>
                    {clients.map((val) => (
                        <Option value={val}>{val}</Option>
                    ))}
                </Select>
            </Form.Item>

            <div className="buttons">
                <Button className="filter-clear-btn" onClick={clear}>Limpar</Button>
                <Button
                    htmlType="submit"
                    className="filter-submit-btn">
                    Aplicar
                </Button>
            </div>
        </Form>
    )
}

export default ContractsPerWalletFilter
