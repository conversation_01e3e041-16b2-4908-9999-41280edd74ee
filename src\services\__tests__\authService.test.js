import axios from 'axios';
import Cookies from 'js-cookie';
import { authService } from '../authService';

// Mock dependencies
jest.mock('axios');
jest.mock('js-cookie');

const mockedAxios = axios;
const mockedCookies = Cookies;

describe('AuthService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Clear any existing cache
    authService.clearUserInfo();
  });

  describe('setAuthToken', () => {
    it('should successfully set auth token and user info', async () => {
      const mockToken = 'mock-cognito-token';
      const mockUserInfo = {
        name: '<PERSON>',
        email: '<EMAIL>',
        permission: 'admin',
        username: 'john.doe'
      };

      mockedAxios.post.mockResolvedValueOnce({
        data: { status: 'success' }
      });

      const result = await authService.setAuthToken(mockToken, mockUserInfo);

      expect(result).toBe(true);
      expect(mockedAxios.post).toHaveBeenCalledWith(
        expect.stringContaining('auth/set-cookie'),
        { token: mockToken, userInfo: mockUserInfo },
        expect.objectContaining({
          withCredentials: true,
          headers: { 'Content-Type': 'application/json' }
        })
      );
    });

    it('should return false when backend fails to set cookie', async () => {
      const mockToken = 'mock-cognito-token';
      const mockUserInfo = { name: 'John Doe' };

      mockedAxios.post.mockResolvedValueOnce({
        data: { status: 'error' }
      });

      const result = await authService.setAuthToken(mockToken, mockUserInfo);

      expect(result).toBe(false);
    });

    it('should handle network errors gracefully', async () => {
      const mockToken = 'mock-cognito-token';
      const mockUserInfo = { name: 'John Doe' };

      mockedAxios.post.mockRejectedValueOnce(new Error('Network error'));

      const result = await authService.setAuthToken(mockToken, mockUserInfo);

      expect(result).toBe(false);
    });
  });

  describe('setUserInfo', () => {
    it('should set user info in cookies', () => {
      const userInfo = {
        name: 'John Doe',
        email: '<EMAIL>',
        permission: 'admin',
        username: 'john.doe'
      };

      authService.setUserInfo(userInfo);

      expect(mockedCookies.set).toHaveBeenCalledWith('dsm_user_name', 'John Doe', expect.any(Object));
      expect(mockedCookies.set).toHaveBeenCalledWith('dsm_user_email', '<EMAIL>', expect.any(Object));
      expect(mockedCookies.set).toHaveBeenCalledWith('dsm_user_permission', 'admin', expect.any(Object));
      expect(mockedCookies.set).toHaveBeenCalledWith('dsm_username', 'john.doe', expect.any(Object));
      expect(mockedCookies.set).toHaveBeenCalledWith('dsm_login_time', expect.any(String), expect.any(Object));
    });
  });

  describe('getUserInfo', () => {
    it('should get user info from cookies', () => {
      mockedCookies.get.mockImplementation((key) => {
        const mockData = {
          'dsm_user_name': 'John Doe',
          'dsm_user_email': '<EMAIL>',
          'dsm_user_permission': 'admin',
          'dsm_username': 'john.doe',
          'dsm_login_time': '2023-01-01T00:00:00.000Z'
        };
        return mockData[key];
      });

      const userInfo = authService.getUserInfo();

      expect(userInfo).toEqual({
        name: 'John Doe',
        email: '<EMAIL>',
        permission: 'admin',
        username: 'john.doe',
        loginTime: '2023-01-01T00:00:00.000Z'
      });
    });
  });

  describe('isAuthenticated', () => {
    it('should return true when user is authenticated', async () => {
      mockedAxios.get.mockResolvedValueOnce({
        data: { data: { authenticated: true } }
      });

      const result = await authService.isAuthenticated();

      expect(result).toBe(true);
      expect(mockedAxios.get).toHaveBeenCalledWith(
        expect.stringContaining('auth/verify'),
        expect.objectContaining({
          withCredentials: true,
          headers: { 'Content-Type': 'application/json' }
        })
      );
    });

    it('should return false when user is not authenticated', async () => {
      mockedAxios.get.mockResolvedValueOnce({
        data: { data: { authenticated: false } }
      });

      const result = await authService.isAuthenticated();

      expect(result).toBe(false);
    });

    it('should return false on network error', async () => {
      mockedAxios.get.mockRejectedValueOnce(new Error('Network error'));

      const result = await authService.isAuthenticated();

      expect(result).toBe(false);
    });
  });

  describe('logout', () => {
    it('should successfully logout and clear cookies', async () => {
      mockedAxios.post.mockResolvedValueOnce({
        data: { status: 'success' }
      });

      await authService.logout();

      expect(mockedAxios.post).toHaveBeenCalledWith(
        expect.stringContaining('auth/logout'),
        {},
        expect.objectContaining({
          withCredentials: true,
          headers: { 'Content-Type': 'application/json' }
        })
      );
      expect(mockedCookies.remove).toHaveBeenCalled();
    });

    it('should clear cookies even if backend call fails', async () => {
      mockedAxios.post.mockRejectedValueOnce(new Error('Network error'));

      await authService.logout();

      expect(mockedCookies.remove).toHaveBeenCalled();
    });
  });

  describe('createAuthenticatedAxios', () => {
    it('should create axios instance with correct configuration', () => {
      const instance = authService.createAuthenticatedAxios();

      expect(instance.defaults.withCredentials).toBe(true);
      expect(instance.defaults.baseURL).toBe(process.env.REACT_APP_API_PERMISSION);
    });
  });

  describe('migrateFromLocalStorage', () => {
    beforeEach(() => {
      // Mock localStorage
      Object.defineProperty(window, 'localStorage', {
        value: {
          getItem: jest.fn(),
          removeItem: jest.fn(),
        },
        writable: true,
      });
    });

    it('should migrate existing localStorage data', async () => {
      const mockToken = 'legacy-jwt-token';
      const mockUserData = {
        jwt: mockToken,
        '@dsm/name': 'John Doe',
        '@dsm/mail': '<EMAIL>',
        '@dsm/permission': 'admin',
        '@dsm/username': 'john.doe'
      };

      window.localStorage.getItem.mockImplementation((key) => mockUserData[key]);
      mockedAxios.post.mockResolvedValueOnce({
        data: { status: 'success' }
      });

      await authService.migrateFromLocalStorage();

      expect(mockedAxios.post).toHaveBeenCalledWith(
        expect.stringContaining('auth/set-cookie'),
        expect.objectContaining({
          token: mockToken,
          userInfo: expect.objectContaining({
            name: 'John Doe',
            email: '<EMAIL>'
          })
        }),
        expect.any(Object)
      );
    });

    it('should not migrate if no legacy data exists', async () => {
      window.localStorage.getItem.mockReturnValue(null);

      await authService.migrateFromLocalStorage();

      expect(mockedAxios.post).not.toHaveBeenCalled();
    });
  });
});
