import { shallowEqual, useSelector } from "react-redux";
import { Col, Select, Typography } from "antd";

import { setContractFieldState } from "../../../store/actions/contract-action";

export const PoolTypeFilter = ({ span }) => {
  const { poolTypes, selectedPool } = useSelector(
    (state) => state.contract,
    shallowEqual
  );
  const { Text } = Typography;

  return (
    <Col span={span}>
      <Text>Tip<PERSON>:</Text>
      <Select
        onChange={(value) =>
          setContractFieldState({ field: "selectedPool", value })
        }
        options={poolTypes}
        value={selectedPool}
        style={{ width: "100%" }}
      />
    </Col>
  );
};
