import React, { useEffect } from "react";
import { Row, Col, Select, Form, Typography } from "antd";
import { useSelector, shallowEqual } from "react-redux";
import { setTechnicalProposalState } from "../../../store/actions/technical-proposal-action";
import Cookies from "js-cookie";

const { Title } = Typography;
const { Option } = Select;

const STATUS_LIST = [
  "não inicializada",
  "em andamento",
  "concluída",
  "revisão técnica",
];

export const StatusField = () => {
  const status = useSelector(
    (state) => state.technicalProposal.status,
    shallowEqual
  );

  return (
    <Col sm={24} md={24} lg={12}>
      <Row>
        <Title level={5} style={{ fontWeight: "400" }}>
          Status
        </Title>
      </Row>
      <Row>
        <Col span={23}>
          <Select
            id="status"
            loading={false}
            showSearch
            placeholder="Status da proposta"
            optionFilterProp="children"
            value={status ? status : Cookies.get("status") ? status : null}
            filterOption={(input, option) =>
              option?.children?.toLowerCase().includes(input?.toLowerCase())
            }
            filterSort={(optionA, optionB) =>
              optionA.children
                .toLowerCase()
                .localeCompare(optionB.children.toLowerCase())
            }
            onSelect={(e) =>
              setTechnicalProposalState({ field: "status", value: e })
            }
            style={{ width: "100%" }}
          >
            {STATUS_LIST.map((value, key) => (
              <Option value={value} key={key}>
                {value}
              </Option>
            ))}
          </Select>
        </Col>
      </Row>
    </Col>
  );
};
