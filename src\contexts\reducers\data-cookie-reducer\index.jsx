export const DataCookieReducerType = {
  project_name: "project_name",
  client_name: "client_name",
  project_type: "project_type",
  project_status: "project_status",
  project_contacts: "project_contacts",
  project_architects: "project_architects",
  project_bu: "project_bu",
  project_opportunity: "project_opportunity",
};

export function CookieFieldsReducer(state, action) {
  switch (action.type) {
    case DataCookieReducerType.project_name:
      if (action.value !== null)
        return { ...state, project_name: action.value };
      else return state;

    case DataCookieReducerType.client_name:
      if (action.value !== null) return { ...state, client_name: action.value };
      else return state;

    case DataCookieReducerType.project_type:
      if (action.value !== null)
        return { ...state, project_type: action.value };
      else return state;

    case DataCookieReducerType.project_status:
      if (action.value !== null)
        return { ...state, project_status: action.value };
      else return state;

    case DataCookieReducerType.project_contacts:
      if (action.value !== null)
        return { ...state, project_contacts: action.value };
      else return state;

    case DataCookieReducerType.project_architects:
      if (action.value !== null)
        return { ...state, project_architects: action.value };
      else return state;

    case DataCookieReducerType.project_bu:
      if (action.value !== null) return { ...state, project_bu: action.value };
      else return state;

    case DataCookieReducerType.project_opportunity:
      if (action.value !== null)
        return { ...state, project_opportunity: action.value };
      else return state;

    default:
      return state;
  }
}
