import "moment/locale/pt-br";

import { Tooltip } from "antd";

import { InfoCircleOutlined } from "@ant-design/icons";

export const CustomerField = ({ value, openModal }) => {
    return (
      <div
        style={{ width: "100%", display: "flex", justifyContent: "space-evenly" }}
      >
        <span>{value.itsm_id || "-"}</span>
  
        {value.itsm_id ? (
          <div>
            <Tooltip title="Clique para ver mais informações">
              <InfoCircleOutlined onClick={() => openModal(value.id)} />
            </Tooltip>
          </div>
        ) : null}
      </div>
    );
  };