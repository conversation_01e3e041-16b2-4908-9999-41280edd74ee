import { <PERSON><PERSON>, <PERSON>, Col, Form, Input, Row, Select } from "antd";
import {
  DeleteOutlined,
  PlusOutlined,
  ArrowLeftOutlined,
  SaveOutlined,
} from "@ant-design/icons";
import { useState } from "react";
import { invalidInputNumberChars } from "../../constants/invalidCharsInputNumber";
import { realMask } from "../../utils/masks";
import { PriceInput } from "../ProposalsMoneyInput/proposalMoneyInput";

export const AdditionalCostsContent = ({ add_proposal }) => {
  const { Option } = Select;
  const [recognizingFormList, setRecognizingFormList] = useState(false);

  // console.log("{ add_proposal } ", add_proposal.getFieldsValue());

  const selectAfter = (
    <Select defaultValue="dolar">
      <Option value="dolar">$</Option>
      <Option value="reais">R$</Option>
    </Select>
  );

  return (
    <Form.List name="aditional_costs">
      {(fields, { add, remove }) => (
        <>
          {fields.map((field, index) => (
            <Form.Item required={false} key={field.key}>
              <Row gutter={24}>
                <Col span={24}>
                  <Card
                    style={{
                      backgroundColor: "#F6F6F6",
                      boxShadow: "0 0 5px rgba(0,0,0,0.1)",
                      borderRadius: "5px",
                      marginRight: "10px",
                    }}
                  >
                    <Row gutter={24}>
                      <Col span={24}>
                        <Form.Item
                          label="Título"
                          {...field}
                          name={[field.name, "title"]}
                          required={true}
                        >
                          <Input
                            onChange={() =>
                              setRecognizingFormList(!recognizingFormList)
                            }
                          />
                        </Form.Item>
                      </Col>
                      <Col lg={12} md={24}>
                        <Form.Item
                          label="Valor mensal"
                          {...field}
                          name={[field.name, "month_value"]}
                          required={true}
                        >
                          <PriceInput
                            index={index}
                            form={add_proposal}
                            formListName={"aditional_costs"}
                            fieldName={"month_value"}
                          />
                        </Form.Item>
                      </Col>
                      <Col lg={12} md={24}>
                        <Form.Item
                          label="Valor anual"
                          {...field}
                          name={[field.name, "year_value"]}
                          required={true}
                        >
                          <PriceInput
                            index={index}
                            formListName={"aditional_costs"}
                            form={add_proposal}
                            fieldName={"year_value"}
                          />
                        </Form.Item>
                      </Col>
                    </Row>
                    <Row justify="end">
                      <Col>
                        <Button
                          danger
                          type="text"
                          onClick={() => remove(field.name)}
                        >
                          Remover item
                          <DeleteOutlined />
                        </Button>
                      </Col>
                    </Row>
                  </Card>
                </Col>
              </Row>
            </Form.Item>
          ))}
          <Button type="text" onClick={() => add()}>
            Adicionar Custo extra
            <PlusOutlined />
          </Button>
        </>
      )}
    </Form.List>
  );
};
