import { useSelector, shallowEqual } from "react-redux";

import { Typography, Row, Col, Select, Tooltip } from "antd";

import {
  setUploadFilesState,
  setUploadReadyFilesState,
} from "../../../../../store/actions/files-action";

import { getCustomerNameOption } from "../../../../../utils/getCustomerNameOption";

export const CustomerStep = () => {
  const { Option } = Select;
  const { Text } = Typography;
  const customers = useSelector((state) => state.files.customers, shallowEqual);
  const customerName = useSelector(
    (state) => state.files.customerName,
    shallowEqual
  );

  const handleSelectCustomer = async (c) => {
    const customer = JSON.parse(c);
    setUploadFilesState({ field: "customerId", value: customer.id });
    setUploadFilesState({
      field: "customerName",
      value: getCustomerNameOption(customer),
    });
    setUploadFilesState({ field: "customerCNPJ", value: customer.cnpj });
    setUploadFilesState({
      field: "customerITSM",
      value: customer?.identifications?.itsm_id || "",
    });
    setUploadReadyFilesState(true);
  };

  return (
    <>
      <Row style={{ paddingBottom: "8px" }}>
        <Text>
          Selecione o{" "}
          <strong>
            Cliente
            <Tooltip title={"Campo obrigatório"}>
              <span style={{ color: "red" }}>*</span>
            </Tooltip>
          </strong>
        </Text>
      </Row>
      <Row align={"middle"} style={{ paddingBottom: "50px" }}>
        <Col span={24}>
          <Select
            showSearch
            onChange={(e) => {
              handleSelectCustomer(e);
            }}
            placeholder={"Selecione o cliente..."}
            value={customerName !== "" ? customerName : null}
            style={{ width: "100%" }}
            loading={customers.length === 0 ? true : false}
            optionFilterProp="children"
            filterOption={(input, option) =>
              option?.children
                ?.toLowerCase()
                .startsWith(input?.toLowerCase()) ||
              option?.children?.toLowerCase().includes(input?.toLowerCase())
            }
            filterSort={(optionA, optionB) =>
              optionA?.children
                ?.toLowerCase()
                .localeCompare(optionB?.children?.toLowerCase())
            }
          >
            {customers?.map((customer, key) => (
              <Option value={JSON.stringify(customer)} key={key}>
                {customer?.names?.fantasy_name || customer?.names?.name}
              </Option>
            ))}
          </Select>
        </Col>
      </Row>
    </>
  );
};
