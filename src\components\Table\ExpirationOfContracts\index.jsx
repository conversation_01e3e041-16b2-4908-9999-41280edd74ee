import React, { useState, useEffect, useMemo } from "react";
import moment from "moment";
import { dynamoGet, useDynamoGet } from "../../../service/apiDsmDynamo";
import { Table, Spin, Row, Typography, Col } from "antd";
import "./ExpirationOfContracts.css";
import { filterTableData } from "../../../utils/filterTableData";
import { SearchInput } from "../../SearchInput";
import { Counter } from "../../Counter";
import {
  getUpdatedContractHours,
  renderTotalHours,
} from "../../../controllers/cockpit/cockpitController";

function ExpirationOfContractsTable(props) {
  const { Text } = Typography;
  const [allContractTypes, setAllContractTypes] = useState([]);
  const [allCustomers, setAllCustomers] = useState([]);
  const [search, setSearch] = useState("");
  const [contractsWithChangedHours, setContractsWithChangedHours] =
    useState(false);
  const dsmCustomers = useDynamoGet(
    `${process.env.REACT_APP_STAGE}-customers`
  )?.data;

  useEffect(() => {
    const loadData = async () => {
      let contractsWithChangedHours = props.expireContracts.filter(
        (contract) => contract.hasChangedHours === 1
      );
      if (contractsWithChangedHours.length === 0)
        return setContractsWithChangedHours(false);
      let changedHoursData = await getUpdatedContractHours(
        contractsWithChangedHours
      );
      setContractsWithChangedHours(changedHoursData);
    };

    loadData();
  }, [props.expireContracts]);

  const columns = [
    {
      dataIndex: "end_date",
      key: "end_date",
      render: (end_date) => (
        <div
          className="priority"
          style={{ background: getPrioriyColor(end_date) }}
        ></div>
      ),
    },
    {
      title: "Data de vencimento",
      dataIndex: "end_date",
      key: "end_date",
      align: "center",
      render: (end_date) => (
        <Text>{moment(end_date).format("DD/MM/YYYY")}</Text>
      ),
    },
    {
      title: "Nome do contrato",
      dataIndex: "name",
      key: "name",
      render: (contract) => <Text>{contract}</Text>,
    },
    {
      title: "Cliente",
      dataIndex: "customer_id",
      key: "customer_id",
      align: "center",
      render: (customer, _) =>
        dsmCustomers?.length > 0 ? (
          <Text>{getCustomerName(_?.customer_id)}</Text>
        ) : (
          <Row justify="center">
            <Spin />
          </Row>
        ),
    },
    {
      title: "Tipo de contrato",
      dataIndex: "type_hours",
      key: "type_hours",
      align: "center",
      render: (type) => <Text>{type}</Text>,
    },
    {
      title: "Carteira",
      dataIndex: "document_id",
      key: "document_id",
      align: "center",
      render: (document) => <Text>{document}</Text>,
    },
    {
      title: "Total",
      dataIndex: "total_hours",
      key: "total_hours",
      align: "center",
      render: (total, data) =>
        renderTotalHours(total, data, contractsWithChangedHours),
    },
  ];

  let data = props.expireContracts;

  function getAllContractTypes() {
    dynamoGet("hml-contract-type")
      .then((res) => {
        setAllContractTypes(res);
      })
      .catch((error) => {
        console.log(error);
      });
  }

  function getContractTypeName(typeId) {
    let contractType = allContractTypes.filter((item) => typeId === item.type);
    return contractType.length === 0 ? "-" : contractType[0].name;
  }

  function getAllCustomer() {
    if (dsmCustomers?.length > 0) {
      setAllCustomers(dsmCustomers);
    }
  }

  function getCustomerName(customerId) {
    let customerName = "-";
    Object.entries(allCustomers).forEach((item) => {
      if (item[1]?.id === customerId) {
        customerName = item[1]?.names?.fantasy_name;
      }
    });
    return customerName;
  }

  function getPrioriyColor(endDate) {
    if (moment(endDate) <= moment().add("30", "days")) {
      return "#FF3C3C";
    } else if (moment(endDate) <= moment().add("60", "days")) {
      return "#F1F43F";
    } else if (moment(endDate) <= moment().add("90", "days")) {
      return "#74CF49";
    }
  }

  useEffect(() => {
    getAllContractTypes();
  }, []);

  useEffect(() => {
    if (dsmCustomers?.length > 0) {
      getAllCustomer();
    }
  }, [dsmCustomers]);

  const tableData = useMemo(() => {
    let filteredData = data || [];
    const searchFields = ["name", "customer_id", "type_hours"];
    filteredData = filterTableData({
      data,
      searchFields,
      search,
    });
    return filteredData;
  }, [data, search]);

  return (
    <Row justify="space-between" style={{ marginTop: "10px" }}>
      <Col>
        <SearchInput
          onChange={(value) => setSearch(value)}
          placeholder="Pesquisar"
        />
      </Col>
      <Col>
        <Counter tableData={tableData} />
      </Col>
      <Col span={24}>
        <Table
          columns={columns}
          dataSource={tableData}
          scroll={{ x: "100%" }}
        />
      </Col>
    </Row>
  );
}
export default ExpirationOfContractsTable;
