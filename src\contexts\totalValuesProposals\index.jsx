import { createContext, useReducer } from "react";
import { reducer } from "../reducers/totalValuesProposal";
import { totalValuesInitialState } from "../../constants/totalValuesInitialState";

const InitialState = {
  totalValues: totalValuesInitialState,
  month: "",
  greatestMonth: "12",
  spreadSetup: [
    {
      month: "12",
      value: 0,
    },
    {
      month: "24",
      value: 0,
    },
    {
      month: "36",
      value: 0,
    },
  ],
  fileNames: [],
  architectureFileNames: [],
  scenarioFileNames: [],
  idFixed: "",
  customer: {},
  oportunities: [],
  selectedOportunity: "",
  status: "",
  paymentMethod: "",
  pipedriveDealOwner: {},
};

export const ProposalContext = createContext({
  proposalState: InitialState,
  setProposalState: () => {},
});

export const ProposalProvider = ({ children }) => {
  const [state, dispatch] = useReducer(reducer, InitialState);

  return (
    <ProposalContext.Provider
      value={{ proposalState: state, setProposalState: dispatch }}
    >
      {children}
    </ProposalContext.Provider>
  );
};
