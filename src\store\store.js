import { combineReducers } from "redux";
import localStorage from "redux-persist/lib/storage";
import { persistReducer, persistStore } from "redux-persist";
import { configureStore } from "@reduxjs/toolkit";

import consumptionReducer from "./reducers/consumption-hours-reducer";
import globalPersistReducer from "./reducers/global-persist-reducer";
import technicalProposalReducer from "./reducers/technical-proposal-reduce";
import filesReducer from "./reducers/files-reducer";
import contractReducer from "./reducers/contract-reducer";
import customersReducer from "./reducers/customers-reducer";
import invoiceReducer from "./reducers/invoices-reducer";
import servicesReducer from "./reducers/services-reducer";
import switchRoleReduce from "./reducers/switch-roles-reducer";
import ticketReportReduce from "./reducers/tickets-report-reduce";
import billingReducer from "./reducers/billing-reducer";

const reducers = combineReducers({
  consumption: consumptionReducer,
  globalPersist: globalPersistReducer,
  technicalProposal: technicalProposalReducer,
  files: filesReducer,
  contract: contractReducer,
  customers: customersReducer,
  invoice: invoiceReducer,
  services: servicesReducer,
  switchRole: switchRoleReduce,
  ticketReport: ticketReportReduce,
  billing: billingReducer,
});

const persistConfig = {
  key: "root",
  storage: localStorage,
  whitelist: ["globalPersist"],
  // Exclude consumption and ticketReport to avoid serialization issues with moment objects
  blacklist: ["consumption", "ticketReport"],
};

const persistedReducer = persistReducer(persistConfig, reducers);

export const store = configureStore({
  reducer: persistedReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: [
          'persist/PERSIST',
          'persist/REHYDRATE',
          'persist/PAUSE',
          'persist/PURGE',
          'persist/REGISTER',
        ],
        // ✅ Aumentar threshold para reduzir warnings
        warnAfter: 128, // Era 32ms, agora 128ms
      },
    }), // ✅ Removido .concat(thunk) - já incluído por padrão
});

export const persistor = persistStore(store);
