import { Select, Col } from "antd";
import { setSelectedFileFilesState } from "../../../../../../store/actions/files-action";
import { useSelector, shallowEqual } from "react-redux";

export const CustomersSelect = ({ allCustomers }) => {
  const { Option } = Select;

  const selectedFile = useSelector(
    (state) => state.files.selectedFile,
    shallowEqual
  );

  const handleSelectCustomer = (value) => {
    const customer = allCustomers?.find(
      (c) => c?.names?.fantasy_name === value
    );

    if (customer === undefined) {
      const customerFantasyName = allCustomers?.find(
        (c) => c?.names?.name === value
      );

      setSelectedFileFilesState({
        field: "selectedFile",
        value: {
          ...selectedFile,
          customerId: customerFantasyName.id,
          customerName: customerFantasyName.names.name,
          customerCNPJ: customerFantasyName.cnpj,
        },
      });
    } else {
      setSelectedFileFilesState({
        field: "selectedFile",
        value: {
          ...selectedFile,
          customerId: customer.id,
          customerName: customer.names.fantasy_name,
          customerCNPJ: customer.cnpj,
        },
      });
    }
  };

  return (
    <Col span={24}>
      <Select
        showSearch
        placeholder="Selecione um cliente"
        optionFilterProp="children"
        defaultValue={selectedFile?.customerName || ""}
        filterOption={(input, option) =>
          option?.children?.toLowerCase().startsWith(input?.toLowerCase()) ||
          option?.children?.toLowerCase().includes(input?.toLowerCase())
        }
        filterSort={(optionA, optionB) =>
          optionA?.children
            ?.toLowerCase()
            .localeCompare(optionB?.children?.toLowerCase())
        }
        onSelect={handleSelectCustomer}
        style={{ width: "100%" }}
      >
        {allCustomers?.map((c) => {
          if (
            c?.names?.fantasy_name !== undefined ||
            c?.names?.fantasy_name !== ""
          ) {
            return (
              <Option
                value={c?.names?.fantasy_name ? c?.names?.fantasy_name : "N/A"}
              >
                {c?.names?.fantasy_name}
              </Option>
            );
          } else {
            return (
              <Option value={c?.names?.name ? c?.names?.name : "N/A"}>
                {c?.names?.name}
              </Option>
            );
          }
        })}
      </Select>
    </Col>
  );
};
