import { useState, useEffect } from 'react'
import { shallowEqual, useSelector } from "react-redux";
import { Col, Typography } from "antd";

import { Select } from "../../../../components/Select";

import { setConsumptionState } from "../../../../store/actions/consumption-hours-action";
import { dynamoGet } from "../../../../service/apiDsmDynamo";

const STATE = process.env.REACT_APP_STAGE

export const ContractTypeField = () => {
    const selectedContract = useSelector(state => state.consumption.selectedContract, shallowEqual)
    const contractTypes = useSelector(state => state.consumption.contractTypes, shallowEqual)

    const [loading, setLoading] = useState(false)

    useEffect(() => {
        let isMounted = true;

        const getTypes = async () => {
            try {
                if (!isMounted) return;

                setLoading(true);

                const response = await dynamoGet(`${STATE}-crm-custom-fields`);

                if (!isMounted) return;

                let labels = [];

                if (response.length > 0) {
                    const consumeTypes = response[0].deal.find(
                        (deal) => deal.name === "Tipo de Consumo"
                    );

                    if (consumeTypes && consumeTypes.options) {
                        labels = consumeTypes.options.map((option, index) => ({
                            value: index + 1,
                            label: option.label
                        }));
                    }
                }

                labels.sort((a, b) => a.label.localeCompare(b.label));
                labels.unshift({ value: 0, label: "Todos" });

                if (isMounted) {
                    setConsumptionState({
                        field: 'contractTypes',
                        value: labels
                    });
                    setLoading(false);
                }
            } catch (error) {
                if (isMounted) {
                    console.error('Error loading contract types:', error);
                    setLoading(false);
                }
            }
        };

        getTypes();

        return () => {
            isMounted = false;
        };
    }, []);

    return (
        <Col
            xl={{ span: 4 }}
            lg={{ span: 10 }}
            md={{ span: 6 }}
            sm={{ span: 10 }}
            style={{ marginBottom: 10 }}
        >
            <Typography.Text>Tipos de Contrato</Typography.Text>
            <Select
                onChange={(value) => setConsumptionState({ field: 'selectedContract', value })}
                options={contractTypes}
                value={selectedContract}
                style={{ width: "100%" }}
                loading={loading}
            />
        </Col>
    )
}