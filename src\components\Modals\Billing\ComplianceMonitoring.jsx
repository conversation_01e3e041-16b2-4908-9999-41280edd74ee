import { <PERSON><PERSON>, Col, Row, Modal, Typography } from "antd";
import { useState } from "react";
import {
  MonitorOutlined,
  CheckCircleTwoTone,
  CloseCircleTwoTone,
} from "@ant-design/icons";
import { useMemo } from "react";
import { ConfigBilling } from "./ConfigBilling";

function getCustomerName(customer) {
  let clientName = "";
  if (customer?.names) {
    if (customer?.names?.name) clientName = customer?.names?.name;
    else clientName = customer?.names?.fantasy_name;
  }

  if (customer?.name) clientName = customer?.name;

  return clientName;
}

export const ComplianceMonitoring = ({
  items,
  customer,
  contract,
  permissions,
}) => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const { Title, Text } = Typography;
  const [filteredItems, setFilteredItems] = useState([]);

  const showModal = async () => {
    setIsModalVisible(true);
    setFilteredItems([
      ...items
        .find((i) => i.customerId === customer.id)
        ?.items.filter(
          (i) =>
            i.name !== "Método de pagamento configurado para invoice" &&
            i.name !== "CNPJ configurado como 18.342.439/0001-68" &&
            i.name !== "MFA na conta root habilitado" &&
            i.name !== "Herança habilitada nas TaxSettings"
        ),
    ]);
  };

  const handleOk = () => {
    setIsModalVisible(false);
  };

  const IconMonitoring = useMemo(() => {
    const checklist = items.find((i) => i.customerId === customer.id);

    if (items) {
      if (checklist) {
        const filteredItems = checklist.items.filter(
          (i) =>
            i.name !== "Método de pagamento configurado para invoice" &&
            i.name !== "CNPJ configurado como 18.342.439/0001-68" &&
            i.name !== "MFA na conta root habilitado" &&
            i.name !== "Herança habilitada nas TaxSettings"
        );
        const includesFalse = filteredItems.some((item) => !item.status);

        if (includesFalse) {
          return (
            <Button
              onClick={showModal}
              style={{ backgroundColor: "#F44336", color: "white" }}
            >
              <MonitorOutlined style={{ fontSize: "1.2rem" }} />
            </Button>
          );
        } else {
          return (
            <Button
              onClick={showModal}
              // loading={loadingState}
              style={{ backgroundColor: "#00B050", color: "white" }}
            >
              <MonitorOutlined style={{ fontSize: "1.2rem" }} />
            </Button>
          );
        }
      }
    }
    return (
      <Button
        onClick={showModal}
        // loading={loadingState}
        style={{ backgroundColor: "#F44336", color: "white" }}
      >
        <MonitorOutlined style={{ fontSize: "1.2rem" }} />
      </Button>
    );
  }, [items]);

  return (
    <Row align="center">
      {IconMonitoring}
      <Modal
        title="Monitoramento de Compliance"
        open={isModalVisible}
        width={"70%"}
        closable={false}
        footer={[
          <Row key="compliance-footer" style={{ margin: "0.75rem" }}>
            <Col span={12} style={{ textAlign: "left" }}>
              {permissions?.data
                ?.map((permission) => {
                  return permission.code;
                })
                .includes("view_config_billing") && (
                <ConfigBilling contract={contract} />
              )}
            </Col>
            <Col span={12}>
              <Button type="primary" onClick={handleOk}>
                Ok
              </Button>
            </Col>
          </Row>,
        ]}
      >
        <Title level={5}>
          <Text>Cliente : </Text>
          {getCustomerName(customer)}
        </Title>
        <Title level={5}>
          <Text>Payer Account : </Text>
          {items.find((i) => i.customerId === customer.id)?.paAccountId}
        </Title>

        <Col span={24}>
          <Row justify="end" style={{ marginBottom: "1rem" }}>
            <Col span={6}>
              <Row justify="center">
                <Title level={5}>Status</Title>
              </Row>
            </Col>
          </Row>

          <Row>
            {items.find((i) => i.customerId === customer.id) !== undefined ? (
              filteredItems.map((i, index) => {
                return (
                  <Col span={24} key={index}>
                    <Row>
                      <Col span={18}>
                        {index % 2 === 0 ? (
                          <Row
                            style={{
                              paddingBottom: ".5rem",
                              paddingTop: ".5rem",
                              paddingLeft: "1rem",
                              backgroundColor: "#ECECEC",
                            }}
                          >
                            <Text>{i.name}</Text>
                          </Row>
                        ) : (
                          <Row
                            style={{
                              paddingBottom: ".5rem",
                              paddingTop: ".5rem",
                              paddingLeft: "1rem",
                            }}
                          >
                            <Text>{i.name}</Text>
                          </Row>
                        )}
                      </Col>
                      <Col span={6}>
                        {index % 2 === 0 ? (
                          <Row
                            justify="center"
                            style={{
                              paddingBottom: ".75rem",
                              paddingTop: ".75rem",
                              backgroundColor: "#ECECEC",
                            }}
                          >
                            {i.status ? (
                              <CheckCircleTwoTone twoToneColor="#52c41a" />
                            ) : (
                              <CloseCircleTwoTone twoToneColor="#eb2f96" />
                            )}
                          </Row>
                        ) : (
                          <Row
                            justify="center"
                            style={{
                              paddingBottom: ".75rem",
                              paddingTop: ".75rem",
                            }}
                          >
                            {i.status ? (
                              <CheckCircleTwoTone twoToneColor="#52c41a" />
                            ) : (
                              <CloseCircleTwoTone twoToneColor="#eb2f96" />
                            )}
                          </Row>
                        )}
                      </Col>
                    </Row>
                  </Col>
                );
              })
            ) : (
              <></>
            )}
          </Row>
        </Col>
      </Modal>
    </Row>
  );
};
