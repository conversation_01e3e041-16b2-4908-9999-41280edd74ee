export function formatConceededExportData(data) {
  return data.map((item) => {
    return {
      ticket_number: item.ticket,
      customer: item.customer,
      requester: item.requester,
      requested_date: item.requestDate,
      approver: item.approver,
      requested_time: item.requestedTime,
      conceded_time: item.conceededTime,
      concession_date: item.conceedDate,
      revoke_date: item.revokeDate !== "-" ? item.revokeDate : "-",
      requested_account: item.requestedAccount.toString(),
    };
  });
}
