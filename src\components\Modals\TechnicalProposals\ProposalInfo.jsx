import React from "react";
import { EyeOutlined, CheckOutlined } from "@ant-design/icons";
import { useState } from "react";
import { But<PERSON>, List, Modal, Row, Collapse, Typography, Select } from "antd";

export const ProposalInfo = ({ state }) => {
  let supportArray = [
    { title: "Nome", value: state[0]?.name },
    { title: "Tipo", value: state[0]?.type },
    {
      title: window.location.href.includes("commercial")
        ? "Status Comercial"
        : "Status",
      value: window.location.href.includes("commercial")
        ? state[0]?.commercialStatus
        : state[0]?.status,
    },
    { title: "Arquitetos", value: state[0]?.architects },
    { title: "Cliente", value: getCustomerName(state[0]) },
    { title: "CNPJ", value: state[0]?.customer.cnpj },
    { title: "Contatos", value: state[0]?.customer.contacts },
    { title: "Oportunidade", value: state[0]?.mainOportunity },
    // { title: "Desafios", value: state[0]?.specialFields[0]?.value },
    // { title: "Cenário apresentado", value: state[0]?.specialFields[1]?.value },
    // { title: "Premissas", value: state[0]?.specialFields[2]?.value },
    // {
    //   title: "Pontos não contemplados",
    //   value: state[0]?.specialFields[3]?.value,
    // },
    // { title: "Arquitetura", value: state[0]?.specialFields[4]?.value },
    // {
    //   title: "Fatores influenciadores",
    //   value: state[0]?.specialFields[5]?.value,
    // },
    // { title: "Resultados esperados", value: state[0]?.specialFields[7]?.value },
    // { title: "Observações", value: state[0]?.specialFields[8]?.value },
    // { title: "Notas internas", value: state[0]?.specialFields[9]?.value },
  ];
  const { Text, Title } = Typography;
  const { Panel } = Collapse;
  const { Option } = Select;
  const [showModal, setShowModal] = useState();

  const handleOk = () => {
    setShowModal(false);
  };

  const handleModalOpen = () => {
    setShowModal(true);
  };

  return (
    <>
      <Button type="text" icon={<EyeOutlined />} onClick={handleModalOpen} />
      <Modal
        title="Informações da proposta"
        open={showModal}
        closable={false}
        footer={null}
        width={"60%"}
      >
        <List
          dataSource={supportArray}
          rowKey={(item) => item?.title || Math.random()}
          renderItem={(item) => (
            <List.Item>
              <List.Item.Meta
                title={item?.title}
                description={
                  typeof item?.value === "string" ? (
                    <div
                      dangerouslySetInnerHTML={{ __html: item?.value }}
                    ></div>
                  ) : (
                    item?.value?.map((currentItem, index) =>
                      item?.title === "Arquitetos" ? (
                        <React.Fragment key={`architect-${index}`}>
                          {currentItem}
                          <br />
                        </React.Fragment>
                      ) : (
                        <React.Fragment key={`email-${index}`}>
                          {currentItem.email}
                          <br />
                        </React.Fragment>
                      )
                    )
                  )
                }
              />
            </List.Item>
          )}
        ></List>
        <Row justify="end">
          <Button type="primary" onClick={handleOk}>
            Ok <CheckOutlined />
          </Button>
        </Row>
      </Modal>
    </>
  );
};

function getCustomerName(item) {
  let clientName = "";

  if (item.customer.names) {
    if (item.customer.names.name) clientName = item.customer.names.name;
    else clientName = item.customer.names.fantasy_name;
  }

  if (item.customer.name) clientName = item.customer.name;

  return clientName;
}
