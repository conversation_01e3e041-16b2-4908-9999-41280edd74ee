import React, { Suspense, startTransition, useEffect, useState } from 'react';
import { Spin } from 'antd';

/**
 * LazyRoute Component - React 18 Compatible
 * Provides a loading fallback for lazy-loaded components with startTransition
 */
const LazyRoute = ({ children, fallback = null }) => {
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Use startTransition para evitar suspense síncrono
    startTransition(() => {
      setIsLoading(false);
    });
  }, []);

  const defaultFallback = (
    <div
      style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        flexDirection: 'column',
        gap: '16px'
      }}
    >
      <Spin size="large" />
      <div>Carregando...</div>
    </div>
  );

  // Se ainda está carregando, mostra fallback
  if (isLoading) {
    return fallback || defaultFallback;
  }

  return (
    <Suspense fallback={fallback || defaultFallback}>
      {children}
    </Suspense>
  );
};

export default LazyRoute;
