import axios from "axios";
import { logger } from "../utils/logger";

export const otrsCalculateHoursProvider = () => {
  const instance = axios.create({
    baseURL: process.env.REACT_APP_API_OTRS_URL,
    timeout: 30000, // ✅ Adicionado timeout
    headers: {
      Authorization: process.env.REACT_APP_TOKEN_NEW,
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    },
  });

  // ✅ Interceptor para logging
  instance.interceptors.request.use(
    (config) => {
      logger.debug('OTRS Calculate Hours Request:', {
        method: config.method?.toUpperCase(),
        url: config.url,
        baseURL: config.baseURL
      });
      return config;
    },
    (error) => {
      logger.error('OTRS Calculate Hours Request Error:', error);
      return Promise.reject(error);
    }
  );

  instance.interceptors.response.use(
    (response) => {
      logger.debug('OTRS Calculate Hours Response:', {
        status: response.status,
        url: response.config?.url
      });
      return response;
    },
    (error) => {
      logger.error('OTRS Calculate Hours Response Error:', {
        status: error.response?.status,
        url: error.config?.url,
        message: error.message
      });
      return Promise.reject(error);
    }
  );

  return instance;
};

export function updateConsumptionHourContract({ contractID }) {
  const provider = otrsCalculateHoursProvider();

  console.log("Atualizando consumo de horas");

  const payload = { contractID };

  if (contractID) {
    setTimeout(() => {
      provider
        .post("/reports/surplus/all", payload)
        .then((response) => {
          console.log("Atualizando consumo de horas");
        })
        .catch((error) => {
          console.log(
            "Não foi possivel atualizar o consumo de horas deste contrato"
          );
        });
    }, 3000);
  }
}
