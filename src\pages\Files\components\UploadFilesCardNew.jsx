import { Card, Row, Typography } from "antd";
import { UploadFilesModal } from "./Modals/UploadFilesModal";
const { Title } = Typography;

export const UploadFilesCardNew = () => {
  return (
    <Card
      style={{
        boxShadow: "0 0 10px rgba(0,0,0,0.1)",
        borderRadius: "20px",
        height: "120px",
      }}
    >
      <div
        style={{
          display: "flex",
          alignItems: "center",
          flexDirection: "column",
          justifyContent: "center",
        }}
      >
        <Row style={{ justifyContent: "center" }}>
          <Title level={4}>Carregue um novo arquivo</Title>
        </Row>
        <Row style={{ justifyContent: "center" }}>
          <UploadFilesModal />
        </Row>
      </div>
    </Card>
  );
};
