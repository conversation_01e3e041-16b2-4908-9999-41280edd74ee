import { EditOutlined, DeleteOutlined } from "@ant-design/icons";
import { useState } from "react";
import { Button, message, Modal, Popconfirm, Table, Tag } from "antd";
// import v4 from "uuid/v4";
import { dynamoPut } from "../../../service/apiDsmDynamo";
import { logNewAuditAction } from "../../../controllers/audit/logNewAuditAction";

export const EditFunction = ({ permission, item, mutate }) => {
  const [showModal, setShowModal] = useState();
  const [loading, setLoading] = useState(false);

  // 🔍 Debug: Verificar estrutura dos dados recebidos
  console.log('🔍 EditFunction: Props recebidas:', {
    hasPermission: !!permission,
    permissionId: permission?.id,
    permissionType: typeof permission?.id,
    hasItem: !!item,
    itemId: item?.id
  });

  const handleDelete = async (props) => {
    const code = props.code;
    setLoading(true);
    await dynamoPut(
      `${process.env.REACT_APP_STAGE}-page-actions`,
      permission.id,
      {
        all: permission.all.map((p) => {
          if (p.page === item.page) {
            return {
              ...p,
              permissions: p.permissions.filter((p) => p.code !== code),
            };
          }

          return p;
        }),
      }
    );

    mutate(
      {
        all: permission.all.map((p) => {
          if (p.page === item.page) {
            return {
              ...p,
              permissions: p.permissions.filter((p) => p.code !== code),
            };
          }

          return p;
        }),
      },
      false
    );

    const username = localStorage.getItem("@dsm/username");
    const title = "Remoção de Funcionalidades";
    const description = `${username} removeu a funcionalidade ${props.action} da página ${item.page}`;
    logNewAuditAction(username, title, description);

    message.success("Função removida da página com sucesso!");
    setLoading(false);
  };

  const columns = [
    {
      title: "Ações na página",
      dataIndex: "action",
      key: "action",
    },
    {
      title: "Código",
      dataIndex: "code",
      key: "code",
      render: (id, props) => {
        return (
          <Tag color="green" key={id}>
            {props.code}
          </Tag>
        );
      },
    },
    {
      title: "Remover",
      dataIndex: "delete",
      key: "delete",
      width: "1%",
      align: "center",
      render: (id, props) => {
        return (
          <Popconfirm
            title="Tem certeza de que quer remover essa ação?"
            onConfirm={() => {
              handleDelete(props);
            }}
            onCancel={() => {}}
            okText="Sim"
            cancelText="Não"
          >
            <Button type="text" danger icon={<DeleteOutlined />} />
          </Popconfirm>
        );
      },
    },
  ];

  const handleModalOpen = () => {
    setShowModal(true);
  };

  return (
    <>
      <Button
        icon={<EditOutlined />}
        type="text"
        onClick={handleModalOpen}
      ></Button>
      <Modal
        title="Visualização de funções"
        open={showModal}
        closable={false}
        onOk={() => setShowModal(false)}
        okText="Ok"
        cancelText="Cancelar"
        onCancel={() => setShowModal(false)}
      >
        {
          <Table
            pagination={true}
            loading={loading}
            dataSource={item?.permissions}
            columns={columns}
            scroll={{ x: 100 }}
          ></Table>
        }
      </Modal>
    </>
  );
};
