import { useState, useMemo, useEffect, useContext } from "react";
import "moment/locale/pt-br";

import { Row, Typography, Table, Tag, Tooltip } from "antd";

import { CustomerInfoModal } from "../../../../components/Modals/SuruplusHours/CustomerInfoModal";

import { CustomerField } from "./customer-field";
import { useToggle } from "../../../../hooks/useToggle";
import { useDynamoGet } from "../../../../service/apiDsmDynamo";

import { realMask } from "../../../../utils/masks";

import * as controller from "../../../../controllers/reports/surplus-hours-controller";
import { shallowEqual, useSelector } from "react-redux";
import { setConsumptionState } from "../../../../store/actions/consumption-hours-action";
import { DynamicTable } from "../../../../components/Table/DynamicTable";
import { TableCell } from "./table-cell";

export const TableFilter = ({ loading }) => {
  const search = useSelector((state) => state.consumption.search, shallowEqual);
  const contracts = useSelector(
    (state) => state.consumption.contracts,
    shallowEqual
  );
  const selectedSquads = useSelector(
    (state) => state.consumption.selectedSquads,
    shallowEqual
  );
  const pageSelected = useSelector(
    (state) => state.consumption.pageSelected,
    shallowEqual
  );
  const pageSize = useSelector(
    (state) => state.consumption.pageSize,
    shallowEqual
  );

  const selectedStatus = useSelector(
    (state) => state.consumption.selectedStatus,
    shallowEqual
  );
  const contractTypes = useSelector(
    (state) => state.consumption.contractTypes,
    shallowEqual
  );
  const selectedContract = useSelector(
    (state) => state.consumption.selectedContract,
    shallowEqual
  );
  const selectedHourType = useSelector(
    (state) => state.consumption.selectedHourType,
    shallowEqual
  );
  const collumnsToShow = useSelector(
    (state) => state.globalPersist.consumption.collumnsToShow,
    shallowEqual
  );

  const [customerIDSelect, setCustomerIDSelect] = useState(null);

  const [isOpen, toggle] = useToggle();
  const customers = useDynamoGet(`${process.env.REACT_APP_STAGE}-customers`);

  const allCollumns = [
    {
      title: "DSM ID",
      dataIndex: "dsm_id",
      key: "dsm_id",
      align: "center",
      width: 80,
      sorter: (a, b) => a.dsm_id.localeCompare(b.dsm_id),
    },
    {
      title: "Contrato",
      dataIndex: "name",
      key: "name",
      width: 250,
      sorter: (a, b) => a.name.toString().localeCompare(b.name.toString()),
      render: (name) => (
        <p style={{ width: "250px", fontSize: "14px" }}>{name}</p>
      ),
    },
    {
      title: "Tipo",
      dataIndex: "type_hours",
      key: "type_hours",
      width: 100,
      sorter: (a, b) =>
        a.type_hours.toString().localeCompare(b.type_hours.toString()),
      align: "center",
    },
    {
      title: "Mês",
      dataIndex: "period",
      key: "period",
      width: 100,
      sorter: (a, b) => a.period.toString().localeCompare(b.period.toString()),
      align: "center",
    },
    {
      title: "Cliente",
      dataIndex: "customer",
      key: "customer",
      align: "center",
      width: 100,
      render: (customer) => (
        <CustomerField
          value={getItsmCustomer(customer)}
          openModal={openModal}
        />
      ),
    },
    {
      title: "Status",
      dataIndex: "active",
      key: "active",
      align: "center",
      width: 100,
      sorter: (a, b) => {
        const activeA = a.active === 1 ? "Ativo" : "Inativo";
        const activeB = b.active === 1 ? "Ativo" : "Inativo";

        return activeA.toString().localeCompare(activeB.toString());
      },
      render: (active) => {
        if (active === 1) {
          return <Tag color="green">Ativo</Tag>;
        } else {
          return <Tag color="red">Inativo</Tag>;
        }
      },
    },
    {
      title: "Squad",
      dataIndex: "squad",
      key: "squad",
      align: "center",
      width: 100,
      sorter: (a, b) => {
        const squadA = a.squad || "-";
        const squadB = b.squad || "-";

        return squadA.toString().localeCompare(squadB.toString());
      },
      render: (squad) => <label>{squad || "-"}</label>,
    },
    {
      title: "Horas Contratadas",
      dataIndex: "",
      key: "",
      align: "center",
      width: 80,
      sorter: (a, b) => {
        const totalHoursInMinutesA = controller.formactMinutosToHours(
          a.totalHoursInMinutes
        );
        const totalHoursInMinutesB = controller.formactMinutosToHours(
          b.totalHoursInMinutes
        );

        return totalHoursInMinutesA
          .toString()
          .localeCompare(totalHoursInMinutesB.toString());
      },
      render: (row, data) => (
        <TableCell total={row.totalHoursInMinutes} data={data} />
      ),
    },
    {
      title: "Horas Expiradas",
      dataIndex: "",
      key: "",
      align: "center",
      width: 80,
      sorter: (a, b) => {
        const expiredHoursA = a.expiredHours || "0";
        const expiredHoursB = b.expiredHours || "0";

        return expiredHoursA.toString().localeCompare(expiredHoursB.toString());
      },
      render: (row) => (
        <p style={{ width: "80px", fontSize: "14px" }}>
          {row.expirePeriod && row.expiredHours > 0 ? (
            <Tooltip
              title={`Hora expiradas referente ao mês ${row.expirePeriod}`}
            >
              <Typography.Text>
                {controller.formactMinutosToHours(row.expiredHours)}
              </Typography.Text>
            </Tooltip>
          ) : (
            <Typography.Text>
              {controller.formactMinutosToHours(row.expiredHours)}
            </Typography.Text>
          )}
        </p>
      ),
    },
    {
      title: "Horas Consumidas",
      dataIndex: "",
      key: "",
      align: "center",
      width: 80,
      sorter: (a, b) => {
        const consumptionTotalMinutesA = controller.formactMinutosToHours(
          a.consumptionTotalMinutes
        );
        const consumptionTotalMinutesB = controller.formactMinutosToHours(
          b.consumptionTotalMinutes
        );

        return consumptionTotalMinutesA
          .toString()
          .localeCompare(consumptionTotalMinutesB.toString());
      },
      render: (row) => (
        <p style={{ width: "80px", fontSize: "14px" }}>
          <Typography.Text>
            {controller.formactMinutosToHours(
              row.consumptionTotalHours * 60 || row.consumptionTotalMinutes
            )}
          </Typography.Text>
        </p>
      ),
    },
    {
      title: "Horas Acumuladas",
      dataIndex: "",
      key: "",
      align: "center",
      width: 80,
      sorter: (a, b) => {
        const accumulatedHoursMinutesA = controller.formactMinutosToHours(
          a.accumulatedHoursMinutes
        );
        const accumulatedHoursMinutesB = controller.formactMinutosToHours(
          b.accumulatedHoursMinutes
        );

        return accumulatedHoursMinutesA
          .toString()
          .localeCompare(accumulatedHoursMinutesB.toString());
      },
      render: (row) => (
        <p style={{ width: "80px", fontSize: "14px" }}>
          <Typography.Text>
            {controller.formactMinutosToHours(row.accumulatedHoursMinutes)}
          </Typography.Text>
        </p>
      ),
    },
    {
      title: "Saldo",
      dataIndex: "",
      key: "",
      align: "center",
      width: 80,
      sorter: (a, b) => {
        const finalBalanceHoursA = controller.formactMinutosToHours(
          a.finalBalanceHours
        );
        const finalBalanceHoursB = controller.formactMinutosToHours(
          b.finalBalanceHours
        );

        return finalBalanceHoursA
          .toString()
          .localeCompare(finalBalanceHoursB.toString());
      },
      render: (row) => (
        <p style={{ width: "80px", fontSize: "14px" }}>
          <Typography.Text type={row.finalBalanceHours && "danger"}>
            {controller.formactMinutosToHours(row.finalBalanceHours)}
          </Typography.Text>
        </p>
      ),
    },
    {
      title: "Horas Excedentes",
      dataIndex: "",
      key: "",
      align: "center",
      width: 80,
      sorter: (a, b) => {
        const surplusHoursMinutesA = controller.formactMinutosToHours(
          a.surplusHoursMinutes
        );
        const surplusHoursMinutesB = controller.formactMinutosToHours(
          b.surplusHoursMinutes
        );

        return surplusHoursMinutesA
          .toString()
          .localeCompare(surplusHoursMinutesB.toString());
      },
      render: (row) => (
        <p style={{ width: "80px", fontSize: "14px" }}>
          <Typography.Text type="danger">
            {controller.formactMinutosToHours(row.surplusHoursMinutes)}
          </Typography.Text>
        </p>
      ),
    },
    {
      title: "Valor Hora",
      dataIndex: "excess_cost",
      key: "excess_cost",
      align: "center",
      width: 80,
      sorter: (a, b) => {
        const excess_costA = a.excess_cost || "0";
        const excess_costB = b.excess_cost || "0";

        return excess_costA.toString().localeCompare(excess_costB.toString());
      },
      render: (excess_cost) => (
        <p style={{ width: "80px", fontSize: "14px" }}>
          <MoneyField value={excess_cost || "0"} />
        </p>
      ),
    },
    {
      title: "Total Excedente",
      dataIndex: "surplusValue",
      key: "surplusValue",
      align: "center",
      width: 80,
      sorter: (a, b) => {
        const surplusValueA = a.surplusValue || "0";
        const surplusValueB = b.surplusValue || "0";

        return surplusValueA.toString().localeCompare(surplusValueB.toString());
      },
      render: (surplusValue) => (
        <p style={{ width: "80px", fontSize: "14px" }}>
          <MoneyField value={surplusValue} />
        </p>
      ),
    },
    {
      title: "Consumo Médio",
      dataIndex: "averageConsumption",
      key: "averageConsumption",
      align: "center",
      width: 80,
      sorter: (a, b) => {
        const averageConsumptionA = a.averageConsumption || "0";
        const averageConsumptionB = b.averageConsumption || "0";

        return averageConsumptionA
          .toString()
          .localeCompare(averageConsumptionB.toString());
      },
      render: (averageConsumption) => (
        <p style={{ width: "80px", fontSize: "14px" }}>
          <Typography.Text>
            {controller.formactMinutosToHours(averageConsumption)}
          </Typography.Text>
        </p>
      ),
    },
    {
      title: "Total Horas Expiradas",
      dataIndex: "totalExpiredHours",
      key: "totalExpiredHours",
      align: "center",
      width: 80,
      sorter: (a, b) => {
        const totalExpiredHoursA = a.totalExpiredHours || "0";
        const totalExpiredHoursB = b.totalExpiredHours || "0";

        return totalExpiredHoursA
          .toString()
          .localeCompare(totalExpiredHoursB.toString());
      },
      render: (totalExpiredHours) => (
        <p style={{ width: "80px", fontSize: "14px" }}>
          <Typography.Text>
            {controller.formactMinutosToHours(totalExpiredHours)}
          </Typography.Text>
        </p>
      ),
    },
  ];

  const contractsFilteredBySquads = useMemo(() => {
    const squadList = selectedSquads.map((squad) => squad.label);

    if (squadList.includes("Todos") || squadList.length === 0) return contracts;

    const filteredContracts = contracts.filter((contract) =>
      squadList.includes(contract.squad)
    );

    return filteredContracts;
  }, [selectedSquads, contracts]);

  const contractsFilteredByHourType = useMemo(() => {
    let filteredContracts = contractsFilteredBySquads;

    if (selectedHourType === 1)
      filteredContracts = contractsFilteredBySquads.filter(
        (contract) => contract.surplusHoursMinutes > 0
      );
    else if (selectedHourType === 2)
      filteredContracts = contractsFilteredBySquads.filter(
        (contract) => contract.surplusHoursMinutes <= 0
      );

    return filteredContracts;
  }, [selectedHourType, contractsFilteredBySquads]);

  const contractsFilteredByType = useMemo(() => {
    let filteredContracts = contractsFilteredByHourType;

    const contractType = contractTypes.find(
      (c) => c.value === selectedContract
    );

    if (contractType.label !== "Todos")
      filteredContracts = contractsFilteredByHourType.filter(
        (contract) =>
          contract.type_hours.toUpperCase() === contractType.label.toUpperCase()
      );

    return filteredContracts;
  }, [contractTypes, selectedContract, contractsFilteredByHourType]);

  const contractsFilteredByStatus = useMemo(() => {
    let filteredContracts = contractsFilteredByType;
    if (selectedStatus === 1)
      filteredContracts = contractsFilteredByType.filter(
        (contract) => contract.active === 1
      );
    else if (selectedStatus === 2)
      filteredContracts = contractsFilteredByType.filter(
        (contract) => contract.active === 0
      );

    return filteredContracts;
  }, [selectedStatus, contractsFilteredByType]);

  const contractsFilteredBySearchInput = useMemo(() => {
    return contractsFilteredByStatus.filter((contract) =>
      controller.filterContracts(search, contract)
    );
  }, [search, contractsFilteredByStatus]);

  const columns = useMemo(() => {
    if (collumnsToShow.length > 0)
      return allCollumns.filter((collumn) =>
        collumnsToShow.includes(collumn.title)
      );

    return allCollumns;
  }, [
    collumnsToShow,
    contracts,
    selectedSquads,
    pageSelected,
    search,
    selectedStatus,
    contractTypes,
    selectedContract,
    selectedHourType,
    customers,
    allCollumns,
    pageSize,
  ]);

  function closeModal() {
    toggle();
    setCustomerIDSelect(null);
  }

  function openModal(customerID) {
    setCustomerIDSelect(customerID);
    toggle();
  }

  function getItsmCustomer(customer) {
    const { data } = customers;
    let newCustomer = null;

    if (data) data.find((c) => c.id === customer.id);

    if (newCustomer) {
      return {
        ...newCustomer.identifications,
        id: newCustomer.id,
      };
    }

    return customer;
  }

  useEffect(() => {
    setConsumptionState({
      field: "filteredContracts",
      value: contractsFilteredBySearchInput,
    });
  }, [contractsFilteredBySearchInput]);

  return (
    <Row>
      <DynamicTable
        data={contractsFilteredBySearchInput}
        columns={columns}
        pagination={{
          current: pageSelected,
          onShowSizeChange: (current, pageSize) => {
            setConsumptionState({
              field: "pageSize",
              value: pageSize,
            });
          },
          onChange: (page) => {
            setConsumptionState({
              field: "pageSelected",
              value: page,
            });
          },
        }}
        loading={loading}
        scroll={{ x: 2000 }}
        title={() =>
          contractsFilteredBySearchInput.length > 0 ? (
            <Typography style={{ textAlign: "right" }}>
              {contractsFilteredBySearchInput.length} registros encontrados
            </Typography>
          ) : null
        }
      />

      {isOpen ? (
        <CustomerInfoModal
          isOpen={isOpen}
          toggle={closeModal}
          customerID={customerIDSelect}
        />
      ) : null}
    </Row>
  );
};

const MoneyField = ({ value }) => {
  return (
    <div
      style={{
        width: "100%",
        display: "flex",
        justifyContent: "space-between",
      }}
    >
      <div>R$</div>
      <div>{realMask(Number(value).toFixed(2))}</div>
    </div>
  );
};
