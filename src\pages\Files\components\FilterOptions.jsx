import { Col, Select, Typography, Row } from "antd";
import { setFilterStateFilesState } from "../../../store/actions/files-action";

export const FilterOptions = () => {
  const { Text } = Typography;
  const { Option } = Select;

  return (
    <>
      <Row align={"middle"}>
        <Col span={24}>
          <Row>
            <Col
              span={8}
              style={{
                justifyContent: "center",
                alignItems: "center",
                display: "flex",
              }}
            >
              <Row>
                <Text>Categoria: </Text>
              </Row>
            </Col>
            <Col span={16}>
              <Select
                onChange={(e) => {
                  setFilterStateFilesState({
                    field: "filterState",
                    value: e,
                  });
                }}
                defaultValue="Todas"
                style={{ width: "100%" }}
              >
                <Option value="Todas">Todas</Option>
                <Option value="Incident Management">Incident Management</Option>
                <Option value="Non-service affecting incidents">
                  Non-service affecting incidents
                </Option>
                <Option value="Performance analysis">
                  Performance analysis
                </Option>
                <Option value="Assets/resources">Assets/resources</Option>
                <Option value="Exceptions">Exceptions</Option>
              </Select>
            </Col>
          </Row>
        </Col>
      </Row>
    </>
  );
};
