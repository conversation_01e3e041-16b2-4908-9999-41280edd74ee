import {
  DeleteOutlined,
  PlusOutlined,
  SolutionOutlined,
  CheckOutlined,
} from "@ant-design/icons";
import { useEffect, useState, useContext } from "react";
import {
  Button,
  Modal,
  Row,
  Col,
  Table,
  Input,
  Typography,
  Form,
  Popconfirm,
  message,
} from "antd";
import { onlyNumber } from "../../../utils/masks";
import {
  ProposalProvider,
  ProposalContext,
} from "../../../contexts/totalValuesProposals";
import { TotalValuesProposalType } from "../../../contexts/reducers/totalValuesProposal";

export const AddOpportunity = ({ oportunitiesParent, setOportunities }) => {
  const { Title } = Typography;
  const [oportunityForm] = Form.useForm();
  const [showModal, setShowModal] = useState();
  const [childOportunities, setChildOportunities] = useState([]);
  const [currentOportunity, setCurrentOportunity] = useState("");
  const { proposalState, setProposalState } = useContext(ProposalContext);

  const handleSubmit = (e) => {
    e.preventDefault();
    setOportunities(childOportunities);
    setProposalState({
      type: TotalValuesProposalType.SET_OPORTUNITIES,
      value: childOportunities,
    });
    setShowModal(false);
  };

  const handleRemoveItem = (props) => {
    const prev = childOportunities;
    message.success(`Oportunidade "${props.name}" removida com sucesso!`);
    setChildOportunities(
      prev.filter((oportunity) => oportunity !== props.name)
    );
  };

  const handleCloseModal = () => {
    setCurrentOportunity("");
    oportunityForm.resetFields();
    setShowModal(false);
  };

  const handleAddItem = () => {
    if (
      childOportunities.find((oportunity) => oportunity === currentOportunity)
    ) {
      message.error(`Oportunidade "${currentOportunity}" já existe!`);
    } else {
      message.success(
        `Oportunidade "${currentOportunity}" adicionada com sucesso!`
      );
      // let prev = childOportunities[0]
      setChildOportunities((prev) => [...prev, currentOportunity]);
      setCurrentOportunity("");
      oportunityForm.resetFields(["oportunity"]);
    }
  };

  const columns = [
    {
      title: "Deal",
      dataIndex: "name",
      key: "name",
      sorter: (a, b) => a?.toString().localeCompare(b?.toString()),
    },
    {
      title: "Remover",
      dataIndex: "remove",
      key: "remove",
      align: "center",
      render: (id, props) => {
        return (
          <Popconfirm
            title={"Tem certeza que deseja remover esta oportunidade?"}
            okText={"Sim"}
            cancelText={"Não"}
            onConfirm={(e) => {
              handleRemoveItem(props);
            }}
          >
            <Button danger type="text" icon={<DeleteOutlined />}></Button>
          </Popconfirm>
        );
      },
    },
  ];

  useEffect(() => {    
    oportunityForm.resetFields(["oportunity"]);
    setChildOportunities(oportunitiesParent);
  }, [oportunitiesParent]);

  return (
    <>
      <Button
        type="text"
        onClick={() => {
          setChildOportunities(oportunitiesParent);
          setShowModal(true);
        }}
      >
        <PlusOutlined />
      </Button>
      <Modal
        title="Oportunidades"
        open={showModal}
        width={"40%"}
        closable={false}
        destroyOnClose={true}
        footer={null}
        onCancel={() => {
          setShowModal(false);
          setOportunities(childOportunities);
        }}
      >
        <Form layout="horizontal" form={oportunityForm} onFinish={handleSubmit}>
          <Row justify="center" gutter={[16, 16]}>
            <Col span={24}>
              <Title level={5} style={{ fontWeight: "400", fontSize: "14px" }}>
                Oportunidade
              </Title>
              <Input
                placeholder="Digite o número da oportunidade (Ex.: 337)"
                required
                style={{ marginBottom: "16px" }}
                value={currentOportunity}
                onChange={(e) => {
                  setCurrentOportunity(onlyNumber(e.target.value));
                }}
              />
              <Row justify="center">
                <Form.Item>
                  <Button
                    type="primary"
                    icon={<PlusOutlined />}
                    onClick={(e) => {
                      handleAddItem(e.target);
                    }}
                    disabled={!currentOportunity}
                  >
                    Adicionar
                  </Button>
                </Form.Item>
              </Row>
            </Col>
          </Row>
        </Form>

        <Row style={{ marginBottom: "1rem" }}>
          <Col span={24}>
            {childOportunities?.length > 0 ? (
              <Table
                rowKey="key"
                dataSource={childOportunities.map((item, index) => {
                  // if (item.length > 0) {
                  return {
                    key: index,
                    name: item,
                    // };
                  };
                })}
                columns={columns}
                scroll={{ x: 100 }}
                pagination={true}
              ></Table>
            ) : (
              <Row justify="center" gutter={[24, 16]}>
                <Col span={24}>
                  <Row justify="center">
                    <SolutionOutlined style={{ fontSize: "5rem" }} />
                  </Row>
                  <Row justify="center">
                    <Title level={5} style={{ fontWeight: "400" }}>
                      Não existem outras oportunidades selecionadas
                    </Title>
                  </Row>
                </Col>
              </Row>
            )}
          </Col>
        </Row>
        <Row gutter={[16, 16]} justify={"space-between"}>
          <Button type="text-color" onClick={handleCloseModal}>
            Cancelar <DeleteOutlined />
          </Button>
          <Button type="primary" onClick={handleSubmit}>
            Salvar <CheckOutlined />
          </Button>
        </Row>
      </Modal>
    </>
  );
};
