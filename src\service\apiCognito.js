import Axios from "axios";
import useSWR from "swr";
import { authService } from "../services/authService";

export function useCognitoGet() {
  const { data, error, mutate } = useSWR(
    process.env.REACT_APP_API_PERMISSION + "/cognito/read",
    async () => {
      const { data } = await Axios.get(
        process.env.REACT_APP_API_PERMISSION + "/cognito/read",
        {
          headers: authService.getAuthHeaders(),
        }
      );

      return data.data;
    }
  );

  return { data, error, mutate };
}

export const cognitoGet = async () => {
  const { data } = await Axios.get(
    process.env.REACT_APP_API_PERMISSION + "/cognito/read",
    {
      headers: authService.getAuthHeaders(),
    }
  );

  return data;
};

export const cognitoPutStatus = async (body) => {
  const { data } = await Axios.put(
    process.env.REACT_APP_API_PERMISSION + "/cognito/update/status",
    { ...body },
    {
      headers: authService.getLegacyAuthHeaders(),
    }
  );

  return data;
};

export const cognitoPutRole = async (body) => {
  const { data } = await Axios.put(
    process.env.REACT_APP_API_PERMISSION + "/cognito/update/role",
    { ...body },
    {
      headers: authService.getLegacyAuthHeaders(),
    }
  );

  return data;
};
