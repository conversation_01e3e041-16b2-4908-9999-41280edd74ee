import '@testing-library/jest-dom';
import React from 'react';

global.ResizeObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

global.IntersectionObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(), 
    removeListener: jest.fn(), 
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
global.localStorage = localStorageMock;

const sessionStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
global.sessionStorage = sessionStorageMock;

global.fetch = jest.fn();

const originalError = console.error;
beforeAll(() => {
  console.error = (...args) => {
    if (
      typeof args[0] === 'string' &&
      args[0].includes('Warning: ReactDOM.render is deprecated')
    ) {
      return;
    }
    originalError.call(console, ...args);
  };
});

afterAll(() => {
  console.error = originalError;
});

jest.mock('antd/lib/config-provider/context', () => ({
  ConfigContext: React.createContext({}),
  ConfigConsumer: ({ children }) => children({}),
}));

jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => jest.fn(),
  useLocation: () => ({
    pathname: '/',
    search: '',
    hash: '',
    state: null,
  }),
}));

jest.setTimeout(30000);

// Mock dos módulos que causam problemas nos testes
jest.mock('./src/utils/preventNetworkErrors', () => ({
  default: {
    restore: jest.fn()
  }
}));

// Mock do logger para evitar logs durante os testes
jest.mock('./src/utils/logger', () => ({
  logger: {
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
    debug: jest.fn()
  }
}));

// Mock do validateEnvironment
jest.mock('./src/utils/validateEnvironment', () => ({
  config: {
    apiUrl: 'http://localhost:3001',
    stage: 'test',
    isDevelopment: true,
    isProduction: false
  },
  validateEnvironment: jest.fn(),
  validateStageConfig: jest.fn()
}));

// Mock do api.js
jest.mock('./src/utils/api', () => ({
  apiDsm: {
    get: jest.fn(),
    post: jest.fn(),
    put: jest.fn(),
    delete: jest.fn(),
    interceptors: {
      request: { use: jest.fn() },
      response: { use: jest.fn() }
    }
  },
  apiProposals: {
    get: jest.fn(),
    post: jest.fn(),
    put: jest.fn(),
    delete: jest.fn(),
    interceptors: {
      request: { use: jest.fn() },
      response: { use: jest.fn() }
    }
  },
  apiOTRS: {
    get: jest.fn(),
    post: jest.fn(),
    put: jest.fn(),
    delete: jest.fn(),
    interceptors: {
      request: { use: jest.fn() },
      response: { use: jest.fn() }
    }
  },
  apiReports: {
    get: jest.fn(),
    post: jest.fn(),
    put: jest.fn(),
    delete: jest.fn(),
    interceptors: {
      request: { use: jest.fn() },
      response: { use: jest.fn() }
    }
  },
  URLS: {
    DSM: 'http://localhost:3001',
    PROPOSALS: 'http://localhost:3002',
    OTRS: 'http://localhost:3003',
    REPORTS: 'http://localhost:3004'
  },
  getHeader: jest.fn(() => ({ 'Authorization': 'Bearer mock-token' }))
}));

// Mock dos serviços de autenticação
jest.mock('./src/services/authService', () => ({
  authService: {
    getTokenFromCookie: jest.fn(() => 'mock-token'),
    getAuthHeaders: jest.fn(() => ({ 'Authorization': 'Bearer mock-token' })),
    isAuthenticated: jest.fn(() => true)
  }
}));

jest.mock('./src/services/httpOnlyAuthService', () => ({
  httpOnlyAuthService: {
    isHttpOnlySupported: true,
    isAuthenticated: jest.fn(() => true),
    authenticate: jest.fn(),
    logout: jest.fn(),
    setupAxiosDefaults: jest.fn(),
    interceptorsConfigured: false
  }
}));
