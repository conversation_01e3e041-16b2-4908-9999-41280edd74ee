version: 0.2

phases:
  install:
    runtime-versions:
      nodejs: 14
    commands:
      - echo INSTALL NPM
  pre_build:
    commands:
      - echo Stack started...
      - echo "NODE_ENV=${NODE_ENV}" >> .env.production
      - echo "REACT_APP_API_BASE_URL=${REACT_APP_API_BASE_URL}" >> .env.production
      - echo "REACT_APP_API_OTRS_BASE_URL=${REACT_APP_API_OTRS_BASE_URL}" >> .env.production
      - echo "REACT_APP_API_CUSTOMER_PORTAL=${REACT_APP_API_CUSTOMER_PORTAL}" >> .env.production
      - echo "REACT_APP_API_ATTACH_POLICY=${REACT_APP_API_ATTACH_POLICY}" >> .env.production
      - echo "REACT_APP_GOOGLE_KEY=${REACT_APP_GOOGLE_KEY}" >> .env.production
      - echo "REACT_APP_MS_TEAMS_WEBHOOK_URL=${REACT_APP_MS_TEAMS_WEBHOOK_URL}" >> .env.production
      - echo "REACT_APP_USER_POOL_ID=${REACT_APP_USER_POOL_ID}" >> .env.production
      - echo "REACT_APP_API_PERMISSION=${REACT_APP_API_PERMISSION}" >> .env.production
      - echo "REACT_APP_TICKET_API=${REACT_APP_TICKET_API}" >> .env.production
      - echo "REACT_APP_PIPEDRIVE_API=${REACT_APP_PIPEDRIVE_API}" >> .env.production
      - echo "REACT_APP_COGNITO_PARSE=${REACT_APP_COGNITO_PARSE}" >> .env.production
      - echo "REACT_APP_STAGE=${REACT_APP_STAGE}" >> .env.production
      - echo "REACT_APP_API_PROPOSALS=${REACT_APP_API_PROPOSALS}" >> .env.production
      - echo "REACT_APP_API_REPORTS_URL=${REACT_APP_API_REPORTS_URL}" >> .env.production
      - echo "REACT_APP_OTRS_HOURS_AUTH=${REACT_APP_OTRS_HOURS_AUTH}" >> .env.production
      - echo "REACT_APP_API_OTRS_URL=${REACT_APP_API_OTRS_URL}" >> .env.production
      - echo "REACT_APP_API_OTRS_TOKEN=${REACT_APP_API_OTRS_TOKEN}" >> .env.production
      - echo "REACT_APP_CUSTOMER_PORTAL_API_TOKEN=${REACT_APP_CUSTOMER_PORTAL_API_TOKEN}" >> .env.production
      - echo PRE BUILD PHASE
      - npm install --force
  build:
    commands:
      - echo BUILD PHASE
      - npm run build
    finally:
      - echo BUILD FINALIZED
  # post_build:
  # commands:
  # - aws cloudfront create-invalidation --distribution-id E1ESR2X22J2MSF --paths '/*'
  # finally:
  #   - echo CLOUDFRONT CACHE REFRESHED
artifacts:
  files:
    - "**/*"
  base-directory: build
