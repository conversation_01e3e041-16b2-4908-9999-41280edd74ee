import { useEffect, useState } from 'react'
import axios from 'axios'
import { Button, Modal, Row, Col, Table, message, Popconfirm, Tooltip } from 'antd'
import {
  PaperClipOutlined,
  DeleteOutlined,
  CheckOutlined,
  PlusOutlined
} from '@ant-design/icons'

export const AddDocument = ({ state, fileList, setFileList }) => {
  const [showModal, setShowModal] = useState();
  const [loading, setLoading] = useState(false);
  const [supportArchitectArray, setSupportArchitectArray] = useState([]);
  const [architectureFileList, setArchitectureFileList] = useState([]);
  const [savedFiles, setSavedFiles] = useState([]);
  const [wasUploaded, setWasUploaded] = useState([]);
  const [firstOpenVerify, setFirstOpenVerify] = useState(true);
  const idFixed = state;
  let count = 0

  const handleCloseModal = file => {
    if(localStorage.getItem('technical-proposal/architectureFileList')){
      file.map(item => {
        !JSON.parse(localStorage.getItem('technical-proposal/architectureFileList')).find(savedFile => savedFile === item)
        &&
        setSupportArchitectArray(supportArchitectArray.filter(currentFile => currentFile === item))
              
      })
    }else{
      setSupportArchitectArray([])
    }
    setShowModal(false)
  }

  const handleRemoveItem = (index, item) => {
    setArchitectureFileList(prev => [
      ...prev.slice(0, index),
      ...prev.slice(index + 1)
    ])
    const prevList = architectureFileList || []
    const concat = `${item.file_name}.${item.file_type}`
    const arr = []
    const supArr = []

    if (!Array.isArray(prevList)) {
      console.warn('⚠️ AddDocument: prevList não é um array:', prevList);
      return;
    }

    prevList.forEach(x => {
      if (x?.value?.name !== concat) {
        arr.push(x)
        supArr.push(x?.value?.name)
      }
    })
    setArchitectureFileList(arr)
    setSupportArchitectArray(supArr)
    localStorage.setItem(
      'technical-proposal/architectureFileList',
      JSON?.stringify(supArr)
    )
  }

  const handleFileSelect = () => {
    const architectureFile =
      document.getElementById('fileArchitecture').files[0]
    if (architectureFile) {
      let exists = false
      const addedFile = {
        id: count++,
        value: architectureFile,
        type: architectureFile.type,
        isSaved: true
      }
      architectureFileList?.map(file => {
        if (file.value === addedFile.value) {
          exists = true
        }
      })
      if (exists === false) {
        const prevList = architectureFileList
        prevList.push(addedFile)
        const arr = []
        for (let i = 0; i < prevList.length; i++) {
          let duplicateArr = arr.filter(
            e => e.value.name === prevList[i].value.name
          )
          if (duplicateArr.length === 0) arr.push(prevList[i])
        }
        setArchitectureFileList(arr)

        const fileName = supportArchitectArray
        fileName.push(addedFile?.value?.name)

        setSupportArchitectArray(
          fileName.filter((e, i) => fileName.indexOf(e) === i)
        )
      }
    } else {
      message.error('Selecione um arquivo')
    }
  }


  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id'
    },
    {
      title: 'Nome do arquivo',
      dataIndex: 'file_name',
      key: 'file_name'
    },
    {
      title: 'Tipo do Arquivo',
      dataIndex: 'file_type',
      key: 'file_type',
      align: 'center'
    },
    {
      title: 'Remover',
      dataIndex: 'remove',
      key: 'remove',
      render: (id, item) => {
        return (
          <Button
            danger
            type="text"
            onClick={() => {
              handleRemoveItem(id, item)
            }}
          >
            <DeleteOutlined />
          </Button>
        )
      }
    }
  ]

  const handleOk = async file => {
    setLoading(true)
    setFirstOpenVerify(false)
    file.map(currentFile => {
      currentFile.isSaved = true
      return file
    })
    
    file?.map(async currentFile => {
      let re = /(?:\.([^.]+))?$/
      let file_format = re.exec(currentFile.value.name)
      let name = `${idFixed}/documents/${file_format.input}`
      const {
        data: { data }
      } = await axios.post(
        `${process.env.REACT_APP_API_PERMISSION}s3/link`,
        {
          operation: 'put',
          key: name,
          bucket: `${process.env.REACT_APP_STAGE}-tickets-documents`,
          content: `application/${file_format[1]}`
        },
        {
          headers: {
            authorization: localStorage.getItem('jwt')
          }
        }
      )
        await axios.put(data, currentFile.value, {
          headers: {
            'Content-Type': `application/${file_format[1]}`
          }
        })
        .then(data => {
          wasUploaded.find(item => item === currentFile.value.name) && message.success('Upload feito com sucesso!')
          setLoading(false)
        })
        .catch(err => {
          message.error('Erro ao tentar fazer upload')
          setLoading(false)
        })
      })
      setShowModal(false)
      setFileList(supportArchitectArray);
    setTimeout(() => {
        setWasUploaded(supportArchitectArray)
    }, 2000);
  }
  

  const handleOpen = () => {
    setShowModal(true)
  }

  return (
    <>
      <Button type="dashed" onClick={handleOpen}>
        Adicionar Anexo
        <PlusOutlined />
      </Button>
      <Modal
        title="Fazer Upload"
        visible={showModal}
        closable={false}
        footer={[
          <Row justify="space-between">
            <Popconfirm
              title="Ao cancelar, você perderá os arquivos não salvos, tem certeza?"
              okText="Sim"
              cancelText="Não"
              onConfirm={() => handleCloseModal(supportArchitectArray)}
            >
              <Button 
                type="text-color" 
              >
                Cancelar <DeleteOutlined />
              </Button>
            </Popconfirm>
            <Tooltip title={supportArchitectArray.length > 0 ? "" : "Adicione arquivos para habilitar"}>
              <Button
                disabled={supportArchitectArray.length > 0 ? false : true}
                type="primary"
                onClick={() => handleOk(architectureFileList)}
                >
                Salvar <CheckOutlined />
              </Button>
            </Tooltip>

          </Row>
        ]}
      >
        <Row
          style={{ display: 'flex', flexDirection: 'column' }}
          justify="center"
        >
          {/* {architectureFileList && architectureFileList.length > 0 && (
            <Table
              dataSource={architectureFileList.map((file, fileKey) => {
                return {
                  id: fileKey + 1,
                  file_name:
                    architectureFileList.length > 0
                      ? file?.value?.name?.replace(
                          `.${file.type.split('/')[1]}`,
                          ''
                        )
                      : '',
                  file_type:
                    architectureFileList.length > 0
                      ? file?.type?.split('/')[1]
                      : '',

                  remove: fileKey
                }
              })}
              columns={columns}
              scroll={{ x: 100 }}
              pagination={false}
              loading={loading}
            ></Table>
          )} */}
          {/* update down below */}
          {supportArchitectArray !== null && (
            <Table
              dataSource={supportArchitectArray.map((file, fileKey) => {
                console.log()
                return {
                  id: fileKey + 1,
                  file_name: file
                    ? file?.replace(`.${file?.split('.')[1]}`, '')
                    : '',
                  file_type: file ? file?.split('.')[1] : '',
                  // isSaved: 
                  remove: fileKey
                }
              })}
              columns={columns}
              scroll={{ x: 100 }}
              pagination={false}
              loading={loading}
            ></Table>
          )}
          <Row justify="center">
            <Button type="dashed" style={{ marginTop: '20px' }}>
              <label htmlFor="fileArchitecture">Selecionar anexo</label>
            </Button>
          </Row>
          <Col>
            <input
              type="file"
              name="fileArchitecture"
              id="fileArchitecture"
              onChange={handleFileSelect}
              style={{ display: 'none' }}
            />
          </Col>
        </Row>
      </Modal>
    </>
  )
}
