import { format } from "date-fns";
export const formatCreateContractForm = (data, diff, response) => {
  if (!data || diff === undefined || !response)
    throw new Error("Missing parameters");
  return {
    active: 1,
    excess_cost: data.excess_cost,
    customer: JSON.parse(data.customer),
    duration: diff,
    executives: [],
    customer_id: JSON.parse(data.customer).id,
    customer_itsm: Number(JSON.parse(data.customer).itsm_id),
    identifications: {
      crm_id: data.crm_id || null,
      itsm_id: response.data.contract_id,
    },
    name: data.name.trim(),
    squad: data.squad,
    target_dates: {
      expected_close_date: new Date(data.expected_start_date[1]._d),
      expected_start_date: new Date(data.expected_start_date[0]._d),
    },
    total_hours: data.total_hours,
    type_hours: data.type_hours,
    s3FolderNotes: data.s3FolderNotes,
    values: {
      value: data.value,
    },
    percentage: {
      block: null,
      freezing: null,
    },
    scope: data.scope,
    created_at: format(new Date(), "yyyy-MM-dd HH:mm:ss"),
    updated_at: format(new Date(), "yyyy-MM-dd HH:mm:ss"),
    emails: [],
    verticals: {
      vertical_aws: null,
      vertical_pre_sale: null,
    },
    pool_type: data.pool_type,
    automatic_renewal: "Sim",
  };
};
