import { useEffect, useState } from "react";

import { LayoutReport } from "../Layout/index.jsx";
import { DynamicTable } from "../../../components/Table/DynamicTable";
import { TicketReportFilters } from "./components/TicketReportFilters.jsx";
import { useSelector } from "react-redux";
import moment from "moment";
import * as controller from "../../../controllers/reports/ticket-reports-controller";
import { setTicketReportState } from "../../../store/actions/ticket-report-action";

import { ViewArticlesModal } from "./components/ViewArticles.jsx";
import { Typography } from "antd";
import { differenceInDays } from "date-fns";
import { Counter } from "../../../components/Counter/index.jsx";

export const TicketsReport = () => {
  const { Text } = Typography;
  const contractSelected = useSelector(
    (state) => state.ticketReport.contractSelected
  );
  const tickets = useSelector((state) => state.ticketReport.tickets);

  const filteredTickets = useSelector(
    (state) => state.ticketReport.filteredTickets
  );
  const loading = useSelector((state) => state.ticketReport.loading);
  const search = useSelector((state) => state.ticketReport.search);

  useEffect(() => {
    window.scrollTo({ left: 0, top: 0, behavior: "smooth" });
  }, []);

  const columns = [
    {
      code: "view_title",
      title: "Número do Ticket",
      dataIndex: "ticket_number",
      key: "ticket_number",
      align: "center",
      sorter: (a, b) => b.ticket_number.localeCompare(a.ticket_number),
      sortDirections: ["descend", "ascend"],
    },
    {
      code: "view_queue",
      title: "Fila",
      dataIndex: "queue_name",
      key: "queue_name",
      align: "center",
      sorter: (a, b) => a.queue_name.localeCompare(b.queue_name),
      sortDirections: ["descend", "ascend"],
    },
    {
      code: "view_customer_name",
      title: "Cliente",
      dataIndex: "customer_representative",
      key: "customer_representative",
      sorter: (a, b) =>
        a.customer_representative.localeCompare(b.customer_representative),
      sortDirections: ["descend", "ascend"],
    },
    {
      code: "view_contract_name",
      title: "Contrato",
      dataIndex: "contract_name",
      key: "contract_name",
      sorter: (a, b) =>
        a.contract_name === null || b.contract_name === null
          ? -1
          : a.contract_name.localeCompare(b.contract_name),
      sortDirections: ["descend", "ascend"],
      align: "center",
      render: (text) => {
        return text ? <Text>{text}</Text> : <Text>N/A</Text>;
      },
    },
    {
      code: "view_owner",
      title: "Proprietário",
      dataIndex: "responsible_user",
      key: "responsible_user",
      sorter: (a, b) => a.created_user.localeCompare(b.created_user),
      sortDirections: ["descend", "ascend"],
    },
    {
      code: "view_ticket_priority",
      title: "Prioridade",
      dataIndex: "ticket_priority",
      key: "ticket_priority",
      sorter: (a, b) => a.ticket_priority.localeCompare(b.ticket_priority),
      sortDirections: ["descend", "ascend"],
    },
    {
      code: "view_ticket_time_spent",
      title: "Tempo total (min)",
      dataIndex: "total_ticket_time",
      key: "ticket_priority",
      align: "center",
      sorter: (a, b) => b.total_ticket_time - a.total_ticket_time,
      sortDirections: ["descend", "ascend"],
    },
    {
      code: "view_create_time",
      title: "Data de Criação",
      dataIndex: "create_time",
      key: "create_time",
      sorter: (a, b) => a.create_time.localeCompare(b.create_time),
      render: (text) => {
        return <span>{moment(text).format("DD/MM/YYYY HH:mm")}</span>;
      },
      sortDirections: ["descend", "ascend"],
    },
    {
      code: "view_status",
      title: "Status",
      dataIndex: "ticket_state",
      key: "ticket_state",
      align: "center",
      sorter: (a, b) => a.ticket_state.localeCompare(b.ticket_state),
      render: (text) => {
        return controller.formatTagViewOnTable(text);
      },
      sortDirections: ["descend", "ascend"],
    },
    {
      code: "view_ticket_age",
      title: "Idade do Ticket",
      dataIndex: "ticket_age",
      key: "ticket_age",
      align: "center",
      render: (_, text) => {
        let ticketAge = controller.formatTicketAge(text, tickets);
        return <Text type={ticketAge.color}>{ticketAge.age}</Text>;
      },
      sorter: (a, b) =>
        differenceInDays(
          new Date(a.create_time),
          new Date(b.create_time)
        ).toFixed(0) -
        differenceInDays(
          new Date(b.create_time),
          new Date(a.create_time)
        ).toFixed(0),
      sortDirections: ["descend", "ascend"],
    },
    {
      code: "view_articles",
      title: "Atividade",
      dataIndex: "articles",
      align: "center",
      key: "articles",
      render: (_, text) => {
        return <ViewArticlesModal ticketNumber={text} />;
      },
    },
  ];

  return (
    <LayoutReport>
      <TicketReportFilters tickets={tickets} />
      <Counter
        tableData={
          search === "" &&
          (contractSelected?.value === "Todos" || contractSelected === "Todos")
            ? tickets
            : filteredTickets
        }
      />
      <DynamicTable
        columns={columns}
        data={
          search === "" &&
          (contractSelected?.value === "Todos" || contractSelected === "Todos")
            ? tickets
            : filteredTickets
        }
        loading={loading}
        scroll={{ x: "100%" }}
      />
    </LayoutReport>
  );
};
