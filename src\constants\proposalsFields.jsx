export const templateProposalsFields = [
  {
    objectName: "readyToPersist",
    value: true,
  },
  {
    objectName: "contacts",
    value: [],
  },
  {
    objectName: "opportunities",
    value: [],
  },
  {
    objectName: "bus",
    value: [],
  },
  {
    objectName: "selectedContact",
    value: null,
  },
  {
    objectName: "type",
    value: "type",
  },
  {
    objectName: "clientName",
    value: "",
  },
  {
    objectName: "status",
    value: "status",
  },
  {
    objectName: "proposalID",
    value: null,
  },
  {
    objectName: "projectName",
    value: "",
  },
  {
    objectName: "changesServicesSelected",
    value: [],
  },
  {
    objectName: "persistID",
    value: "persistID",
  },
  {
    objectName: "calculator",
    value: "awsCosts",
  },
  {
    objectName: "architects",
    value: [],
  },
  {
    objectName: "selectedServices",
    value: "selectedServices",
  },
  {
    objectName: "opportunities",
    value: [],
  },
  {
    objectName: "selectedOpportunity",
    value: "",
  },
  {
    objectName: "scenarios",
    value: "Cenário apresentado",
  },
  {
    objectName: "premisses",
    value: "Premissas",
  },
  {
    objectName: "challenges",
    value: "Desafios",
  },
  {
    objectName: "extra_points",
    value: "Pontos não contemplados",
  },
  {
    objectName: "main_factors",
    value: "Fatores influenciadores",
  },
  {
    objectName: "observations",
    value: "Observações",
  },
  {
    objectName: "architecture",
    value: "Arquitetura",
  },
  {
    objectName: "internal_notes",
    value: "Notas Internas",
  },
  {
    objectName: "additional_costs",
    value: "Custos adicionais",
  },
  {
    objectName: "expected_results",
    value: "Resultados esperados",
  },
  {
    objectName: "serviceList",
    value: "serviceList",
  },
  {
    objectName: "selectedCustomer",
    value: null,
  },
  {
    objectName: "ticketData",
    value: null,
  },
  {
    objectName: "fileNames",
    value: "fileNames",
  },
  {
    objectName: "totalValues",
    value: "totalValues",
  },
  {
    objectName: "architectureFileNames",
    value: "architectureFileNames",
  },
  {
    objectName: "scenarioFileNames",
    value: "scenarioFileNames",
  },
  {
    objectName: "pipedriveDealOwner",
    value: "proposal?.pipedriveDealOwner ? proposal?.pipedriveDealOwner : null",
  },
];

export const editProposalsFields = [
  {
    objectName: "readyToPersist",
    value: true,
  },
  {
    objectName: "contacts",
    value: "contacts",
  },
  {
    objectName: "bus",
    value: "bus",
  },
  {
    objectName: "selectedContact",
    value: "[customer][mainContact]",
  },
  {
    objectName: "type",
    value: "type",
  },
  {
    objectName: "clientName",
    value: "clientName",
  },
  {
    objectName: "status",
    value: "status",
  },
  {
    objectName: "proposalID",
    value: "id",
  },
  {
    objectName: "projectName",
    value: "name",
  },
  {
    objectName: "changesServicesSelected",
    value: [],
  },
  {
    objectName: "persistID",
    value: "persistID",
  },
  {
    objectName: "calculator",
    value: "awsCosts",
  },
  {
    objectName: "architects",
    value: "architects",
  },
  {
    objectName: "selectedServices",
    value: "selectedServices",
  },
  {
    objectName: "opportunities",
    value: "opportunity",
  },
  {
    objectName: "selectedOpportunity",
    value: "mainOportunity",
  },
  {
    objectName: "scenarios",
    value: "scenarios",
  },
  {
    objectName: "premisses",
    value: "premisses",
  },
  {
    objectName: "challenges",
    value: "challenges",
  },
  {
    objectName: "extra_points",
    value: "extra_points",
  },
  {
    objectName: "main_factors",
    value: "main_factors",
  },
  {
    objectName: "observations",
    value: "observations",
  },
  {
    objectName: "architecture",
    value: "architecture",
  },
  {
    objectName: "internal_notes",
    value: "internal_notes",
  },
  {
    objectName: "additional_costs",
    value: "additional_costs",
  },
  {
    objectName: "expected_results",
    value: "expected_results",
  },
  {
    objectName: "serviceList",
    value: "serviceList",
  },
  {
    objectName: "selectedCustomer",
    value: "selectedCustomer",
  },
  {
    objectName: "fileNames",
    value: "fileNames",
  },
  {
    objectName: "totalValues",
    value: "totalValues",
  },
  {
    objectName: "architectureFileNames",
    value: "architectureFileNames",
  },
  {
    objectName: "scenarioFileNames",
    value: "scenarioFileNames",
  },
  {
    objectName: "ticketData",
    value: "ticketData",
  },
  {
    objectName: "commercialStatus",
    value: "commercialStatus",
  },
  {
    objectName: "active",
    value: "active",
  },
  {
    objectName: "monthSelected",
    value: "monthSelected",
  },
  {
    objectName: "spreadSetup",
    value: "spreadSetup",
  },
  {
    objectName: "pipedriveDealOwner",
    value: "pipedriveDealOwner",
  },
];
