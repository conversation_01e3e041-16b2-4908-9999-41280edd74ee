import { useState, useEffect } from "react";

import { Row, message } from "antd";

import { LayoutReport } from "../Layout";
import { ConfigPage } from './config-page';

import { TableFilter } from "./components/table-filter";
import { SearchField } from "./components/search-field";
import { TypeFilters } from "./components/types-filters";
import { SearchButton } from "./components/search-button";
import { DateFilters } from './components/date-filters.jsx';
import { NextUpdateField } from "./components/next-update-field";
import { SquadSelectorField } from "./components/squad-selector-field";
import { ExtractOptionButtons } from "./components/extract-option-buttons";

import * as controller from "../../../controllers/reports/surplus-hours-controller";
import { ContractTypeField } from "./components/contract-type-field";

export const ConsumptionHoursReport = () => {
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    initPage();
    window.scrollTo({ left: 0, top: 0, behavior: "smooth" });
  }, []);

  function initPage() {
    controller.getSquads();
    handleSearch();
  }

  async function handleSearch() {
    try {
      await controller.search(setLoading);
    } catch (error) {
      setLoading(false);
      message.error("Ops... Não foi possivel carregar os contratos");
      console.log(error);
    }
  }

  return (
    <LayoutReport>
      
      <NextUpdateField />

      <br />

      <ConfigPage />

      <Row style={{ width: "100%" }}>
        <ContractTypeField />
        <DateFilters />
        <SquadSelectorField />
        <SearchButton
          handleSearch={handleSearch}
          loading={loading}
        />
      </Row>

      <br />

      <Row style={{ width: "100%" }}>
        <SearchField />
        <TypeFilters />
        <ExtractOptionButtons loading={loading} />
      </Row>

      <Row>
        <TableFilter loading={loading} />
      </Row>

    </LayoutReport>
  );
};