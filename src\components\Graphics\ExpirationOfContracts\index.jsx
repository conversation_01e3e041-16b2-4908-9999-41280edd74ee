import { useEffect, useRef, useState } from "react";
import { ArcElement, Chart as ChartJS, Tooltip } from "chart.js";
import { Doughnut, getElementAtEvent } from "react-chartjs-2";
import {
  BgColorsOutlined,
  FilterFilled,
  StarFilled,
  StarOutlined,
} from "@ant-design/icons";
import {
  Modal,
  Popover,
  Spin,
  Typography,
  Row,
  Col,
  Card,
  Button,
  message,
} from "antd";
import moment from "moment";
import "./ExpirationOfContracts.css";
import ExpirationOfContractsFilter from "../../Modals/Filters/ExpirationOfContractsFilter";
import ExpirationOfContractsTable from "../../Table/ExpirationOfContracts";
import { otrsPost } from "../../../service/apiOtrs";
import ColorPicker from "../../ColorPicker";
import { useMemo } from "react";
import { CockpitsHeader } from "../../CockpitsHeader";

ChartJS.register(ArcElement, Tooltip);

export const options = {
  responsive: true,
  plugins: {
    legend: {
      position: "top",
    },
    title: {
      display: true,
    },
  },
};

function ExpirationOfContracts(props) {
  const { Title, Text } = Typography;
  const [isFavorite, setIsFavorite] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [loading, setLoading] = useState(true);
  const [wallets, setWallets] = useState([]);
  const [colors, setColors] = useState();
  const GRAPH_NAME = "ExpirationOfContracts";
  const [graphValues, setGraphValues] = useState();
  const [expireContracts, setExpireContracts] = useState([]);
  const [popoverVisibility, setPopoverVisibility] = useState(false);
  const [contractData, setContractData] = useState([]);
  const [activeIndex, setActiveIndex] = useState(0);
  const [allProfessionals, setAllProfessionals] = useState([]);
  const [contractTypes, setContractType] = useState([]);
  const [filterSelected, setFilterSelected] = useState();

  const DEFAULT_COLOR_ONE = "#FF3C3C";
  const DEFAULT_COLOR_TWO = "#F1F43F";
  const DEFAULT_COLOR_THREE = "#74CF49";

  const chartRef = useRef();

  const data = {
    labels: [" 30 Dias", " 60 Dias", " 90 Dias"],
    datasets: [
      {
        label: "# of Votes",
        data: [
          graphValues?.thirtyDays,
          graphValues?.sixtyDays,
          graphValues?.ninetyDays,
        ],
        backgroundColor: [
          colors ? colors[0]?.color : DEFAULT_COLOR_ONE,
          colors ? colors[1]?.color : DEFAULT_COLOR_TWO,
          colors ? colors[2]?.color : DEFAULT_COLOR_THREE,
        ],
        borderColor: [
          "rgba(255, 255, 255, 1)",
          "rgba(255, 255, 255, 1)",
          "rgba(255, 255, 255, 1)",
        ],
        borderWidth: 1,
      },
    ],
  };

  function showModal() {
    setModalVisible(!modalVisible);
  }

  function handleOk() {
    setModalVisible(false);
  }

  function handleCancel() {
    setModalVisible(false);
  }

  function getAllInfo() {
    setLoading(true);
    if (props.contracts && props.wallets) {
      const arrayContracts = props.contracts;
      setWallets(props.wallets);
      swapMonth(arrayContracts);
    }
  }

  function getGraphColor() {
    let storage_color = JSON.parse(localStorage.getItem("colors"));
    if (storage_color) {
      setColors(storage_color[GRAPH_NAME]);
    }
  }

  function swapMonth(contractsResponse, filter) {
    let values = {
      thirtyDays: 0,
      sixtyDays: 0,
      ninetyDays: 0,
    };
    const contracts = [[], [], []];
    let arrayContracts = [];

    for (let value in contractsResponse) {
      if (moment(contractsResponse[value].end_date) > moment()) {
        if (
          moment(contractsResponse[value].end_date) <=
          moment().add("30", "days")
        ) {
          values.thirtyDays++;
          contracts[0].push(contractsResponse[value]);
          arrayContracts.push(contractsResponse[value]);
        } else if (
          moment(contractsResponse[value].end_date) <=
          moment().add("60", "days")
        ) {
          values.sixtyDays++;
          contracts[1].push(contractsResponse[value]);
          arrayContracts.push(contractsResponse[value]);
        } else if (
          moment(contractsResponse[value].end_date) <=
          moment().add("90", "days")
        ) {
          values.ninetyDays++;
          contracts[2].push(contractsResponse[value]);
          arrayContracts.push(contractsResponse[value]);
        }
      }
    }

    if (filter) {
      setGraphValues(values);
      setContractData(contracts);
      setLoading(false);
      return true;
    } else {
      setExpireContracts(arrayContracts);
      setContractData(contracts);
      contracts.length === 0 &&
        message.info(
          "Não há contratos a vencer a partir das informações fornecidas"
        );
      setGraphValues(values);
      setLoading(false);
    }
  }

  let isExpired = useMemo(() => {
    if (graphValues) {
      let sum =
        graphValues.thirtyDays + graphValues.sixtyDays + graphValues.ninetyDays;
      return sum;
    }
    return 0, [data];
  }, [data, graphValues]);

  function checkIsVisible() {
    let localStorageData = JSON.parse(localStorage.getItem("favorites"));
    if (localStorageData) {
      var index = localStorageData.findIndex(
        (value) => value.component === GRAPH_NAME
      );
      return index !== -1;
    } else {
      return false;
    }
  }

  function changeColors(colors) {
    let storage_color = JSON.parse(localStorage.getItem("colors"));
    if (!storage_color) {
      storage_color = {};
    }
    storage_color[GRAPH_NAME] = colors;
    localStorage.setItem("colors", JSON.stringify(storage_color));
    setColors(colors);
  }

  async function updateFavorite() {
    await props?.setVisionFavorite(GRAPH_NAME);
    setIsFavorite(checkIsVisible());
  }

  async function getFilteredChartData(filter) {
    setLoading(true);
    let filteredExpiredContracts = structuredClone(expireContracts);
    setFilterSelected(filter);

    if (
      !filter.active &&
      !filter.inactive &&
      !filter.carteira &&
      !filter.sdm &&
      !filter.contractType
    ) {
      const newData = { thirtyDays: 0, sixtyDays: 0, ninetyDays: 0 };
      setGraphValues(newData);
      setLoading(false);
      return true;
    }

    if (filter.active || filter.inactive) {
      if (filter.active && !filter.inactive) {
        filteredExpiredContracts = filteredExpiredContracts.filter(
          (item) => item.valid_id === 1
        );
      } else if (filter.inactive && !filter.active) {
        filteredExpiredContracts = filteredExpiredContracts.filter(
          (item) => item.valid_id !== 1
        );
      }
    }

    if (filter.carteira) {
      filteredExpiredContracts = filteredExpiredContracts.filter(
        (item) =>
          item.document_id?.toLowerCase() === filter.carteira.toLowerCase()
      );
    }

    if (filter.sdm) {
      const tickets = await otrsPost("read/tickets/pdm", {
        params: filter.sdm,
      });

      let filteredExpiredContractsSDM = [];
      filteredExpiredContracts.forEach((item) => {
        tickets.data.forEach((ticket) => {
          if (ticket.id_contract === item.contract_id) {
            if (
              !filteredExpiredContractsSDM.find(
                (i) => i.contract_id === ticket.id_contract
              )
            ) {
              filteredExpiredContractsSDM.push(item);
            }
          }
        });
      });
      filteredExpiredContracts = filteredExpiredContractsSDM;
    }

    if (filter.contractType) {
      filteredExpiredContracts = filteredExpiredContracts.filter(
        (item) => item.type_hours === filter.contractType
      );
    }

    if (filteredExpiredContracts.length === 0) {
      message.warning(
        "Nenhum contrato encontrado com as informações inseridas."
      );
      setLoading(false);
    }

    swapMonth(filteredExpiredContracts, true);
  }

  function handleVisibleChange(e) {
    setPopoverVisibility(!popoverVisibility);
  }

  function handleCardClick(index) {
    if (isExpired !== 0) {
      setActiveIndex(index);
      showModal();
    }
  }

  function onClickChart(event) {
    const { index } = getElementAtEvent(chartRef.current, event)[0];
    setActiveIndex(index);
    showModal();
  }

  const sideCardsContent = [
    {
      title: "30 dias",
      contentKey: "thirtyDays",
      cardColor: colors ? colors[0]?.color : DEFAULT_COLOR_ONE,
    },
    {
      title: "60 dias",
      contentKey: "sixtyDays",
      cardColor: colors ? colors[1]?.color : DEFAULT_COLOR_TWO,
    },
    {
      title: "90 dias",
      contentKey: "ninetyDays",
      cardColor: colors ? colors[2]?.color : DEFAULT_COLOR_THREE,
    },
  ];

  useEffect(() => {
    getAllInfo();
    getGraphColor();
    if (props.professionals) {
      setAllProfessionals(props.professionals);
    }
    if (props.contractTypes) {
      setContractType(props.contractTypes);
    }
  }, [
    props.contracts,
    props.wallets,
    props.professionals,
    props.contractTypes,
  ]);

  useEffect(() => {
    setIsFavorite(checkIsVisible());
  }, [props.favorites]);

  return (
    <Card
      bordered="false"
      style={{
        borderRadius: "20px",
        height: "100%",
        boxShadow: "0 0 10px rgba(0,0,0,0.1)",
        minWidth: "390px",
        minHeight: "326px",
        alignItems: loading ? "center" : "",
        display: loading ? "flex" : "",
        justifyContent: loading ? "center" : "",
      }}
    >
      {loading ? (
        <Row
          style={{
            height: "100%",
            width: "100%",
            display: "flex",
            justifyContent: "center",
            flexDirection: "column",
          }}
          align="middle"
        >
          <Spin />
          <Text>Carregando...</Text>
        </Row>
      ) : (
        <>
          <CockpitsHeader
            title={
              <Col span={18}>
                <Title level={4} style={{ fontWeight: 400 }}>
                  Vencimento dos contratos
                </Title>
              </Col>
            }
            isFavorite={isFavorite}
            updateFavorite={updateFavorite}
            filter={
              <>
                <Popover
                  open={popoverVisibility}
                  placement="left"
                  trigger="click"
                  onOpenChange={handleVisibleChange}
                  content={() => {
                    return (
                      <ExpirationOfContractsFilter
                        contractTypes={contractTypes}
                        allProfessionals={allProfessionals}
                        handleVisibleChange={handleVisibleChange}
                        wallets={wallets}
                        filter={getFilteredChartData}
                        filterSelected={filterSelected}
                      />
                    );
                  }}
                ></Popover>

                <FilterFilled onClick={() => handleVisibleChange()} />
              </>
            }
            colorPicker={
              <Popover
                placement="left"
                title={"Personalização de cor"}
                content={() => {
                  return (
                    <ColorPicker
                      changeColors={changeColors}
                      options={data?.labels.map((val, index) => {
                        return val, index;
                      })}
                      state={colors}
                    />
                  );
                }}
              >
                <BgColorsOutlined />
              </Popover>
            }
          />
          <Row
            justify="space-between"
            align="middle"
            gutter={[0, 32]}
            style={{ justifyContent: isExpired !== 0 ? "" : "flex-start" }}
          >
            <Row gutter={[16, 32]}>
              <Col span={24}>
                {sideCardsContent.map((item, index) => {
                  return (
                    <Card
                      key={index}
                      hoverable={isExpired !== 0}
                      bordered
                      style={{
                        borderColor: item.cardColor,
                        padding: "0px",
                        borderRadius: "8px",
                        marginBottom: "5px",
                        height: "75px",
                      }}
                      onClick={() => handleCardClick(index)}
                    >
                      <div
                        style={{ backgroundColor: item.cardColor }}
                        className="bar"
                      ></div>
                      <Title level={5}>{item.title}</Title>
                      <Text>
                        Total: {graphValues && graphValues[item.contentKey]}
                      </Text>
                    </Card>
                  );
                })}
              </Col>
            </Row>
            <div className="graphic-expiration">
              {isExpired !== 0 ? (
                <Doughnut
                  ref={chartRef}
                  onClick={onClickChart}
                  data={data}
                  options={{
                    responsive: true,
                    cutoutPercentage: 80,
                    plugins: {
                      legend: {
                        display: false,
                      },
                    },
                  }}
                />
              ) : (
                <div
                  style={{
                    marginLeft: "65px",
                    display: "flex",
                    justifyContent: "center",
                    alignItems: "center",
                    height: "100%",
                    width: "100%",
                  }}
                >
                  Nenhum contrato a vencer*
                </div>
              )}
            </div>
          </Row>

          <Modal
            width={"70vw"}
            closable={true}
            footer={null}
            centered
            open={modalVisible}
            onCancel={handleCancel}
          >
            <ExpirationOfContractsTable
              expireContracts={contractData[activeIndex]}
              showModal={showModal}
            />
          </Modal>
        </>
      )}
    </Card>
  );
}

export default ExpirationOfContracts;
