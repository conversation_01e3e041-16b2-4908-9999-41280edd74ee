import {
  setTechnicalProposalReduce,
  setAllTechnicalProposalReduce,
  setTechnicalProposalCustomerReduce,
  cleanTechnicalProposalReduce,
  setCalculatorItemTechnicalProposalReduce,
  setAdditionalCostsItemTechnicalProposalReduce,
  setTechnicalProposalPersistInfoReduce
} from "../reducers/technical-proposal-reduce";

import { store } from "../store";

export const setTechnicalProposalState = ({ field, value }) => {
  store.dispatch(setTechnicalProposalReduce({ field, value }));
};

export const setTechnicalProposalCustomerState = ({
  clientName,
  selectedCustomer,
}) => {
  store.dispatch(
    setTechnicalProposalCustomerReduce({ clientName, selectedCustomer })
  );
};

export const setAllTechnicalProposalState = (payload) => {
  store.dispatch(setAllTechnicalProposalReduce(payload));
};

export const setCalculatorItemTechnicalProposalState = (payload) => {
  store.dispatch(setCalculatorItemTechnicalProposalReduce(payload));
};

export const setAdditionalCostsItemTechnicalProposalState = (payload) => {
  store.dispatch(setAdditionalCostsItemTechnicalProposalReduce(payload));
};

export const cleanTechnicalProposalState = () => {
  store.dispatch(cleanTechnicalProposalReduce());
};

export const setTechnicalProposalPersistInfoState = (payload) => {
  store.dispatch(setTechnicalProposalPersistInfoReduce(payload));
};