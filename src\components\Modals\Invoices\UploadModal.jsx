import { useState } from "react";
import { Progress, message, <PERSON>ton, Modal, Row, Col } from "antd";
import { CloudUploadOutlined } from "@ant-design/icons";

import axios from "axios";
import { getDate, getMonth, getYear } from "date-fns";

import { logNewAuditAction } from "../../../controllers/audit/logNewAuditAction";

export const UploadModal = (props) => {
  const [showModal, setShowModal] = useState();

  const [progress, setProgress] = useState(0);
  const [selectedFile, setSelectedFile] = useState(null);

  const handleFileInput = (e) => {
    setSelectedFile(e.target.files[0]);
  };

  const uploadFile = async (file) => {
    let re = /(?:\.([^.]+))?$/;

    let file_format = re.exec(file.name);

    let name = `${getYear(new Date())}/${getMonth(new Date()) + 1}/${getDate(
      new Date()
    )}/${file_format.input}`;

    const {
      data: { data },
    } = await axios.post(
      `${process.env.REACT_APP_API_PERMISSION}s3/link`,
      {
        operation: "put",
        key: name,
        bucket: `${process.env.REACT_APP_STAGE}-dsm-dolar`,
        content: `application/${file_format[1]}`,
      },
      {
        headers: {
          authorization: localStorage.getItem("jwt"),
        },
      }
    );

    axios
      .put(data, file, {
        headers: {
          "Content-Type": `application/${file_format[1]}`,
        },
      })
      .then((data) => {
        setProgress(100);
        const username = localStorage.getItem("@dsm/username");
        const title = "Upload de Invoice";
        const description = `${username} fez o uplaod da invoice ${name}`;
        logNewAuditAction(username, title, description);
        message.success("Upload feito com sucesso!");
      })
      .catch((err) => {
        setProgress(101);
        message.error("Erro ao tentar fazer upload");
      });
  };

  return (
    <>
      <Button
        data-testid="upload-button"
        type="primary"
        style={{ width: "100%" }}
        onClick={() => {
          setShowModal(true);
        }}
      >
        <CloudUploadOutlined />
        Upload
      </Button>
      <Modal
        data-testid="upload-modal"
        title="Fazer Upload"
        open={showModal}
        closable={false}
        onOk={() => setShowModal(false)}
        okText="Fechar"
        cancelText="Cancelar"
        onCancel={() => {
          setShowModal(false);
          setProgress(0);
        }}
      >
        <Row align="middle" justify="center">
          <Progress
            type="circle"
            percent={progress}
            status={progress > 100 ? "exception" : "success"}
          />
        </Row>
        <Row style={{ marginTop: "2em" }} align="middle" justify="center">
          <Col>
            <Button type="primary">
              <label htmlFor="file">Selecionar o arquivo...</label>
            </Button>
          </Col>
          <Col>
            <input
              type="file"
              name="file"
              id="file"
              accept=".pdf"
              onChange={handleFileInput}
              style={{ display: "none" }}
            />
            <Col style={{ marginLeft: ".5em" }}>{selectedFile?.name}</Col>
          </Col>
        </Row>
        <Row style={{ marginTop: "2em" }} align="middle" justify="center">
          <Button type="primary" onClick={() => uploadFile(selectedFile)}>
            Concluir
          </Button>
        </Row>
      </Modal>
    </>
  );
};
