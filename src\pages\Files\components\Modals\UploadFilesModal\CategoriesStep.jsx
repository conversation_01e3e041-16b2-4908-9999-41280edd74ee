import { Typo<PERSON>, Row, <PERSON>, Tooltip } from "antd";
import { useSelector, shallowEqual } from "react-redux";
import {
  setUploadReadyFilesState,
  setUploadFilesState,
} from "../../../../../store/actions/files-action";

import { Select } from "../../../../../components/Select";

const categories = [
  { value: "Incident Management", label: "Incident Management" },
  {
    value: "Non-service affecting incidents",
    label: "Non-service affecting incidents",
  },
  { value: "Performance analysis", label: "Performance analysis" },
  { value: "Assets/resources", label: "Assets/resources" },
  { value: "Exceptions", label: "Exceptions" },
];

export const CategoriesStep = () => {
  const { Text } = Typography;
  const category = useSelector((state) => state.files.category, shallowEqual);

  return (
    <>
      <Row style={{ paddingBottom: "8px" }}>
        <Text>
          Selecione uma{" "}
          <strong>
            Categoria
            <Tooltip title={"Campo obrigatório"}>
              <span style={{ color: "red" }}>*</span>
            </Tooltip>
          </strong>
        </Text>
      </Row>
      <Row align={"middle"} style={{ paddingBottom: "50px" }}>
        <Col span={24}>
          <Select
            onChange={(e) => {
              setUploadFilesState({ field: "category", value: e });
              setUploadReadyFilesState(true);
            }}
            placeholder={"Selecione uma categoria..."}
            options={categories}
            value={category !== "" ? category : null}
            style={{ width: "100%" }}
          />
        </Col>
      </Row>
    </>
  );
};
