import { Typo<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Col } from "antd";
import { CloudUploadOutlined } from "@ant-design/icons";
import { useSelector, shallowEqual } from "react-redux";
import {
  cleanFilesState,
  setCategoryState,
  setUploadReadyFilesState,
} from "../../../store/actions/files-action";
import { useToggle } from "../../../hooks/useToggle";

export const UploadCategorySelect = () => {
  const { Option } = Select;
  const { Text } = Typography;
  const category = useSelector((state) => state.files.category, shallowEqual);
  const uploadReady = useSelector(
    (state) => state.files.uploadReady,
    shallowEqual
  );

  const [toggleModalValue, toggle] = useToggle();

  return (
    <>
      <Button
        type="default"
        icon={<CloudUploadOutlined />}
        onClick={() => {
          toggle();
          setUploadReadyFilesState(false);
        }}
      >
        Upload de arquivo
      </Button>
      <Modal
        title="Categoria"
        open={toggleModalValue}
        width={"50%"}
        closable={false}
        centered
        footer={
          <Row justify={"end"}>
            <Button
              type="default"
              onClick={() => {
                toggle();
                cleanFilesState();
              }}
            >
              Cancelar
            </Button>
            <Button
              type="primary"
              onClick={() => {
                toggle();
              }}
              disabled={!uploadReady}
            >
              Ok
            </Button>
          </Row>
        }
      >
        <Row style={{ paddingTop: "50px", paddingBottom: "10px" }}>
          <Text>
            Selecione uma <strong>Categoria</strong>
          </Text>
        </Row>
        <Row align={"middle"} style={{ paddingBottom: "50px" }}>
          <Col span={24}>
            <Select
              onChange={(e) => {
                setCategoryState(e);
                setUploadReadyFilesState(true);
              }}
              placeholder={"Selecione uma categoria..."}
              value={category !== "" ? category : null}
              style={{ width: "100%" }}
            >
              <Option value="Incident Management">Incident Management</Option>
              <Option value="Non-service affecting incidents">
                Non-service affecting incidents
              </Option>
              <Option value="Performance analysis">Performance analysis</Option>
              <Option value="Assets/resources">Assets/resources</Option>
              <Option value="Exceptions">Exceptions</Option>
            </Select>
          </Col>
        </Row>
      </Modal>
    </>
  );
};
