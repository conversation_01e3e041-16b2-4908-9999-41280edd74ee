import { Col, Row, Space, Tooltip } from "antd";
import { StarFilled, StarOutlined } from "@ant-design/icons";
export const CockpitsHeader = ({
  title,
  isFavorite,
  updateFavorite,
  filter,
  colorPicker,
  hasFavoriteOption = true,
}) => {
  return (
    <Row justify="space-between" align="middle">
      {title}
      <Space size="large">
        {hasFavoriteOption && (
          <Col span={2}>
            {!isFavorite ? (
              <Tooltip title="Adicionar aos favoritos">
                <StarOutlined onClick={updateFavorite} className="star-icon" />
              </Tooltip>
            ) : (
              <Tooltip title="Remover dos favoritos">
                <StarFilled onClick={updateFavorite} className="star-icon" />
              </Tooltip>
            )}
          </Col>
        )}
        {filter ? (
          <Col span={2}>
            <Tooltip title="Filtrar">{filter}</Tooltip>
          </Col>
        ) : null}
        {colorPicker ? (
          <Col span={2}>
            <Tooltip title="Personalizar">{colorPicker}</Tooltip>
          </Col>
        ) : null}
      </Space>
    </Row>
  );
};
