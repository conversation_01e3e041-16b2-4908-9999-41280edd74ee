export const DateFormater = (proposal) => {
    if(
        proposal?.pipedriveCreatedAt !== "" &&
        proposal?.pipedriveCreatedAt !== undefined
    ) {
        const date = new Date(`${proposal?.pipedriveCreatedAt}`);

        const formatedDay = date?.getDate();
        const formatedMounth = date?.getMonth();
        const formatedYear = date?.getFullYear();

        const formatedDate = `${formatedDay}/${formatedMounth + 1}/${formatedYear}`

        return formatedDate;
    } else {
        return null;
    }
}