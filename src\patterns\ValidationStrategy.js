/**
 * Strategy Pattern Implementation for Validation
 * Follows Strategy Pattern, Open/Closed Principle, and Single Responsibility Principle
 */

/**
 * Validation Strategy Interface
 * Defines the contract for all validation strategies
 */
class ValidationStrategy {
  /**
   * Validate a value
   * @param {any} value - Value to validate
   * @param {object} options - Validation options
   * @returns {object} - Validation result { isValid: boolean, message: string }
   */
  validate(value, options = {}) {
    throw new Error('validate method must be implemented');
  }

  /**
   * Get strategy name
   */
  getName() {
    return this.constructor.name;
  }
}

/**
 * Required Field Validation Strategy
 */
class RequiredValidationStrategy extends ValidationStrategy {
  validate(value, options = {}) {
    const { message = 'Este campo é obrigatório' } = options;
    
    const isValid = value !== null && 
                   value !== undefined && 
                   value !== '' && 
                   (!Array.isArray(value) || value.length > 0);

    return {
      isValid,
      message: isValid ? '' : message,
      strategy: 'required'
    };
  }
}

/**
 * Email Validation Strategy
 */
class EmailValidationStrategy extends ValidationStrategy {
  validate(value, options = {}) {
    const { message = 'Email inválido' } = options;
    
    if (!value) {
      return { isValid: true, message: '', strategy: 'email' };
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const isValid = emailRegex.test(value);

    return {
      isValid,
      message: isValid ? '' : message,
      strategy: 'email'
    };
  }
}

/**
 * Length Validation Strategy
 */
class LengthValidationStrategy extends ValidationStrategy {
  validate(value, options = {}) {
    const { 
      min = 0, 
      max = Infinity, 
      message = `Deve ter entre ${min} e ${max} caracteres` 
    } = options;

    if (!value) {
      return { isValid: true, message: '', strategy: 'length' };
    }

    const length = value.toString().length;
    const isValid = length >= min && length <= max;

    return {
      isValid,
      message: isValid ? '' : message,
      strategy: 'length'
    };
  }
}

/**
 * Pattern Validation Strategy
 */
class PatternValidationStrategy extends ValidationStrategy {
  validate(value, options = {}) {
    const { 
      pattern, 
      message = 'Formato inválido' 
    } = options;

    if (!value || !pattern) {
      return { isValid: true, message: '', strategy: 'pattern' };
    }

    const regex = pattern instanceof RegExp ? pattern : new RegExp(pattern);
    const isValid = regex.test(value);

    return {
      isValid,
      message: isValid ? '' : message,
      strategy: 'pattern'
    };
  }
}

/**
 * Number Range Validation Strategy
 */
class NumberRangeValidationStrategy extends ValidationStrategy {
  validate(value, options = {}) {
    const { 
      min = -Infinity, 
      max = Infinity, 
      message = `Deve estar entre ${min} e ${max}` 
    } = options;

    if (value === null || value === undefined || value === '') {
      return { isValid: true, message: '', strategy: 'numberRange' };
    }

    const numValue = Number(value);
    
    if (isNaN(numValue)) {
      return {
        isValid: false,
        message: 'Deve ser um número válido',
        strategy: 'numberRange'
      };
    }

    const isValid = numValue >= min && numValue <= max;

    return {
      isValid,
      message: isValid ? '' : message,
      strategy: 'numberRange'
    };
  }
}

/**
 * Date Validation Strategy
 */
class DateValidationStrategy extends ValidationStrategy {
  validate(value, options = {}) {
    const { 
      format = 'YYYY-MM-DD',
      message = 'Data inválida' 
    } = options;

    if (!value) {
      return { isValid: true, message: '', strategy: 'date' };
    }

    let date;
    
    if (value instanceof Date) {
      date = value;
    } else {
      date = new Date(value);
    }

    const isValid = !isNaN(date.getTime());

    return {
      isValid,
      message: isValid ? '' : message,
      strategy: 'date'
    };
  }
}

/**
 * Custom Function Validation Strategy
 */
class CustomValidationStrategy extends ValidationStrategy {
  constructor(validationFunction, strategyName = 'custom') {
    super();
    this.validationFunction = validationFunction;
    this.strategyName = strategyName;
  }

  validate(value, options = {}) {
    try {
      const result = this.validationFunction(value, options);
      
      // Ensure result has the correct format
      if (typeof result === 'boolean') {
        return {
          isValid: result,
          message: result ? '' : (options.message || 'Valor inválido'),
          strategy: this.strategyName
        };
      }
      
      return {
        isValid: result.isValid || false,
        message: result.message || '',
        strategy: this.strategyName
      };
    } catch (error) {
      return {
        isValid: false,
        message: `Erro na validação: ${error.message}`,
        strategy: this.strategyName
      };
    }
  }

  getName() {
    return this.strategyName;
  }
}

/**
 * Validation Context
 * Uses strategies to validate values
 */
class ValidationContext {
  constructor() {
    this.strategies = new Map();
    this.registerDefaultStrategies();
  }

  /**
   * Register default validation strategies
   */
  registerDefaultStrategies() {
    this.registerStrategy('required', new RequiredValidationStrategy());
    this.registerStrategy('email', new EmailValidationStrategy());
    this.registerStrategy('length', new LengthValidationStrategy());
    this.registerStrategy('pattern', new PatternValidationStrategy());
    this.registerStrategy('numberRange', new NumberRangeValidationStrategy());
    this.registerStrategy('date', new DateValidationStrategy());
  }

  /**
   * Register a validation strategy
   */
  registerStrategy(name, strategy) {
    if (!(strategy instanceof ValidationStrategy)) {
      throw new Error('Strategy must extend ValidationStrategy');
    }
    this.strategies.set(name, strategy);
  }

  /**
   * Get a validation strategy
   */
  getStrategy(name) {
    return this.strategies.get(name);
  }

  /**
   * Validate a value using a specific strategy
   */
  validate(value, strategyName, options = {}) {
    const strategy = this.getStrategy(strategyName);
    
    if (!strategy) {
      throw new Error(`Unknown validation strategy: ${strategyName}`);
    }

    return strategy.validate(value, options);
  }

  /**
   * Validate a value using multiple strategies
   */
  validateMultiple(value, validations = []) {
    const results = [];
    let isValid = true;

    for (const validation of validations) {
      const { strategy: strategyName, ...options } = validation;
      const result = this.validate(value, strategyName, options);
      
      results.push(result);
      
      if (!result.isValid) {
        isValid = false;
        // Stop on first error (fail-fast)
        break;
      }
    }

    return {
      isValid,
      results,
      message: isValid ? '' : results.find(r => !r.isValid)?.message || '',
    };
  }

  /**
   * Get all available strategies
   */
  getAvailableStrategies() {
    return Array.from(this.strategies.keys());
  }

  /**
   * Create a custom validation strategy
   */
  createCustomStrategy(name, validationFunction) {
    const strategy = new CustomValidationStrategy(validationFunction, name);
    this.registerStrategy(name, strategy);
    return strategy;
  }
}

/**
 * Validation Builder
 * Fluent interface for building validation rules
 */
export class ValidationBuilder {
  constructor(context = null) {
    this.context = context || new ValidationContext();
    this.rules = [];
  }

  /**
   * Add required validation
   */
  required(message) {
    this.rules.push({ strategy: 'required', message });
    return this;
  }

  /**
   * Add email validation
   */
  email(message) {
    this.rules.push({ strategy: 'email', message });
    return this;
  }

  /**
   * Add length validation
   */
  length(min, max, message) {
    this.rules.push({ strategy: 'length', min, max, message });
    return this;
  }

  /**
   * Add pattern validation
   */
  pattern(pattern, message) {
    this.rules.push({ strategy: 'pattern', pattern, message });
    return this;
  }

  /**
   * Add number range validation
   */
  numberRange(min, max, message) {
    this.rules.push({ strategy: 'numberRange', min, max, message });
    return this;
  }

  /**
   * Add date validation
   */
  date(format, message) {
    this.rules.push({ strategy: 'date', format, message });
    return this;
  }

  /**
   * Add custom validation
   */
  custom(name, validationFunction, message) {
    this.context.createCustomStrategy(name, validationFunction);
    this.rules.push({ strategy: name, message });
    return this;
  }

  /**
   * Build and return validation function
   */
  build() {
    return (value) => this.context.validateMultiple(value, this.rules);
  }

  /**
   * Get validation rules
   */
  getRules() {
    return [...this.rules];
  }

  /**
   * Clear all rules
   */
  clear() {
    this.rules = [];
    return this;
  }
}

/**
 * Singleton Validation Context
 */
class ValidationManager {
  constructor() {
    if (ValidationManager.instance) {
      return ValidationManager.instance;
    }
    
    this.context = new ValidationContext();
    ValidationManager.instance = this;
  }

  static getInstance() {
    if (!ValidationManager.instance) {
      ValidationManager.instance = new ValidationManager();
    }
    return ValidationManager.instance;
  }

  getContext() {
    return this.context;
  }

  createBuilder() {
    return new ValidationBuilder(this.context);
  }
}

// Export singleton instance
export const validationManager = ValidationManager.getInstance();
export const validationContext = validationManager.getContext();

export {
  ValidationStrategy,
  ValidationContext,
  RequiredValidationStrategy,
  EmailValidationStrategy,
  LengthValidationStrategy,
  PatternValidationStrategy,
  NumberRangeValidationStrategy,
  DateValidationStrategy,
  CustomValidationStrategy,
};

export default ValidationContext;
