import {
  Row,
  Col,
  Input,
  Table,
  Select,
  Tag,
  Button,
  Empty,
  Popconfirm,
  Space,
  Tooltip,
} from "antd";
import { useState } from "react";
import { DeleteOutlined, EditOutlined, PlusOutlined } from "@ant-design/icons";
import { ViewServiceModal } from "../../Modals/TechnicalProposals/ViewService";
import { TextColor } from "../../../hooks/ColorInverter";
import { AddService } from "../../Modals/TechnicalProposals/AddService";
import { NavLink } from "react-router-dom";
import { AddManagementService } from "../../Modals/TechnicalProposals/AddManagementService";

export const TableProposals = (servicesInfo) => {
  const [loading, setLoading] = useState(false);
  const [search, setSearch] = useState("");

  const handleAddServices = (data) => {
    servicesInfo.setServices(data);
  };

  const columns = [
    {
      title: "Nome",
      dataIndex: "name",
      key: "name",
      sorter: (a, b) => a?.name.localeCompare(b?.name),
    },
    {
      title: "Descrição",
      dataIndex: "description",
      key: "description",
      render: (id, item) => {
        if (item?.description?.length < 100) {
          return item?.description ? item?.description : "-";
        } else {
          return (
            <div>
              {item?.description ? (
                <>{item?.description?.substring(0, 100)}...</>
              ) : (
                <Row justify="center">-</Row>
              )}
            </div>
          );
        }
      },
    },
    {
      title: "Tag",
      dataIndex: "tag",
      key: "tag",
      align: "center",
      sorter: (a, b) => a?.tag?.name.localeCompare(b?.tag?.name),
      render: (id, item) => {
        return TextColor(item?.tag?.rgb, item?.tag?.name);
      },
    },
    {
      title: "8x5 SETUP",
      dataIndex: "SETUP85",
      key: "SETUP85",
      align: "center",
      render: (id, item) => {
        // let burstHours = [];
        // servicesInfo.state[1].map((h) => {
        //   if (h.formName === "setup8x5" || h.formName === "setup24x7") {
        //     burstHours.push(h.burst);
        //   }
        // });
        // const burstHoursPercentage = burstHours.reduce((a, b) => a + b, 0);
        let percentageBruteValue = 0;
        if (item.management === true) {
          let totalManagementHours = 0;
          let supportManagementHours8x5 = [];
          let supportManagementHours24x7 = [];
          servicesInfo.state[0].map((s) => {
            // iterating all services
            if (s.management === false) {
              s.tasks.map((t) => {
                supportManagementHours8x5.push(t.estimates["8x5setup"]);
                supportManagementHours24x7.push(t.estimates["24x7setup"]);
              });
            }
          });

          let sumHours8x5 = supportManagementHours8x5.reduce(
            (a, b) => a + b,
            0
          );
          let sumHours24x7 = supportManagementHours24x7.reduce(
            (a, b) => a + b,
            0
          );

          totalManagementHours = sumHours8x5 + sumHours24x7;

          item.tasks.map((cur) => {
            if (cur.taskTime.type === "percentage") {
              percentageBruteValue += Math.ceil(
                (cur.taskTime.time * totalManagementHours) / 100
              );
              // percentageBruteValue += Math.ceil(
              //   (cur.taskTime.time * burstHoursPercentage) / 100
              // );
            }
          });
        }
        let SETUP85Support = [];
        item.tasks.map((task) => {
          if (!task.taskTime) {
            SETUP85Support.push(task.estimates["8x5setup"]);
          } else if (task.taskTime.type === "hour") {
            SETUP85Support.push(task.taskTime.time);
          }
        });
        return item.management === false ? (
          <>{SETUP85Support.reduce((a, b) => a + b, 0)}</>
        ) : (
          <>
            {SETUP85Support.reduce((a, b) => a + b, 0) + percentageBruteValue}
          </>
        );
      },
    },
    {
      title: "24x7 SETUP",
      dataIndex: "SETUP247",
      key: "SETUP247",
      align: "center",
      render: (id, item) => {
        let SETUP247Support = [];
        item.tasks.map((task) => {
          SETUP247Support.push(task.estimates["24x7setup"]);
        });
        return <>{SETUP247Support.reduce((a, b) => a + b, 0)}</>;
      },
    },
    {
      title: "Editar",
      dataIndex: "edit",
      key: "edit",
      align: "center",
      render: (id, item) => {
        if (servicesInfo.state[servicesInfo.state.length - 1] === "technical") {
          let editDataFormat = servicesInfo.state[1];
          let allData = [
            {
              item,
              editDataFormat,
            },
          ];

          return (
            <NavLink
              to={
                item.management === false
                  ? "/technical-proposals-services/edit"
                  : "/service-management/edit"
              }
              state={item.management === false ? item : allData}
            >
              <Button
                type="text"
                onClick={() => {
                  localStorage.setItem(
                    "technical-proposal/services",
                    JSON.stringify(servicesInfo.state[0])
                  );
                  localStorage.setItem(
                    "technical-proposal/customer",
                    JSON.stringify({
                      client_name: servicesInfo?.state[2],
                      cnpj: servicesInfo?.state[3]?.cnpj,
                      contacts: servicesInfo?.state[3]?.contacts,
                    })
                  );
                  localStorage.setItem("CollapseValidator", true);
                }}
              >
                <EditOutlined />
              </Button>
            </NavLink>
          );
        }
      },
    },
    {
      title: "Remover",
      dataIndex: "remove",
      key: "remove",
      align: "center",
      render: (id, item) => {
        return (
          <Popconfirm
            title="Você tem certeza que deseja remover este serviço?"
            onConfirm={() => {
              servicesInfo.setServices((prev) => prev.filter((i) => i.id !== item.id));
            }}
            okText="Sim"
            cancelText="Não"
          >
            <Button type="text" danger>
              <DeleteOutlined />
            </Button>
          </Popconfirm>
        );
      },
    },
  ];

  const pagination = {
    data: [],
    total:
      servicesInfo.state[0].length < 20 ? servicesInfo.state[0].length : 500,
  };

  return (
    <>
      <Row
        justify="space-between"
        style={{ marginBlock: "1rem" }}
        gutter={[8, 8]}
      >
        {/* {!window.location.href.includes("commercial") && ( */}
        {servicesInfo.state[servicesInfo.state.length - 1] === "technical" ? (
          <>
            <Col>
              <Space wrap>
                <>
                  <AddService
                    services={servicesInfo.state[0]}
                    setServices={(data) => handleAddServices(data)}
                  />
                  <AddManagementService
                    services={servicesInfo.state[0]}
                    setServices={(data) => handleAddServices(data)}
                  />
                </>
              </Space>
            </Col>
            {/* )} */}
            <Col>
              <Input
                style={{
                  width: "300px",
                  height: "35px",
                  borderRadius: "7px",
                }}
                placeholder="Buscar por Nome, Descrição, Tag..."
                onChange={(e) => setSearch(e.target.value)}
              />
            </Col>
          </>
        ) : (
          <></>
        )}
      </Row>
      <Row justify="center" align="middle" style={{ marginBottom: "15px" }}>
        <Col span={24}>
          {servicesInfo.state[0].length > 0 ? (
            <Table
              scroll={{ x: 100 }}
              columns={
                servicesInfo.state[servicesInfo?.state?.length - 1] ===
                "technical"
                  ? columns
                  : columns.filter(
                      (column) =>
                        column.dataIndex !== "edit" &&
                        column.dataIndex !== "remove"
                    )
              }
              pagination={pagination}
              dataSource={servicesInfo.state[0]?.filter((e) => {
                const data = [
                  e?.name
                    ? e?.name?.toString().includes(search?.toLowerCase())
                    : false,
                  e?.tag.name
                    ? e?.tag.name?.toString().includes(search?.toLowerCase())
                    : false,
                  e?.description?.toLowerCase()
                    ? e?.description
                        .toLowerCase()
                        ?.toString()
                        .includes(search?.toLowerCase())
                    : false,
                  e?.name
                    ? e?.name?.toLowerCase().includes(search?.toLowerCase())
                    : false,
                ];

                for (let i = 0; i < data.length; i++) {
                  if (data[i] === true) {
                    return e;
                  }
                }
                if (search === "") return e;

                return null;
              })}
            />
          ) : (
            <Empty
              description="Nenhum serviço encontrado"
              style={{ margin: "30px 0" }}
            />
          )}
        </Col>
      </Row>
    </>
  );
};
