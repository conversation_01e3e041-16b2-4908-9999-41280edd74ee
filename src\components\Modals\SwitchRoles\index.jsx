import Axios from "axios";
import {
  Button,
  Col,
  Form,
  Input,
  InputNumber,
  message,
  Modal,
  Popover,
  Row,
  Tooltip,
} from "antd";
import { LikeOutlined } from "@ant-design/icons";
import { useState, useCallback } from "react";
import { dynamoPost, invokeStateMachine } from "../../../service/apiDsmDynamo";
import { logNewAuditAction } from "../../../controllers/audit/logNewAuditAction";
import {
  formatInvokeStateMachineBody,
  getPermissionSetFromUser,
  otrsPermissionNotification,
} from "./controller/AllowAccess";
import { allowSolicitationFromPreviousApi } from "../../../service/previousModelSwitchRoleAPI";

export const AllowAccess = (props) => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [loadingAllow, setLoadingAllow] = useState(false);
  const [loading, setLoading] = useState(false);
  const [timeV, setTimeV] = useState(false);
  const { data, getSolicitations, origin, permissionSets } = props;
  const [form] = Form.useForm();
  const [timeForm] = Form.useForm();

  function delay(time) {
    return new Promise((resolve) => setTimeout(resolve, time));
  }

  const allowSolicitation = useCallback(
    async (formData) => {
      formData.action = "allow";

      setLoadingAllow(true);

      if (permissionSets.find((x) => x.user === data.username)) {
        let dataWithPermissionSet = {
          ...data,
          arn: permissionSets.find((x) => x.user === data.username).arn,
        };
        await allowSolicitationFromPreviousApi(formData, dataWithPermissionSet);
      } else {
        let permissionSetAttachedToUser = getPermissionSetFromUser(
          permissionSets,
          data
        );

        let formattedAllowBody = formatInvokeStateMachineBody({
          formData,
          data,
          permissionSetAttachedToUser,
        });
        await invokeStateMachine(formattedAllowBody);
      }

      let username = localStorage.getItem("@dsm/username");
      let title = "Acesso Switch Role";
      let description = `Acesso permitido ao usuário '${
        data.username
      }' na conta: '${data.account_id}' com a role '${
        permissionSets.find((x) => x.user === data.username)
          ? "darede-full"
          : `${data.username.replace(".", "-")}-darede`
      }'. Permitido por: ${localStorage.getItem("@dsm/username")}`;

      await otrsPermissionNotification(data.client_ticket, description);
      logNewAuditAction(username, title, description);
      await delay(2000);
      await getSolicitations();
      setLoadingAllow(false);
      message.success("Acesso permitido com sucesso!");
      setTimeV(false);
    },
    [permissionSets, data]
  );

  const denyAccess = async () => {
    setLoading(true);
    try {
      const formattedDenyBody = formatInvokeStateMachineBody({ data });
      await invokeStateMachine(formattedDenyBody);
      props.setShowModal(false);
      await getSolicitations();
    } catch (error) {

    }

    await dynamoPost(`${process.env.REACT_APP_STAGE}-audits`, {
      username: localStorage.getItem("@dsm/username"),
      name: "Acesso Switch Role",
      description: `Acesso negado ao usuário '${data.username}' na conta: '${
        data.account_id
      }' com a role 'darede-full'. Negado por: ${localStorage.getItem(
        "@dsm/username"
      )}`,
      created_at: new Date(),
      updated_at: new Date(),
    });

    message.success("Acesso negado com sucesso!");
    await delay(2000);
    await getSolicitations();

    setLoading(false);
    handleCancel();
  };

  const showModal = () => {
    setTimeV(false);
    setIsModalVisible(true);
  };

  const handleOk = () => {
    form.submit();
  };

  const handleCancel = () => {
    setIsModalVisible(false);
    form.resetFields();
  };

  return (
    <Tooltip
      title={
        origin === "client"
          ? "Remova este acesso"
          : "Permita o acesso deste usuário"
      }
      placement="left"
    >
      <Popover
        title="Deseja confimar acesso a este usuário?"
        content={
          <>
            <Form
              labelCol={{ span: 5 }}
              wrapperCol={{ span: 19 }}
              form={timeForm}
              onFinish={allowSolicitation}
            >
              <Form.Item label="Tempo" name="time">
                <Row align="bottom" gutter={4}>
                  <Col span={22}>
                    <InputNumber
                      defaultValue={
                        data.time ? data.time / 60 : data.requestedTime / 60
                      }
                      style={{ width: "100%" }}
                    />
                  </Col>
                  <Col>H</Col>
                </Row>
              </Form.Item>
              <Row justify="space-between" style={{ marginTop: "1em" }}>
                <Button onClick={() => setTimeV(false)}>Cancelar</Button>
                <Button type="danger" onClick={showModal}>
                  Negar
                </Button>
                <Tooltip
                  title={
                    origin === "client"
                      ? "Apenas o Time de Segurança pode permitir o acesso"
                      : ""
                  }
                >
                  <Button
                    loading={loadingAllow}
                    htmlType="submit"
                    type="primary"
                    disabled={origin === "client" ? true : false}
                  >
                    Permitir
                  </Button>
                </Tooltip>
              </Row>
            </Form>
          </>
        }
        open={timeV}
        trigger="click"
        placement="leftTop"
        onOpenChange={() =>
          isModalVisible === true ? setTimeV(false) : setTimeV(true)
        }
      >
        <Button type="primary">
          <LikeOutlined />
        </Button>
        <Modal
          title="Diga o motivo por qual irá negar este acesso"
          okButtonProps={{ type: "danger" }}
          open={isModalVisible}
          confirmLoading={loading}
          onCancel={handleCancel}
          cancelText="Cancelar"
          onOk={handleOk}
          okText="Negar"
        >
          <Form form={form} onFinish={denyAccess}>
            <Form.Item name="description" style={{ margin: "0" }}>
              <Input.TextArea placeholder="Digite aqui o motivo..." />
            </Form.Item>
          </Form>
        </Modal>
      </Popover>
    </Tooltip>
  );
};
