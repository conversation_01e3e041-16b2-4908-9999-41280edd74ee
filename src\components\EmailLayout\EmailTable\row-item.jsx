import { useContext } from 'react'
import styleSheet from './style.module.scss'
import { DeleteOutlined, PlusOutlined } from '@ant-design/icons'
import { Tag } from 'antd'

import { EmailAutomationContext } from '../../../contexts/email-automation'
import { EmailAutomationReducerType } from "../../../contexts/reducers/email-automation-reducer"

import { CollumnItem } from './collumn-item'

export function generateEmptyCollumn() {
    const newCollumn = {
        value: ''
    }

    return newCollumn
}

export const RowItem = ({ row, indexTable, indexRow }) => {
    const { emailAutomationState, setEmailAutomationState } = useContext(EmailAutomationContext)
    const { templateList } = emailAutomationState

    function addCollumnTable() {
        const newTemplateListStr = JSON.stringify(templateList)
        const newTemplateList = JSON.parse(newTemplateListStr)
        const table = newTemplateList[indexTable]
        const rows = table.value

        const newCollumn = generateEmptyCollumn()
        rows[indexRow].collumns.push(newCollumn)

        table.value = rows
        newTemplateList[indexTable] = table

        setEmailAutomationState({
            type: EmailAutomationReducerType.SET_TEMPLATE_LIST,
            value: newTemplateList
        })
    }

    function removeLastCollumnTable() {
        const newTemplateListStr = JSON.stringify(templateList)
        const newTemplateList = JSON.parse(newTemplateListStr)
        const table = newTemplateList[indexTable]
        const rows = table.value
        const collumns = rows[indexRow].collumns

        if (collumns.length === 1) {
            deleteRow()
            return
        }

        collumns.pop()

        rows[indexRow].collumns = collumns
        table.value = rows
        newTemplateList[indexTable] = table

        setEmailAutomationState({
            type: EmailAutomationReducerType.SET_TEMPLATE_LIST,
            value: newTemplateList
        })
    }

    function deleteRow() {
        const newTemplateListStr = JSON.stringify(templateList)
        const newTemplateList = JSON.parse(newTemplateListStr)
        const table = newTemplateList[indexTable]
        const rows = table.value

        const newRows = rows.filter((row, index) => index !== indexTable)

        table.value = newRows
        newTemplateList[indexTable] = table

        setEmailAutomationState({
            type: EmailAutomationReducerType.SET_TEMPLATE_LIST,
            value: newTemplateList
        })
    }

    return (
        <tr className={styleSheet['row-item']} style={{ display: 'flex' }}>
            {
                row.collumns.map((col, key) =>
                    <CollumnItem
                        indexCollumn={key}
                        indexRow={indexRow}
                        indexTable={indexTable}
                        value={col.value}
                        key={key}
                    />
                )
            }

            <div className={styleSheet['table-options-container']} style={{ display: 'none' }}>
                <Tag color="green" onClick={addCollumnTable}>
                    <PlusOutlined />
                </Tag>

                <Tag color="red" onClick={removeLastCollumnTable}>
                    <DeleteOutlined />
                </Tag>
            </div>
        </tr>
    )
}