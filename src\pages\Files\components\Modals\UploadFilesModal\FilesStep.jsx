import {
  Typo<PERSON>,
  Upload,
  <PERSON><PERSON>,
  <PERSON>confirm,
  <PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON>lt<PERSON>,
  Alert,
  message,
} from "antd";
import { CloudUploadOutlined, CloudDownloadOutlined } from "@ant-design/icons";
import { useSelector, shallowEqual } from "react-redux";

import { setUploadFilesState } from "../../../../../store/actions/files-action";
import { useDebounce } from "../../../../../hooks/useDebouce";
import { useEffect, useState } from "react";

const { Dragger } = Upload;
const { Title } = Typography;

const MAX_FILE_SIZE_IN_BYTES = 100000000; // 10^8 bytes === 100MB

export const FilesStep = () => {
  const [filesEvent, setFilesEvent] = useState([]);

  const filesToUpload = useSelector(
    (state) => state.files.filesToUpload,
    shallowEqual
  );

  function downloadFile(file) {
    const anchor = document.createElement("a");
    const url = URL.createObjectURL(file.originFileObj);

    anchor.download = file.name;
    anchor.href = url;
    anchor.click();
  }

  function removeFile(position) {
    const newList = filesToUpload.filter((f, index) => index !== position);
    setUploadFilesState({ field: "filesToUpload", value: newList });
  }
  const debouncedChangeFile = useDebounce(filesEvent, 500);

  useEffect(() => {
    if (filesEvent.length > 0) {
      let isError = false;
      filesEvent.forEach((file) => {
        if (file.size > MAX_FILE_SIZE_IN_BYTES) {
          message.error(
            "Este arquivo contém tamanho maior do que o permitido",
            3
          );
          isError = true;
        }

        if (!file.type) {
          message.error(`O arquivo ${file.name} não possui extensão`, 3);
          isError = true;
        }
      });

      if (isError) return;

      setUploadFilesState({
        field: "filesToUpload",
        value: [...filesEvent],
      });
    }
  }, [debouncedChangeFile]);

  return (
    <>
      <Dragger
        multiple={true}
        action={null}
        beforeUpload={() => {
          return false;
        }}
        onChange={({ fileList }) => setFilesEvent(fileList)}
        itemRender={() => {}}
        fileList={filesToUpload}
        style={{ borderColor: "rgba(0, 165, 85, 1)", borderRadius: 20 }}
      >
        <CloudUploadOutlined style={{ color: "#AAAAAA", fontSize: 45 }} />
        <Title level={4} style={{ color: "#666666", fontWeight: 500 }}>
          Clique ou Arraste e solte arquivos aqui
        </Title>
      </Dragger>
      <Alert
        type="warning"
        showIcon
        description={`O tamanho máximo permitido para cada arquivo é de 100MB`}
        style={{ marginTop: 10, borderRadius: 10 }}
      />
      <div>
        {filesToUpload.map((file, index) => (
          <Row
            key={index}
            style={{
              padding: 6,
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <Col md={18}>{file.name}</Col>
            <Col md={3}>
              <Tooltip title="Clique para fazer o download">
                <Button
                  style={{ padding: "0" }}
                  type="text"
                  onClick={() => downloadFile(file)}
                >
                  <CloudDownloadOutlined style={{ fontSize: 15 }} />
                </Button>
              </Tooltip>
            </Col>
            <Col md={3}>
              <Popconfirm
                placement="leftBottom"
                title="Deseja remover este arquivo?"
                onConfirm={() => removeFile(index)}
                cancelText={"Cancelar"}
                okText={"Ok"}
                style={{
                  backgroundColor: "transparent",
                  border: "none",
                  cursor: "pointer",
                  color: "black",
                }}
              >
                <Button style={{ padding: "0" }} type="text">
                  <Tag color="red">Excluir</Tag>
                </Button>
              </Popconfirm>
            </Col>
          </Row>
        ))}
      </div>
    </>
  );
};
