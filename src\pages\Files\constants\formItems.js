import { v4 } from "uuid";
import { TagFormItem } from "../components/TagsFormItem";
import { allCategories } from "./categories";
export const formItems = [
  {
    label: "Cliente",
    type: "select",
    mode: "default",
    rules: [
      {
        required: true,
        message: "Por favor, selecione um cliente",
      },
    ],
    placeholder: "Selecione um cliente",
    options: [
      {
        value: v4(),
        label: "teste",
      },
    ],
  },
  {
    label: "Categoria",
    type: "select",
    mode: "default",
    rules: [
      {
        required: true,
        message: "Por favor, selecione uma categoria",
      },
    ],
    placeholder: "Selecione uma categoria",
    options: allCategories,
  },
  {
    label: "Tags",
    type: "select",
    mode: "multiple",
    rules: [
      {
        required: true,
        message: "Por favor, selecione uma tag",
      },
    ],
    placeholder: "Adicione tags",
    component: <TagFormItem />,
    options: [],
  },
  {
    label: "Email",
    type: "input",
    rules: [
      {
        required: false,
      },
    ],
    placeholder: "Email do cliente",
  },
];
