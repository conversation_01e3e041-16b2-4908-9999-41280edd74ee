export const ticketsReportColumns = [
  {
    label: "Número do Ticket",
    key: "ticket_number",
  },
  {
    label: "Assunto",
    key: "ticket_subject",
  },
  {
    label: "Data de Abertura",
    key: "create_time",
  },
  {
    label: "Última Atualização",
    key: "change_time",
  },
  {
    label: "Status",
    key: "ticket_state",
  },
  {
    label: "Idade do Ticket",
    key: "ticket_age",
  },
  {
    label: "Proprietário",
    key: "responsible_user",
  },
  {
    label: "Solicitante",
    key: "customer_representative",
  },
  {
    label: "Tempo total (min)",
    key: "total_ticket_time",
  },
  {
    label: "<PERSON>la",
    key: "queue_name",
  },
  {
    label: "Tipo",
    key: "ticket_type",
  },
  {
    label: "Carteira",
    key: "squad",
  },
  {
    label: "Prioridade",
    key: "ticket_priority",
  },
  {
    label: "Contrato",
    key: "contract_name",
  },
  {
    label: "Id <PERSON>trato",
    key: "contract_id",
  },
  {
    label: "Cliente",
    key: "customer_representative",
  },
  {
    label: "Id do Cliente",
    key: "customer_id",
  },
];

export const ticketsReportDetailedColumns = [
  {
    label: "Número do Ticket",
    key: "ticket_number",
  },
  {
    label: "Assunto",
    key: "ticket_subject",
  },
  {
    label: "Data de Abertura",
    key: "ticket_create_time",
  },
  {
    label: "Última Atualização",
    key: "ticket_change_time",
  },
  {
    label: "Idade do Ticket",
    key: "ticket_age",
  },
  {
    label: "Status",
    key: "ticket_state",
  },
  {
    label: "Proprietário",
    key: "owner",
  },
  {
    label: "Solicitante",
    key: "requester",
  },
  {
    label: "Fila",
    key: "queue",
  },
  {
    label: "Tipo",
    key: "ticket_type",
  },
  {
    label: "Carteira",
    key: "squad",
  },
  {
    label: "Prioridade",
    key: "ticket_priority",
  },
  {
    label: "Contrato (Ticket)",
    key: "contract_name",
  },
  {
    label: "Id Contrato (Ticket)",
    key: "contract_id",
  },
  {
    label: "Id do Artigo",
    key: "article_id",
  },
  {
    label: "Assunto do Artigo",
    key: "article_subject",
  },
  {
    label: "Data de Criação do Artigo",
    key: "article_created_at",
  },
  {
    label: "Tempo apontando (min)",
    key: "article_time",
  },
  {
    label: "Corpo do Artigo",
    key: "article_description",
  },
  {
    label: "Autor do Artigo",
    key: "article_author",
  },
  {
    label: "Contrato (Artigo)",
    key: "contract_name",
  },
  {
    label: "Id Contrato (Artigo)",
    key: "contract_id",
  },
  {
    label: "Cliente",
    key: "customer",
  },
  {
    label: "Id do Cliente",
    key: "customer_id",
  },
];
