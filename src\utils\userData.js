export const getToken = user => {
  return user.signInUserSession?.idToken.jwtToken
}

export function getPayload(user) {
  if (user.signInUserSession !== undefined) {
    return user.signInUserSession?.idToken.payload
  }
  return user?.idToken.payload
}

export function hasPermissionToAccess(userData, page, feature) {
  let userPermissions

  if (userData !== null && userData.responsability_id !== undefined) {
    userPermissions = feature
  }

  if (userPermissions?.includes(page)) {
    return true
  }
  console.log(userData)

  return false
}
