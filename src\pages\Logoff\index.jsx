import { Row, Col, Button, Image, Divider, Space } from "antd";
import Logo from "../../assets/images/logo_full.png";
import { NavLink, useLocation } from "react-router-dom";
import { useEffect } from "react";
import { authService } from "../../services/authService";

export const Logoff = () => {
  const { state } = useLocation();

  useEffect(() => {
    // Clear authentication when logoff page loads
    const performLogout = async () => {
      try {
        await authService.logout();
      } catch (error) {
        console.error('Error during logout:', error);
      }
    };

    performLogout();
  }, []);

  return (
    <>
      <Row
        align="middle"
        justify="center"
        style={{ minHeight: "100vh", background: "#ebedef" }}
      >
        <Col
          xs={8}
          lg={8}
          xl={6}
          style={{
            display: "flex",
            justifyContent: "center",
            padding: "2em",
            borderRadius: "10px",
            border: "1px solid #c9c9c9",
            flexDirection: "column",
            backgroundColor: "#ffffff",
          }}
        >
          <Space direction="vertical" align="center" size="middle">
            <Image preview={false} src={Logo}></Image>
            {state === "logoff" ? (
              <Divider>
                Seu tempo de acesso expirou...
                <br />
                <NavLink to="/login">
                  <Button type="link">Realize o login novamente.</Button>
                </NavLink>
              </Divider>
            ) : (
              <Divider>
                Logout feito com sucesso!
                <br />
                <NavLink to="/login">
                  <Button type="link">Realizar Login novamente</Button>
                </NavLink>
              </Divider>
            )}
          </Space>
        </Col>
      </Row>
    </>
  );
};
