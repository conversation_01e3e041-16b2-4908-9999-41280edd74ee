import { useState, useEffect } from "react";
import { shallowEqual, useSelector } from "react-redux";
import { Col, Typography } from "antd";

import { Select } from "../../../components/Select";
import { setContractFieldState } from "../../../store/actions/contract-action";
import { dynamoGet } from "../../../service/apiDsmDynamo";

const STATE = process.env.REACT_APP_STAGE;

export const ConsumptionTypeFilter = ({ span }) => {
  const { contractTypes, selectedContract } = useSelector(
    (state) => state.contract,
    shallowEqual
  );
  const { Text } = Typography;

  const [loading, setLoading] = useState(false);

  useEffect(() => {
    getTypes();
  }, []);

  async function getTypes() {
    try {
      setLoading(true);

      let response = await dynamoGet(`${STATE}-crm-custom-fields`);
      let labels = [];

      if (response.length > 0) {
        const consumeTypes = response[0].deal.find((deal) =>
          deal.name.includes("Tipo de Consumo")
        );
        labels = consumeTypes.options.map((option, index) => {
          return {
            value: index + 1,
            label: option.label,
          };
        });
      }

      labels.sort((a, b) => a.label.localeCompare(b.label));
      labels.unshift({ value: 0, label: "Todos" });

      setContractFieldState({
        field: "contractTypes",
        value: labels,
      });

      setLoading(false);
    } catch (error) {
      console.log(error);
      setLoading(false);
    }
  }

  return (
    <Col span={span} style={{ alignSelf: "end" }}>
      <Text>Tipo de Consumo:</Text>
      <Select
        onChange={(value) =>
          setContractFieldState({ field: "selectedContract", value })
        }
        options={contractTypes}
        value={selectedContract}
        style={{ width: "100%" }}
        loading={loading}
      />
    </Col>
  );
};
