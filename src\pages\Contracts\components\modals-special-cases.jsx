import {
  <PERSON><PERSON>,
  Col,
  Modal,
  Row,
  Space,
  Form,
  Typography,
  Collapse,
  Input,
  Divider,
  message,
  Popconfirm,
  Table,
  Icon,
  Tooltip,
} from "antd";
import React, { useState } from "react";
import { shallowEqual, useSelector } from "react-redux";
import { SwapOutlined, LoadingOutlined } from "@ant-design/icons";
import * as contractController from "../../../controllers/contracts/contract-controller";
import { dsmApiProvider } from "../../../provider/dsm-api-provider";
import { dynamoPut } from "../../../service/apiDsmDynamo";

const validContractTypes = ["Anual", "Semestral", "Trimestral", "Mensal"];

export const ModalChangeContractHours = ({ contractId, contractData }) => {
  const { Text } = Typography;
  const [consumptionHours, setConsumptionHours] = useState([]);
  const [loading, setLoading] = useState(false);
  const [confirmDisable, setConfirmDisable] = useState(true);
  const currentContract = useSelector(
    (state) => state.contract.currentContract,
    shallowEqual
  );

  const [form] = Form.useForm();

  const [open, setOpen] = useState(false);

  const onClose = () => {
    setOpen(false);
  };

  const onConfirm = async () => {
    const dsmProvider = dsmApiProvider();
    const { previous_hours_, new_hours } = form.getFieldsValue();
    if (previous_hours_ === new_hours) {
      message.error("Não é possível alterar para a mesma quantidade de horas.");
      return;
    }

    const body = {
      previous_hours: previous_hours_,
      new_hours: new_hours,
      updated_by: localStorage.getItem("@dsm/username"),
      updated_consumption_hours: new Date().toISOString(),
      month: new Date().getMonth() + 1,
      year: new Date().getFullYear(),
      full_date: `${new Date().getMonth() + 1}-${new Date().getFullYear()}`,
      contract_id: currentContract.id,
      customer_id: currentContract.customer_id.toString(),
      itsm_id: currentContract.identifications.itsm_id.toString(),
    };

    try {
      await dsmProvider.post("/create", body, {
        headers: {
          "Content-Type": "application/json",
          DynamoDB: `${process.env.REACT_APP_STAGE}-consumption-hours`,
        },
      });
      message.success("Horas atualizadas com sucesso.");
      dynamoPut(
        `${process.env.REACT_APP_STAGE}-contracts`,
        currentContract.id,
        {
          hasChangedHours: 1,
        }
      );
      onClose();
    } catch (error) {
      console.error("error", error);
      message.error("Erro ao atualizar horas.");
    }
  };

  const handleOpenModal = async () => {
    setOpen(true);
    setLoading(true);
    await contractController.getContract(contractId);
    const consumptionHours = await contractController.getConsumptionHours(
      contractId
    );

    setConsumptionHours(
      consumptionHours.sort((a, b) =>
        b.updated_consumption_hours.localeCompare(a.updated_consumption_hours)
      )
    );
    setLoading(false);
  };

  return (
    <>
      <Tooltip
        title={
          validContractTypes.includes(contractData?.type_hours)
            ? ""
            : `Não é possível alterar horas para contratos do tipo ${contractData?.type_hours}`
        }
      >
        <Button
          type="text"
          style={{ height: "10px", padding: "0 10px" }}
          onClick={handleOpenModal}
          disabled={!validContractTypes.includes(contractData?.type_hours)}
        >
          <SwapOutlined />
        </Button>
      </Tooltip>
      <Modal
        closable={false}
        title="Upgrade/Downgrade de horas"
        open={open}
        footer={[
          <ModalFooter
            key="footer"
            onConfirm={onConfirm}
            onClose={onClose}
            confirmDisable={confirmDisable}
          />,
        ]}
      >
        {loading ? (
          <Row justify="center">
            <LoadingOutlined />
          </Row>
        ) : (
          <>
            <Space direction="vertical">
              <Text>{currentContract?.name}</Text>
              <Text>ITSM: {currentContract?.identifications?.itsm_id}</Text>
              <Text>DSM ID: {currentContract?.dsm_id}</Text>
            </Space>
            <Divider />
            <PreviousHours data={consumptionHours} />
            <Form form={form} layout="vertical">
              <Space align="center">
                <Form.Item
                  label="Horas atuais"
                  name="previous_hours_"
                  initialValue={
                    consumptionHours?.length > 0
                      ? consumptionHours[0].new_hours
                      : currentContract.total_hours
                  }
                >
                  <Input type="number" placeholder="Horas" disabled={true} />
                </Form.Item>

                <SwapOutlined />
                <Form.Item
                  required
                  label="Novas horas"
                  name="new_hours"
                  initialValue={
                    consumptionHours?.length > 0
                      ? consumptionHours[0].new_hours
                      : currentContract.total_hours
                  }
                >
                  <Input
                    type="number"
                    placeholder="Horas"
                    onChange={(e) => {
                      if (
                        parseInt(e.target.value) ===
                        parseInt(currentContract.total_hours)
                      ) {
                        setConfirmDisable(true);
                      } else {
                        setConfirmDisable(false);
                      }
                    }}
                  />
                </Form.Item>
              </Space>
            </Form>
          </>
        )}
      </Modal>
    </>
  );
};

const ModalFooter = ({ onConfirm, onClose, confirmDisable }) => {
  return (
    <Row justify="space-between">
      <Col>
        <Button onClick={onClose}>Cancelar</Button>
      </Col>
      <Col>
        <Popconfirm
          title={
            <div style={{ textAlign: "justify" }}>
              Ao confirmar, as horas serão atualizadas e, com isso, o cálculo do
              consumo
              <br />
              de horas a partir de agora será baseado na nova quantidade de
              horas.
              <br />
              Tem certeza que deseja confirmar?
            </div>
          }
          onConfirm={onConfirm}
          okText="Sim"
          cancelText="Não"
        >
          <Button type="primary" disabled={confirmDisable}>
            Confirmar
          </Button>
        </Popconfirm>
      </Col>
    </Row>
  );
};

const PreviousHours = ({ data }) => {
  const { Panel } = Collapse;
  const columns = [
    {
      title: "Data de atualização",
      dataIndex: "updated_consumption_hours",
      key: "updated_consumption_hours",
      align: "center",
      sorter: (a, b) =>
        new Date(a.updated_consumption_hours) -
        new Date(b.updated_consumption_hours),
      render: (date) => new Date(date).toLocaleDateString(),
    },
    {
      title: "Horas atuais",
      dataIndex: "new_hours",
      key: "new_hours",
      align: "center",
    },
    {
      title: "Horas anteriores",
      dataIndex: "previous_hours",
      key: "previous_hours",
      align: "center",
    },
    {
      title: "Autor da atualização",
      dataIndex: "updated_by",
      key: "updated_by",
      align: "center",
    },
  ];

  return (
    <Collapse
      bordered={false}
      style={{
        marginBottom: "20px",
        backgroundColor: "#008000",
        borderRadius: "2px",
        padding: "2px",
      }}
    >
      <Panel
        header="Histórico de horas"
        key="1"
        style={{ backgroundColor: "#fff" }}
      >
        {" "}
        <Table columns={columns} dataSource={data} />
      </Panel>
    </Collapse>
  );
};
