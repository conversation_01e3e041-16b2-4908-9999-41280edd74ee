import React, { createContext, useReducer } from "react";
import { ServicesReducer } from "../reducers/services-reducer";

const initialValue = {
  technical_proposal_services: [],
};

export const ServicesContext = createContext({
  servicesState: initialValue,
  setServicesState: () => {},
});

export const ServicesProvider = ({ children }) => {
  const [state, setServicesState] = useReducer(ServicesReducer, initialValue);

  return (
    <ServicesContext.Provider
      value={{
        servicesState: state,
        setServicesState: setServicesState,
      }}
    >
      {children}
    </ServicesContext.Provider>
  );
};
