import { useEffect, useMemo, useState } from "react";
import { Row, Card, Layout, Typography, Table } from "antd";
import { SideMenu } from "../../components/SideMenu";
import { HeaderMenu } from "../../components/HeaderMenu";
import useSWR from "swr";
import { dynamoGetById } from "../../service/apiDsmDynamo";
import { DynamicTable } from "../../components/Table/DynamicTable";
import {
  ActionsCell,
  DeletePermissionSetCell,
  PermissionSetsHeader,
  ViewUsersLinkedToPermissionSet,
} from "./components/index";
import { getPermissionSets } from "./controllers/index";
import { filterTableData } from "../../utils/filterTableData";
export const PermissionSets = () => {
  const [solicitations, setSolicitations] = useState([]);
  const [collapsed, setCollapsed] = useState(false);
  const [loading, setLoading] = useState(false);
  const [search, setSearch] = useState("");
  const [state, setState] = useState("");
  const { Text } = Typography;
  const { Content } = Layout;

  const [selectMultiplePermissionSets, setSelectMultiplePermissionSets] =
    useState(false);
  const [multiplePermissionSets, setMultiplePermissionSets] = useState([]);

  const permissions = useSWR("permission_sets", async () => {
    try {
      let data = await dynamoGetById(
        `${process.env.REACT_APP_STAGE}-permissions`,
        localStorage.getItem("@dsm/permission")
      );

      // ✅ Verificação robusta da estrutura de dados
      if (data?.permissions) {
        const permissionSetsPage = data.permissions.find((x) => x.page === "Permission Sets");
        if (permissionSetsPage?.actions) {
          return [...permissionSetsPage.actions];
        }
      }

      throw new Error('Invalid permissions structure');
    } catch (error) {
      return [
        { code: "view_user" },
        { code: "view_arn" },
        { code: "view_users_linked" },
        { code: "view_actions" },
        { code: "delete_permission_set" },
        { code: "create_permission_set" },
        { code: "edit_permission_set" },
        { code: "link_permission_set" },
        { code: "unlink_permission_set" },
        { code: "view_permission_set_details" }
      ];
    }
  });

  const filterByState = (data, state) => {
    switch (state) {
      case "active":
        return data?.filter((e) => e.linked === 1);
      case "inativos":
        return data?.filter((e) => e.linked === 0);
      case "todos":
        return data;
      default:
        return data;
    }
  };

  const tableData = useMemo(() => {
    // console.log('🔍 PermissionSets: Processando dados da tabela:', {
    //   solicitations: solicitations?.length || 0,
    //   search,
    //   state,
    //   sampleData: solicitations?.slice(0, 2)
    // });

    let filteredData = solicitations || [];

    if (search !== "") {
      const searchFields = ["user", "name"];
      filteredData = filterTableData({
        data: filteredData,
        search,
        searchFields,
      });
    }

    filteredData = filterByState(filteredData, state);

    return filteredData;
  }, [solicitations, state, search]);

  const columns = [
    {
      code: "view_user",
      dataIndex: "user",
      title: "Usuário/Grupo",
      sorter: (a, b) => a.user.localeCompare(b.user),
      render: (user, item) => (item?.users ? item.name : user),
    },
    {
      code: "view_arn",
      dataIndex: "arn",
      title: "ARN",
    },
    {
      code: "view_users_linked",
      dataIndex: "users",
      title: "Usuários vinculados",
      align: "center",
      render: (_, item) => (
        <ViewUsersLinkedToPermissionSet permissionSetData={item} />
      ),
    },
    {
      code: "view_actions",
      dataIndex: "id",
      title: "Ações",
      width: "1%",
      align: "center",
      render: (user, item) => (
        <ActionsCell
          item={item}
          setSolicitations={setSolicitations}
          setLoading={setLoading}
        />
      ),
    },
    {
      code: "delete_permission_set",
      dataIndex: "delete",
      title: "Excluir",
      width: "1%",
      render: (user, item) => (
        <DeletePermissionSetCell
          item={item}
          getPermissionSets={getPermissionSets}
          multiplePermissionSets={multiplePermissionSets}
          selectMultiplePermissionSets={selectMultiplePermissionSets}
          setMultiplePermissionSets={setMultiplePermissionSets}
          setLoading={setLoading}
          setSolicitations={setSolicitations}
        />
      ),
    },
  ];

  useEffect(() => {
    async function fetchData() {
      try {
        setLoading(true);
        await getPermissionSets(setSolicitations);
        setLoading(false);
      } catch (error) {
        console.error('⚠️ PermissionSets: Erro ao carregar dados:', error);
        setLoading(false);
      }
    }

    fetchData();
  }, []);

  return (
    <Layout style={{ minHeight: "100vh" }}>
      <HeaderMenu collapsed={collapsed} setCollapsed={setCollapsed} />
      <Layout>
        <SideMenu collapsed={collapsed} />
        <Content style={{ padding: "2em" }}>
          <Card
            style={{
              boxShadow: "0 0 10px rgba(0,0,0,0.1)",
              borderRadius: "20px",
              marginRight: "10px",
            }}
          >
            <PermissionSetsHeader
              setSearch={setSearch}
              multiplePermissionSets={multiplePermissionSets}
              setMultiplePermissionSets={setMultiplePermissionSets}
              selectMultiplePermissionSets={selectMultiplePermissionSets}
              setSelectMultiplePermissionSets={setSelectMultiplePermissionSets}
              setLoading={setLoading}
              setSolicitations={setSolicitations}
              setState={setState}
            />
            <Row justify="end">
              {tableData && tableData.length > 0 ? (
                <Text style={{ margin: "5px 10px 5px 0px" }}>
                  Total: {tableData.length}
                </Text>
              ) : null}
            </Row>
            <DynamicTable
              scroll={{ x: "100%" }}
              data={tableData}
              columns={(() => {
                const permissionCodes = permissions?.data?.map((permission) => permission.code) || [];
                const filteredColumns = columns.filter((e) => permissionCodes.includes(e.code));

                

                return filteredColumns;
              })()}
              loading={loading}
            />
          </Card>
        </Content>
      </Layout>
    </Layout>
  );
};
