import { useEffect, useState, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';
// ✅ authService removido - não é mais necessário para MFA

const setupMfaInterceptor = () => {
  axios.interceptors.request.eject(0);

  axios.interceptors.request.use(
    (config) => {
      const originalUrl = config.url;

      const frontendPatterns = [
        'dev.dsm.darede.com.br/mfa',
        'https://dev.dsm.darede.com.br/mfa',
        'http://dev.dsm.darede.com.br/mfa',
        '/mfa?code=',
        'mfa?code='
      ];

      let shouldRedirect = false;
      for (const pattern of frontendPatterns) {
        if (originalUrl && originalUrl.includes(pattern)) {
          shouldRedirect = true;
          break;
        }
      }

      if (shouldRedirect) {
        console.log('� INTERCEPTOR: Bloqueando requisição para frontend:', originalUrl);

        const apiBaseURL = process.env.REACT_APP_API_PERMISSION;

        if (originalUrl.includes('?code=')) {
          const codeMatch = originalUrl.match(/code=([^&]+)/);
          if (codeMatch) {
            config.url = `${apiBaseURL}/mfa?code=${codeMatch[1]}`;
            console.log('✅ INTERCEPTOR: URL redirecionada para:', config.url);
          }
        }
      }

      return config;
    },
    (error) => {
      return Promise.reject(error);
    }
  );
};

setupMfaInterceptor();

const detectValueType = (value) => {
  if (!value) return 'INVALID';

  if (value.startsWith('eyJ') && value.includes('.')) {
    console.log('🔐 DETECTADO: Token Cognito JWT');
    return 'COGNITO_TOKEN';
  }

  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
  if (uuidRegex.test(value)) {
    console.log('🔐 DETECTADO: Código MFA UUID');
    return 'MFA_CODE';
  }

  console.log('❌ DETECTADO: Valor inválido ou desconhecido');
  return 'INVALID';
};

export const useMfaCodeProcessor = () => {
  const navigate = useNavigate();
  const [mfaResult, setMfaResult] = useState(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState(null);
  const [authToken, setAuthToken] = useState(null);

  
  const processValue = useCallback(async (value) => {
    if (isProcessing) return;

    setIsProcessing(true);
    setError(null);

    try {
      const valueType = detectValueType(value);
      console.log('🔍 Tipo detectado:', valueType, 'para valor:', value);

      switch (valueType) {
        case 'COGNITO_TOKEN':
          console.log('🔐 PROCESSANDO: Token Cognito JWT');
          await processAuthenticationWithToken(value);
          break;

        case 'MFA_CODE':
          console.log('🔐 PROCESSANDO: Código MFA via API');
          await processMfaCodeViaApi(value);
          break;

        default:
          console.error('❌ Valor inválido ou desconhecido:', value);
          throw new Error('Valor de autenticação inválido');
      }
    } catch (error) {
      console.error('❌ Erro no processamento:', error);
      setError(error);
      throw error;
    } finally {
      setIsProcessing(false);
    }
  }, [isProcessing]);

  
  const processMfaCodeViaApi = useCallback(async (code) => {
    try {
      console.log('🔐 Iniciando processamento MFA via API');
      console.log('📡 Fazendo API call para /mfa (não para frontend)');

      const apiBaseURL = axios.defaults.baseURL || process.env.REACT_APP_API_PERMISSION;
      const mfaUrl = `${apiBaseURL}/mfa?code=${code}`;

      console.log('🎯 URL COMPLETA da API MFA:', mfaUrl);
      console.log('🔧 Axios baseURL configurado:', axios.defaults.baseURL);
      console.log('🚀 Fazendo requisição para API (não frontend)');

      const mfaResponse = await axios.get(`/mfa?code=${code}`, {
        timeout: 30000,
        withCredentials: true,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });

      console.log('✅ Resposta MFA recebida:', mfaResponse.data);
      console.log('🔍 Tipo da resposta:', typeof mfaResponse.data);
      console.log('🔍 Resposta exata:', JSON.stringify(mfaResponse.data));
      setMfaResult(mfaResponse.data);

      // ✅ VERIFICAÇÃO REMOVIDA: "Redirecionamento MFA" é resposta antiga
      // Nova resposta é "MFA processado com sucesso" (tratada abaixo)

      if (mfaResponse.data?.token) {
        console.log('🔐 Token retornado pela API MFA, processando...');
        await processAuthenticationWithToken(mfaResponse.data.token);
      } else {
        console.warn('⚠️ Resposta MFA inesperada:', mfaResponse.data);
        console.warn('⚠️ MFA deve ter definido cookies HttpOnly automaticamente');
        // ✅ NÃO CHAMAR authenticate() - MFA já processou autenticação
        console.log('✅ Assumindo autenticação via cookies HttpOnly');
      }
    } catch (error) {
      if (error.response?.status === 404) {
        console.warn('⚠️ Endpoint /mfa não encontrado');
        console.warn('⚠️ Assumindo que autenticação foi processada via cookies HttpOnly');
        // ✅ NÃO CHAMAR authenticate() - Pode causar loops ou erros
        console.log('✅ Continuando com fluxo normal...');
      } else {
        console.error('❌ Erro no processamento MFA:', error);
        throw error;
      }
    }
  }, []);

  const processMfaCode = useCallback(async (code) => {
    if (!code || code.length < 10) {
      throw new Error('Código MFA inválido');
    }

    if (isProcessing) {
      console.warn('Processamento MFA já em andamento...');
      return;
    }

    setIsProcessing(true);
    setError(null);

    try {
      console.log('🔐 Iniciando processamento MFA via API');
      console.log('📡 Fazendo API call para /mfa (não para frontend)');

      let retries = 0;
      const maxRetries = 10;
      while (!axios.defaults.baseURL && retries < maxRetries) {
        console.log(`⏳ Aguardando configuração do axios... (tentativa ${retries + 1}/${maxRetries})`);
        await new Promise(resolve => setTimeout(resolve, 200));
        retries++;
      }

      if (!axios.defaults.baseURL) {
        console.warn('⚠️ Axios ainda não configurado após aguardar, usando URL hardcoded');
      } else {
        console.log('✅ Axios configurado com sucesso:', axios.defaults.baseURL);
      }

      const apiBaseURL = axios.defaults.baseURL || process.env.REACT_APP_API_PERMISSION;
      const mfaUrl = `${apiBaseURL}/mfa?code=${code}`;

      console.log('🎯 URL COMPLETA da API MFA:', mfaUrl);
      console.log('🔧 Axios baseURL configurado:', axios.defaults.baseURL);
      console.log('🚀 Fazendo requisição para API (não frontend)');

      const mfaResponse = await axios.get(axios.defaults.baseURL ? `/mfa?code=${code}` : mfaUrl, {
        timeout: 30000,
        withCredentials: true,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });

      console.log('✅ Resposta MFA recebida:', mfaResponse.data);
      console.log('🔍 Tipo da resposta:', typeof mfaResponse.data);
      console.log('🔍 Resposta exata:', JSON.stringify(mfaResponse.data));
      setMfaResult(mfaResponse.data);

      if (mfaResponse.data?.success === true) {
        console.log('✅ Código MFA processado com sucesso');
        console.log('✅ Usuário autenticado via MFA');
        console.log('📊 Estado ANTES da atualização:', {
          localStorage: {
            name: localStorage.getItem('@dsm/name'),
            username: localStorage.getItem('@dsm/username'),
            mfaAuth: localStorage.getItem('mfa-authenticated')
          },
          cookies: document.cookie
        });

        try {
          // ✅ USAR DADOS DO USUÁRIO QUE JÁ VÊEM NA RESPOSTA MFA
          console.log('mfaResponse..  ', mfaResponse);
          const userData = mfaResponse.data.user;
          console.log('👤 Dados do usuário obtidos da resposta MFA:', userData);

          if (userData) {
            // ✅ SALVAR NO FORMATO ESPERADO PELO SISTEMA PRINCIPAL (SEM TOKEN)
            localStorage.setItem('@dsm/name', userData.name || userData.email || 'Usuário MFA');
            localStorage.setItem('@dsm/username', userData.username || userData.email || '<EMAIL>');
            localStorage.setItem('@dsm/email', userData.email || '<EMAIL>');
            localStorage.setItem('@dsm/permission', userData.permission || userData.role || 'user');
            // ✅ TOKEN NÃO É SALVO NO LOCALSTORAGE - É HTTPONLY COOKIE

            // ✅ MANTER COMPATIBILIDADE COM MFA
            localStorage.setItem('mfa-authenticated', 'true');
            localStorage.setItem('auth-user', JSON.stringify({
              id: userData.id || 'mfa-user',
              email: userData.email || '<EMAIL>',
              name: userData.name || userData.email || 'Usuário MFA',
              mfa_verified: true,
              authenticated: true,
              permission: userData.permission || userData.role || 'user'
            }));

            console.log('✅ Dados salvos no formato correto do sistema');
          } else {
            console.warn('⚠️ Dados do usuário não encontrados, usando fallback');

            // ✅ FALLBACK COM DADOS MÍNIMOS (SEM TOKEN)
            localStorage.setItem('@dsm/name', 'Usuário MFA');
            localStorage.setItem('@dsm/username', '<EMAIL>');
            localStorage.setItem('@dsm/email', '<EMAIL>');
            localStorage.setItem('@dsm/permission', 'user');
            localStorage.setItem('mfa-authenticated', 'true');
            // ✅ TOKEN NÃO É SALVO NO LOCALSTORAGE - É HTTPONLY COOKIE
          }

        } catch (error) {
          console.error('❌ Erro ao processar dados do usuário:', error);
          console.log('🔄 Usando dados de fallback...');

          // ✅ FALLBACK EM CASO DE ERRO (SEM TOKEN)
          localStorage.setItem('@dsm/name', 'Usuário MFA');
          localStorage.setItem('@dsm/username', '<EMAIL>');
          localStorage.setItem('@dsm/email', '<EMAIL>');
          localStorage.setItem('@dsm/permission', 'user');
          localStorage.setItem('mfa-authenticated', 'true');
          // ✅ TOKEN NÃO É SALVO NO LOCALSTORAGE - É HTTPONLY COOKIE

          // ✅ ATUALIZAR ESTADO MESMO COM FALLBACK
          console.log('🔄 Atualizando estado com dados de fallback...');
          const authEvent = new CustomEvent('authStateChanged', {
            detail: {
              authenticated: true,
              user: { id: 'mfa-user', email: '<EMAIL>' },
              method: 'mfa-fallback'
            }
          });
          window.dispatchEvent(authEvent);

          // Aguardar processamento
          await new Promise(resolve => setTimeout(resolve, 100));
        }

        // ✅ ATUALIZAR ESTADO DE AUTENTICAÇÃO
        console.log('🔄 Atualizando estado de autenticação...');

        // ✅ DEFINIR userData DA RESPOSTA MFA
        const userData = mfaResponse.data.user || { id: 'mfa-user', email: '<EMAIL>' };

        // Disparar evento customizado para notificar componentes sobre mudança de autenticação
        const authEvent = new CustomEvent('authStateChanged', {
          detail: {
            authenticated: true,
            user: userData,
            method: 'mfa'
          }
        });
        window.dispatchEvent(authEvent);

        // ✅ ORDEM CORRETA PARA EVITAR LOOP INFINITO
        console.log('🧹 Limpando URL antes de qualquer redirecionamento...');

        // 1. PRIMEIRO: Limpar código da URL
        const newUrl = new URL(window.location);
        newUrl.searchParams.delete('code');
        window.history.replaceState({}, '', newUrl);

        // 2. AGUARDAR para garantir que a URL foi atualizada
        await new Promise(resolve => setTimeout(resolve, 500));

        // 3. LOGS FINAIS
        console.log('� Estado atual do localStorage:', {
          name: localStorage.getItem('@dsm/name'),
          username: localStorage.getItem('@dsm/username'),
          email: localStorage.getItem('@dsm/email'),
          permission: localStorage.getItem('@dsm/permission'),
          mfaAuthenticated: localStorage.getItem('mfa-authenticated')
        });

        // 4. DEPOIS: Redirecionar para home (SEM reload para evitar loop)
        console.log('🏠 Redirecionando para home após MFA bem-sucedido...');
        window.location.href = '/';

        return;
      }

      if (mfaResponse.data?.token) {
        console.log('🔐 Token retornado pela API MFA, processando...');
        await processAuthenticationWithToken(mfaResponse.data.token);
      } else {
        console.warn('⚠️ Resposta MFA inesperada:', mfaResponse.data);
        console.warn('⚠️ MFA deve ter definido cookies HttpOnly automaticamente');
        // ✅ NÃO CHAMAR authenticate() - MFA já processou autenticação
        console.log('✅ Assumindo autenticação via cookies HttpOnly');
      }

    } catch (error) {
      console.error('❌ Erro no processamento MFA:', error);

      if (error.response?.status === 404) {
        console.warn('⚠️ Endpoint /mfa não encontrado');
        console.warn('⚠️ Assumindo que autenticação foi processada via cookies HttpOnly');
        // ✅ NÃO CHAMAR authenticate() - Pode causar loops ou erros
        console.log('✅ Continuando com fluxo normal...');
      } else {
        setError(error.message);
        throw error;
      }
    } finally {
      setIsProcessing(false);
    }
  }, [isProcessing]);

  /**
   * Processar autenticação com token do MFA
   */
  const processAuthenticationWithToken = useCallback(async (token) => {
    try {
      console.log('� Enviando token para /auth/set-token');

      const apiBaseURL = process.env.REACT_APP_API_PERMISSION;
      const authUrl = `${apiBaseURL}/auth/set-token`;
      console.log('🎯 URL COMPLETA da API Auth:', authUrl);

      const authResponse = await axios.post(authUrl, {
        token,
        source: 'mfa'
      }, {
        timeout: 30000,
        withCredentials: true,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });

      console.log('✅ Token enviado com sucesso:', authResponse.data);
      setAuthToken(token);

      if (authResponse.data.success) {
        navigate('/');
      } else {
        throw new Error('Falha na autenticação com token');
      }

    } catch (error) {
      console.error('❌ Erro ao enviar token:', error);
      throw error;
    }
  }, [navigate]);

  // ✅ FUNÇÃO REMOVIDA: processAuthenticationWithCognito
  // Não é mais necessária - MFA processa autenticação diretamente via cookies HttpOnly

  
  useEffect(() => {
    const processValueFromUrl = async () => {
      const urlParams = new URLSearchParams(window.location.search);
      const authValue = urlParams.get('code');

      if (authValue && !isProcessing && !mfaResult) {
        console.log('🔐 Valor de autenticação detectado na URL:', authValue);

        if (!axios.defaults.baseURL) {
          console.log('⏳ Aguardando configuração da API...');
          console.log('� Axios baseURL atual:', axios.defaults.baseURL);
          return; 
        }

        console.log('✅ API configurada, processando valor...');
        console.log('🔧 Axios baseURL configurado:', axios.defaults.baseURL);
        console.log('�🚫 INTERCEPTANDO navegação para /mfa - processando via API');

        const valueType = detectValueType(authValue);
        console.log('🔍 Tipo detectado na URL:', valueType);

        const newUrl = new URL(window.location);
        newUrl.searchParams.delete('code');
        newUrl.searchParams.delete('state');
        window.history.replaceState({}, '', newUrl.pathname);

        try {
          await processValue(authValue);
        } catch (error) {
          console.error('❌ Erro no processamento automático:', error);
        }
      }
    };

    processValueFromUrl();
  }, [processValue, isProcessing, mfaResult, axios.defaults.baseURL]);

  const clearUrlAfterProcessing = useCallback(() => {
    const newUrl = new URL(window.location);
    newUrl.searchParams.delete('code');
    newUrl.searchParams.delete('state');
    window.history.replaceState({}, '', newUrl.pathname);
    console.log('🧹 URL limpa após processamento MFA');
  }, []);

  return {
    mfaResult,
    isProcessing,
    error,
    authToken,
    processMfaCode,
    processValue,
    detectValueType,
    clearUrlAfterProcessing 
  };
};

export const AuthenticationHandler = () => {
  const { mfaResult, isProcessing, error } = useMfaCodeProcessor();
  const [authStatus, setAuthStatus] = useState('checking');

  useEffect(() => {
    const checkAuthentication = async () => {
      try {
        console.log('🔍 Verificando status de autenticação...');

        const response = await axios.get('/auth/verify', {
          withCredentials: true 
        });

        if (response.data.authenticated) {
          console.log('✅ Usuário autenticado:', response.data.user);
          setAuthStatus('authenticated');
        } else {
          console.log('❌ Usuário não autenticado');
          setAuthStatus('unauthenticated');
        }

      } catch (error) {
        console.error('❌ Erro na verificação de autenticação:', error);
        setAuthStatus('unauthenticated');
      }
    };

    if (!isProcessing) {
      checkAuthentication();
    }
  }, [isProcessing, mfaResult]);

  useEffect(() => {
    if (mfaResult) {
      console.log('🔐 Processando resultado do MFA:', mfaResult);

      if (mfaResult.success) {
        setAuthStatus('checking');
      }
    }
  }, [mfaResult]);

  if (isProcessing) {
    return (
      <div className="auth-processing">
        <h2>🔐 Processando autenticação MFA...</h2>
        <p>Aguarde enquanto verificamos seu código de autenticação.</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="auth-error">
        <h2>❌ Erro na autenticação MFA</h2>
        <p>Erro: {error}</p>
        <button onClick={() => window.location.href = '/login'}>
          Tentar novamente
        </button>
      </div>
    );
  }

  if (authStatus === 'checking') {
    return (
      <div className="auth-checking">
        <h2>🔍 Verificando autenticação...</h2>
        <p>Aguarde...</p>
      </div>
    );
  }

  if (authStatus === 'unauthenticated') {
    return (
      <div className="auth-required">
        <h2>🔐 Autenticação necessária</h2>
        <p>Você precisa fazer login para acessar esta página.</p>
        <button onClick={() => window.location.href = '/login'}>
          Fazer Login
        </button>
      </div>
    );
  }

  return null; 
};


export const authServiceCorrected = {
  async processMfaCode(code) {
    try {
      console.log('🔐 Processando código MFA via API:', code);

      const response = await axios.get(`/mfa?code=${code}`, {
        withCredentials: true 
      });

      console.log('✅ Código MFA processado:', response.data);
      return response.data;

    } catch (error) {
      console.error('❌ Erro ao processar código MFA:', error);
      throw error;
    }
  },

  /**
   * Verifica status de autenticação
   */
  async checkAuthStatus() {
    try {
      const response = await axios.get('/auth/verify', {
        withCredentials: true 
      });
      return response.data;
    } catch (error) {
      console.error('❌ Erro na verificação de autenticação:', error);
      return { authenticated: false, error: error.message };
    }
  },

  /**
   * Faz login com token Cognito
   */
  async loginWithCognitoToken(token) {
    try {
      console.log('🔐 Fazendo login com token Cognito...');

      const response = await axios.post('/auth/set-token', { token }, {
        withCredentials: true 
      });

      console.log('✅ Login realizado com sucesso');
      return response.data;

    } catch (error) {
      console.error('❌ Erro no login:', error);
      throw error;
    }
  }
};


export const dynamicApiConfigCorrected = {
  async initialize() {
    try {
      console.log('🚀 Inicializando configuração dinâmica da API...');

      const response = await axios.get('/auth/config', {
        // REMOVIDO: withCredentials para evitar CORS
      });
      const config = response.data;

      console.log('✅ Configuração da API obtida:', config);

      axios.defaults.baseURL = config.instructions.axios.baseURL;
      // REMOVIDO: withCredentials para evitar CORS

      console.log('✅ Axios configurado:', {
        baseURL: axios.defaults.baseURL,
        withCredentials: axios.defaults.withCredentials
      });

      return config;

    } catch (error) {
      console.error('❌ Erro na configuração dinâmica:', error);
      throw error;
    }
  }
};

export default {
  useMfaCodeProcessor,
  AuthenticationHandler,
  authService: authServiceCorrected,
  dynamicApiConfig: dynamicApiConfigCorrected
};
