import { useState } from "react";

import {
  Row,
  Col,
  Button,
} from "antd";

import {
  SettingOutlined,
} from "@ant-design/icons";

import { ConfigModal } from "../../../components/Modals/SuruplusHours/ConfigModal";
import { useToggle } from "../../../hooks/useToggle";

export const ConfigPage = () => { 

  const [isOpen, toggle] = useToggle();

    return (
      <Row style={{ justifyContent: "flex-end" }}>
        <Col
          xl={{ span: 1 }}
          lg={{ span: 1 }}
          md={{ span: 1 }}
          sm={{ span: 2 }}
          style={{ marginBottom: 5 }}
        >
          <Button
            style={{ width: "100%" }}
            icon={ <SettingOutlined />}
            onClick={toggle}
          />
  
        </Col>

        {isOpen ? (
        <ConfigModal
          isOpen={isOpen}
          toggle={toggle}
        />
      ) : null}
  
      </Row>
    );
  };