import Axios from "axios";

export const portalGet = async (route) => {
  
  const { data } = await Axios.get(
    process.env.REACT_APP_API_CUSTOMER_PORTAL + route
  );

  return data;
};

export const portalPost = async (route, body) => {
  
  const { data } = await Axios.post(
    process.env.REACT_APP_API_CUSTOMER_PORTAL + route,
    body
  );

  return data;
};

export const portalPut = async (route, body) => {
  
  const { data } = await Axios.put(
    process.env.REACT_APP_API_CUSTOMER_PORTAL + route,
    body
  );

  return data;
};

export const portalDelete = async (route, body) => {
  
  const { data } = await Axios.delete(
    process.env.REACT_APP_API_CUSTOMER_PORTAL + route,
    body
  );

  return data;
};
