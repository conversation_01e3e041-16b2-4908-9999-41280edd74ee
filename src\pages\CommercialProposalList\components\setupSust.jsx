import { Col, Row, Typography } from "antd";
import { dynamoGetById } from "../../../service/apiDsmDynamo";
import { useState, useEffect } from "react";
import { LoadingOutlined } from "@ant-design/icons";
import { getProposalValues } from "../../../controllers/Proposals/newGetProposalValues";
export const SetupSust = ({ item, index }) => {
  const [loading, setLoading] = useState(false);
  const [setupValue, setSetupValue] = useState(0);
  const [sustValue, setSustValue] = useState(0);
  const { Text } = Typography;

  const handleFormatValues = async (item) => {
    setLoading(true);
    const proposal = await dynamoGetById(
      `${process.env.REACT_APP_STAGE}-proposals`,
      item.id
    );
    let setupValue = getProposalValues(proposal, "setup", proposal?.spread);
    let sustValue = getProposalValues(proposal, "sust", proposal?.spread);
    setSetupValue(setupValue);
    setSustValue(sustValue);
    setLoading(false);
    return { setupValue, sustValue };
  };

  useEffect(() => {
    handleFormatValues(item);
  }, [item]);

  return (
    <Row>
      <Col xs={18} sm={18} md={18} lg={18} xl={12}>
        <Row justify="start">
          <Text style={{ fontSize: ".75rem" }}>SETUP: </Text>
        </Row>
        <Row justify="start">
          <Text style={{ fontSize: ".75rem" }}>SUST:</Text>
        </Row>
      </Col>
      <Col xs={4} sm={4} md={4} lg={4} xl={12}>
        <Row justify="start">
          <Text style={{ fontSize: ".75rem" }}>
            {loading ? <LoadingOutlined /> : setupValue}
          </Text>
        </Row>
        <Row justify="start">
          <Text style={{ fontSize: ".75rem" }}>
            {loading ? <LoadingOutlined /> : sustValue}
          </Text>
        </Row>
      </Col>
    </Row>
  );
};
