import { filterTableData } from "../../../utils/filterTableData";
import { tableData } from "../../../__mocks__/utils/data";

describe("filterTableData", () => {
  it("should return all data if search is empty", () => {
    const result = filterTableData({
      searchFields: ["name"],
      search: "",
      data: tableData,
    });
    expect(result).toEqual(tableData);
  });
  it("should return filtered data if search is not empty searching for one parameter only", () => {
    const result = filterTableData({
      searchFields: ["name"],
      search: "<PERSON>",
      data: tableData,
    });
    expect(result).toEqual([
      {
        name: "<PERSON>",
        description:
          "Lorem ipsum dolor sit amet, consectetur adipiscing user1.",
        profession: "Software Engineer",
        tag: "tag1",
      },
      {
        name: "<PERSON>",
        description:
          "Lorem ipsum dolor sit amet, consectetur adipiscing user3.",
        profession: "Software Engineer",
        tag: "tag3",
      },
    ]);
  });
  it("should return filtered data if search is not empty searching for multiple parameters", () => {
    const result = filterTableData({
      searchFields: ["name", "tag"],
      search: "tag2",
      data: tableData,
    });
    expect(result).toEqual([
      {
        name: "Jane Doe",
        description:
          "Lorem ipsum dolor sit amet, consectetur adipiscing user2.",
        profession: "Software Engineer",
        tag: "tag2",
      },
    ]);
  });
  it("should return empty array if search is not empty but no data matches", () => {
    const result = filterTableData({
      searchFields: ["name"],
      search: "No match",
      data: tableData,
    });
    expect(result).toEqual([]);
  });
  it("should return an empty array for not finding the specified search field", () => {
    const result = filterTableData({
      searchFields: ["does not exist"],
    });
    expect(result).toEqual([]);
  });
  it("should return an empty array for not specifying any search fields ", () => {
    const result = filterTableData({
      searchFields: [],
    });
    expect(result).toEqual([]);
  });
  it("should return an empty array for specifying a search field as being a string ", () => {
    const result = filterTableData({
      searchFields: "",
    });
    expect(result).toEqual([]);
  });
  it("should return an empty array for specifying a search field as being undefined ", () => {
    const result = filterTableData({
      searchFields: undefined,
    });
    expect(result).toEqual([]);
  });
  it("should return an empty array for specifying a search field as being null ", () => {
    const result = filterTableData({
      searchFields: null,
    });
    expect(result).toEqual([]);
  });
  it("should return an empty array for specifying a search parameter as being undefined ", () => {
    const result = filterTableData({
      search: undefined,
    });
    expect(result).toEqual([]);
  });
  it("should return an empty array for specifying a search parameter as being null ", () => {
    const result = filterTableData({
      search: null,
    });
    expect(result).toEqual([]);
  });
  it("should return an empty array for specifying a search parameter as being an array ", () => {
    const result = filterTableData({
      search: ["test", "test2"],
    });
    expect(result).toEqual([]);
  });
  it("should return an empty array for specifying an invalid data type (null)", () => {
    const result = filterTableData({
      data: null,
    });
    expect(result).toEqual([]);
  });
  it("should return an empty array for specifying an invalid data type (undefined)", () => {
    const result = filterTableData({
      data: undefined,
    });
    expect(result).toEqual([]);
  });
});
