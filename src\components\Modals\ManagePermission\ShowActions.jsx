import React, { useState } from "react";
import { <PERSON><PERSON>, But<PERSON>, List } from "antd";

export const ShowActions = (props) => {
  const [show, setShow] = useState(false);
  const [modalIndex, setModalIndex] = useState([]);
  const {
    pageIndex,
    page: { page },
    permissions,
  } = props;

  const showModal = () => {
    setShow(true);
  };

  const handleOk = () => {
    setShow(false);
  };

  const handleCancel = () => {
    setShow(false);
  };

  return (
    <>
      <Button
        type="link"
        size="small"
        style={{ marginLeft: "10px" }}
        key={pageIndex}
        onClick={() => {
          showModal();
          setModalIndex(pageIndex);
        }}
      >
        {page}
      </Button>
      <Modal
        title="Funcionalidades com acesso na página"
        open={show}
        onOk={handleOk}
        onCancel={handleCancel}
        cancelText="Cancelar"
      >
        <List
          pagination={{ pageSize: 10 }}
          renderItem={(item) => <List.Item>{item}</List.Item>}
          dataSource={permissions[modalIndex]?.actions.map(({ action }) => {
            return action;
          })}
        />
      </Modal>
    </>
  );
};
