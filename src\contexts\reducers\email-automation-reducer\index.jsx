export const EmailAutomationReducerType = {
    SET_HEADER: 'SET_HEADER',
    SET_FOOTER: 'SET_FOOTER',
    SET_TEMPLATE_LIST: 'SET_TEMPLATE_LIST',
}

export function EmailAutomationReducer(state, action) {
    switch (action.type) {
        case EmailAutomationReducerType.SET_HEADER:
            if (action.value !== null)
                return { ...state, showHeader: action.value };
            else
                return state
        case EmailAutomationReducerType.SET_FOOTER:
            if (action.value !== null)
                return { ...state, showFooter: action.value };
            else
                return state
        case EmailAutomationReducerType.SET_TEMPLATE_LIST:
            if (action.value !== null)
                return { ...state, templateList: action.value };
            else
                return state
        default:
            return state
    }
}