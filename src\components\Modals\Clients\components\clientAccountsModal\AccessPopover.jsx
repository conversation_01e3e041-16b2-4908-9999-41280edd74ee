import { Col, Form, Popover, Row, Skeleton, Button, Input } from "antd";
import axios from "axios";
import { CreateTicket } from "./CreateTicket";
import { useState } from "react";

export const AccessPopover = ({
  permissionSets,
  setHasAccess,
  hasAccess,
  loadingAccess,
  loadingAccount,
  setSolicitations,
  setLoadingAccount,
  setAccountID,
  account_id,
  requestAccountAccess,
  item,
  currentUserAccesses,
  client,
}) => {
  const [switchForm] = Form.useForm();
  const [ticketNumber, setTicketNumber] = useState("");
  const [popoverZIndex, setPopoverZIndex] = useState(2000);

  const handleTicketNumberUpdate = (ticket) => {
    setTicketNumber(ticket);
    switchForm.setFieldsValue({
      ticket_id: ticket,
    });
  };

  return (
    <Popover
      zIndex={popoverZIndex}
      content={
        <Skeleton active loading={loadingAccount} style={{ width: "395px" }}>
          {hasAccess === "not_yet" ? (
            "Seu acesso já foi solicitado e está sendo analisado. Por favor, aguarde."
          ) : hasAccess === "exceeded" ? (
            "Você já possui 10 acessos ativos. Por favor, realize o desligamento de algum acesso para solicitar um novo."
          ) : hasAccess === "true" ? (
            <a
              rel="noreferrer"
              style={{ color: "#0f9347" }}
              target="_blank"
              href={`https://signin.aws.amazon.com/switchrole?account=${account_id}&roleName=${
                permissionSets.data.find(
                  (p) => p.user === localStorage.getItem("@dsm/name")
                )
                  ? "darede-full"
                  : localStorage.getItem("@dsm/name").replace(".", "-") +
                    "-darede"
              }&displayName=${item.profile}`}
            >
              Clique aqui para entrar na conta
            </a>
          ) : hasAccess === "expired" || hasAccess === "none" ? (
            <Form onFinish={requestAccountAccess} form={switchForm}>
              <Row align="center" gutter={12} justify="center">
                <Col>
                  <Form.Item
                    rules={[
                      {
                        required: true,
                        message: "Preencha este campo para solicitar o acesso",
                      },
                    ]}
                    style={{ marginBottom: "1em" }}
                    name="ticket_id"
                  >
                    <Input
                      placeholder="Número do Ticket"
                      value={ticketNumber}
                    />
                  </Form.Item>
                </Col>
                <Col>
                  <Form.Item
                    rules={[
                      {
                        required: true,
                        message: "Preencha este campo para solicitar o acesso",
                      },
                    ]}
                    style={{ marginBottom: "1em" }}
                    name="time"
                  >
                    <Input
                      placeholder="Tempo (em horas)"
                      type="number"
                      min="1"
                    />
                  </Form.Item>
                </Col>
              </Row>

              <Row justify="space-between">
                <Col>
                  <CreateTicket
                    client={client}
                    setTicketNumber={handleTicketNumberUpdate}
                    accounts={[item.account_id]}
                    setPopoverZIndex={setPopoverZIndex}
                  />
                </Col>

                <Col>
                  <Button
                    loading={loadingAccess}
                    type="primary"
                    htmlType="submit"
                  >
                    Solicitar Acesso
                  </Button>
                </Col>
              </Row>
            </Form>
          ) : (
            ""
          )}
        </Skeleton>
      }
      title={
        loadingAccount ? (
          <Skeleton.Button block active style={{ width: "395px" }} />
        ) : hasAccess === "not_yet" || hasAccess === "exceeded" ? (
          ""
        ) : hasAccess === "expired" ? (
          "Seu acesso expirou, solicite um novo acesso."
        ) : hasAccess === "true" ? (
          "Seu acesso foi liberado!"
        ) : (
          "Preencha as informações para solicitar acesso."
        )
      }
      trigger="click"
    >
      <Button
        type="link"
        onClick={async () => {
          setLoadingAccount(true);
          await axios
            .get(
              process.env.REACT_APP_API_PERMISSION +
                "read/switch-role-exists/" +
                localStorage.getItem("@dsm/username") +
                "/" +
                account_id,
              {
                headers: {
                  Authorization: localStorage.getItem("jwt"),
                },
              }
            )
            .then((res) => {
              let { Items } = res.data.data;
              setSolicitations([]);
              setHasAccess("none");
              setAccountID([account_id]);
              if (Items.length > 0) {
                Items.some((item) => {
                  if (item.active === "Aprovado") {
                    setHasAccess("true");
                    setSolicitations(item);
                    return true;
                  } else if (item.active === "Solicitado") {
                    setHasAccess("not_yet");
                    setSolicitations(item);
                    return true;
                  } else if (item.allowed === false) {
                    setSolicitations(item);
                    return true;
                  } else {
                    setHasAccess("none");
                    setSolicitations([]);
                    return false;
                  }
                });
              } else {
                if (currentUserAccesses.length >= 10) {
                  setHasAccess("exceeded");
                } else {
                  setHasAccess("none");
                }
              }

              setLoadingAccount(false);
            });
        }}
        style={{ color: "#0f9347" }}
      >
        {account_id}
      </Button>
    </Popover>
  );
};
