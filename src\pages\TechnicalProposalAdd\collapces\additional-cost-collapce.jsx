import { <PERSON><PERSON>, Card, Col, Form, Input, Row, Select, Popconfirm } from "antd";
import { DeleteOutlined, PlusOutlined } from "@ant-design/icons";
import { invalidInputNumberChars } from "../../../constants/invalidCharsInputNumber";
import { useSelector, shallowEqual } from "react-redux";
import { MainCollapse } from "../../../components/Collapse/CommercialProposal";
import {
  setTechnicalProposalState,
  setAdditionalCostsItemTechnicalProposalState,
} from "../../../store/actions/technical-proposal-action";
import { store } from "../../../store/store";
import { realMask } from "../../../utils/masks";

export const AdditionalCostCollapce = () => {
  const additionalCosts = useSelector(
    (state) => state.technicalProposal.additional_costs,
    shallowEqual
  );

  const ButtonAddAdditionalCost = () => {
    const item = {
      title: "",
      month_value: "",
      year_value: "",
    };

    let newAdditionalCosts = [];

    if (additionalCosts) {
      const newAdditionalCostsStr = JSON.stringify(additionalCosts);
      newAdditionalCosts = JSON.parse(newAdditionalCostsStr);
    }

    setTechnicalProposalState({
      field: "additional_costs",
      value: [...newAdditionalCosts, item],
    });
  };

  return (
    <Row>
      <Col span={24} key={0}>
        <MainCollapse
          title="Custos adicionais"
          editor={
            <Form.List name="aditional_costs">
              {(fields, { add, remove }) => (
                <>
                  {additionalCosts !== undefined
                    ? additionalCosts.map((value, index) => (
                        <Form.Item required={false} key={index}>
                          <AdditionalCostsItem index={index} data={value} />
                        </Form.Item>
                      ))
                    : null}
                  <Button type="text" onClick={() => ButtonAddAdditionalCost()}>
                    Adicionar Custo extra
                    <PlusOutlined />
                  </Button>
                </>
              )}
            </Form.List>
          }
        ></MainCollapse>
      </Col>
    </Row>
  );
};

const AdditionalCostsItem = ({ index, data }) => {
  const { Option } = Select;

  function handleRemoveItem() {
    const { additional_costs } = store.getState().technicalProposal;
    const newAdditionalCostsStr = JSON.stringify(additional_costs);
    const newAdditionalCosts = JSON.parse(newAdditionalCostsStr);

    newAdditionalCosts.splice(index, 1);

    setTechnicalProposalState({
      field: "additional_costs",
      value: newAdditionalCosts,
    });
  }

  const selectAfter = (
    <Select
      defaultValue="dolar"
      onSelect={(e) => {
        console.log({ e });
      }}
    >
      <Option value="dolar">$</Option>
      <Option value="reais">R$</Option>
    </Select>
  );

  return (
    <>
      <Row gutter={24}>
        <Col span={24}>
          <Card
            id="additional-costs"
            style={{
              backgroundColor: "#F6F6F6",
              boxShadow: "0 0 5px rgba(0,0,0,0.1)",
              borderRadius: "5px",
              marginRight: "10px",
            }}
          >
            <Row gutter={24}>
              <Col span={24}>
                <Form.Item label="Título" required={true}>
                  <Input
                    value={data.title}
                    onChange={(e) => {
                      setAdditionalCostsItemTechnicalProposalState({
                        index: index,
                        value: {
                          ...data,
                          title: e.currentTarget.value,
                        },
                      });
                    }}
                  />
                </Form.Item>
              </Col>
              <Col lg={12} md={24}>
                <Form.Item label="Valor mensal" required={true}>
                  <Input
                    value={data.month_value}
                    onKeyDown={(e) => {
                      invalidInputNumberChars.find(
                        (keyPressed) => keyPressed === e.key
                      ) && e.preventDefault();
                    }}
                    addonAfter={
                      <Select
                        defaultValue={
                          data.month_unit ? data.month_unit : "dolar"
                        }
                        onSelect={(e) => {
                          setAdditionalCostsItemTechnicalProposalState({
                            index: index,
                            value: {
                              ...data,
                              month_unit: e,
                            },
                          });
                        }}
                      >
                        <Option value="dolar">$</Option>
                        <Option value="reais">R$</Option>
                      </Select>
                    }
                    type="text"
                    maxLength={14}
                    onChange={(e) => {
                      setAdditionalCostsItemTechnicalProposalState({
                        index: index,
                        value: {
                          ...data,
                          month_value: realMask(e.currentTarget.value),
                        },
                      });
                    }}
                  />
                </Form.Item>
              </Col>
              <Col lg={12} md={24}>
                <Form.Item label="Valor anual" required={true}>
                  <Input
                    value={data.year_value}
                    onKeyDown={(e) => {
                      invalidInputNumberChars.find(
                        (keyPressed) => keyPressed === e.key
                      ) && e.preventDefault();
                    }}
                    addonAfter={
                      <Select
                        defaultValue={data.year_unit ? data.year_unit : "dolar"}
                        onSelect={(e) => {
                          setAdditionalCostsItemTechnicalProposalState({
                            index: index,
                            value: {
                              ...data,
                              year_unit: e,
                            },
                          });
                        }}
                      >
                        <Option value="dolar">$</Option>
                        <Option value="reais">R$</Option>
                      </Select>
                    }
                    type="text"
                    maxLength={14}
                    onChange={(e) => {
                      setAdditionalCostsItemTechnicalProposalState({
                        index: index,
                        value: {
                          ...data,
                          year_value: realMask(e.currentTarget.value),
                        },
                      });
                    }}
                  />
                </Form.Item>
              </Col>
            </Row>
            <Row justify="end">
              <Col>
                <Popconfirm
                  title="Deseja realmente remover este item?"
                  onConfirm={handleRemoveItem}
                  okText="Sim"
                  cancelText="Não"
                >
                  <Button danger type="text">
                    Remover item
                    <DeleteOutlined />
                  </Button>
                </Popconfirm>
              </Col>
            </Row>
          </Card>
        </Col>
      </Row>
    </>
  );
};
