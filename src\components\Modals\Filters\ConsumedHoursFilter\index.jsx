import { React, useEffect, useState } from 'react'
import { Checkbox, Select, DatePicker, Button, Form } from 'antd'
import { CloseSquareFilled } from '@ant-design/icons'

function ConsumedHoursFilter(props) {
    const {Option} = Select;
    const [form] = Form.useForm();

    function onFilterFinish(values) {
        props.filterIssues(values)
        props.handleVisibleChange()
    }

    function onFilterFailed() {}

    function clear() {
        form.resetFields();
    }

    useEffect(() => {}, [])

    return (
        <Form
            form={form}
            style={{ display: 'flex', flexDirection: 'column' }}
            name="basic"
            onFinish={onFilterFinish}
            onFinishFailed={onFilterFailed}
            autoComplete="off">
             <div style={{ display: 'flex',  justifyContent: 'space-between' }}>
                <h2>Horas por mês</h2>
                <button className='filter-close-btn'>X</button>
            </div>
            <Form.Item name="contract_type">
                <Select
                    showSearch
                    style={{ width: '100%' }}
                    placeholder="Carteira"
                    optionFilterProp="children"
                    filterOption={(input, option) =>
                        option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0 ||
                        option.props.value.toLowerCase().indexOf(input.toLowerCase()) >= 0
                    }>
                    <Option value="Todos">Sest-1</Option>
                    <Option value="Logikee">Exxxx</Option>
                </Select>
                <Select
                    showSearch
                    style={{ width: '100%' }}
                    placeholder="SDM"
                    optionFilterProp="children"
                    filterOption={(input, option) =>
                        option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0 ||
                        option.props.value.toLowerCase().indexOf(input.toLowerCase()) >= 0
                    }>
                    <Option value="Todos">Todos</Option>
                    <Option value="Logikee">Logikee</Option>
                </Select>
            </Form.Item>

                <Form.Item name="dataAbertura">
                    <div>
                        <p>Data</p>
                        <DatePicker />
                    </div>
                </Form.Item>

            <div className="buttons">
                <Button className="filter-clear-btn" onClick={clear}>Limpar</Button>
                <Button
                    htmlType="submit"
                    className="filter-submit-btn">
                    Aplicar
                </Button>
            </div>
        </Form>
    )
}

export default ConsumedHoursFilter
