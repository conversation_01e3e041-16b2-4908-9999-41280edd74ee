.disabled {
     pointer-events: none;
}

.enabled {
     pointer-events: all !important;
}

.select-vip {
     background: #0053F4;
     color: #FFFFFF;
}

.select-vip:hover {
     background: #0053F4;
}

.select-prime {
     background: #9747FF;
     color: #FFFFFF;
}

.select-prime:hover {
     background: #9747FF;
}

.select-gold {
     background: #D1F010;
     color: #FFFFFF;
}

.select-gold:hover {
     background: #D1F010;
}

.select-silver {
     background: #43914F;
     color: #FFFFFF;
}

.select-silver:hover {
     background: #43914F;
}

.test {
     background: black;
}

.button-back{
     display: flex;
     justify-content: center;
     align-items: center;
     border-radius: 20px;
     width: 20px;
     font-size: 22px !important;
}

.table-clientCategory{
     display: flex;
     justify-content: flex-start;
     align-items: center;
     margin-left: 20px;
}

.table-card-top h2{
     margin: 0;
     margin-left: 30px;
     font-weight: 700;
}