import { useEffect, useState } from "react";
import { HeaderMenu } from "../../components/HeaderMenu";
import { SideMenu } from "../../components/SideMenu";
import { Layout } from "antd";
import { Content } from "antd/lib/layout/layout";
import { Col, Row } from "antd";
import OpenTickets from "../../components/Graphics/OpenTickets";
import HoursPerProfissional from "../../components/Graphics/HoursPerProfissional";
import { BornupGraph } from "../../components/Graphics/BornupGraph";
import { otrsGet } from "../../service/apiOtrs";
import { dynamoGet } from "../../service/apiDsmDynamo";
import { CardLoading } from "../../components/Graphics/Loading";
import { setActiveCustomersWithActiveContractState } from "../../store/actions/customers-action";
import { shallowEqual, useSelector } from "react-redux";
import { getCustomerByParameter } from "../../controllers/clients/clientsController";

export const CockpitAnalista = (props) => {
  const { collapsed, setCollapsed } = props;
  const [allContracts, setAllContracts] = useState([]);
  const [allSquads, setAllSquads] = useState([]);
  const [loading, setLoading] = useState(false);
  const [contractsFromCustomer, setContractsFromCustomer] = useState();
  const activeCustomersWithActiveContract = useSelector(
    (state) => state.customers.activeCustomersWithActiveContract,
    shallowEqual
  );
  const getActiveCustomersWithActiveContract = async () => {
    const route = "customers/read/hasActiveContracts";
    const customers = await getCustomerByParameter(route, { status: 1 });
    setActiveCustomersWithActiveContractState(customers);
  };
  const allComponents = [
    <BornupGraph allContracts={allContracts} allWallets={allSquads} />,
    <HoursPerProfissional
      currentCockpit="analist"
      clients={activeCustomersWithActiveContract}
      contracts={allContracts}
      contractsFromCustomer={contractsFromCustomer}
      setContractsFromCustomer={(value) => setContractsFromCustomer(value)}
    />,
    <OpenTickets
      isModal={true}
      currentCockpit="analist"
      allCustomers={activeCustomersWithActiveContract}
    />,
  ];

  async function getAllContracts() {
    const { data } = await otrsGet("read/contract/all/0");
    setAllContracts(Object.values(data));
  }

  async function getAllSquads() {
    const squads = await dynamoGet(`${process.env.REACT_APP_STAGE}-wallets`);
    setAllSquads(squads);
  }

  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      await getAllContracts();
      await getAllSquads();
      getActiveCustomersWithActiveContract();
      setLoading(false);
    };

    loadData();
  }, []);

  return (
    <Layout style={{ minHeight: "100vh" }}>
      <HeaderMenu collapsed={collapsed} setCollapsed={setCollapsed} />
      <Layout>
        <SideMenu collapsed={collapsed} />
        <Content style={{ padding: "2em" }}>
          <Row gutter={[12, 24]}>
            {allComponents.map((item, index) => {
              return (
                <Col xs={24} sm={24} md={24} xl={12} key={index}>
                  {loading === true ? <CardLoading /> : item}
                </Col>
              );
            })}
          </Row>
        </Content>
      </Layout>
    </Layout>
  );
};
