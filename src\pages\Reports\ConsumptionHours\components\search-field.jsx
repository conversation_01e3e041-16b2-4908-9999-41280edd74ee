import { Col, Typography } from "antd";
import { SearchInput } from "../../../../components/SearchInput";
import { setConsumptionState } from "../../../../store/actions/consumption-hours-action";

export const SearchField = () => {

    return (
        <Col
            xl={{ span: 6 }}
            lg={{ span: 6 }}
            md={{ span: 10 }}
            sm={{ span: 21 }}
            style={{ flexDirection: "column", display: "flex", marginBottom: 10 }}
        >
            <Typography.Text>Filtrar: </Typography.Text>
            <SearchInput
                onChange={(text) => setConsumptionState({ field: 'search', value: text })}
                placeholder={"Pesquisar . . ."}
            />
        </Col>
    )
}