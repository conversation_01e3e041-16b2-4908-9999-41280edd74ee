import {
  Row,
  Col,
  Select,
  Typography,
  Input,
  Tooltip,
  Card,
  Space,
  Form,
} from "antd";
import { useEffect, useState, useContext } from "react";
import { dynamoGet } from "../../service/apiDsmDynamo";
import { invalidInputNumberChars } from "../../constants/invalidCharsInputNumber";
import { formatHourName } from "../../controllers/Proposals/formatHourName";
import { ProposalContext } from "../../contexts/totalValuesProposals";
import { TotalValuesProposalType } from "../../contexts/reducers/totalValuesProposal";
import { totalValuesController } from "../../controllers/Proposals/totalValuesTechnical";

export const AddHourValueCardDaredeInvestments = ({
  title, // 8x5 SETUP,
  hourClass,
  costManagementData,
  services,
}) => {
  const { Text } = Typography;
  const { Option } = Select;
  const { proposalState, setProposalState } = useContext(ProposalContext);

  const filteredTotalValues = proposalState?.totalValues?.find(
    (v) => v.hourClass === hourClass
  );

  const filteredTotalValuesByMonth = filteredTotalValues?.values?.find(
    (v) => v.month === proposalState?.month
  );

  console.log("hourvaluecard", { filteredTotalValuesByMonth });

  const handleInputValue = async (value, hourClass, hourType) => {
    console.log({ value });
    if (value < 0) value = 0;
    if (value === "reais" || value === "percentage") {
      const calculatedValue = await totalValuesController(
        proposalState,
        costManagementData,
        services
      );
      console.log("hourvalue card", { calculatedValue });

      setProposalState({
        type: TotalValuesProposalType.SET_TOTAL_VALUES,
        value: calculatedValue,
      });
    } else {
      const calculatedValue = await totalValuesController(
        proposalState,
        costManagementData,
        services
      );
      console.log("hourvalue card", { calculatedValue });

      setProposalState({
        type: TotalValuesProposalType.SET_TOTAL_VALUES,
        value: calculatedValue,
      });
    }
  };

  useEffect(() => {
    const fetchProposalTotalValues = async () => {
      dynamoGet(`${process.env.REACT_APP_STAGE}-cost-management`).then(
        async (data) => {
          const res = data.find(
            (d) => d.id === `${process.env.REACT_APP_COST_MANAGEMENT_OBJ_ID}`
          );

          const calculatedValue = await totalValuesController(
            proposalState,
            res,
            services
          );
          console.log("hourvalue card", { calculatedValue });

          setProposalState({
            type: TotalValuesProposalType.SET_TOTAL_VALUES,
            value: calculatedValue,
          });
        }
      );
    };

    fetchProposalTotalValues();
  }, []);

  return (
    <Col xxl={8} xl={24} sm={24} style={{ textAlign: "center" }}>
      <Text>{title}</Text>
      <Card
        style={{
          width: "100%",
          backgroundColor: title.includes("Adicional") ? "#fff" : "#0F9347",
          borderColor: title.includes("Adicional") ? "#000" : "none",
          borderRadius: "5px",
        }}
      >
        <Row justify="space-between" gutter={[4, 16]}>
          {filteredTotalValuesByMonth
            ? filteredTotalValuesByMonth?.values?.map((t, i) => {
                return (
                  <Col span={12} style={{ textAlign: "center" }}>
                    <Text
                      style={{
                        color: title.includes("Adicional") ? "#000" : "#fff",
                      }}
                    >
                      {formatHourName(t.formName)}
                    </Text>
                    <Space size={"small"}>
                      <Tooltip title="Valor hora">
                        <Input
                          disabled
                          placeholder={`Valor em Gerenciamento de Custos`}
                          style={{ textAlign: "center", color: "#000" }}
                          type="number"
                          value={t.hourValue}
                        />
                      </Tooltip>
                      <Tooltip title="Valor do desconto">
                        <Input
                          style={{ textAlign: "center" }}
                          placeholder="Valor do desconto"
                          type="number"
                          onKeyDown={(e) => {
                            invalidInputNumberChars.find(
                              (keyPressed) => keyPressed === e.key
                            ) && e.preventDefault();
                          }}
                          onChange={(e) =>
                            handleInputValue(
                              e.target.value,
                              title.includes("Adicional")
                                ? "additionalHourValue"
                                : "hourValue", // since it is not discount values
                              t
                            )
                          }
                          addonAfter={
                            <Select
                              style={{ padding: 0 }}
                              defaultValue="reais"
                              onChange={(value) => {
                                handleInputValue(
                                  value,
                                  title.includes("Adicional")
                                    ? "additionalHourValue"
                                    : "hourValue", // since it is not discount values
                                  t
                                );
                              }}
                            >
                              <Option value="reais">R$</Option>
                              <Option value="percentage">%</Option>
                            </Select>
                          }
                        />
                      </Tooltip>
                    </Space>
                  </Col>
                );
              })
            : null}
        </Row>
      </Card>
    </Col>
  );
};
