import React, { useEffect, useState, useMemo, useContext } from "react";
import { SearchOutlined } from '@ant-design/icons';

import {
  Form,
  Modal,
  Table,
  Input,
  message
} from "antd";
import axios from 'axios'
import { toPng } from 'html-to-image';
import { EmailAutomationContext, EmailItemTypes } from "../../../contexts/email-automation";

export const SendEmailAutomationModal = ({ isOpen, toggle, from, subject }) => {
  const { emailAutomationState } = useContext(EmailAutomationContext)
  const [users, setUsers] = useState([])
  const [tableLoading, setTableLoading] = useState(false)
  const [loading, setLoading] = useState(false)
  const [search, setSearch] = useState('')

  const [selectedEmails, setSelectedEmails] = useState([])

  useEffect(() => {
    getUsers()
  }, [])

  function checkAll(isChecked) {
    const inputs = document.querySelectorAll('.ck-email-automation')

    inputs.forEach(input => {
      input.checked = isChecked
    })

    if (isChecked) {
      const emails = users.map(user => user.email)
      setSelectedEmails(emails)
      return
    }

    setSelectedEmails([])
  }

  function checkOne(email) {
    const checked = selectedEmails.includes(email)

    if (checked) {
      const newEmails = selectedEmails.filter(e => e !== email)
      setSelectedEmails(newEmails)
      return
    }

    setSelectedEmails(emails => [
      ...emails,
      email
    ])
  }

  const collumns = useMemo(() => {
    return [
      {
        code: 'view_check',
        title: () => (
          <input
            type="checkbox"
            id="ck-all"
            onClick={(e) => checkAll(e.currentTarget.checked)}
          />
        ),
        render: ({ email }) => (
          <input
            type="checkbox"
            id={`ck-${email}`}
            className="ck-email-automation"
            checked={selectedEmails.includes(email)}
            onChange={() => checkOne(email)}
          />
        ),
        width: '50px'
      },
      {
        code: 'view_email',
        title: 'Usuário',
        dataIndex: 'email',
        defaultSortOrder: 'descend',
        sortDirections: ['descend', 'ascend'],
        sorter: (a, b) => a.email - b.email,
        render: (email) => (
          <label for={`ck-${email}`}>{email}</label>
        ),
        width: '200px'
      }
    ]
  }, [selectedEmails])

  function getUsers() {
    const { REACT_APP_API_PERMISSION } = process.env
    const headers = { authorization: localStorage.getItem("jwt") }

    setTableLoading(true)
    axios.get(`${REACT_APP_API_PERMISSION}cognito/read`, { headers })
      .then(response => {
        const { data } = response.data
        setUsers(data)
        setTableLoading(false)
      })
      .catch(() => {
        setTableLoading(false)
      })
  }

  async function sendEmail() {
    const { REACT_APP_API_PERMISSION } = process.env
    const headers = {
      "Content-Type": "multipart/form-data",
      authorization: localStorage.getItem("jwt")
    }

    const { templateList } = emailAutomationState
    const emailTemplate = document.getElementById('template-email-automation')
    const emailTemplateClone = emailTemplate.cloneNode(true)

    emailTemplateClone.id = 'template-email-automation-clone'
    document.body.append(emailTemplateClone)

    for (let index = 0; index < templateList.length; index++) {
      const item = templateList[index]
      if (item.type === EmailItemTypes.TIMELINE) {
        const timelineElement = document.querySelector(`#template-email-automation-clone #timeline-${index}`)
        const timelineImage = await toPng(timelineElement)

        const image = document.createElement('img')
        image.src = timelineImage

        timelineElement.innerHTML = ""
        timelineElement.appendChild(image)
      }
    }

    let formData = new FormData()

    // encrypting so as not to lose the special characters
    const encriptyBody = Buffer.from(emailTemplateClone.innerHTML).toString('base64')
    const encriptySubject = Buffer.from(subject).toString('base64')

    formData.append('html', encriptyBody)
    formData.append('emails', selectedEmails.join(';'))
    formData.append('from', from)
    formData.append('subject', encriptySubject)

    setLoading(true)
    axios.post(`${REACT_APP_API_PERMISSION}email/send`, formData, { headers })
      .then(() => {
        message.success("Email enviado com sucesso!");
        setLoading(false)
        toggle()
      })
      .catch((error) => {
        setLoading(false)
        if (error.response) {
          const { data, status } = error.response

          if (status === 400) {
            message.error(data.message);
            return
          }
          
          message.error("Ops.. Não foi possível enviar o email!");
        }
        else {
          message.error("Ops.. Não foi possível enviar o email!");
        }
      })

    document.getElementById('template-email-automation-clone').remove()

  }

  function filterUser(user) {
    let includes = false

    if (user.email) {
      const email = user.email.toUpperCase()

      if (email.includes(search.toUpperCase()))
        includes = true
    }

    return includes
  }

  let filtered = users.filter(user => filterUser(user))

  return (
    <Modal
      title="Selecione os usuários que receberam o email"
      open={isOpen}
      onOk={sendEmail}
      okText="Disparar email"
      onCancel={toggle}
      cancelText="Cancelar"
      confirmLoading={loading}
    >
      <Form
        layout="vertical"
        onFinish={toggle}
        requiredMark={false}
        form={form}
      >

        <Input
          prefix={<SearchOutlined />}
          onChange={(e) => setSearch(e.currentTarget.value)}
          placeholder="Pesquisar . . ."
          style={{ marginBottom: 16 }}
        />

        <Table
          loading={tableLoading}
          columns={collumns}
          dataSource={filtered}
        />

      </Form>
    </Modal>
  );
};