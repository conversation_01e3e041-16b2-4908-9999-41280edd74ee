/**
 * Hook de autenticação otimizado para performance
 * Implementa estratégias avançadas de otimização
 */

import { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { httpOnlyAuthService } from '../services/httpOnlyAuthService';
import { logger } from '../utils/logger';

/**
 * Hook otimizado para autenticação com estratégias de performance
 */
export const useOptimizedAuth = () => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [user, setUser] = useState(null);
  const navigate = useNavigate();
  
  // Refs para controle de estado interno
  const lastCheckTime = useRef(0);
  const checkInProgress = useRef(false);
  const authCache = useRef({ authenticated: false, user: null, timestamp: 0 });
  
  // Configurações de cache e throttling
  const CACHE_DURATION = 30000; // 30 segundos
  const MIN_CHECK_INTERVAL = 5000; // 5 segundos mínimo entre verificações
  const MAX_RETRIES = 3;

  /**
   * Verifica se o cache ainda é válido
   */
  const isCacheValid = useCallback(() => {
    const now = Date.now();
    return (now - authCache.current.timestamp) < CACHE_DURATION;
  }, []);

  /**
   * Atualiza o cache de autenticação
   */
  const updateCache = useCallback((authenticated, userData) => {
    authCache.current = {
      authenticated,
      user: userData,
      timestamp: Date.now()
    };
  }, []);

  /**
   * Verificação de autenticação com cache e throttling
   */
  const checkAuthStatus = useCallback(async (forceCheck = false) => {
    const now = Date.now();
    
    // Throttling: evita verificações muito frequentes
    if (!forceCheck && (now - lastCheckTime.current) < MIN_CHECK_INTERVAL) {
      logger.debug('Verificação de auth throttled');
      return authCache.current.authenticated;
    }

    // Evita verificações simultâneas
    if (checkInProgress.current) {
      logger.debug('Verificação de auth já em progresso');
      return authCache.current.authenticated;
    }

    // Usar cache se válido e não forçado
    if (!forceCheck && isCacheValid()) {
      logger.debug('Usando cache de autenticação');
      setIsAuthenticated(authCache.current.authenticated);
      setUser(authCache.current.user);
      return authCache.current.authenticated;
    }

    checkInProgress.current = true;
    lastCheckTime.current = now;

    try {
      setIsLoading(true);

      const authStatus = await httpOnlyAuthService.getAuthStatus();

      if (authStatus.authenticated && authStatus.user) {
        setUser(authStatus.user);
        setIsAuthenticated(true);
        updateCache(true, authStatus.user);
        
        logger.debug('Usuário autenticado (verificação otimizada)', {
          email: authStatus.user.email,
          cached: false
        });
      } else {
        setUser(null);
        setIsAuthenticated(false);
        updateCache(false, null);
        
        logger.debug('Usuário não autenticado (verificação otimizada)');
      }

      return authStatus.authenticated;
    } catch (error) {
      logger.error('Erro na verificação otimizada de autenticação', { 
        error: error.message 
      });
      
      // Em caso de erro, manter estado anterior se cache for recente
      if (isCacheValid()) {
        logger.debug('Mantendo estado anterior devido a erro');
        return authCache.current.authenticated;
      }
      
      setUser(null);
      setIsAuthenticated(false);
      updateCache(false, null);
      return false;
    } finally {
      setIsLoading(false);
      checkInProgress.current = false;
    }
  }, [isCacheValid, updateCache]);

  /**
   * Login otimizado
   */
  const login = useCallback(async (code) => {
    try {
      setIsLoading(true);
      const result = await httpOnlyAuthService.authenticate(code);
      
      if (result.user) {
        setUser(result.user);
        setIsAuthenticated(true);
        updateCache(true, result.user);
        
        logger.info('Login realizado com sucesso (otimizado)', {
          email: result.user.email
        });
      }
      
      return result;
    } catch (error) {
      logger.error('Erro no login otimizado', { error: error.message });
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [updateCache]);

  /**
   * Logout otimizado
   */
  const logout = useCallback(async () => {
    try {
      await httpOnlyAuthService.logout();
      setUser(null);
      setIsAuthenticated(false);
      updateCache(false, null);
      
      logger.info('Logout realizado com sucesso (otimizado)');
      navigate('/login');
    } catch (error) {
      logger.error('Erro no logout otimizado', { error: error.message });
      // Mesmo com erro, limpar estado local
      setUser(null);
      setIsAuthenticated(false);
      updateCache(false, null);
      navigate('/login');
    }
  }, [navigate, updateCache]);

  /**
   * Utilitários memoizados
   */
  const authUtils = useMemo(() => ({
    getToken: () => httpOnlyAuthService.getTokenFromCookie(),
    getAuthHeaders: () => httpOnlyAuthService.getAuthHeaders(),
    hasPermission: (permission) => httpOnlyAuthService.hasPermission(permission),
    clearCache: () => {
      authCache.current = { authenticated: false, user: null, timestamp: 0 };
    }
  }), []);

  /**
   * Verificação inteligente baseada em eventos
   */
  useEffect(() => {
    if (!isAuthenticated) return;

    let timeoutId;
    let retryCount = 0;

    const smartCheck = async () => {
      try {
        const authenticated = await checkAuthStatus();
        if (!authenticated) {
          logger.info('Sessão expirada detectada (verificação inteligente)');
          await logout();
        }
        retryCount = 0; // Reset em caso de sucesso
      } catch (error) {
        retryCount++;
        if (retryCount < MAX_RETRIES) {
          const delay = Math.min(1000 * Math.pow(2, retryCount), 30000);
          timeoutId = setTimeout(smartCheck, delay);
        } else {
          logger.error('Máximo de tentativas de verificação atingido');
        }
      }
    };

    // Verificação baseada em visibilidade
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        smartCheck();
      }
    };

    // Verificação baseada em foco
    const handleFocus = () => {
      smartCheck();
    };

    // Verificação baseada em atividade (debounced)
    let activityTimeout;
    const handleActivity = () => {
      clearTimeout(activityTimeout);
      activityTimeout = setTimeout(smartCheck, 60000); // 1 minuto após atividade
    };

    // Eventos otimizados
    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('focus', handleFocus);
    
    const activityEvents = ['mousedown', 'keydown', 'touchstart'];
    activityEvents.forEach(event => {
      document.addEventListener(event, handleActivity, { passive: true });
    });

    // Verificação periódica reduzida (10 minutos)
    const interval = setInterval(smartCheck, 600000);

    return () => {
      clearTimeout(timeoutId);
      clearTimeout(activityTimeout);
      clearInterval(interval);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('focus', handleFocus);
      activityEvents.forEach(event => {
        document.removeEventListener(event, handleActivity);
      });
    };
  }, [isAuthenticated, checkAuthStatus, logout]);

  // Verificação inicial
  useEffect(() => {
    checkAuthStatus(true); // Força verificação inicial
  }, [checkAuthStatus]);

  return {
    // Estado
    isAuthenticated,
    isLoading,
    user,
    
    // Ações otimizadas
    login,
    logout,
    checkAuthStatus,
    
    // Utilitários
    ...authUtils,
    
    // Métricas de performance
    getCacheInfo: () => ({
      isValid: isCacheValid(),
      timestamp: authCache.current.timestamp,
      age: Date.now() - authCache.current.timestamp
    })
  };
};
