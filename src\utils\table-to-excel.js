/* eslint-disable array-callback-return */
import { saveAs } from 'file-saver';

export default function createExcelTable({ id, filename = '', borda = false }) {
    var url;
    var tableSelect, i;
    var dataType = 'application/vnd.ms-excel;charset=utf-8,%EF%BB%BF';
    var tableHTML;

    if (Array.isArray(id)) {
        if (borda) {
            tableSelect = document.querySelectorAll(`#borderTitle`);
            for (i = 0; i < tableSelect.length; i++) {
                tableSelect[i].style.border = "1px solid black";
            }
            tableSelect = document.querySelectorAll(`#border`);
            for (i = 0; i < tableSelect.length; i++) {
                tableSelect[i].style.border = "1px solid black";
                tableSelect[i].style.fontWeight = "lighter";
            }

        }

        var ArrayTableSelect = [];
        id.map(item => {
            ArrayTableSelect.push(document.querySelector(`#${item}`));
        });
        tableSelect = ArrayTableSelect;

        var ArrayOuterHtml = [];
        tableSelect.map(item => {
            if (item)
                ArrayOuterHtml.push(item.outerHTML.replace(/ /g, '%20'));
        })
        tableHTML = ArrayOuterHtml;
    } else {
        if (borda) {
            tableSelect = document.querySelectorAll(`#borderTitle`);
            for (i = 0; i < tableSelect.length; i++) {
                tableSelect[i].style.border = "1px solid black";
            }
            tableSelect = document.querySelectorAll(`#border`);
            for (i = 0; i < tableSelect.length; i++) {
                tableSelect[i].style.border = "1px solid black";
                tableSelect[i].style.fontWeight = "normal";
            }

        }
        tableSelect = document.querySelector(`#${id}`);

        tableHTML = tableSelect.outerHTML.replace(/ /g, '%20');
    }
    filename = filename ? filename + '.xls' : 'excelData.xls';

    if (Array.isArray(tableHTML)) {
        switch (tableHTML.length) {
            case 2:
                url = 'data:' + dataType + tableHTML[0] + tableHTML[1];
                break;
            case 3:
                url = 'data:' + dataType + tableHTML[0] + tableHTML[1] + tableHTML[2];
                break;
            case 4:
                url = 'data:' + dataType + tableHTML[0] + tableHTML[1] + tableHTML[2] + tableHTML[3];
                break;
            case 5:
                url = 'data:' + dataType + tableHTML[0] + tableHTML[1] + tableHTML[2] + tableHTML[3] + tableHTML[4];
                break;
            default:
                url = 'data:' + dataType + tableHTML[0] + tableHTML[1];
                break;
        }
    } else {
        url = 'data:' + dataType + tableHTML;
    }

    saveAs(url, filename);
}

export function tableToExcel({ id, filename }) {
    const dataType = 'application/vnd.ms-excel;charset=utf-8,%EF%BB%BF';

    const tableSelect = document.getElementById(id);
    const tableHTML = tableSelect.outerHTML.replace(/ /g, '%20');

    const url = 'data:' + dataType + ', ' + tableHTML


    saveAs(url, filename);
}