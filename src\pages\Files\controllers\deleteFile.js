import axios from "axios";
import { dynamoDelete } from "../../../service/apiDsmDynamo";

export const deleteFile = async (file) => {
  const backUrl = `${process.env.REACT_APP_API_NEW}delete/objects`;
  const body = {
    bucketName: `${process.env.REACT_APP_STAGE}-files-archive`,
    fileKeys: [`${file.customerId}-${encodeURI(file.customerName)}/${file.id}`],
  };
  const headers = {
    headers: {
      authorization: localStorage.getItem("jwt"),
    },
  };
  try {
    await axios.post(backUrl, body, headers);
    await dynamoDelete(`${process.env.REACT_APP_STAGE}-files-archive`, file.id);
  } catch (error) {
    console.log({ error });
  }
};
