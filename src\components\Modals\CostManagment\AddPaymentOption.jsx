import {
  DeleteOutlined,
  PlusOutlined,
  CreditCardOutlined
} from '@ant-design/icons'
import { useState } from 'react'
import { Button, Modal, Row, Col, Table, Input, Typography } from 'antd'

let count = 1

export const AddPaymentOption = () => {
  const { Title } = Typography
  const [showModal, setShowModal] = useState()
  const [currentPaymentOption, setCurrentPaymentOption] = useState({
    id: '',
    name: '',
    description: ''
  })
  const [listInput, setListInput] = useState([])

  const handleSubmit = () => {
    setShowModal(false)
  }

  const handleRemoveItem = index => {
    setListInput(prev => [...prev.slice(0, index), ...prev.slice(index + 1)])
  }

  const handleAddItem = () => {
    let index = 0
    const addedItem = {
      id: count++,
      name: currentPaymentOption.name,
      description: currentPaymentOption.description
    }
    setListInput(prev => [
      ...prev.slice(0, index + 1),
      addedItem,
      ...prev.slice(index + 1)
    ])
  }

  const columns = [
    {
      title: 'Nome',
      dataIndex: 'name',
      key: 'name'
    },
    {
      title: 'Descrição',
      dataIndex: 'description',
      key: 'description'
    },
    {
      title: 'Remover',
      dataIndex: 'remove',
      key: 'remove',
      render: (id, props) => {
        return (
          <Button
            danger
            type="text"
            icon={<DeleteOutlined />}
            onClick={() => {
              handleRemoveItem(props.id)
            }}
          ></Button>
        )
      }
    }
  ]
  return (
    <>
      <Button
        type="primary"
        onClick={() => {
          setShowModal(true)
        }}
      >
        Adicionar Forma de Pagamento <PlusOutlined />
      </Button>
      <Modal
        title="Adicionar forma de pagamento"
        visible={showModal}
        closable={false}
        onOk={handleSubmit}
        okText="Salvar"
        cancelText="Cancelar"
        onCancel={() => {
          setShowModal(false)
          setListInput([])
        }}
      >
        <Row
          gutter={[16, 16]}
          justify="center"
          style={{ marginBottom: '45px' }}
        >
          <Col span={12}>
            <Title level={5} style={{ fontWeight: '400', fontSize: '14px' }}>
              Forma de pagamento
            </Title>
            <Input
              required
              onChange={e => {
                setCurrentPaymentOption({
                  ...currentPaymentOption,
                  name: e.target.value
                })
              }}
            />
          </Col>
          <Col span={12}>
            <Title level={5} style={{ fontWeight: '400', fontSize: '14px' }}>
              Descrição
            </Title>
            <Input
              required
              onChange={e => {
                setCurrentPaymentOption({
                  ...currentPaymentOption,
                  description: e.target.value
                })
              }}
            />
          </Col>
          <Col>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => {
                handleAddItem()
              }}
              disabled={
                !currentPaymentOption.name || !currentPaymentOption.description
              }
            >
              Adicionar
            </Button>
          </Col>
        </Row>

        {listInput.length > 0 ? (
          <Table
            dataSource={listInput.map((item, index) => {
              return {
                id: index,
                name: item.name,
                description: item.description
              }
            })}
            columns={columns}
            scroll={{ x: 100 }}
            pagination={false}
          ></Table>
        ) : (
          <Row justify="center" gutter={[24, 16]}>
            <Col
              style={{
                backgroundColor: '#0f9347',
                borderRadius: '128px',
                opacity: '.75',
                boxShadow: '10px 10px 10px rgba(15, 147, 71, 0.8)'
              }}
            >
              <CreditCardOutlined
                style={{ fontSize: '128px', color: '#fff', margin: '18px' }}
              />
            </Col>
            <Col>
              <Title level={5} style={{ fontWeight: '400' }}>
                Não existem outras formas de pagamento adicionadas
              </Title>
            </Col>
          </Row>
        )}
      </Modal>
    </>
  )
}
