import { useState } from 'react'
import { But<PERSON>, Modal } from 'antd'
import { EyeOutlined } from '@ant-design/icons'

export const ViewServiceModal = () => {
  const [isModalVisible, setIsModalVisible] = useState(false)

  const showModal = () => {
    setIsModalVisible(true)
  }

  const handleOk = () => {
    setIsModalVisible(false)
  }

  const handleCancel = () => {
    setIsModalVisible(false)
  }

  return (
    <>
      <Button type="text" onClick={showModal}>
        <EyeOutlined />
      </Button>
      <Modal
        title="Visualização de serviço"
        open={isModalVisible}
        onOk={handleOk}
        onCancel={handleCancel}
      >
        <p>Some contents...</p>
        <p>Some contents...</p>
        <p>Some contents...</p>
      </Modal>
    </>
  )
}
