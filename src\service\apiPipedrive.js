import { message } from "antd";
import Axios from "axios";

export const pipeUpdate = async (body) => {
  try {
    const jwt = localStorage.getItem("jwt");

    console.log("body: ", body);

    const { data } = await Axios.put(
      `${process.env.REACT_APP_PIPEDRIVE_API}deals/update/pipedrive`,
      body,
      {
        headers: { Authorization: jwt },
      }
    );

    return data;
  } catch (error) {
    console.log({ error });
    message.warning("Número da oportunidade não foi encontrado no Pipedrive!");
  }
};
