async function updateHourValue(
  value,
  totalValuesState,
  hourClass,
  month,
  formName
) {
  if (!value) return totalValuesState.totalValues;

  let newObjTotalValues = [...totalValuesState.totalValues]
    .find((v) => v.hourClass === hourClass)
    ?.values?.find((v) => v.month === month)
    ?.values?.forEach((v) => {
      if (v.formName === formName) {
        if (v["discountUnit"] === "percentage") {
          const newHourWithDiscount =
            Number(value) * ((100 - v["discountValue"]) / 100);
          v["totalValue"] = v["totalHours"] * newHourWithDiscount;
          v["hourValue"] = Number(value);
        } else {
          v["totalValue"] =
            v["totalHours"] * (Number(value) - v["discountValue"]);
          v["hourValue"] = Number(value);
        }
      }
    });

  return newObjTotalValues ? newObjTotalValues : totalValuesState.totalValues;
}

export const handleHourValue = async (proposalItem, costManagementData) => {
  if (!costManagementData || !proposalItem) return false;

  let newPropolsalItem = Object.assign({}, proposalItem);
  const newCostdata = Object.assign({}, costManagementData);

  newPropolsalItem?.totalValues?.forEach(async (t) => {
    const costHourClass =
      t.hourClass === "additionalHourValue"
        ? "daredeAdditionalHours"
        : "daredeHourValue";

    t.values.forEach((monthValue) => {
      monthValue.values.forEach(async (hourTypeInfo) => {
        const costManagementHourValue = newCostdata[costHourClass]
          ?.find((hourValues) => hourValues.month === monthValue.month)
          ?.values?.find(
            (costHourType) => costHourType.name === hourTypeInfo.formName
          )?.value;

        if (hourTypeInfo.hourValue !== costManagementHourValue) {
          const newTotalValues = await updateHourValue(
            costManagementHourValue,
            proposalItem,
            t.hourClass,
            monthValue.month,
            hourTypeInfo.formName
          );
          newPropolsalItem.totalValues = newTotalValues;
        }
      });
    });
  });

  return newPropolsalItem;
};
