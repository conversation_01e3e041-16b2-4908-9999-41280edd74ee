import { Col, Row, Select, Typography } from "antd";
import { shallowEqual, useSelector } from "react-redux";
import {
  setUploadFileFilesState,
  setUploadReadyFilesState,
} from "../../../store/actions/files-action";
import useSWR from "swr";
import { dynamoGetById } from "../../../service/apiDsmDynamo";
import { useMemo } from "react";

export const UploadTagSelect = () => {
  const { Option } = Select;
  const { Text } = Typography;

  const uploadFile = useSelector(
    (state) => state.files.uploadFile,
    shallowEqual
  );

  const allTags = useSelector((state) => state.files.allTags, shallowEqual);

  const handleTagsSelect = (value) => {
    let newTags = [];
    value.forEach((t) => {
      const tag = allTags.find((i) => i.name === t);
      newTags.push(tag);
    });
    setUploadFileFilesState({
      field: "uploadFile",
      value: { ...uploadFile, tags: newTags },
    });

    if (
      newTags.length > 0 &&
      uploadFile.type !== "" &&
      uploadFile.createdAt !== "" &&
      uploadFile.updatedAt !== "" &&
      uploadFile.updatedAt !== "" &&
      uploadFile.userName !== "" &&
      uploadFile.userEmail !== "" &&
      uploadFile.customerName !== "" &&
      uploadFile.category !== ""
    ) {
      setUploadReadyFilesState(true);
    } else {
      setUploadReadyFilesState(false);
    }
  };

  return (
    <>
      <Row>
        <Text>Tags*</Text>
      </Row>
      <Row gutter={[8, 8]}>
        <Col span={24}>
          <Select
            style={{ width: "100%" }}
            mode={"multiple"}
            allowClear
            showSearch
            placeholder={"Selecione as tags"}
            value={
              uploadFile?.tags?.length > 0
                ? uploadFile.tags.map((t) => t.name)
                : []
            }
            optionFilterProp="children"
            filterOption={(input, option) =>
              option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
            }
            onChange={(value) => handleTagsSelect(value)}
          >
            {allTags?.map((option, key) => (
              <Option value={option?.name} key={key}>
                {option?.name}
              </Option>
            ))}
          </Select>
        </Col>
      </Row>
    </>
  );
};
