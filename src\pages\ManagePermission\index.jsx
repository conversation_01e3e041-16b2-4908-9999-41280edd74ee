import Axios from "axios";
import { useEffect, useMemo, useState } from "react";
import {
  <PERSON><PERSON>,
  Card,
  Col,
  Input,
  Layout,
  message,
  Popconfirm,
  Row,
  Space,
  Table,
  Typography,
} from "antd";
import { Content } from "antd/lib/layout/layout";
import { HeaderMenu } from "../../components/HeaderMenu";
import { SideMenu } from "../../components/SideMenu";
import { DeleteOutlined } from "@ant-design/icons";
import { AddPermissionModal } from "../../components/Modals/ManagePermission/AddPermissionModal";
import { EditPermissionModal } from "../../components/Modals/ManagePermission/EditPermissionModal";
import { ShowActions } from "../../components/Modals/ManagePermission/ShowActions";
import {
  dynamoDelete,
  dynamoGet,
  dynamoGetById,
} from "../../service/apiDsmDynamo";
import useSWR from "swr";
import { logNewAuditAction } from "../../controllers/audit/logNewAuditAction";
import { authService } from "../../services/authService";

export const ManagePermission = () => {
  const [collapsed, setCollapsed] = useState(false);
  const [loading, setLoading] = useState(false);
  const [search, setSearch] = useState("");
  const [data, setData] = useState([]);
  const [permissionsData, setPermissionsData] = useState([]);
  const { Text } = Typography;

  const permissions = useSWR("manage_permissions", async () => {
    try {
      let data = await dynamoGetById(
        `${process.env.REACT_APP_STAGE}-permissions`,
        localStorage.getItem("@dsm/permission")
      );

      // ✅ Verificação robusta da estrutura de dados
      if (data?.permissions) {
        const managePermissionsPage = data.permissions.find((x) => x.page === "Gerenciar Permissoes");
        if (managePermissionsPage?.actions) {
          return [...managePermissionsPage.actions];
        }
      }

      throw new Error('Invalid permissions structure');
    } catch (error) {
      return [
        { code: "view_name" },
        { code: "view_permission" },
        { code: "view_edit" },
        { code: "view_remove" },
        { code: "create_permission" },
        { code: "edit_permission" },
        { code: "delete_permission" }
      ];
    }
  });

  const getPermission = async () => {
    try {
      setLoading(true);

      let data = await dynamoGet(`${process.env.REACT_APP_STAGE}-permissions`);

      if (!Array.isArray(data)) {
        console.error('❌ ManagePermission: Dados de permissões não são um array:', data);
        data = [];
      }

      let arr = [];

      data.map((item) => {
        if (Array.isArray(item.permissions)) {
          return item.permissions.map((permission) => {
            arr.push(permission.page);
            arr.filter((item) => item !== undefined);
          });
        }
        return null;
      });

      // console.log('🔍 ManagePermission: Dados processados:', {
      //   finalData: data?.length || 0,
      //   pages: arr?.length || 0
      // });

      setData(data);
      setLoading(false);
    } catch (error) {
      console.error('⚠️ ManagePermission: Erro ao carregar dados:', error);
      setLoading(false);
    }
  };

  useEffect(() => {
    getPermission();
  }, []);

  const deleteData = async (id) => {
    const response = await Axios.get(`${process.env.REACT_APP_API_PERMISSION}/cognito/read`, {
      headers: authService.getAuthHeaders(),
    });

    const data = response?.data?.data || response?.data || [];

    if (!Array.isArray(data)) {
      console.error('❌ ManagePermission: Dados recebidos não são um array:', data);
      return;
    }

    let arr = [];

    data.forEach((e) => {
      arr.push(
        process.env.REACT_APP_STAGE === "dev"
          ? e.dev_permission
          : process.env.REACT_APP_STAGE === "hml"
          ? e.hml_permission
          : e.permission
      );
    });

    if (arr.filter((i) => i).includes(id)) {
      message.error(
        "Não foi possível excluir está permissão pois está relacionada com um usuário..."
      );
    } else {
      await dynamoDelete(`${process.env.REACT_APP_STAGE}-permissions`, id);

      const permissionName = permissionsData.find((p) => p.id === id);
      const username = localStorage.getItem("@dsm/username");
      const title = "Deleção de Permissão";

      const permissionDisplayName = permissionName?.name_permission || `ID: ${id}`;
      const description = `${username} deletou a permissão: '${permissionDisplayName}'`;

      logNewAuditAction(username, title, description);

      message.success("Permissão excluida com sucesso!");
      getPermission();
    }
  };

  const columns = [
    {
      code: "view_name",
      title: "Nome da permissão",
      dataIndex: "name_permission",
      key: "name_permission",
    },
    {
      code: "view_permission",
      title: "Permissões",
      dataIndex: "permissions",
      key: "permissions",
      render: (permissions) => {
        return permissions?.map((page, pageIndex) => {
          return (
            <>
              <ShowActions
                permissions={permissions}
                pageIndex={pageIndex}
                page={page}
              />
            </>
          );
        });
      },
    },
    {
      code: "view_edit",
      title: "Editar",
      dataIndex: "id",
      key: "id",
      width: "1%",
      render: (id, item) => {
        return (
          <EditPermissionModal
            getPermission={getPermission}
            item={item}
            id={id}
          />
        );
      },
    },
    {
      code: "view_remove",
      title: "Excluir",
      dataIndex: "id",
      key: "id",
      width: "1%",
      render: (id) => {
        return (
          <Popconfirm
            okText="Sim"
            cancelText="Não"
            title="Tem certeza que deseja excluir esta permissão?"
            placement="leftTop"
            onConfirm={() => deleteData(id)}
          >
            <Button danger type="text">
              <DeleteOutlined />
            </Button>
          </Popconfirm>
        );
      },
    },
  ];

  const filterBySearch = (data, search) => {
    let filteredData = [];

    if (data) {
      filteredData = data.filter((e) => {
        let verifyNamePemission = false;

        if (e.name_permission) {
          verifyNamePemission = e.name_permission
            .toString()
            .toLowerCase()
            .includes(search.toLowerCase());
        }

        if (verifyNamePemission) return e;
      });
    }

    return filteredData;
  };
  const tableData = useMemo(() => {
    let filteredData = data || [];

    if (search !== "") {
      filteredData = filterBySearch(filteredData, search);
    }

    setPermissionsData(filteredData);
    return filteredData;
  }, [data, search]);

  return (
    <>
      <Layout style={{ minHeight: "100vh" }}>
        <HeaderMenu collapsed={collapsed} setCollapsed={setCollapsed} />
        <Layout>
          <SideMenu collapsed={collapsed} />
          <Content style={{ padding: "2em" }}>
            <Card
              style={{
                boxShadow: "0 0 10px rgba(0,0,0,0.1)",
                borderRadius: "20px",
                marginRight: "10px",
              }}
            >
              <Row justify="space-between" style={{ marginBottom: "20px" }}>
                <Col>
                  <Space wrap>
                    <Input
                      onChange={(e) => setSearch(e.target.value)}
                      style={{
                        width: "300px",
                        height: "35px",
                        borderRadius: "7px",
                      }}
                      placeholder="Buscar permissão..."
                    />
                  </Space>
                </Col>
                <Col>
                  {permissions?.data
                    ?.map((permission) => {
                      return permission.code;
                    })
                    .includes("create_permission") && (
                    <AddPermissionModal getPermission={getPermission} />
                  )}
                </Col>
              </Row>
              <Row justify="end">
                {tableData && tableData.length > 0 ? (
                  <Text style={{ margin: "5px 10px 5px 0px" }}>
                    Total: {tableData.length}
                  </Text>
                ) : null}
              </Row>
              <Table
                scroll={{ x: "100%" }}
                dataSource={tableData}
                columns={(() => {
                  const permissionCodes = permissions?.data?.map((permission) => permission.code) || [];
                  const filteredColumns = columns.filter((e) => permissionCodes.includes(e.code));

                  return filteredColumns;
                })()}
                loading={loading}
              />
            </Card>
          </Content>
        </Layout>
      </Layout>
    </>
  );
};
