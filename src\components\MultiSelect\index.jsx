import Select from 'react-select'

export const MultiSelect = ({ options, onChange, value }) => {

    return (
        <Select
            options={options}
            isMulti={true}
            onChange={(values) => onChange(values)}
            value={value}
            styles={{
                control: (provided, state) => {
                    return {
                        ...provided,
                        borderColor: state.isFocused ? '#00B050' : 'hsl(0, 0%, 80%)',
                        '&:hover': {
                            borderColor: state.isFocused ? '#00B050' : 'hsl(0, 0%, 80%)',
                        },
                        minHeight: '33px',
                        height: '33px',
                        borderRadius: 0
                    };
                },
                container: (provided) => {
                    return {
                        ...provided,
                        width: '100%',
                        minHeight: '33px',
                        height: '33px',
                    };
                },
                valueContainer: (provided) => {
                    return {    
                        ...provided,
                        minHeight: '33px',
                        height: '33px',
                    };
                },
                indicatorsContainer: (provided) => ({
                    ...provided,
                    minHeight: '33px',
                    height: '33px',
                }),
            }}
        />
    )
}