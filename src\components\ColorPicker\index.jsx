import React, { useState } from "react";
import reactCSS from "reactcss";
import { SketchPicker } from "react-color";
import "./ColorPicker.css";
import { useEffect } from "react";
import { <PERSON><PERSON>, Card, Col, Row, Typography } from "antd";

export default function ColorPicker(props) {
  const { Title, Text } = Typography;
  const [state, setState] = useState(props.state ? props.state : {});

  useEffect(() => {
    let newState = structuredClone(state);
    props?.options?.forEach((val, index) => {
      newState[val] = {
        displayColorPicker: false,
        color: props?.currentComponent
          ? props?.state
            ? props.state[val]?.color
            : props?.initialColors[index]
          : props?.state
          ? props.state[val]?.color
          : "#000000",
      };
    });
    setState(newState);
  }, [props.state]);

  function handleClick(name) {
    let newState = structuredClone(state);
    newState[name].displayColorPicker = !state[name].displayColorPicker;
    setState(newState);
  }

  function handleClose(name) {
    let newState = structuredClone(state);
    newState[name].displayColorPicker = false;
    setState(newState);
  }

  function handleChange(color, name) {
    let newState = structuredClone(state);
    newState[name].color = color.hex;
    // localStorage.setItem('coloTest', color.hex)
    setState(newState);
    // props.changeColors(state)
  }

  function handleSave() {
    // localStorage.setItem('coloTest', color.hex)
    props.changeColors(state);
  }

  const styles = reactCSS({
    default: {
      color: {
        width: "36px",
        height: "14px",
        borderRadius: "2px",
        background: `${state?.color}`,
      },
      swatch: {
        padding: "5px",
        background: "#fff",
        borderRadius: "1px",
        boxShadow: "0 0 0 1px rgba(0,0,0,.1)",
        display: "inline-block",
        cursor: "pointer",
      },
      popover: {
        position: "absolute",
        zIndex: "2",
      },
      cover: {
        position: "fixed",
        top: "0px",
        right: "0px",
        bottom: "0px",
        left: "0px",
      },
    },
  });

  return (
    <>
      <Row justify="space-between" style={{ width: "200px" }}>
        {Object.entries(state).map((val, index) => {
          return (
            <Col
              span={Object.entries(state).length === 3 ? 12 : 12}
              key={index}
            >
              <Card
                hoverable
                onClick={() => handleClick(val[0])}
                style={{
                  padding: 0,
                  borderRadius: "8px",
                  margin: "5px 3px",
                }}
              >
                <Text>Cor {val[0]}</Text>
                <div
                  className="test"
                  style={{ backgroundColor: state[val[0]]?.color }}
                />
              </Card>
              {state[val[0]]?.displayColorPicker ? (
                <div style={styles.popover}>
                  <div
                    style={styles.cover}
                    onClick={() => handleClose(val[0])}
                  />
                  <SketchPicker
                    color={state[val[0]].color}
                    onChange={(color) => handleChange(color, val[0])}
                  />
                </div>
              ) : null}
            </Col>
          );
        })}
      </Row>
      <Row justify="end">
        <Button
          style={{ marginTop: "10px" }}
          type="primary"
          onClick={() => handleSave()}
        >
          Save
        </Button>
      </Row>
    </>
  );
}
