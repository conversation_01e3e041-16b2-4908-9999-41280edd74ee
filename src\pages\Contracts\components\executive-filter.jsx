import { shallowEqual, useSelector } from "react-redux";
import { Col, Select, Typography } from "antd";

import { setContractFieldState } from "../../../store/actions/contract-action";

export const ExecutiveFilter = ({ span }) => {
  const { selectedExecutive, users } = useSelector(
    (state) => state.contract,
    shallowEqual
  );
  const { Text } = Typography;
  const { Option } = Select;

  return (
    <Col span={span}>
      <Text>Executivo:</Text>
      <Select
        showSearch
        allowClear
        placeholder="Filtrar por executivo"
        onChange={(value) =>
          setContractFieldState({ field: "selectedExecutive", value })
        }
        value={selectedExecutive}
        style={{ width: "100%" }}
        optionFilterProp="children"
        filterOption={(input, option) =>
          option.children?.toLowerCase().indexOf(input?.toLowerCase()) >= 0
        }
      >
        {users?.map((executive, index) => (
          <Option value={executive.email} key={index}>
            {executive.email}
          </Option>
        ))}
      </Select>
    </Col>
  );
};
