import { format } from "date-fns";
import { checkDay } from "../../../../hooks/checkDay";
import { otrsGet, otrsPost, otrsPut } from "../../../../service/apiOtrs";

export const formatAccountBodyRequest = (
  data,
  updatedAccount,
  isEditing,
  accounts
) => {
  let copyClient = JSON.parse(JSON.stringify(data));
  updatedAccount = {
    ...updatedAccount,
    updated_at: format(new Date(), "yyyy-MM-dd HH:mm:ss"),
  };
  let newAccounts = isEditing
    ? accounts.filter((account) => account.id !== updatedAccount.id)
    : JSON.parse(JSON.stringify(accounts));
  newAccounts.push(updatedAccount);

  if (!data.cnpj) copyClient.cnpj = "-";
  let body = {
    ...copyClient,
    accounts: newAccounts,
  };

  return body;
};

export const checkTicketType = (ticketData, data) => {
  const fridayIndex = 5;
  let checkWeekend = checkDay(fridayIndex);
  let ticketType = ticketData?.type?.toLowerCase();

  if (
    (ticketType.includes("rds") ||
      (ticketType.includes("projeto") && !checkWeekend) ||
      ticketType.includes("incidente")) &&
    data.time > 24
  ) {
    return {
      status: false,
      message:
        "Não é possível solicitar mais de 24 horas para tickets do tipo RDS, Incidente e Projeto.",
    };
  } else if (ticketType.includes("projeto") && checkWeekend && data.time > 72) {
    return {
      status: false,
      message:
        "Só é possível solicitar até 72 horas para tickets do tipo Projeto no final de semana.",
    };
  } else {
    return true;
  }
};

export const listQueuesOTRS = async (id) => {
  try {
    const queues = await otrsGet(`read/ticket/queues/allByUser/${id}`, {
      queue_active: true,
    });
    return queues.data;
  } catch (error) {
    console.log(error);
    return [];
  }
};

export const getUserOtrs = async () => {
  try {
    const email = localStorage.getItem("@dsm/mail");
    const { data } = await otrsGet(`read/user/email/${email}`);
    return data[0].id;
  } catch (error) {
    console.log(error);
  }
};

export const createTicketOtrs = async (data, userId) => {
  try {
    const { data: ticket } = await otrsPost("create/ticket", data);
    if (ticket.TicketNumber) {
      await otrsPut("close/ticket", {
        ...data,
        tktNumber: ticket.TicketNumber,
        closed: "pending auto close+",
        ownerTicket: userId,
        tktId: ticket.TicketID,
      });
    }
    return ticket;
  } catch (error) {
    console.log(error);
    return error;
  }
};

export const formatQueues = (queues) => {
  const formattedTreeData = [];

  queues.forEach((queue) => {
    const pathSegments = queue.name.split("::");
    let currentNode = formattedTreeData;

    pathSegments.forEach((segment, level) => {
      let existingNode = currentNode.find((node) => node.title === segment);

      if (!existingNode) {
        existingNode = {
          title: segment,
          value:
            level === pathSegments.length - 1
              ? queue.id
              : `${segment}-${queue.id}`,
          disabled: level !== pathSegments.length - 1,
          children: [],
        };
        currentNode.push(existingNode);
      }

      currentNode = existingNode.children;
    });
  });

  return formattedTreeData;
};

export const getPersonsOtrs = async (clientId) => {
  try {
    const { data } = await otrsGet(`read/person/all/0`);
    const filteredData = Object.values(data).filter(
      (person) =>
        person.customer_id === clientId.toString() && person.valid_id === 1
    );

    return filteredData;
  } catch (error) {
    console.log(error);
    return error;
  }
};
