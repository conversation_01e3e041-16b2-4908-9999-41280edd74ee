import { React, useEffect, useState } from "react";
import {
  Select,
  Button,
  Form,
  Typography,
  Row,
  DatePicker,
  message,
} from "antd";
import { otrsGet } from "../../../../service/apiOtrs";
import { CloseCircleOutlined } from "@ant-design/icons";
import { filterByOrderOfInput } from "../../../../utils/filterByOrderOfInput";

function FilterTicketsPerProfissional(props) {
  const { Title } = Typography;
  const [allProfessional, setAllProfessional] = useState([]);
  const [allWallets, setAllWallets] = useState([]);
  const [errorFeedbackValidation, setErrorFeedbackValidation] = useState(false);
  const [allSelectedProfessionals, setAllSelectedProfessionals] = useState([]);
  const allQueues = Object.values(props.queues);
  const { Option } = Select;
  const [form] = Form.useForm();

  function onFilterFinish(values) {
    props.filter(values);
    props.hiddenPopover();
  }

  function onFilterFailed() {}

  function clear() {
    form.resetFields();
  }

  function getAllUsers() {
    const professionalNames = [];

    Object.entries(props.professionals).forEach((item) => {
      const user = item[1];
      professionalNames.push({
        id: user.id,
        login: user.login,
        name: `${user.first_name}  ${user.last_name}`,
      });
    });

    setAllProfessional(professionalNames);
  }

  function getAllInfo() {
    setAllWallets(props.wallets);
    getAllUsers();
  }

  function orderListByKey(data, key, order) {
    const compareValues =
      (key, order = "asc") =>
      (elemA, elemB) => {
        if (!elemA.hasOwnProperty(key) || !elemB.hasOwnProperty(key)) return 0;
        const comparison = elemA[key].localeCompare(elemB[key]);
        return order === "desc" ? comparison * -1 : comparison;
      };
    return data.sort(compareValues(key, order));
  }

  async function getUserId() {
    const res = await otrsGet(
      `read/user/email/${localStorage.getItem("@dsm/mail")}`
    ).then((res) => {
      localStorage.setItem("@otrs/userId", res?.data[0]?.id);
    });
  }

  const limit = 6;

  useEffect(() => {
    if (allSelectedProfessionals?.length > limit - 1) {
      setErrorFeedbackValidation(true);
    } else {
      setErrorFeedbackValidation(false);
    }
  }, [allSelectedProfessionals]);

  useEffect(() => {
    getAllInfo();
    getUserId();
  }, [props.wallets, props.professionals]);

  return (
    <Form
      form={form}
      style={{ display: "flex", flexDirection: "column" }}
      name="basic"
      initialValues={{
        remember: true,
      }}
      onFinish={onFilterFinish}
      onFinishFailed={onFilterFailed}
      autoComplete="off"
    >
      <Row justify="space-between">
        <Title level={4} style={{ fontWeight: 400 }}>
          Tickets por profissional
        </Title>
        <Button type="text" onClick={props.hiddenPopover}>
          <CloseCircleOutlined />
        </Button>
      </Row>
      <Form.Item
        validateStatus={errorFeedbackValidation ? "error" : ""}
        name="professionals"
        rules={[
          {
            required: true,
            message: "Por favor preencha esse campo",
          },
          {
            validator: (_, value) => {
              if (value?.length >= limit) {
                return Promise.reject(
                  `Você pode selecionar no máximo ${limit - 1} profissionais`
                );
              }
              return Promise.resolve();
            },
          },
        ]}
      >
        <Select
          showSearch
          mode="multiple"
          maxTagCount={1}
          style={{
            width: "100%",
            borderColor: errorFeedbackValidation ? "red" : "",
          }}
          onChange={(value) => {
            setAllSelectedProfessionals(value);
          }}
          placeholder="Profissional"
          optionFilterProp="children"
          loading={allProfessional.length === 0}
          filterOption={(input, option) =>
            filterByOrderOfInput(input, option.children)
          }
        >
          {orderListByKey(allProfessional, "name").map((professional) => {
            return (
              <Option key={professional.id} value={professional.id}>
                {professional.name}
              </Option>
            );
          })}
        </Select>
      </Form.Item>

      <Form.Item name="wallet">
        <Select
          showSearch
          style={{ width: "100%" }}
          placeholder="Carteira"
          optionFilterProp="children"
          loading={allWallets.length === 0}
          filterOption={(input, option) =>
            filterByOrderOfInput(input, option.props.children)
          }
        >
          {allWallets.map((wallet) => {
            return (
              <Option key={wallet.id} value={wallet.name}>
                {wallet.name}
              </Option>
            );
          })}
        </Select>
      </Form.Item>

      <Form.Item name="queue">
        <Select
          showSearch
          style={{ width: "100%" }}
          placeholder="Fila"
          optionFilterProp="children"
          filterOption={(input, option) =>
            option.props.children.toLowerCase().indexOf(input.toLowerCase()) >=
              0 ||
            option.props.value.toLowerCase().indexOf(input.toLowerCase()) >= 0
          }
        >
          {allQueues.map((queue) => {
            return (
              <Option key={queue.id} value={queue.name}>
                {queue.name}
              </Option>
            );
          })}
        </Select>
      </Form.Item>

      <Form.Item
        name="monthSelected"
        rules={[{ required: true, message: "Por favor preencha esse campo" }]}
      >
        <DatePicker style={{ width: "100%" }} picker="month" />
      </Form.Item>

      <Form.Item name="buttons">
        <Row justify="space-between">
          <Button onClick={clear} type="default">
            Limpar
          </Button>
          <Button htmlType="submit" type="primary">
            Aplicar
          </Button>
        </Row>
      </Form.Item>
    </Form>
  );
}

export default FilterTicketsPerProfissional;
