export async function verifySustMinHours(totalValues, months) {
  for (let i = 0; i < months.length; i++) {
    const totalValuesFilteredByMonth = totalValues.find(
      (v) => v.month === months[i]
    );

    const sust24x7 = totalValuesFilteredByMonth.values.find(
      (d) => d.formName === "sust24x7"
    ).totalHours;

    const sust8x5 = totalValuesFilteredByMonth.values.find(
      (d) => d.formName === "sust8x5"
    ).totalHours;

    if (sust8x5 > 0 && sust8x5 < 10) {
      if (sust24x7 >= 10) {
        return true;
      } else {
        return false;
      }
    }
    if (sust24x7 > 0 && sust24x7 < 4) {
      return false;
    }
  }

  return true;
}
