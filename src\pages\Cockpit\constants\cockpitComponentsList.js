export const walletComponents = [
  { component: "ContractsPerWallet", name: "Contratos por carteira" },
  { component: "ExpirationOfContracts", name: "Vencimento dos contratos" },
  { component: "OpenTickets", name: "Tickets abertos" },
  {
    component: "TicketWalletManagement",
    name: "Gerenciamento de tickets por contrato",
  },
];

export const profissionalComponents = [
  { component: "HoursLimitGraph", name: "Limite diario de horas" },
  { component: "HoursPerProfissional", name: "Horas por mes" },
  { component: "ProfissionalInVacation", name: "<PERSON><PERSON><PERSON>" },
  { component: "TicketsPerProfissional", name: "Ticket por profissional" },
];

export const clientComponents = [
  { component: "UnopenedTicketsGraph", name: "Sem abrir tickets" },
  { component: "AverageConsumption", name: "Media de consumo" },
  { component: "NewContracts", name: "Novos contratos" },
  { component: "ExpireHours", name: "Horas a expirar e expiradas" },
  { component: "ReservedHours", name: "Horas reservadas" },
  { component: "ConsumedHoursPerMonth", name: "<PERSON>ras consumidas" },
];
