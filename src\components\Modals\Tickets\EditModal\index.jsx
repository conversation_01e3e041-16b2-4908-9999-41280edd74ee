import { But<PERSON>, Modal, Form, Row, message } from "antd";
import { useState } from "react";
import { Select } from "antd";
import { EditOutlined } from "@ant-design/icons";
import axios from "axios";
import { UsersSelect } from "../../../UserSelect";
import { CheckOutlined, DeleteOutlined } from "@ant-design/icons";
import { otrsGet, otrsPut } from "../../../../service/apiOtrs";
import { logNewAuditAction } from "../../../../controllers/audit/logNewAuditAction";

const { Option } = Select;

export const EditTicketModal = ({
  userQueues,
  ticketData,
  ticketEdited,
  setTicketEdited,
}) => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [receiver, setReceiver] = useState("");
  const [queue, setQueue] = useState("");
  const [architects, setArchitects] = useState([]);

  const showModal = () => {
    setIsModalVisible(true);
    setLoading(true);
    async function getUsers() {
      const { data } = await axios.get(
        `${process.env.REACT_APP_API_PERMISSION}cognito/read`,
        {
          headers: { Authorization: localStorage.getItem("jwt") },
        }
      );
      let users = [];
      for (let i = 0; i < data.data.length; i++) {
        users.push(data.data[i]);
      }
      setArchitects(users);
    }

    async function getAllData() {
      await getUsers();
    }
    getAllData();
    setLoading(false);
  };

  const handleOk = async (data) => {
    setLoading(true);
    data["ticket_queue"] = queue;
    data["ticket_owner"] = receiver;
    try {
      await otrsPut(`update/ticket/${ticketData.ticket_number}`, {
        queue: data.ticket_queue,
        email: data.ticket_owner,
      });
      message.success("Ticket atualizado com sucesso");
      setIsModalVisible(false);
      setTicketEdited(!ticketEdited);

      const username = localStorage.getItem("@dsm/username");
      const title = "Edição de Ticket";
      const description = `${username} editou o ticket de nº ${ticketData.ticket_number}`;
      logNewAuditAction(username, title, description);

      setLoading(false);
    } catch (err) {
      message.error(
        err?.response?.status === 404
          ? err.response.data.data
          : "Erro ao tentar atualizar ticket"
      );
      setLoading(false);
    }
    setIsModalVisible(false);
    setQueue("");
    setReceiver("");
  };

  const handleCancel = () => {
    setQueue("");
    setReceiver("");
    setIsModalVisible(false);
  };

  return (
    <>
      <Button type="text" icon={<EditOutlined />} onClick={showModal}></Button>
      <Modal
        title="Editar"
        open={isModalVisible}
        onCancel={handleCancel}
        onOk={form.submit}
        confirmLoading={loading}
      >
        <Form form={form} layout="vertical" onFinish={handleOk}>
          <Form.Item label="Indique a nova fila do ticket:" name="ticket_queue">
            <UsersSelect
              placeholder="Selecione a fila"
              currentValue={queue}
              name={"queue"}
              queueItems={
                localStorage.getItem("@otrs/queues")
                  ? JSON.parse(localStorage.getItem("@otrs/queues"))
                  : []
              }
              setQueue={(queue) => setQueue(queue)}
            />
          </Form.Item>
          <Form.Item
            label="Indique o novo proprietário do ticket:"
            name="ticket_owner"
          >
            <UsersSelect
              placeholder="Selecione o novo proprietário"
              currentValue={receiver}
              name={"receiver"}
              receiver={architects}
              setReceiver={(receiver) => setReceiver(receiver)}
            />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};
