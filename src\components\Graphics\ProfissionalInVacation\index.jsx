import {
  Card,
  Col,
  Modal,
  Pagination,
  Row,
  Select,
  Spin,
  Table,
  Typography,
} from "antd";
import { React, useEffect, useState } from "react";
import { StarFilled, StarOutlined } from "@ant-design/icons";
import "./ProfissionalInVacation.css";
import TableProfessionalInVacation from "../../Table/ProfessionalInVacation";
import { otrsGet, otrsPost } from "../../../service/apiOtrs";
import { filterByOrderOfInput } from "../../../utils/filterByOrderOfInput";

const STATE_IN_VACATION = 0;
const STATE_LEAVE_DAREDE = 2;

export default function ProfissionalInVacation(props) {
  const { Title, Text } = Typography;
  const [isFavorite, setIsFavorite] = useState(false);
  const [professional, setProfessional] = useState([]);
  const [page, setPage] = useState(1);
  const [modalVisible, setModalVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState([]);
  const [allProfessional, setAllProfessional] = useState([]);
  const [professionalData, setProfessionalData] = useState([]);

  const [filter, setFilter] = useState({
    vacation: 0,
    professional: "",
  });
  const GRAPH_NAME = "ProfissionalInVacation";

  const { Option } = Select;

  const columns = [
    {
      title: "Profissional",
      dataIndex: "first_name",
      key: "first_name",
      render: (first_name) => <Text>{first_name}</Text>,
    },
    {
      title: "Tickets",
      dataIndex: "ticket",
      key: "ticket",
      render: (ticket) => <Text>{ticket.length}</Text>,
    },
  ];

  useEffect(() => {
    setIsFavorite(checkIsVisible());
    getAllProfessionals();
  }, [props.favorites]);

  function getAllProfessionals() {
    setLoading(true);
    otrsGet("read/user/all/0")
      .then(async (resp) => {
        let inactive = await inactiveProfessionals(resp.data);

        setProfessional(inactive);
        await paginateProfessionals(inactive.reverse(), 10, 1);
        setLoading(false);
      })
      .catch((error) => {
        console.log(error);
      });
  }

  async function inactiveProfessionals(professionals) {
    let inactive = professionals.filter((professional) => {
      return professional.valid_id !== 1;
    });

    inactive.forEach((professional) => {
      professional.first_name += ` ${professional.last_name}`;
    });
    return inactive;
  }

  async function tableFill(page) {
    for (const item of page) {
      let ticket = await getAllTicketsByProfessional(item.login);
      item.ticket = ticket.data;
    }
    setData(page);
    setAllProfessional(page);
  }

  async function paginateProfessionals(professionals, pageSize, pageNumber) {
    setLoading(true);
    let page = professionals.slice(
      (pageNumber - 1) * pageSize,
      pageNumber * pageSize
    );
    setPage(pageNumber);

    await tableFill(page);
    setLoading(false);
  }

  async function getAllTicketsByProfessional(login) {
    return await otrsPost("read/tickets/pdm", { params: login });
  }

  function showModal() {
    setModalVisible(!modalVisible);
  }

  function handleOk() {
    setModalVisible(false);
  }

  function handleCancel() {
    setModalVisible(false);
  }

  function checkIsVisible() {
    let localStorageData = JSON.parse(localStorage.getItem("favorites"));
    if (localStorageData) {
      var index = localStorageData.findIndex(
        (value) => value.component === GRAPH_NAME
      );

      return index !== -1;
    } else {
      return false;
    }
  }

  function swapVisible() {
    let object = structuredClone(props.componentVisibles);
    object.kanban = !object.kanban;
    props.setComponentVisibles(object);
  }

  function handleVisibleChange() {
    setModalVisible(!modalVisible);
  }

  async function updateFavortite() {
    await props?.setVisionFavorite(GRAPH_NAME);
    setIsFavorite(checkIsVisible());
  }

  function filterProfessional(value) {
    let newFilter = structuredClone(filter);
    if (value === "Todos") {
      return setData(allProfessional);
    }
    newFilter.professional = value;
    setFilter(newFilter);
    newFilter = allProfessional.filter((item) => item.login === value);
    setData(newFilter);
    filterTable(newFilter);
  }

  function filterTable(filters) {
    let newFilteredClient = structuredClone(data);
    if (filters.vacation !== -1) {
      if (filters.vacation === STATE_IN_VACATION) {
        newFilteredClient = newFilteredClient.filter(
          (val) => val.vacation === STATE_IN_VACATION
        );
      } else if (filters.vacation === STATE_LEAVE_DAREDE) {
        newFilteredClient = newFilteredClient.filter(
          (val) => val.vacation === STATE_LEAVE_DAREDE
        );
      }
    }

    if (filters.client && filters.client !== "Todos") {
      newFilteredClient = newFilteredClient.filter(
        (val) => val.client === filters.client
      );
    }

    //   setFilteredClient(newFilteredClient)
  }

  const sortAlpha = (array) => {
    const sortedArray = array.sort((a, b) => {
      if (a.first_name > b.first_name) {
        return 1;
      }
      if (a.first_name < b.first_name) {
        return -1;
      }
      return 0;
    });

    return sortedArray;
  };

  return (
    <Card
      bordered="false"
      style={{
        borderRadius: "20px",
        height: "100%",
        minWidth: "425px",
        minHeight: "410px",
      }}
    >
      {loading ? (
        <Row
          style={{ height: "100%", width: "100%" }}
          align="middle"
          justify="center"
        >
          <Spin />
        </Row>
      ) : (
        <>
          <Title level={4} style={{ fontWeight: 400 }}>
            Profissionais em Férias
          </Title>
          <Row justify="space-between" style={{ marginBottom: "10px" }}>
            <Col span={22}>
              <Select
                showSearch
                style={{ width: "70%" }}
                placeholder="Profissional"
                optionFilterProp="children"
                onChange={(value) => {
                  filterProfessional(value);
                }}
                filterOption={(input, option) =>
                  filterByOrderOfInput(
                    input,
                    option?.value?.split("@")[0]?.toLowerCase()
                  )
                }
              >
                <Option value={"Todos"}>{"Todos"}</Option>
                {sortAlpha(professional)?.map((val, index) => (
                  <Option key={index} value={val?.login}>
                    {val?.first_name} {val?.last_name}
                  </Option>
                ))}
              </Select>
            </Col>
            <Col span={1}>
              {!isFavorite ? (
                <StarOutlined onClick={updateFavortite} className="star-icon" />
              ) : (
                <StarFilled onClick={updateFavortite} className="star-icon" />
              )}
            </Col>
          </Row>
          <div className="container-table">
            <Table
              scroll={{ y: 180 }}
              pagination={false}
              columns={columns}
              dataSource={data}
            />
            <Pagination
              onChange={(page, pageSize) => {
                paginateProfessionals(professional, pageSize, page);
              }}
              defaultCurrent={1}
              className="paginator"
              current={page}
              size="small"
              responsive={true}
              total={professional.length}
            />
          </div>
          <Modal
            closable={false}
            width={"1090px"}
            style={{ display: "flex", justifyContent: "center" }}
            bodyStyle={{ backgroundColor: "transparent" }}
            cancelButtonProps={{ style: { display: "none" } }}
            footer={null}
            open={modalVisible}
            onOk={handleOk}
            onCancel={handleCancel}
          >
            <TableProfessionalInVacation
              professional={professionalData}
              showModal={showModal}
            />
          </Modal>
        </>
      )}
    </Card>
  );
}
