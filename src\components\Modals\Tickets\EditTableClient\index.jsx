import { But<PERSON>, Mo<PERSON>, <PERSON>, Checkbox, Row, Col } from "antd";
import { MoreOutlined } from "@ant-design/icons";
import { useState } from "react";
import { Select } from "antd";

const { Option } = Select;

export const EditTableClient = (props) => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const { clients_information, setClientsInfo } = props;
  const [form] = Form.useForm();

  const showModal = () => {
    setIsModalVisible(true);
  };

  const handleOk = () => {
    form.submit();
  };

  const handleCancel = () => {
    setIsModalVisible(false);
  };

  const handleSubmit = async (data) => {
    setClientsInfo(
      clients_information.filter((a) => {
        if (data.lista.includes(a.code)) {
          return a.code;
        }
      })
    );
    setIsModalVisible(false);
  };

  return (
    <>
      <Button
        type="primary"
        icon={<MoreOutlined />}
        onClick={showModal}
        style={{ boxShadow:'none', height: 'auto' }}
      ></Button>
      <Modal
        title="Configuração"
        open={isModalVisible}
        onOk={handleOk}
        onCancel={handleCancel}
      >
        <Form layout="vertical" onFinish={handleSubmit} form={form} >
          <Form.Item name="lista" label="Fila selecionada:">
            <Checkbox.Group
              style={{
                width: "100%",
              }}
            >
              <Row>
                <Col span={8}>
                  <Checkbox value="name">Nome</Checkbox>
                </Col>
                <Col span={8}>
                  <Checkbox value="last_name">Sobrenome</Checkbox>
                </Col>
                <Col span={8}>
                  <Checkbox value="user">Usuário</Checkbox>
                </Col>
                <Col span={8}>
                  <Checkbox value="email">E-mail</Checkbox>
                </Col>
                <Col span={8}>
                  <Checkbox value="client">Cliente</Checkbox>
                </Col>
              </Row>
            </Checkbox.Group>
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};
