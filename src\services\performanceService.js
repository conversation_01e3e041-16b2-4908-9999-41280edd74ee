/**
 * Serviço de Monitoramento de Performance
 * Coleta e analisa métricas de performance da aplicação
 */

class PerformanceService {
  constructor() {
    this.metrics = new Map();
    this.observers = new Map();
    this.isSupported = this.checkSupport();
    this.startTime = performance.now();
    
    if (this.isSupported) {
      this.setupObservers();
      this.collectInitialMetrics();
    }
  }

  /**
   * Verifica suporte do navegador
   */
  checkSupport() {
    return !!(
      window.performance &&
      window.performance.mark &&
      window.performance.measure &&
      window.PerformanceObserver
    );
  }

  /**
   * Configura observers de performance
   */
  setupObservers() {
    try {
      // Observer para navegação
      if (PerformanceObserver.supportedEntryTypes.includes('navigation')) {
        const navObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            this.processNavigationEntry(entry);
          }
        });
        navObserver.observe({ entryTypes: ['navigation'] });
        this.observers.set('navigation', navObserver);
      }

      // Observer para recursos
      if (PerformanceObserver.supportedEntryTypes.includes('resource')) {
        const resourceObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            this.processResourceEntry(entry);
          }
        });
        resourceObserver.observe({ entryTypes: ['resource'] });
        this.observers.set('resource', resourceObserver);
      }

      // Observer para paint
      if (PerformanceObserver.supportedEntryTypes.includes('paint')) {
        const paintObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            this.processPaintEntry(entry);
          }
        });
        paintObserver.observe({ entryTypes: ['paint'] });
        this.observers.set('paint', paintObserver);
      }

      // Observer para layout shifts
      if (PerformanceObserver.supportedEntryTypes.includes('layout-shift')) {
        const clsObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            this.processLayoutShiftEntry(entry);
          }
        });
        clsObserver.observe({ entryTypes: ['layout-shift'] });
        this.observers.set('layout-shift', clsObserver);
      }

      // Observer para largest contentful paint
      if (PerformanceObserver.supportedEntryTypes.includes('largest-contentful-paint')) {
        const lcpObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            this.processLCPEntry(entry);
          }
        });
        lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
        this.observers.set('largest-contentful-paint', lcpObserver);
      }

      // Observer para first input delay
      if (PerformanceObserver.supportedEntryTypes.includes('first-input')) {
        const fidObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            this.processFIDEntry(entry);
          }
        });
        fidObserver.observe({ entryTypes: ['first-input'] });
        this.observers.set('first-input', fidObserver);
      }
    } catch (error) {
      console.warn('Erro ao configurar observers de performance:', error);
    }
  }

  /**
   * Coleta métricas iniciais
   */
  collectInitialMetrics() {
    // Métricas de navegação
    const navigation = performance.getEntriesByType('navigation')[0];
    if (navigation) {
      this.processNavigationEntry(navigation);
    }

    // Métricas de paint
    const paintEntries = performance.getEntriesByType('paint');
    paintEntries.forEach(entry => this.processPaintEntry(entry));

    // Métricas de recursos
    const resourceEntries = performance.getEntriesByType('resource');
    resourceEntries.forEach(entry => this.processResourceEntry(entry));
  }

  /**
   * Processa entrada de navegação
   */
  processNavigationEntry(entry) {
    const metrics = {
      domContentLoaded: entry.domContentLoadedEventEnd - entry.domContentLoadedEventStart,
      loadComplete: entry.loadEventEnd - entry.loadEventStart,
      domInteractive: entry.domInteractive - entry.navigationStart,
      domComplete: entry.domComplete - entry.navigationStart,
      redirectTime: entry.redirectEnd - entry.redirectStart,
      dnsTime: entry.domainLookupEnd - entry.domainLookupStart,
      connectTime: entry.connectEnd - entry.connectStart,
      requestTime: entry.responseStart - entry.requestStart,
      responseTime: entry.responseEnd - entry.responseStart,
      renderTime: entry.domComplete - entry.responseEnd,
      totalTime: entry.loadEventEnd - entry.navigationStart
    };

    this.setMetric('navigation', metrics);
  }

  /**
   * Processa entrada de recurso
   */
  processResourceEntry(entry) {
    const resourceMetrics = this.getMetric('resources') || [];
    
    const resource = {
      name: entry.name,
      type: this.getResourceType(entry),
      size: entry.transferSize || 0,
      duration: entry.duration,
      startTime: entry.startTime,
      endTime: entry.responseEnd
    };

    resourceMetrics.push(resource);
    this.setMetric('resources', resourceMetrics);
  }

  /**
   * Processa entrada de paint
   */
  processPaintEntry(entry) {
    this.setMetric(entry.name, entry.startTime);
  }

  /**
   * Processa entrada de layout shift
   */
  processLayoutShiftEntry(entry) {
    const cls = this.getMetric('cumulativeLayoutShift') || 0;
    this.setMetric('cumulativeLayoutShift', cls + entry.value);
  }

  /**
   * Processa entrada de LCP
   */
  processLCPEntry(entry) {
    this.setMetric('largestContentfulPaint', entry.startTime);
  }

  /**
   * Processa entrada de FID
   */
  processFIDEntry(entry) {
    this.setMetric('firstInputDelay', entry.processingStart - entry.startTime);
  }

  /**
   * Determina tipo de recurso
   */
  getResourceType(entry) {
    if (entry.initiatorType) {
      return entry.initiatorType;
    }
    
    const url = new URL(entry.name);
    const extension = url.pathname.split('.').pop().toLowerCase();
    
    const typeMap = {
      'js': 'script',
      'css': 'link',
      'png': 'img',
      'jpg': 'img',
      'jpeg': 'img',
      'gif': 'img',
      'svg': 'img',
      'woff': 'font',
      'woff2': 'font',
      'ttf': 'font'
    };
    
    return typeMap[extension] || 'other';
  }

  /**
   * Define métrica
   */
  setMetric(key, value) {
    this.metrics.set(key, {
      value,
      timestamp: Date.now(),
      performanceNow: performance.now()
    });
  }

  /**
   * Obtém métrica
   */
  getMetric(key) {
    const metric = this.metrics.get(key);
    return metric ? metric.value : null;
  }

  /**
   * Obtém todas as métricas
   */
  getAllMetrics() {
    const result = {};
    for (const [key, metric] of this.metrics.entries()) {
      result[key] = metric.value;
    }
    return result;
  }

  /**
   * Marca início de uma operação
   */
  markStart(name) {
    if (!this.isSupported) return;
    
    try {
      performance.mark(`${name}-start`);
    } catch (error) {
      console.warn('Erro ao marcar início:', error);
    }
  }

  /**
   * Marca fim de uma operação e calcula duração
   */
  markEnd(name) {
    if (!this.isSupported) return null;
    
    try {
      const endMark = `${name}-end`;
      const startMark = `${name}-start`;
      
      performance.mark(endMark);
      performance.measure(name, startMark, endMark);
      
      const measure = performance.getEntriesByName(name, 'measure')[0];
      const duration = measure ? measure.duration : null;
      
      this.setMetric(`custom-${name}`, duration);
      
      // Limpar marks
      performance.clearMarks(startMark);
      performance.clearMarks(endMark);
      performance.clearMeasures(name);
      
      return duration;
    } catch (error) {

      return null;
    }
  }

  /**
   * Mede duração de uma função
   */
  measureFunction(name, fn) {
    this.markStart(name);
    
    try {
      const result = fn();
      
      if (result && typeof result.then === 'function') {
        // Função assíncrona
        return result.finally(() => {
          this.markEnd(name);
        });
      } else {
        // Função síncrona
        this.markEnd(name);
        return result;
      }
    } catch (error) {
      this.markEnd(name);
      throw error;
    }
  }

  /**
   * Obtém estatísticas de recursos
   */
  getResourceStats() {
    const resources = this.getMetric('resources') || [];
    
    const stats = {
      total: resources.length,
      totalSize: 0,
      totalDuration: 0,
      byType: {},
      slowest: null,
      largest: null
    };

    resources.forEach(resource => {
      stats.totalSize += resource.size;
      stats.totalDuration += resource.duration;
      
      if (!stats.byType[resource.type]) {
        stats.byType[resource.type] = {
          count: 0,
          size: 0,
          duration: 0
        };
      }
      
      stats.byType[resource.type].count++;
      stats.byType[resource.type].size += resource.size;
      stats.byType[resource.type].duration += resource.duration;
      
      if (!stats.slowest || resource.duration > stats.slowest.duration) {
        stats.slowest = resource;
      }
      
      if (!stats.largest || resource.size > stats.largest.size) {
        stats.largest = resource;
      }
    });

    return stats;
  }

  /**
   * Obtém Core Web Vitals
   */
  getCoreWebVitals() {
    return {
      LCP: this.getMetric('largestContentfulPaint'), // < 2.5s
      FID: this.getMetric('firstInputDelay'), // < 100ms
      CLS: this.getMetric('cumulativeLayoutShift'), // < 0.1
      FCP: this.getMetric('first-contentful-paint'), // < 1.8s
      TTFB: this.getNavigationMetric('responseStart') // < 600ms
    };
  }

  /**
   * Obtém métrica de navegação específica
   */
  getNavigationMetric(key) {
    const navigation = this.getMetric('navigation');
    return navigation ? navigation[key] : null;
  }

  /**
   * Gera relatório de performance
   */
  generateReport() {
    return {
      timestamp: Date.now(),
      sessionDuration: performance.now() - this.startTime,
      coreWebVitals: this.getCoreWebVitals(),
      navigation: this.getMetric('navigation'),
      resources: this.getResourceStats(),
      customMetrics: this.getCustomMetrics(),
      memoryUsage: this.getMemoryUsage()
    };
  }

  /**
   * Obtém métricas customizadas
   */
  getCustomMetrics() {
    const custom = {};
    for (const [key, metric] of this.metrics.entries()) {
      if (key.startsWith('custom-')) {
        custom[key.replace('custom-', '')] = metric.value;
      }
    }
    return custom;
  }

  /**
   * Obtém uso de memória
   */
  getMemoryUsage() {
    if (performance.memory) {
      return {
        usedJSHeapSize: performance.memory.usedJSHeapSize,
        totalJSHeapSize: performance.memory.totalJSHeapSize,
        jsHeapSizeLimit: performance.memory.jsHeapSizeLimit
      };
    }
    return null;
  }

  /**
   * Limpa métricas antigas
   */
  cleanup() {
    const now = Date.now();
    const maxAge = 300000; // 5 minutos
    
    for (const [key, metric] of this.metrics.entries()) {
      if (now - metric.timestamp > maxAge) {
        this.metrics.delete(key);
      }
    }
  }

  /**
   * Destrói o serviço
   */
  destroy() {
    // Desconectar observers
    for (const observer of this.observers.values()) {
      observer.disconnect();
    }
    this.observers.clear();
    
    // Limpar métricas
    this.metrics.clear();
  }
}

// Instância singleton
export const performanceService = new PerformanceService();

export default performanceService;
