import { render } from "@testing-library/react";
import { Counter } from "../../../../components/Counter";

describe("Counter Component", () => {
  it("should render correctly", () => {
    render(<Counter tableData={[]} />);
  });
  it("should render the correct number of items", () => {
    const { getByText } = render(<Counter tableData={[1, 2, 3]} />);
    expect(getByText("Total: 3")).toBeInTheDocument();
  });
  it("should not render if there is no data", () => {
    const tableData = [];
    const { queryByTestId } = render(<Counter tableData={tableData} />);
    const counterElement = queryByTestId("counter");

    expect(counterElement).not.toBeInTheDocument();
  });
  it("should not render if no data is passed", () => {
    const { queryByTestId } = render(<Counter tableData={undefined} />);
    const counterElement = queryByTestId("counter");

    expect(counterElement).not.toBeInTheDocument();
  });
});
