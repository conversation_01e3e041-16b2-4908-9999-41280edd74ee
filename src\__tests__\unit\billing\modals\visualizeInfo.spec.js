import { contractMock, invoicesMock } from "../../../../__mocks__/billing/data";
import {
  formatInvoiceList,
  calculateTotal,
  calculatePercentage,
  calculateContractPercentage,
} from "../../../../components/Modals/Billing/controllers/visualizeInfo";

describe("formatInvoiceList", () => {
  it("Should return a list of correctly formatted invoices", () => {
    const invoices = [
      {
        full_date: "2024-01",
        accounts: [
          { cost: "100", line_item_type: "Usage" },
          { cost: "-20", line_item_type: "Tax" },
          { cost: "50", line_item_type: "SavingsPlanCoveredUsage" },
        ],
      },
      {
        full_date: "2024-02",
        accounts: [
          { cost: "80", line_item_type: "Usage" },
          { cost: "-10", line_item_type: "Tax" },
          { cost: "-30", line_item_type: "SavingsPlanCoveredUsage" },
        ],
      },
    ];

    const expectedOutput = [
      { name: "Jan/2024", $: "150.00", formattedValue: "$150,00" },
      { name: "Feb/2024", $: "50.00", formattedValue: "$50,00" },
    ];

    const formattedInvoices = formatInvoiceList(invoices);
    expect(formattedInvoices).toEqual(expectedOutput);
  });

  it("Should return an empty list if no arguments are provided", () => {
    const invoices = null;
    const formattedInvoices = formatInvoiceList(invoices);
    expect(formattedInvoices).toEqual([]);
  });
});

describe("calculateTotal", () => {
  it("Should calculate the total correctly", () => {
    const invoices = [{ $: "100" }, { $: "200" }, { $: "50" }];

    const total = calculateTotal(invoices);

    expect(total).toBe("$350,00");
  });

  it("Should return $0.00 if there are no invoices", () => {
    const invoices = [];

    const total = calculateTotal(invoices);

    expect(total).toBe("$0,00");
  });
});

describe("calculatePercentage", () => {
  it("Should calculate the percentage correctly", () => {
    const invoices = [{ $: "100" }, { $: "200" }, { $: "50" }];
    const commitmentValue = "500,00";

    const percentage = calculatePercentage(invoices, commitmentValue);

    expect(percentage).toBe("70.00");
  });

  it("Should return 0.00% if there are no invoices", () => {
    const invoices = [];
    const commitmentValue = "500,00";

    const percentage = calculatePercentage(invoices, commitmentValue);

    expect(percentage).toBe("0.00");
  });
});

describe("calculateContractPercentage", () => {
  it("Should calculate the contract percentage correctly", async () => {
    const percentage = await calculateContractPercentage(
      contractMock,
      invoicesMock
    );
    expect(percentage).toBe("100.00");
  });
});
