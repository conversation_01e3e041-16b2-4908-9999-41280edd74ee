import { dynamoGenericGetById } from "../../service/apiDynamoGeneric";
import { logNewAuditAction } from "./logNewAuditAction";

export const logBillingEdit = (contractId, customerId) => {
  if (!contractId || contractId === "") {
    const returnMessage = {
      statusCode: 501,
      message: `Error adding edit billing audit action log. Using wrong parameter when calling the function. Wrong parameter considered: 'contractId'`,
    };
    return returnMessage;
  }

  if (!customerId || customerId === "") {
    const returnMessage = {
      statusCode: 502,
      message: `Error adding edit billing audit action log. Using wrong parameter when calling the function. Wrong parameter considered: 'customerId'`,
    };
    return returnMessage;
  }

  try {
    dynamoGenericGetById(
      `${process.env.REACT_APP_STAGE}-contracts`,
      contractId
    ).then((contractData) => {
      if (contractData) {
        const username = localStorage.getItem("@dsm/username");
        const title = "Edição de Billing";
        const description = `${username} editou o billing do contrato: ${
          contractData.name
        } de DSM ID: ${contractData.dsm_id || ""}`;
        logNewAuditAction(username, title, description);

        const returnMessage = {
          statusCode: 200,
          message: `Success adding edit billing audit action log: ${title}`,
        };

        return returnMessage;
      } else {
        dynamoGenericGetById(
          `${process.env.REACT_APP_STAGE}-customers`,
          customerId
        ).then((customerData) => {
          const username = localStorage.getItem("@dsm/username");
          const title = "Edição de Billing";
          const description = `${username} editou um billing onde os dados do contrato não foram encontrados. Customer ITSM ID: ${
            customerData.identifications.itsm_id || ""
          } Contract ID: ${contractId}`;
          logNewAuditAction(username, title, description);

          const returnMessage = {
            statusCode: 201,
            message: `Success adding edit billing audit action log with no contract data: ${title}`,
          };

          return returnMessage;
        });
      }
    });
  } catch (error) {
    const returnMessage = {
      statusCode: 500,
      message: `Error adding edit billing audit action log. Error message: ${error}`,
    };
    return returnMessage;
  }
};
