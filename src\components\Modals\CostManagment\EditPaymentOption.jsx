import {
    PlusOutlined,
    DeleteOutlined,
    EditOutlined,
    CreditCardOutlined
  } from "@ant-design/icons";
  import { useState } from "react";
  import {
    Button,
    Modal,
    Row,
    Col,
    Table,
    Input,
    Typography,
    Popover
  } from "antd";
  
  let count = 1;
  
  export const EditPaymentOption = ({paymentOption}) => {
    const { Title } = Typography;
    const [inputValidation, setInputValidation] = useState({
        name: false,
        description: false,
    });
    const [showModal, setShowModal] = useState();
    const [currentPaymentOption, setCurrentPaymentOption] = useState({
      id: paymentOption.id,
      name: paymentOption.name,
      description: paymentOption.description,
    });
    const [listInput, setListInput] = useState([
        {
            id: paymentOption.id,
            name: paymentOption.name,
            description: paymentOption.description,
        }
    ]);
  
    const handleSubmit = () => {
      setShowModal(false)
    }
  
    const handleRemoveItem = (index) => {
      setListInput((prev) => [...prev.slice(0, index), ...prev.slice(index + 1)]);
    };
  
    const handleAddItem = () => {
      let index = 0;
      const addedItem = {
        id: count++,
        name: currentPaymentOption.name,
        description: currentPaymentOption.description,
      };
      setListInput((prev) => [
        ...prev.slice(0, index + 1),
        addedItem,
        ...prev.slice(index + 1),
      ]);
    };
  
    const columns = [
      {
        title: "Nome",
        dataIndex: "name",
        key: "name",
      },
      {
        title: "Descrição",
        dataIndex: "description",
        key: "description",
      },
      {
        title: "Remover",
        dataIndex: "remove",
        key: "remove",
        render: (id, props) => {
          return (
            <Button
              danger
              type="text"
              icon={<DeleteOutlined />}
              onClick={() => {
                handleRemoveItem(props.id);
              }}
            ></Button>
          );
        },
      },
    ];
    return (
      <>
        <Button
          type="text"
          onClick={() => {
            setShowModal(true);
          }}
          icon={<EditOutlined />}
        >
        </Button>
        <Modal
          title="Adicionar forma de pagamento"
          visible={showModal}
          closable={false}
          onOk={handleSubmit}
          okText="Salvar"
          cancelText="Cancelar"
          onCancel={() => {
            setShowModal(false);
            setListInput([]);
  
          }}
        >
  
        <Row gutter={[16, 16]} justify="center" style={{ marginBottom: '45px' }}>
          <Col span={12}>
            <Title level={5} style={{ fontWeight: "400", fontSize: "14px" }}>
              Alterar nome
            </Title>
            <Input
              status={inputValidation.name === true ? "error" : "success"}
              placeholder="Novo nome"  
              required
              onChange={(e) => {
                if (e.target.value !== paymentOption.name) {
                    setCurrentPaymentOption({
                    ...currentPaymentOption,
                    name: e.target.value,
                    });
                    setInputValidation({
                        ...inputValidation,
                        name: false,
                    });

                }else{
                    setInputValidation({
                        ...inputValidation,
                        name: true,
                    });
                }
              }}
            />
          </Col>
          <Col span={12}>
            <Title level={5} style={{ fontWeight: "400", fontSize: "14px" }}>
              Descrição
            </Title>
            <Input
              status={inputValidation.description === true ? "error" : "success"}
              placeholder="Nova descrição"  
              required
              onChange={(e) => {
                if (e.target.value !== paymentOption.description) {
                    setCurrentPaymentOption({
                    ...currentPaymentOption,
                    description: e.target.value,
                    });
                    setInputValidation({
                        ...inputValidation,
                        description: false,
                    });

                }else{
                    setInputValidation({
                        ...inputValidation,
                        description: true,
                    });
                }
              }}
            />
          </Col>
          <Col>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => {
                handleAddItem();
              }}
              disabled={currentPaymentOption.name !== paymentOption.name || currentPaymentOption.description !== paymentOption.description}
            >
              Adicionar
            </Button>
          </Col>
        </Row>
  
          {listInput.length > 0 ?
              <Table
              dataSource={listInput.map((item, index) => {
                return {
                  id: index,
                  name: item.name,
                  description: item.description,
                };
              })}
              columns={columns}
              scroll={{ x: 100 }}
              pagination={false}
            ></Table>
            :
            <Row justify="center" gutter={[24, 16]}>
              <Col style={{
                  backgroundColor: '#0f9347', 
                  borderRadius: '128px', 
                  opacity: '.75',
                  boxShadow: '10px 10px 10px rgba(15, 147, 71, 0.8)'
                }}>
                <CreditCardOutlined style={{fontSize: '128px', color: '#fff', margin: '18px'}}/>
              </Col>
              <Col>
                <Title level={5} style={{fontWeight: '400'}}>Não existem outras formas de pagamento adicionadas</Title>
              </Col>
            </Row>
          }
        </Modal>
      </>
    );
  };
  