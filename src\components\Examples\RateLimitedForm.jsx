import React, { useState } from 'react';
import { Form, Input, Button, Card, Space, message } from 'antd';
import { useFormRateLimit } from '../../hooks/useRateLimit';
import RateLimitIndicator, { FormRateLimitWarning } from '../RateLimit/RateLimitIndicator';

/**
 * Exemplo de formulário com rate limiting
 * Demonstra como integrar controle de taxa em formulários
 */
const RateLimitedForm = ({ formName = 'example', onSubmit, ...props }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  // Hook de rate limiting para formulários
  const rateLimiter = useFormRateLimit(formName, {
    maxRequests: 5,
    windowMs: 300000, // 5 minutos
    onLimitReached: (info) => {
      message.warning(
        `Limite de submissões atingido. Tente novamente em ${Math.ceil(info.resetTime - Date.now()) / 1000} segundos.`
      );
    }
  });

  /**
   * Manipula o envio do formulário com rate limiting
   */
  const handleSubmit = async (values) => {
    if (rateLimiter.isLimited) {
      message.error('Limite de submissões atingido. Aguarde antes de tentar novamente.');
      return;
    }

    setLoading(true);

    try {
      // Usar o wrapper de rate limiting
      const submitWithRateLimit = rateLimiter.withRateLimit(async (data) => {
        if (onSubmit) {
          return await onSubmit(data);
        }
        
        // Simular envio
        await new Promise(resolve => setTimeout(resolve, 1000));
        return { success: true };
      });

      const result = await submitWithRateLimit(values);
      
      if (result.success) {
        message.success('Formulário enviado com sucesso!');
        form.resetFields();
      }
    } catch (error) {
      if (error.message.includes('Rate limit exceeded')) {
        message.error('Muitas tentativas. Aguarde antes de tentar novamente.');
      } else {
        message.error('Erro ao enviar formulário: ' + error.message);
      }
    } finally {
      setLoading(false);
    }
  };

  /**
   * Reset manual do rate limiter
   */
  const handleReset = () => {
    rateLimiter.reset();
    message.info('Rate limiter resetado');
  };

  return (
    <Card 
      title="Formulário com Rate Limiting"
      extra={
        <Space>
          <RateLimitIndicator
            requestCount={rateLimiter.requestCount}
            maxRequests={rateLimiter.maxRequests}
            isLimited={rateLimiter.isLimited}
            timeRemaining={rateLimiter.timeRemaining}
            formatTimeRemaining={rateLimiter.formatTimeRemaining}
            usagePercentage={rateLimiter.usagePercentage}
            size="small"
            type="line"
            showAlert={false}
          />
          <Button size="small" onClick={handleReset}>
            Reset
          </Button>
        </Space>
      }
      {...props}
    >
      <Space direction="vertical" style={{ width: '100%' }} size="large">
        {/* Aviso de rate limit */}
        <FormRateLimitWarning
          isLimited={rateLimiter.isLimited}
          timeRemaining={rateLimiter.timeRemaining}
          formatTimeRemaining={rateLimiter.formatTimeRemaining}
          maxRequests={rateLimiter.maxRequests}
          requestCount={rateLimiter.requestCount}
        />

        {/* Indicador detalhado */}
        <RateLimitIndicator
          requestCount={rateLimiter.requestCount}
          maxRequests={rateLimiter.maxRequests}
          isLimited={rateLimiter.isLimited}
          timeRemaining={rateLimiter.timeRemaining}
          formatTimeRemaining={rateLimiter.formatTimeRemaining}
          usagePercentage={rateLimiter.usagePercentage}
          showProgress={true}
          showAlert={false}
          showTooltip={true}
          type="line"
        />

        {/* Formulário */}
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          disabled={rateLimiter.isLimited}
        >
          <Form.Item
            label="Nome"
            name="name"
            rules={[
              { required: true, message: 'Por favor, insira seu nome!' }
            ]}
          >
            <Input placeholder="Digite seu nome" />
          </Form.Item>

          <Form.Item
            label="Email"
            name="email"
            rules={[
              { required: true, message: 'Por favor, insira seu email!' },
              { type: 'email', message: 'Email inválido!' }
            ]}
          >
            <Input placeholder="Digite seu email" />
          </Form.Item>

          <Form.Item
            label="Mensagem"
            name="message"
            rules={[
              { required: true, message: 'Por favor, insira uma mensagem!' }
            ]}
          >
            <Input.TextArea 
              rows={4} 
              placeholder="Digite sua mensagem"
            />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
                disabled={rateLimiter.isLimited || !rateLimiter.canMakeRequest}
              >
                Enviar ({rateLimiter.remainingRequests} restantes)
              </Button>
              
              <Button onClick={() => form.resetFields()}>
                Limpar
              </Button>
            </Space>
          </Form.Item>
        </Form>

        {/* Informações de debug */}
        <Card size="small" title="Debug Info" type="inner">
          <Space direction="vertical" size="small">
            <div>Requisições: {rateLimiter.requestCount}/{rateLimiter.maxRequests}</div>
            <div>Limitado: {rateLimiter.isLimited ? 'Sim' : 'Não'}</div>
            <div>Pode fazer requisição: {rateLimiter.canMakeRequest ? 'Sim' : 'Não'}</div>
            <div>Uso: {Math.round(rateLimiter.usagePercentage)}%</div>
            <div>Restantes: {rateLimiter.remainingRequests}</div>
            {rateLimiter.timeRemaining > 0 && (
              <div>Reset em: {rateLimiter.formatTimeRemaining()}</div>
            )}
          </Space>
        </Card>
      </Space>
    </Card>
  );
};

export default RateLimitedForm;
