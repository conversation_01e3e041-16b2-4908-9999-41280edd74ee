/**
 * Script para configurar permissões padrão
 * Resolve o problema do Permission ID "1" não encontrado
 * 
 * Como usar:
 * 1. Abrir console do navegador na página do DSM
 * 2. Copiar e colar este código
 * 3. Executar: setupDefaultPermissions()
 */

window.setupDefaultPermissions = async function() {
  console.log('🚀 Iniciando configuração de permissões padrão...');
  
  try {
    // Importar utilitários necessários
    const { dynamoPost, dynamoGetById } = await import('../service/apiDsmDynamo');
    
    const tableName = `${process.env.REACT_APP_STAGE || 'dev'}-permissions`;
    const permissionId = "1";
    
    // Verificar se já existe
    console.log('🔍 Verificando se registro já existe...');
    try {
      const existing = await dynamoGetById(tableName, permissionId);
      if (existing) {
        console.log('✅ Registro de permissão já existe!');
        console.log('📋 Detalhes:', existing);
        return { success: true, message: 'Registro já existe', data: existing };
      }
    } catch (error) {
      console.log('⚠️ Registro não existe, criando...');
    }
    
    // Criar registro padrão
    const defaultRecord = {
      id: "1",
      name_permission: "Permissão Padrão de Desenvolvimento",
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      permissions: [
        {
          page: "Home",
          actions: [
            { code: "view_dashboard" },
            { code: "view_statistics" }
          ]
        },
        {
          page: "Catálogo de Serviços",
          actions: [
            { code: "view_name" },
            { code: "view_description" },
            { code: "view_tag" },
            { code: "view_edit" },
            { code: "view_actions" },
            { code: "create_service" },
            { code: "edit_service" },
            { code: "delete_service" },
            { code: "view_service_details" },
            { code: "export_services" },
            { code: "create_service_management" }
          ]
        },
        {
          page: "Clientes",
          actions: [
            { code: "view_clients" },
            { code: "create_client" },
            { code: "edit_client" },
            { code: "view_client_details" },
            { code: "view_client_contracts" }
          ]
        },
        {
          page: "Contratos",
          actions: [
            { code: "view_contracts" },
            { code: "create_contract" },
            { code: "edit_contract" },
            { code: "view_contract_details" },
            { code: "view_contract_info" },
            { code: "view_contract_executives" },
            { code: "info_modal_view_start_date" },
            { code: "info_modal_view_end_date" },
            { code: "info_modal_view_scope" },
            { code: "info_modal_view_contract_name" },
            { code: "view_info_modal_tab_temporal_info" },
            { code: "view_info_modal_tab_general_info" },
            { code: "view_info_modal_tab_financial_info" },
            { code: "view_info_modal_tab_client_info" },
            { code: "view_info_modal_tab_contract_team_info" },
            { code: "view_info_modal_tab_governance_team_info" }
          ]
        }
      ]
    };
    
    console.log('🔧 Criando registro de permissão padrão...');
    await dynamoPost(tableName, defaultRecord);
    
    console.log('✅ Registro criado com sucesso!');
    console.log('📋 Detalhes:', {
      id: defaultRecord.id,
      name: defaultRecord.name_permission,
      pages: defaultRecord.permissions.length,
      totalActions: defaultRecord.permissions.reduce((total, page) => total + page.actions.length, 0)
    });
    
    // Verificar se foi criado corretamente
    console.log('🔍 Verificando criação...');
    const created = await dynamoGetById(tableName, permissionId);
    
    if (created) {
      console.log('✅ Verificação bem-sucedida! Registro existe na base de dados.');
      console.log('🔄 Recarregue a página para aplicar as novas permissões.');
      return { success: true, message: 'Registro criado com sucesso', data: created };
    } else {
      throw new Error('Registro não foi encontrado após criação');
    }
    
  } catch (error) {
    console.error('❌ Erro ao configurar permissões padrão:', error);
    return { success: false, message: error.message, error };
  }
};

// Instruções de uso
console.log(`
🔧 CONFIGURAÇÃO DE PERMISSÕES PADRÃO

Para resolver o problema "Permission ID '1' não encontrado":

1. Execute no console: setupDefaultPermissions()
2. Aguarde a confirmação de sucesso
3. Recarregue a página

Ou execute manualmente:
window.setupDefaultPermissions().then(result => {
  if (result.success) {
    console.log('✅ Sucesso!', result.message);
    window.location.reload();
  } else {
    console.error('❌ Erro:', result.message);
  }
});
`);

export default window.setupDefaultPermissions;
