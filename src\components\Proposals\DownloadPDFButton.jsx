import { useState } from "react";
import { Button } from "antd";
import { DownloadOutlined } from "@ant-design/icons";
import { generatePDF } from "../../controllers/Proposals/downloadPDF";

export const DownloadPDFButton = ({ data }) => {
  const [downloadPDFLoading, setDownloadPDFLoading] = useState(false);

  const handleClick = async () => {
    setDownloadPDFLoading(true);
    await generatePDF(data);
    setDownloadPDFLoading(false);
  };

  return (
    <Button
      type="text"
      icon={<DownloadOutlined />}
      loading={downloadPDFLoading}
      onClick={handleClick}
    />
  );
};
