.graphBar-swapButtons {
  display: flex;
  gap: 20px;
  width: 40px;
  height: 100%;
  flex-direction: column;
}

.hours-per-month-header {
  display: flex;
  justify-content: space-between;
  width: 100%;
  margin-top: -15px;
  padding-right: 20px;
  padding-left: 20px;
  align-items: center;
}

.barGrapich-icon {
  background-color: #43914f;
  color: white;
}

.barGrapich-icon-off {
  background-color: white;
  color: #43914f;
}

.barGrapich-icon-off,
.barGrapich-icon {
  height: 35px;
  border-radius: 10px;
  cursor: pointer;
  width: 35px;
  padding: 5px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.items {
  width: 100%;
  height: 80%;
  overflow: auto;
  margin-left: 15px;
  padding-right: 10px;
}

.items::-webkit-scrollbar {
  width: 10px;
  background-color: rgba(206, 206, 206, 0.5); /* color of the scroll thumb */
  border-radius: 5px; /* roundness of the scroll thumb */
}

.items::-webkit-scrollbar-thumb {
  background-color: rgba(137, 137, 137, 0.6); /* color of the scroll thumb */
  height: 20px;
  border-radius: 10px; /* roundness of the scroll thumb */
}

p {
  margin: 0;
}
