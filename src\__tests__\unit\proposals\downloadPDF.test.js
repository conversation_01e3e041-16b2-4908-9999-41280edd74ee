import {
  capitalizeFirstLetter,
  payloadPDF,
} from "../../../controllers/Proposals/downloadPDF";

describe("capitalizeFirstLetter", () => {
  it("Should capitalize the first letter of each word in a dot-separated string", () => {
    const input = "nome.sobrenome";
    const expectedOutput = "Nome Sobrenome";
    expect(capitalizeFirstLetter(input)).toEqual(expectedOutput);
  });

  it("Should return an empty string if the input is an empty string", () => {
    const input = "";
    const expectedOutput = "";
    expect(capitalizeFirstLetter(input)).toEqual(expectedOutput);
  });

  it("Should correctly capitalize a single word", () => {
    const input = "nome";
    const expectedOutput = "Nome";
    expect(capitalizeFirstLetter(input)).toEqual(expectedOutput);
  });
});

describe("payloadPDF", () => {
  it("Should return an object with fields filled correctly when all fields are provided", () => {
    const data = {
      id: 1,
      name: "<PERSON>jeto Teste",
      customer: { name: "Cliente Teste" },
      mainOportunity: "4477",
      paymentSelected: "Pagamento Selecionado",
      pipedriveDealOwner: {
        name: "Dono do Negócio",
        email: "<EMAIL>",
      },
    };
    const expectedOutput = {
      customer: "Cliente Teste",
      project: "Projeto Teste",
      proposalID: 1,
      mainOportunity: "4477",
      paymentMethod: "Pagamento Selecionado",
      user: { name: "Dono do Negócio", mail: "<EMAIL>" },
    };
    expect(payloadPDF(data)).toEqual(expectedOutput);
  });

  it("Should use default values for user's name and email when not provided", () => {
    const data = {
      id: 1,
      name: "Projeto Teste",
      customer: { name: "Cliente Teste" },
      mainOportunity: "4477",
      paymentSelected: "Pagamento Selecionado",
      pipedriveDealOwner: null,
    };
    const expectedOutput = {
      customer: "Cliente Teste",
      project: "Projeto Teste",
      proposalID: 1,
      mainOportunity: "4477",
      paymentMethod: "Pagamento Selecionado",
      user: { name: "", mail: "" },
    };
    expect(payloadPDF(data)).toEqual(expectedOutput);
  });

  it("Should use the name and email of the user provided via localStorage if available", () => {
    localStorage.setItem("@dsm/name", "nome.sobrenome");
    localStorage.setItem("@dsm/mail", "<EMAIL>");

    const data = {
      id: 1,
      name: "Projeto Teste",
      customer: { name: "Cliente Teste" },
      mainOportunity: "4477",
      paymentSelected: "Pagamento Selecionado",
      pipedriveDealOwner: null,
    };
    const expectedOutput = {
      customer: "Cliente Teste",
      project: "Projeto Teste",
      proposalID: 1,
      mainOportunity: "4477",
      paymentMethod: "Pagamento Selecionado",
      user: { name: "Nome Sobrenome", mail: "<EMAIL>" },
    };
    expect(payloadPDF(data)).toEqual(expectedOutput);

    localStorage.removeItem("@dsm/name");
    localStorage.removeItem("@dsm/mail");
  });
});
