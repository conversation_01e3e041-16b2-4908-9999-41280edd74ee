export const bannerHoursController = (regimeType, hour, totalHours) => {
  // console.log("regimeType, hour: ", regimeType, hour);
  const regimes = totalHours;
  const regime = regimes
    .filter((r) => r.type === regimeType)
    .find((r) => r.formName.includes(hour.type));

  let totalValue = 0;
  let totalRegimeHours = 0;
  if (regime) {
    totalRegimeHours = regime.total;
    if (hour.type === "8x5") {
      const { totalValue8x5, discountType8x5, discountValue8x5 } = hour;

      const totalHourRegimeValue = totalRegimeHours * totalValue8x5;
      let discountValue = 0;
      if (discountType8x5 === "percentage")
        discountValue = (totalHourRegimeValue / 100) * discountValue8x5;
      else discountValue = totalRegimeHours * discountValue8x5;

      totalValue = totalHourRegimeValue - discountValue;
    } else {
      const { totalValue24x7, discountType24x7, discountValue24x7 } = hour;

      const totalHourRegimeValue = totalRegimeHours * totalValue24x7;
      let discountValue = 0;
      if (discountType24x7 === "percentage")
        discountValue = (totalHourRegimeValue / 100) * discountValue24x7;
      else discountValue = totalRegimeHours * discountValue24x7;

      totalValue = totalHourRegimeValue - discountValue;
    }
  }

  return [totalValue, totalRegimeHours];
};
