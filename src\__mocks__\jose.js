/**
 * Mock para a biblioteca jose
 * Simula as funcionalidades JWT para testes
 */

export const decodeJwt = jest.fn((token) => {
  // Mock de um payload JWT típico
  return {
    sub: 'test-user',
    email: '<EMAIL>',
    exp: Math.floor(Date.now() / 1000) + 3600, // 1 hora no futuro
    iat: Math.floor(Date.now() / 1000),
    iss: 'test-issuer'
  };
});

export const jwtVerify = jest.fn(async (token, secret) => {
  return {
    payload: {
      sub: 'test-user',
      email: '<EMAIL>',
      exp: Math.floor(Date.now() / 1000) + 3600,
      iat: Math.floor(Date.now() / 1000),
      iss: 'test-issuer'
    },
    protectedHeader: {
      alg: 'HS256',
      typ: 'JWT'
    }
  };
});

export const SignJWT = jest.fn().mockImplementation(() => ({
  setProtectedHeader: jest.fn().mockReturnThis(),
  setIssuedAt: jest.fn().mockReturnThis(),
  setExpirationTime: jest.fn().mockReturnThis(),
  setSubject: jest.fn().mockReturnThis(),
  sign: jest.fn().mockResolvedValue('mock-jwt-token')
}));

export const importSPKI = jest.fn().mockResolvedValue('mock-public-key');
export const importPKCS8 = jest.fn().mockResolvedValue('mock-private-key');

// Mock padrão
const jose = {
  decodeJwt,
  jwtVerify,
  SignJWT,
  importSPKI,
  importPKCS8
};

export default jose;
