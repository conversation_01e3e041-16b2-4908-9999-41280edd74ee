export async function getCustomerBrandNameString(customerName) {
  let newCustomerName = customerName.replace(/[./]/g, "-");
  newCustomerName = newCustomerName.replace(/\s/g, "-");
  newCustomerName = newCustomerName.replace(/%20/g, "-");

  return newCustomerName;
}

export function getCustomerBrandNameStringSync(customerName) {
  let newCustomerName = customerName.replace(/[./]/g, "-");
  newCustomerName = newCustomerName.replace(/\s/g, "-");
  newCustomerName = newCustomerName.replace(/%20/g, "-");

  return newCustomerName;
}
