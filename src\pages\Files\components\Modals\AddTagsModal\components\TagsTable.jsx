import { DeleteOutlined, PlusOutlined } from "@ant-design/icons";
import {
  Button,
  Col,
  Input,
  Row,
  Spin,
  Table,
  Typography,
  message,
} from "antd";
import { setAllTagsFilesState } from "../../../../../../store/actions/files-action";
import { useState } from "react";
import { shallowEqual, useSelector } from "react-redux";
import { v4 } from "uuid";
import {
  dynamoDelete,
  dynamoPut,
} from "../../../../../../service/apiDsmDynamo";
import { logNewAuditAction } from "../../../../../../controllers/audit/logNewAuditAction";

export const TagsTable = () => {
  const { Text } = Typography;
  const [currentTag, setCurrentTag] = useState("");
  const [validTag, setValidTag] = useState("");
  const [loading, setLoading] = useState(false);

  const allTags = useSelector((state) => state.files.allTags, shallowEqual);

  const handleAddTag = async () => {
    const tagAlreadyExists = allTags.find(
      (t) => t.name.toUpperCase() === currentTag.toUpperCase()
    );
    if (!currentTag || currentTag === "") {
      message.warning("Insira um valor de tag", 3);
      setValidTag("error");
      return;
    } else if (tagAlreadyExists) {
      message.warning("Tag já existente, insira um outro valor", 3);
      setValidTag("error");
    } else {
      setLoading(true);
      const newTag = {
        id: v4(),
        name: currentTag,
      };
      await dynamoPut(
        `${process.env.REACT_APP_STAGE}-files-tags`,
        newTag.id,
        newTag
      );
      const newTags = [...allTags, newTag];
      setAllTagsFilesState(newTags);
      setCurrentTag("");
      setValidTag("");
      setLoading(false);

      const username = localStorage.getItem("@dsm/username");
      const title = "Adição de Tag - Arquivos";
      const description = `${username} adicionou a tag: ${currentTag}`;
      logNewAuditAction(username, title, description);
    }
  };

  const handleDeleteTag = async (value) => {
    setLoading(true);
    await dynamoDelete(`${process.env.REACT_APP_STAGE}-files-tags`, value.id);
    const remainingTags = allTags.filter((t) => t.name !== value.name);
    setAllTagsFilesState(remainingTags);
    setLoading(false);

    const username = localStorage.getItem("@dsm/username");
    const title = "Deleção de Tag - Arquivos";
    const description = `${username} deletou a tag: ${value.name}`;
    logNewAuditAction(username, title, description);
  };

  const columns = [
    {
      title: "Tag",
      dataIndex: "tag",
      key: "tag",
      width: "90%",
      render: (text, record) => {
        return <Text>{record.name}</Text>;
      },
    },
    {
      title: "Remover",
      dataIndex: "remove",
      key: "remove",
      align: "center",
      render: (text, record) => (
        <Button
          icon={<DeleteOutlined />}
          danger
          type="text"
          onClick={() => {
            handleDeleteTag(record);
          }}
        ></Button>
      ),
    },
  ];

  return (
    <Row gutter={[8, 8]} style={{ height: "100%" }}>
      {loading ? (
        <Col span={24}>
          <Row justify="center" align={"middle"} style={{ height: "100%" }}>
            <Spin size="large" />
          </Row>
        </Col>
      ) : (
        <Col span={24}>
          <Row justify="space-between" gutter={[8, 8]}>
            <Col span={22}>
              <Input
                placeholder="Insira uma tag"
                value={currentTag}
                onChange={(e) => setCurrentTag(e.target.value)}
                status={validTag}
              />
            </Col>
            <Col span={2}>
              <Row justify={"end"}>
                <Button
                  icon={<PlusOutlined />}
                  type="primary"
                  onClick={handleAddTag}
                ></Button>
              </Row>
            </Col>
          </Row>
          <Row style={{ marginTop: 10 }}>
            <Col span={24}>
              <Table columns={columns} dataSource={allTags} />
            </Col>
          </Row>
        </Col>
      )}
    </Row>
  );
};
