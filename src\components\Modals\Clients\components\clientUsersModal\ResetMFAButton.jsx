import { Button, Col, Input, Popover, Row, Tooltip } from "antd";
import { LockOutlined } from "@ant-design/icons";
import { useState } from "react";

export const ResetMFAButton = ({
  resetMfa,
  item,
  setSelectedContact,
  setTicket,
  ticket,
  selectedContact,
  mfaLoading,
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const handlePopoverVisibility = (visible) => {
    setTicket("");
    setIsVisible(visible);
    setSelectedContact(item?.email);
  };

  return (
    <Row justify="center">
      <Tooltip title="Resetar MFA da conta">
        <Popover
          trigger="click"
          title="Deseja resetar o MFA da conta?"
          open={selectedContact === item?.email ? isVisible : false}
          onOpenChange={(visible) => handlePopoverVisibility(visible)}
          content={
            <Row justify="space-between" gutter={[8, 8]}>
              <Col span={24}>
                <Input
                  value={ticket}
                  type="number"
                  placeholder="Digite o número do ticket de reset de MFA"
                  onChange={(e) => setTicket(e.target.value)}
                  onFocus={() => setSelectedContact(item?.email)}
                />
              </Col>
              <Col>
                <Button
                  type="default"
                  onClick={() => handlePopoverVisibility(false)}
                >
                  Cancelar
                </Button>
              </Col>
              <Col>
                <Button
                  disabled={ticket === "" ? true : false}
                  type="primary"
                  onClick={() => resetMfa(item?.email)}
                >
                  Resetar
                </Button>
              </Col>
            </Row>
          }
        >
          <Button
            icon={<LockOutlined />}
            loading={
              mfaLoading !== true || selectedContact !== item?.email
                ? false
                : true
            }
            type="link"
            style={{ color: '#00B050' }}
          ></Button>
        </Popover>
      </Tooltip>
    </Row>
  );
};
