import { logBillingCreate } from "../../../controllers/audit/logBillingCreate";
import { dynamoGenericPost } from "../../../service/apiDynamoGeneric";
import { getCustomerById } from "../../../utils/getCustomerById";
import { getContractById } from "../../../utils/getContractById";

jest.mock("../../../service/apiDynamoGeneric");
jest.mock("../../../utils/getCustomerById");
jest.mock("../../../utils/getContractById");
jest.mock("../../../controllers/audit/logNewAuditAction");
jest.mock("../../../__mocks__/localStorage/localStorage");

describe("testing logBillingCreate function", () => {
  it("should return an error message if the contractId is missing", async () => {
    const resultPromise = Promise.resolve(
      logBillingCreate(null, "64802305-db22-4399-88bf-4d7cd5424a79")
    );

    const result = await resultPromise;
    expect(result.statusCode).toBe(501);
    expect(result.message).toContain("contractId");
  });

  it("should return an error message if the customerId is missing", async () => {
    const resultPromise = Promise.resolve(
      logBillingCreate("b2c549ba-4ac6-4a1d-858e-743702baccd8", "")
    );

    const result = await resultPromise;

    expect(result.statusCode).toBe(502);
    expect(result.message).toContain("customerId");
  });

  it("should add a billing audit action log and return a success message 200 because there is contractData defined", async () => {
    localStorage.setItem("@dsm/username", "nice.user");

    getContractById.mockResolvedValue({
      id: "contract123",
      name: "Contract Name",
      identifications: {
        itsm_id: "12345",
      },
    });

    dynamoGenericPost.mockResolvedValue({
      data: "Audit log added successfully",
    });

    const expectedLogNewAuditActionResponse = {
      statusCode: 200,
      message: "Success adding create billing audit action log",
    };

    jest
      .spyOn(
        require("../../../controllers/audit/logNewAuditAction"),
        "logNewAuditAction"
      )
      .mockResolvedValue(expectedLogNewAuditActionResponse);

    const result = await logBillingCreate("contract123", "customer456");

    localStorage.clear();
    expect(result.statusCode).toBe(200);
    expect(result.message).toContain(
      "Success adding create billing audit action log"
    );
  });

  it("should add a billing audit action log and return a success message 201 because there is NO contractData defined", async () => {
    localStorage.setItem("@dsm/username", "nice.user");

    getContractById.mockResolvedValue(null);

    getCustomerById.mockResolvedValue({
      id: "customer456",
      identifications: {
        itsm_id: "765234",
      },
    });

    dynamoGenericPost.mockResolvedValue({
      data: "Audit log added successfully",
    });

    const expectedLogNewAuditActionResponse = {
      statusCode: 200,
      message: "Success adding create billing audit action log",
    };

    jest
      .spyOn(
        require("../../../controllers/audit/logNewAuditAction"),
        "logNewAuditAction"
      )
      .mockResolvedValue(expectedLogNewAuditActionResponse);

    const result = await logBillingCreate("contract123", "customer456");

    localStorage.clear();
    expect(result.statusCode).toBe(201);
    expect(result.message).toContain(
      "Success adding create billing audit action log"
    );
  });
});
