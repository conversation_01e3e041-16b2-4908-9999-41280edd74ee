.cadence-card-global {
  margin: 10px 0px;
}

.cadence-cards {
  overflow-y: scroll;
  overflow-y: scroll;
  overflow-x: hidden;
  height: 80%;
  overflow-x: hidden;
  height: 80%;
}

.cadence-card {
  background-color: #f0f2f5;
  border-radius: 10px;
  width: 100%;
  height: 92px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px;
}

.cadence-header {
  display: flex;
  justify-content: space-between;
  padding: 5px 20px;
  align-items: center;
}

.cadence-title {
  font-family: "Open Sans";
  font-style: normal;
  font-weight: 600;
  font-size: 24px;
  line-height: 110%;
}

.cadence-icons {
  display: flex;
  gap: 10px;
}

.cadence-date {
  text-align: left;
  padding-left: 25px;
  padding-top: 10px;
}

.cadence-left-infos {
  display: flex;
  flex-direction: column;
  margin-top: 12px;
}

.right-icons-single {
  font-size: 12px;
  color: black;
}

.cadence-left-single {
  display: flex;
  gap: 10px;
  align-items: center;
  justify-content: space-between;
}

.cadence-left-infos p {
  font-weight: 400;
  font-size: 16px;
}

.cadence-left {
  width: 51px;
  height: 26px;
  border-radius: 5px;
}

.cadence-left-green {
  background-color: #1ebd71;
  color: white;
}

.cadence-left-gray {
  background-color: #bac2bf;
  color: white;
}

.right-icons {
  display: flex;
  gap: 10px;
}

.center h3 {
  font-family: "Open Sans";
  font-style: normal;
  font-weight: 600;
  font-size: 16px;
  line-height: 110%;
}
