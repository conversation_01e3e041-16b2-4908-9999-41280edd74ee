import { React, useEffect, useState } from "react";
import {
  BarElement,
  CategoryScale,
  Chart as ChartJS,
  Legend,
  LinearScale,
  Title,
  Tooltip,
} from "chart.js";
import {
  AlignLeftOutlined,
  BarChartOutlined,
  FilterFilled,
} from "@ant-design/icons";
import {
  Card,
  Col,
  DatePicker,
  Popover,
  Row,
  Space,
  Spin,
  Typography,
} from "antd";
import "./HoursPerProfissional.css";
import moment from "moment";
import VerticalBar from "./VerticalBar";
import HoursPerProfissionalFilter from "../../Modals/Filters/HoursPerProfissionalFilter";
import { otrsPost } from "../../../service/apiOtrs";
import { HoursPerCustomer } from "../../CockpitTabs/ProfessionalsTab/hoursPerCustomer";
import { SearchInput } from "../../SearchInput";
import { CockpitsHeader } from "../../CockpitsHeader";

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
);

export const options = {
  plugins: {
    legend: {
      position: "top",
    },
    title: {
      display: true,
    },
  },
};

export default function HoursPerProfissional(props) {
  const { Title, Text } = Typography;
  const [currentFilter, setCurrentFilter] = useState();
  const [filteredClients, setFilteredClients] = useState();
  const [modalVisible, setModalVisible] = useState(false);
  const [tablePage, setTablePage] = useState(0);
  const [colors, setColors] = useState();
  const [loading, setLoading] = useState(false);
  const [graphLoading, setGraphLoading] = useState(false);
  const [cardsListLoading, setCardsListLoading] = useState(false);
  const [allTickets, setTickets] = useState([]);
  const [allTimeByCustomer, setAllTimeByCustomer] = useState([]);
  const [allGraphData, setAllGraphData] = useState([]);
  const [allTimeByCustomerByMonth, setAllTimeByCustomerByMonth] = useState([]);
  const [searchedDate, setSearchedDate] = useState(moment().format("MMMM/YY"));
  const [searchFilter, setSearchFilter] = useState("");
  const currentMonth = moment();
  const previousMonth = moment().subtract(1, "month");
  const monthBeforePrevious = moment().subtract(2, "month");
  const GRAPH_NAME = "HoursPerProfissional";

  const firstDayOfTheMonth = moment().startOf("month").format();
  const lastDayOfTheMonth = moment().endOf("month").format();

  const GRAPH_BAR_VERTICAL = 0;
  const GRAPH_BAR_HORIZONTAL = 1;

  const notBillableContracts = [
    "Darede Apoio Técnico",
    "Darede Labs",
    "Darede - Atendimento 8x5",
    "Darede - Atendimento 24x7",
    "Darede SI",
  ];

  function checkIsVisible() {
    let localStorageData = JSON.parse(localStorage.getItem("favorites"));
    if (localStorageData) {
      var index = localStorageData.findIndex(
        (value) => value.component === GRAPH_NAME
      );

      return index !== -1;
    } else {
      return false;
    }
  }

  async function getTickets() {
    const today = moment();
    const tickets = await otrsPost("read/tickets/time", {
      params: {
        start: today.format("YYYY") + "/06/01",
        end: today.format("YYYY/MM/DD"),
      },
    });
    setTickets(tickets.data);
    getMonthTickets(tickets.data);
  }

  async function getMonthTickets(tickets) {
    setLoading(true);

    let payload = [];

    Object.entries(tickets).forEach((ticket) => {
      let inPayload = payload.find(
        (item) => item.client === ticket[1].client_name
      );
      if (!inPayload) {
        payload.push({
          client: ticket[1].client_name,
          time: parseInt(ticket[1].consumed_min),
          contract: ticket[1].contract_name,
        });
      } else {
        let index = payload.indexOf(inPayload);
        payload[index].time += parseInt(ticket[1].consumed_min);
      }
    });

    payload.map((item) => {
      if (notBillableContracts.includes(item.contract)) {
        item["billada"] = false;
      } else {
        item["billada"] = true;
      }
      return payload;
    });
    let ticketsInHours = transformMinuresInHours(payload);
    setLoading(false);
  }

  function transformMinuresInHours(tickesInMinutes) {
    tickesInMinutes.map((item) => {
      const totalMinutes = item.time;
      const hours = Math.floor(totalMinutes / 60);
      const minutes = (totalMinutes % 60) / 100;
      const result = hours + minutes;
      item.time = result;
    });
    return tickesInMinutes;
  }

  function getGraphColor() {
    let storage_color = JSON.parse(localStorage.getItem("colors"));
    if (storage_color) {
      setColors(storage_color[GRAPH_NAME]);
    }
  }

  function handleVisibleChange() {
    setModalVisible(!modalVisible);
  }

  async function getFormerMonthsHours(filter) {
    let payload = [
      {
        name: monthBeforePrevious.format("MMMM"),
        startDate: monthBeforePrevious.startOf("month").format(),
        endDate: monthBeforePrevious.endOf("month").format(),
        billable: 0,
        notBillable: 0,
      },
      {
        name: previousMonth.format("MMMM"),
        startDate: previousMonth.startOf("month").format(),
        endDate: previousMonth.endOf("month").format(),
        billable: 0,
        notBillable: 0,
      },
      {
        name: currentMonth.format("MMMM"),
        startDate: currentMonth.startOf("month").format(),
        endDate: currentMonth.endOf("month").format(),
        billable: 0,
        notBillable: 0,
      },
    ];

    for (let i = 0; i < payload.length; i++) {
      const ticketInfo = await otrsPost(
        "read/tickets/totalTimeByCustomerInTimeRange",
        {
          params: {
            end: payload[i].endDate,
            start: payload[i].startDate,
            customerId: filter?.client,
            contractId: filter?.contract,
          },
        }
      );

      ticketInfo.data.map((ticketData) => {
        const isBillable = notBillableContracts.some(
          (item) => ticketData.contract_name === item
        );
        if (isBillable === false) {
          if (filter?.billable || !filter?.Nbillable) {
            payload[i].billable +=
              ticketData.customer_total_consumed_min !== null
                ? parseInt(ticketData.customer_total_consumed_min) / 60
                : 0;
          }
        } else {
          if (!filter?.billable || filter?.Nbillable) {
            payload[i].notBillable +=
              ticketData.customer_total_consumed_min !== null
                ? parseInt(ticketData.customer_total_consumed_min) / 60
                : 0;
          }
        }
      });
    }

    let ticketsInHours = transformMinuresInHours(payload);
    setAllGraphData(ticketsInHours);
    setTimeout(() => {
      setGraphLoading(false);
    }, 5000);
  }

  async function getFilteredChartData(filter) {
    let filteredData = structuredClone(allTickets);
    setCurrentFilter(filter);

    await getFormerMonthsHours(filter);

    if (filter.client) {
      filteredData = filteredData.filter(
        (item) => item.client_name === filter.client
      );

      const getTicketsFromCustomer = await otrsPost("read/tickets/customer", {
        params: filter.client,
      }).then((res) =>
        setAllTimeByCustomer(
          filter.contract
            ? res.data
                .filter((item) => item.id_contract === filter.contract)
                .reduce(
                  (acc, cur) =>
                    parseInt(acc) +
                    parseInt(cur.consumed_min !== null ? cur.consumed_min : 0),
                  0
                )
            : res.data.reduce(
                (acc, cur) =>
                  parseInt(acc) +
                  parseInt(cur.consumed_min !== null ? cur.consumed_min : 0),
                0
              )
        )
      );
    }
    if (filter.contract) {
      filteredData = filteredData.filter(
        (item) => item.id_contract === filter.contract
      );
    }
    if (filter.professional) {
      filteredData = filteredData.filter(
        (item) => item.user_name === filter.professional
      );
    }
    if (filter.Nbillable) {
      let notBillableArray = [];
      for (let notBillable of notBillableContracts) {
        filteredData.forEach((item) => {
          if (item.contract_name === notBillable) {
            notBillableArray.push(item);
          }
        });
      }
      filteredData = notBillableArray;
    }
    if (filter.billable) {
      let notBillableArray = [];
      for (let notBillable of notBillableContracts) {
        filteredData.forEach((item) => {
          if (item.contract_name !== notBillable) {
            notBillableArray.push(item);
          }
        });
      }
      filteredData = notBillableArray;
    }
    getMonthTickets(filteredData);
  }

  async function getCustomersConsumedTimePerMonth(startDate, endDate) {
    setCardsListLoading(true);
    const ticketInfo = await otrsPost(
      "read/tickets/totalTimeByCustomerInTimeRange",
      {
        params: {
          end: endDate,
          start: startDate,
        },
      }
    );

    setAllTimeByCustomerByMonth(ticketInfo.data);
    setCardsListLoading(false);
  }

  useEffect(() => {
    const loadData = async () => {
      getGraphColor();
      await getTickets();
      await getCustomersConsumedTimePerMonth(
        firstDayOfTheMonth,
        lastDayOfTheMonth
      );
    };

    loadData();
  }, []);

  return (
    <Card
      bordered="false"
      style={{
        borderRadius: "20px",
        height: "100%",
        boxShadow: "0 0 10px rgba(0,0,0,0.1)",
        minWidth: "476px",
        minHeight: "414px",
      }}
    >
      {loading ? (
        <Row style={{ height: "100%" }} justify="center" align="middle">
          <Spin />
        </Row>
      ) : (
        <>
          <Row justify="space-between">
            <Col span={18}>
              <CockpitsHeader
                title={
                  <Col span={14} style={{ alignSelf: "start" }}>
                    <Title level={4} style={{ fontWeight: 400 }}>
                      Horas por cliente
                    </Title>
                  </Col>
                }
                hasFavoriteOption={false}
                filter={
                  tablePage === GRAPH_BAR_VERTICAL ? (
                    <Col span={2}>
                      <Popover
                        open={modalVisible}
                        onOpenChange={handleVisibleChange}
                        placement="left"
                        content={() => {
                          return (
                            <Row>
                              <HoursPerProfissionalFilter
                                handleVisibleChange={handleVisibleChange}
                                currentFilter={currentFilter}
                                setCurrentFilter={(value) =>
                                  setCurrentFilter(value)
                                }
                                contractsFromCustomer={
                                  props?.contractsFromCustomer
                                }
                                setContractsFromCustomer={(value) =>
                                  props?.setContractsFromCustomer(value)
                                }
                                filter={getFilteredChartData}
                                contracts={props.contracts}
                                clients={props.clients}
                                loading={graphLoading}
                                setLoading={(value) => setGraphLoading(value)}
                              />
                            </Row>
                          );
                        }}
                        trigger="click"
                      >
                        <FilterFilled className="filter-icon" />
                      </Popover>
                    </Col>
                  ) : null
                }
              />
            </Col>
            <Space align="center">
              <BarChartOutlined
                className={
                  tablePage === 0 ? "barGrapich-icon" : "barGrapich-icon-off"
                }
                onClick={() => setTablePage(GRAPH_BAR_VERTICAL)}
              />
              <AlignLeftOutlined
                onClick={() => setTablePage(GRAPH_BAR_HORIZONTAL)}
                className={
                  tablePage === 1 ? "barGrapich-icon" : "barGrapich-icon-off"
                }
              />
            </Space>
            {tablePage === 0 ? (
              <Col span={24} style={{ alignSelf: "start", marginTop: "10px" }}>
                <VerticalBar
                  loading={graphLoading}
                  setLoading={(value) => setGraphLoading(value)}
                  colors={colors}
                  barChartData={allGraphData}
                  graphData={filteredClients}
                  filterParameter={currentFilter}
                />
              </Col>
            ) : (
              <Row gutter={[12, 12]}>
                <Col span={12}>
                  <DatePicker
                    picker="month"
                    allowClear={false}
                    value={
                      moment(searchedDate, "MMMM/YYYY")
                        ? moment(searchedDate, "MMMM/YYYY")
                        : moment()
                    }
                    onChange={(date, dateString) => {
                      setSearchedDate(moment(dateString).format("MMMM/YYYY"));
                      getCustomersConsumedTimePerMonth(
                        moment(dateString)
                          .startOf("month")
                          .format("YYYY-MM-DD"),
                        moment(dateString).endOf("month").format("YYYY-MM-DD")
                      );
                    }}
                  ></DatePicker>
                </Col>
                <Col span={12}>
                  <SearchInput
                    placeholder="Buscar..."
                    onChange={(e) => {
                      setSearchFilter(e);
                    }}
                  />
                </Col>
                <HoursPerCustomer
                  loading={cardsListLoading}
                  searchedDate={searchedDate}
                  searchFilter={searchFilter}
                  currentFilter={currentFilter}
                  allTimeByCustomerByMonth={allTimeByCustomerByMonth}
                  notBillableContracts={notBillableContracts}
                  contracts={props.contracts}
                />
              </Row>
            )}
          </Row>
        </>
      )}
    </Card>
  );
}
