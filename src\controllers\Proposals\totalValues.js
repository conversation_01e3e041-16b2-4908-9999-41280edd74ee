import { totalHoursController } from "./totalHours";

async function getCostManagementValues(cm) {
  let values = [
    { name: "hourValue", values: [] },
    { name: "additionalHourValue", values: [] },
  ];

  cm?.daredeHourValue?.map((h) => {
    values[0].values.push(h);
  });

  cm.daredeAdditionalHours.map((h) => {
    values[1].values.push(h);
  });

  return values;
}

export const totalValuesController = async (
  totalValues,
  month,
  costManagement,
  services,
  state,
  hourClass
) => {
  //cmArr = costManagementArray
  if (costManagement.length === 0) return;

  const currentHourClass = hourClass ? hourClass : "hourValue";
  console.log({ currentHourClass });

  const cmArr = await getCostManagementValues(costManagement);

  const totalHours = await totalHoursController(services, state);
  if (services?.length > 0 && state && totalValues?.length > 0) {
    const hourValueClass = totalValues.find(
      (v) => v.hourClass === currentHourClass
    );
    const totalValueMonth = hourValueClass?.values?.find(
      (v) => v.month === month
    );

    let totals = [];
    for (let i = 0; i < cmArr.length; i++) {
      let contractTypes = [];
      // iterating hour types 'hourValue' and 'additionalHourValue'
      // contractHourType can be of 'hourValue' or 'additionalHourValue'
      const contractHourArr = cmArr[i];
      for (let j = 0; j < contractHourArr.values.length; j++) {
        let contractValues = [];
        // iterating all hour type values (6 values)
        const contractHourObjValues = contractHourArr.values[j];
        for (let k = 0; k < totalHours.length; k++) {
          // iterating all totalHours values
          let discountValue = 0;
          const hourType = totalHours[k].formName;
          const hourValue = contractHourObjValues?.values?.find(
            (v) => v.name === hourType
          );
          const totalValueObj = totalValueMonth?.values?.find(
            (v) => v.formName === hourType
          );
          if (hourValue !== undefined) {
            const burst = totalValueObj?.burst
              ? Number(totalValueObj.burst)
              : 0;

            const totalHoursBytype = Number(totalHours[k].value) + burst;

            const totalHourRegimeValue = totalHoursBytype * hourValue.value;
            if (totalValueObj.discountUnit === "percentage") {
              discountValue =
                (totalHourRegimeValue / 100) * totalValueObj.discountValue;
            } else {
              discountValue = totalHours[k].value * totalValueObj.discountValue;
            }
            let totalValue = totalHourRegimeValue - discountValue;

            if (totalValue < 0) totalValue = 0;

            const contractValueObj = {
              formName: totalValueObj.formName,
              type: totalValueObj.type,
              discountUnit: totalValueObj.discountUnit,
              discountValue: totalValueObj.discountValue,
              hourValue: hourValue.value,
              totalHours: totalHoursBytype,
              totalValue: totalValue,
              burst: burst,
              hours: totalHours[k].value,
            };
            contractValues.push(contractValueObj);
          }
        }
        const contractTypeObj = {
          month: contractHourObjValues.month,
          values: contractValues,
        };
        contractTypes.push(contractTypeObj);
      }
      const totalObj = {
        hourClass: contractHourArr.name,
        values: contractTypes,
      };
      totals.push(totalObj);
    }

    return totals;
  } else {
    return totalValues;
  }
};
