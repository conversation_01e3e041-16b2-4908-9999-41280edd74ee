import { apiProposals, getHeader } from "../../utils/api";
import { capitalizeFirstLetter } from "./capitalizeFirstLetter";

export async function generatePDF(
  proposalData,
  customerName,
  customerBrand,
  customerPDF,
  user
) {
  const headers = getHeader();

  const payload = {
    ...proposalData,
    customer: customerPDF,
    user: user,
  };

  if (customerBrand) {
    let formData = new FormData();
    formData.append("image", customerBrand);

    let name = customerName.replace(/\/|\\/gm, "-");
    name = customerName.replace(/ /gm, "-");

    await apiProposals
      .put(`/brand/${name}`, formData, { headers })
      .catch((error) => {
        console.log(error);
      });
  }

  apiProposals.post(`/generate/pdf`, payload, { headers }).catch((error) => {
    console.log(error);
  });
}
