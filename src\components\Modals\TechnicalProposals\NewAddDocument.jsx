import axios from "axios";
import { useEffect, useState, useContext } from "react";

import {
  <PERSON><PERSON>,
  Modal,
  Row,
  Col,
  Table,
  message,
  Popconfirm,
  Tooltip,
  Typography,
  Alert,
  Badge,
} from "antd";
import {
  PaperClipOutlined,
  DeleteOutlined,
  CheckOutlined,
  DownloadOutlined,
} from "@ant-design/icons";

import { ProposalContext } from "../../../contexts/totalValuesProposals";
import { TotalValuesProposalType } from "../../../contexts/reducers/totalValuesProposal";
import { v4 } from "uuid";
import { setTechnicalProposalState } from "../../../store/actions/technical-proposal-action";
import { shallowEqual, useSelector } from "react-redux";
import { useToggle } from "../../../hooks/useToggle";
import {
  s3DeleteMultipleObjects,
  s3ListObjects,
  s3UploadObj,
} from "../../../service/apiDsmS3";
import { getFilesToBeDeleted } from "../../../controllers/Proposals/getFilesToBeDeleted";
import { getBase64 } from "../../../controllers/Proposals/getBase64";

const FileDownload = require("js-file-download");

export const AddDocument = () => {
  const fileNames = useSelector(
    (state) => state.technicalProposal.fileNames,
    shallowEqual
  );

  const id = useSelector(
    (state) => state.technicalProposal.persistID,
    shallowEqual
  );

  const [loading, setLoading] = useState(false);
  const { proposalState, setProposalState } = useContext(ProposalContext);
  const [firstOpenVerify, setFirstOpenVerify] = useState(true);

  const { Text } = Typography;
  const [fileId, setFileId] = useState(0);

  const idFixed = proposalState.idFixed === "" ? v4() : proposalState.idFixed;

  const [toggleModalValue, toggle] = useToggle();

  const [newFile, setNewFile] = useState(null);
  const [files, setFiles] = useState([]);
  const [notUploadedFilesId, setNotUploadedFilesId] = useState([]);

  const handleRemoveItem = (index, item) => {
    const prevList = fileNames;
    const arr = [];
    for (let i = 0; i < prevList.length; i++) {
      if (prevList[i].id !== item.id) {
        arr.push(prevList[i]);
      }
    }
    setTechnicalProposalState({
      field: "fileNames",
      value: arr,
    });
  };

  const handleFileSelect = () => {
    const fileInput = document.getElementById("file");
    const file = fileInput.files[0];
    fileInput.value = "";

    if (file) {
      setFiles((state) => [...state, file]);

      let exists = false;
      const addedFile = {
        id: fileNames.length + 1,
        lastModified: file.lastModified,
        name: file.name,
        size: file.size,
        type: file.type,
      };

      setNotUploadedFilesId((state) => [...state, fileNames.length + 1]);

      const newId = fileId + 1;
      setFileId(newId);

      for (let i = 0; i < proposalState.fileNames.length; i++) {
        if (proposalState.fileNames[i].name === addedFile.name) {
          exists = true;
        }
      }

      if (exists === false) {
        setTechnicalProposalState({
          field: "fileNames",
          value: [...fileNames, addedFile],
        });
      }
    } else {
      message.error("Selecione um arquivo");
    }
  };

  const handleOk = async (file) => {
    setLoading(true);
    setFirstOpenVerify(false);

    setNotUploadedFilesId([]);

    const filesStr = JSON.stringify(fileNames);
    const filesRedux = JSON.parse(filesStr);

    setProposalState({
      type: TotalValuesProposalType.SET_ID_FIXED,
      value: idFixed,
    });

    let filesToBeDeleted = await getFilesToBeDeleted(
      id,
      fileNames,
      true,
      "/documents"
    );
    let filesToBeDeletedKeys = [];

    const prefix = `${id}/documents/`;
    filesToBeDeleted.forEach((file) => {
      const key = prefix + file;
      filesToBeDeletedKeys.push(key);
    });

    filesRedux?.forEach(async (currentFile, fileIndex) => {
      // let file_format = currentFile.type.split("/")[1];
      let name = `${id}/documents/${currentFile.name}`;

      const fileToTransform = files.find(
        (file) => file.name === currentFile.name
      );

      if (fileToTransform) {
        const base64obj = await getBase64(fileToTransform);

        const payload = {
          bucketName: `${process.env.REACT_APP_STAGE}-proposals-documents`,
          obj: base64obj,
          fileName: name,
          contentType:
            currentFile.type ===
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
              ? "application/msword"
              : `${currentFile.type}`,
        };

        try {
          const res = await s3UploadObj(payload);
          message.success("Upload dos arquivos feito com sucesso!");
        } catch (error) {
          console.log({ error });
          message.error("Houve um erro ao fazer o upload dos arquivos!");
        }
      }
    });

    const payloadDelete = {
      bucketName: `${process.env.REACT_APP_STAGE}-proposals-documents`,
      fileKeys: filesToBeDeletedKeys,
    };

    try {
      await s3DeleteMultipleObjects(payloadDelete);
    } catch (error) {
      console.log({ error }, { filesToBeDeletedKeys });
      message.error(
        "Houve um erro ao fazer a deleção dos arquivos de arquitetura!"
      );
    }

    setLoading(false);

    toggle();
  };

  const handleGet = async (file) => {
    setLoading(true);
    toggle();
    let name = `${id}/documents/${file}`;

    const {
      data: { data },
    } = await axios.post(
      `${process.env.REACT_APP_API_PERMISSION}s3/link`,
      {
        operation: "get",
        key: encodeURI(name),
        bucket: `${process.env.REACT_APP_STAGE}-proposals-documents`,
      },
      {
        headers: {
          authorization: localStorage.getItem("jwt"),
        },
      }
    );

    axios
      .get(data, {
        responseType: "blob",
      })
      .then((response) => {
        FileDownload(response.data, `${file}`);
        setLoading(false);
      })
      .catch((err) => {
        setLoading(false);
        message.error("Erro ao tentar fazer download! ", { err });
      });

    toggle();
  };

  const handleCancel = async () => {
    setLoading(true);
    const filesToBeDeleted = await getFilesToBeDeleted(
      id,
      fileNames,
      false,
      "/documents"
    );

    const newFiles = fileNames.filter((f) => !filesToBeDeleted.includes(f));

    setTechnicalProposalState({
      field: "fileNames",
      value: newFiles,
    });

    toggle();

    setLoading(false);
  };

  const columns = [
    {
      title: "ID",
      dataIndex: "id",
      key: "id",
      render: (id, item) => {
        return <Text>{item.id}</Text>;
      },
    },
    {
      title: "Nome do arquivo",
      dataIndex: "file_name",
      key: "file_name",
      render: (id, item) => {
        return <Text>{item.name}</Text>;
      },
    },
    {
      title: "Tipo do Arquivo",
      dataIndex: "file_type",
      key: "file_type",
      align: "center",
      render: (id, item) => {
        return <Text>{item.type}</Text>;
      },
    },
    {
      title: "Download",
      dataIndex: "download",
      key: "download",
      align: "center",
      render: (id, item) => {
        return (
          <Button
            type="text"
            onClick={() => {
              handleGet(item.name);
            }}
            disabled={notUploadedFilesId.find((id) => id === item.id)}
          >
            <DownloadOutlined />
          </Button>
        );
      },
    },
    {
      title: "Remover",
      dataIndex: "remove",
      key: "remove",
      render: (id, item, index) => {
        return (
          <Button
            danger
            type="text"
            onClick={() => {
              handleRemoveItem(index, item);
            }}
          >
            <DeleteOutlined />
          </Button>
        );
      },
    },
  ];

  return (
    <>
      <Badge count={fileNames.length} title="Arquivos salvos">
        <Button type="text-color" onClick={toggle}>
          Anexos
          <PaperClipOutlined />
        </Button>
      </Badge>
      <Modal
        title="Anexos"
        visible={toggleModalValue}
        closable={false}
        width={"60%"}
        footer={[
          <Row justify="space-between">
            <Popconfirm
              title="Ao cancelar, você perderá os arquivos não salvos, tem certeza?"
              okText="Sim"
              cancelText="Não"
              onConfirm={handleCancel}
            >
              <Button type="text-color">
                Cancelar <DeleteOutlined />
              </Button>
            </Popconfirm>
            <Tooltip
              title={
                fileNames.length > 0 ? "" : "Adicione arquivos para habilitar"
              }
            >
              <Button type="primary" onClick={handleOk}>
                Salvar <CheckOutlined />
              </Button>
            </Tooltip>
          </Row>,
        ]}
      >
        <Row
          style={{ display: "flex", flexDirection: "column" }}
          justify="center"
        >
          <Table
            dataSource={fileNames}
            columns={columns}
            scroll={{ x: 100 }}
            pagination={true}
            loading={loading}
          ></Table>

          <Row justify="center">
            <Button type="dashed" style={{ marginTop: "20px" }}>
              <label htmlFor="file">Selecionar anexo</label>
            </Button>
          </Row>
          <Col>
            <input
              type="file"
              name="file"
              id="file"
              onChange={handleFileSelect}
              style={{ display: "none" }}
            />
          </Col>

          <Row justify="center" style={{ marginTop: "1rem" }}>
            <Col>
              <Alert
                type="warning"
                showIcon
                message={"O tamanho máximo permitido do arquivo é de 2MB"}
              />
            </Col>
          </Row>
        </Row>
      </Modal>
    </>
  );
};
