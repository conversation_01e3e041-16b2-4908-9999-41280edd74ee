import { useSelector, shallowEqual } from "react-redux";
import { Row, Col, Typography, Tooltip } from "antd";
import { INVOICE_BALANCE_STATES } from "../../../store/reducers/invoices-reducer";
import React, { useMemo } from "react";

import { moneyMask } from "../../../utils/money-maks";

const { Text } = Typography;
export const DolarInvoice = () => {
  const state = useSelector((state) => state.invoice.state, shallowEqual);
  const dolar = useSelector((state) => state.invoice.dolar, shallowEqual);
  const totalCash = useSelector(
    (state) => state.invoice.totalCash,
    shallowEqual
  );

  const total = useMemo(() => {
    const negative = totalCash?.total_neg || 0;
    const positive = totalCash?.total_pos || 0;
    const edp = Math.abs(totalCash.total_edp);
    const spp = Math.abs(totalCash.total_spp);

    let total = parseFloat(positive) + parseFloat(negative) + edp + spp;
    return { total, edp, spp };
  }, [totalCash, state]);

  const totalReal = useMemo(() => {
    return total.total * (dolar ? parseFloat(dolar) : 0);
  }, [total.total, dolar]);

  return (
    <>
      <Col style={{ marginBottom: "1em" }} md={5}>
        <Tooltip title="Valor do dolar da AWS">
          <Row style={{ justifyContent: "space-between" }}>
            <Text style={{ fontSize: 16 }}>Moeda:</Text>
            <Text style={{ fontSize: 16 }}>
              R${" "}
              {isNaN(parseFloat(dolar))
                ? "0,00"
                : parseFloat(dolar).toString().replace(".", ",")}
            </Text>
          </Row>
        </Tooltip>
        <Tooltip title="Valor Total em dólar (Custos + EDP + SPP)">
          <Row style={{ justifyContent: "space-between" }}>
            <Text style={{ fontSize: 16 }}>Total US$: </Text>
            <Text style={{ fontSize: 16 }}>
              {moneyMask(total.total.toFixed(2))}
            </Text>
          </Row>
        </Tooltip>
        <Tooltip title="Valor Total em real (Custos + EDP + SPP)">
          <Row style={{ justifyContent: "space-between" }}>
            <Text style={{ fontSize: 16 }}>Total R$: </Text>
            <Text style={{ fontSize: 16 }}>
              {moneyMask(totalReal.toFixed(2))}
            </Text>
          </Row>
        </Tooltip>
      </Col>
      <Col style={{ marginBottom: "1em" }} md={{ span: 4, offset: 1 }}>
        <Tooltip title="Cobranças negativas pelas quais a AWS reembolsou o dinheiro">
          <Row style={{ justifyContent: "space-between" }}>
            <Text style={{ fontSize: 16 }}>Refund:</Text>
            <Text style={{ fontSize: 16 }}>
              US$ {moneyMask(totalCash.total_refund.toFixed(2))}
            </Text>
          </Row>
        </Tooltip>
        <Row style={{ justifyContent: "space-between" }}>
          <Text style={{ fontSize: 16 }}>EDP:</Text>
          <Text style={{ fontSize: 16 }}>
            US$ {moneyMask(Math.abs(total.edp).toFixed(2))}
          </Text>
        </Row>
        <Row style={{ justifyContent: "space-between" }}>
          <Text style={{ fontSize: 16 }}>SPP:</Text>
          <Text style={{ fontSize: 16 }}>
            US$ {moneyMask(Math.abs(total.spp).toFixed(2))}
          </Text>
        </Row>
      </Col>
    </>
  );
};
