import React, { useState, useEffect } from "react";
import { Table, Popover } from "antd";
import TicketsOfWalletManagesFilter from "../../Modals/Filters/TicketsOfWalletManagesFilter";
import { AppstoreFilled, PlusSquareOutlined } from "@ant-design/icons";
import "./TicketKanban.css";
import { otrsGet } from "../../../service/apiOtrs";
import moment from "moment/moment";

const KANBAN_PAGE = 0;
const TABLE_PAGE = 1;

// const data = [
//     {
//         key: '1',
//         priority: '#43914F',
//         client: 'Darede',
//         owner: 'Semestral',
//         numberOfTicket: 12,
//         state: 'In Progress',
//         title: 'Testando',
//         type: 'Sust-E1',
//         total: 40,
//         monthTotal: 3,
//     },
// ]

export default function TableTicketKanban(props) {
  const [allCustomers, setAllCustomers] = useState([]);
  const [allUsers, setAllUsers] = useState([]);
  const [tableData, setTableData] = useState([]);
  const [popoverVisibility, setPopoverVisibility] = useState(false);

  const { data, setSelectedPage, showModal } = props;

  function getPriorityColor(priorityId) {
    if (priorityId === 5) {
      return "#FF3C3C";
    } else if (priorityId === 4) {
      return "#F1F43F";
    } else if (priorityId === 3 || priorityId === 1) {
      return "#74CF49";
    } else {
      return "#fff";
    }
  }

  const columns = [
    {
      dataIndex: "ticket_priority_id",
      key: "ticket_priority_id",
      render: (priorityId) => (
        <div className="priority-container">
          <div
            className="priority"
            style={{ backgroundColor: getPriorityColor(priorityId) }}
          ></div>
        </div>
      ),
    },
    {
      title: "Cliente",
      dataIndex: "customer_id",
      key: "customer_id",
      render: (customerId) => (
        <p className="table-font">{getCustomerName(customerId)}</p>
      ),
    },
    {
      title: "Titulo",
      dataIndex: "subject",
      key: "subject",
      render: (subject) => <p className="table-font">{subject}</p>,
    },
    {
      title: "Estado",
      dataIndex: "ticket_state",
      key: "ticket_state",
      render: (ticketState) => <p className="state">{ticketState}</p>,
    },
    {
      title: "Tipo",
      dataIndex: "type",
      key: "type",
      render: (type) => <p className="table-font">{type}</p>,
    },
    {
      title: "Proprietario",
      dataIndex: "owner_ticket",
      key: "owner_ticket",
      render: (ownerTicket) => (
        <p className="table-font">{getUserName(ownerTicket)}</p>
      ),
    },
    {
      title: "Data de Criação",
      dataIndex: "ticket_create_time",
      key: "ticket_create_time",
      render: (createTime) => (
        <p className="table-font">{moment(createTime).format("DD/MM/YYYY")}</p>
      ),
    },
  ];

  function handlePopoverVisibility() {
    setPopoverVisibility(!popoverVisibility);
  }

  async function getAllUsers() {
    otrsGet("read/user/all/0")
      .then((res) => {
        const { data } = res;
        setAllUsers(data);
      })
      .catch((error) => {
        console.log(error);
      });
  }

  function getUserName(userLogin) {
    let userName = "-";

    Object.entries(allUsers).forEach((item) => {
      const user = item[1];
      if (user.login === userLogin) {
        userName = user.first_name + " " + user.last_name;
      }
    });

    return userName;
  }

  function getAllCustomers() {
    otrsGet("read/customer/all/0")
      .then((res) => {
        const { data } = res;
        setAllCustomers(data);
      })
      .catch((error) => {
        console.log(error);
      });
  }

  function getCustomerName(id) {
    let customerName = "-";

    Object.entries(allCustomers).forEach((item) => {
      const customer = item[1];
      if (customer.customer_id === id) {
        customerName = customer.name;
      }
    });

    return customerName;
  }

  function getTableData() {
    let allTickets = [];

    for (let item of data) {
      allTickets = allTickets.concat(item.tickets);
    }

    setTableData(allTickets);
  }

  useEffect(() => {
    getAllUsers();
    getAllCustomers();
    getTableData();
  }, []);

  return (
    <div style={{ backgroundColor: "#fff" }}>
      <div className="table-card-top-left flex gap-7">
        <PlusSquareOutlined
          onClick={() => setSelectedPage(KANBAN_PAGE)}
          className="star-icon text-[#43914F] text-3xl"
        />
        <AppstoreFilled
          onClick={() => setSelectedPage(TABLE_PAGE)}
          className="star-icon text-[#43914F] text-3xl"
        />
        <div className="table-filter">
          <Popover
            open={popoverVisibility}
            onClick={handlePopoverVisibility}
            placement="left"
            content={() => {
              return (
                <TicketsOfWalletManagesFilter
                  handlePopoverVisibility={handlePopoverVisibility}
                />
              );
            }}
            trigger="click"
          ></Popover>
        </div>
      </div>
      <Table columns={columns} dataSource={tableData} />
    </div>
  );
}
