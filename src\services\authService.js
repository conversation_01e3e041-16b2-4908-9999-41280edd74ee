import axios from 'axios';
import { verifyExpTime } from '../service/verifyExpTime';

/**
 * Authentication Service - Sistema original com JWT no localStorage
 */
class AuthService {
  constructor() {
    this.apiBaseUrl = process.env.REACT_APP_API_PERMISSION || '';
  }

  /**
   * Armazenar token JWT no localStorage (sistema original)
   */
  setToken(token) {
    localStorage.setItem('jwt', token);
  }

  /**
   * Recuperar token JWT do localStorage
   */
  getToken() {
    return localStorage.getItem('jwt');
  }

  /**
   * Verificar se usuário está autenticado
   */
  async isAuthenticated() {
    const token = this.getToken();
    if (!token) return false;

    try {
      const isExpired = await verifyExpTime(token);
      return !isExpired;
    } catch (error) {
      console.error('Erro ao verificar token:', error);
      return false;
    }
  }

  /**
   * Armazenar informações do usuário no localStorage (sistema original)
   */
  setUserInfo(userInfo) {
    const { name, email, permission, username } = userInfo;

    localStorage.setItem('@dsm/name', name || '');
    localStorage.setItem('@dsm/mail', email || '');
    localStorage.setItem('@dsm/permission', permission || '');
    localStorage.setItem('@dsm/username', username || '');
    localStorage.setItem('@dsm/time', new Date().toISOString());
  }

  /**
   * Recuperar informações do usuário do localStorage
   */
  getUserInfo() {
    return {
      name: localStorage.getItem('@dsm/name'),
      email: localStorage.getItem('@dsm/mail'),
      permission: localStorage.getItem('@dsm/permission'),
      username: localStorage.getItem('@dsm/username'),
      loginTime: localStorage.getItem('@dsm/time'),
    };
  }

  /**
   * Alias para getUserInfo (compatibilidade)
   */
  getUserData() {
    return this.getUserInfo();
  }

  /**
   * Fazer logout (limpar localStorage)
   */
  logout() {
    localStorage.removeItem('jwt');
    localStorage.removeItem('@dsm/name');
    localStorage.removeItem('@dsm/mail');
    localStorage.removeItem('@dsm/permission');
    localStorage.removeItem('@dsm/username');
    localStorage.removeItem('@dsm/time');
  }

  /**
   * Criar instância do axios com token de autenticação
   */
  createSimpleAxios(baseURL) {
    const token = this.getToken();

    return axios.create({
      baseURL: baseURL || this.apiBaseUrl,
      headers: token ? {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      } : {
        'Content-Type': 'application/json'
      }
    });
  }

  /**
   * Criar instância do axios com autenticação (compatibilidade)
   */
  createAuthenticatedAxios(baseURL, options = {}) {
    const token = this.getToken();

    return axios.create({
      baseURL: baseURL || this.apiBaseUrl,
      headers: token ? {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      } : {
        'Content-Type': 'application/json'
      }
    });
  }

  /**
   * Criar instância da API (compatibilidade)
   */
  createApiInstance(baseURL) {
    return this.createSimpleAxios(baseURL);
  }

  /**
   * Obter token do cookie (compatibilidade - retorna do localStorage)
   */
  getTokenFromCookie() {
    return this.getToken();
  }

  /**
   * Obter headers de autenticação (método principal)
   */
  getAuthHeaders() {
    const token = this.getToken();
    return token ? {
      'Authorization': token, // Formato original sem Bearer prefix
    } : {};
  }

  /**
   * Obter headers de autenticação legacy (compatibilidade)
   */
  getLegacyAuthHeaders() {
    return this.getAuthHeaders();
  }
}

// Export singleton instance
export const authService = new AuthService();
export default authService;
