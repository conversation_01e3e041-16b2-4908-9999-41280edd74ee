.state {
  padding: 5px;
  background-color: #0053f4;
  color: white;
  border-radius: 5px;
  text-align: center;
}

.tableCard {
  margin: 0 auto;
  padding: 16px;
  background-color: rgb(255, 255, 255);
  box-shadow: 0px 0px 10px 2px rgba(113, 113, 113, 0.25);
  border-radius: 24px;
  width: 1090px;
  height: 300px;
}

.table-card {
  margin: 0 auto;
  padding: 16px;
  background-color: rgb(255, 255, 255);
  box-shadow: 0px 0px 10px 2px rgba(113, 113, 113, 0.25);
  border-radius: 24px;
}

.btn-hidden {
  cursor: pointer;
}

.table-card-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.button button {
  background-color: #43914f;
  border: none;
  color: white;
  border-radius: 5px;
  padding: 6px 24px;
  box-shadow: 0px 0px 10px 2px rgba(64, 64, 64, 0.25);
  font-weight: bold;
  font-size: 16px;
  line-height: 22px;
}

.table-filter p {
  padding-top: 16px;
}

.container-table {
  width: 100%;
  margin: 0 auto;
}

.table {
  overflow-y: scroll;
  height: 500px;
}

.table::-webkit-scrollbar-thumb {
  background-color: rgba(137, 137, 137, 0.6); /* color of the scroll thumb */
  height: 20px;
  border-radius: 10px; /* roundness of the scroll thumb */
}

.table::-webkit-scrollbar {
  width: 10px !important;
  background-color: rgba(206, 206, 206, 0.5); /* color of the scroll thumb */
  border-radius: 5px; /* roundness of the scroll thumb */
}

.priority-container {
  display: flex;
  align-items: center;
  gap: 16px;
}
.priority {
  height: 80px;
  width: 4px;
}
