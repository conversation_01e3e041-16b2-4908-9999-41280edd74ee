import { useEffect, useState } from "react";
import { CalendarOutlined } from "@ant-design/icons";
import {
  Layout,
  Card,
  Row,
  Col,
  Input,
  Table,
  Tag,
  Button,
  DatePicker,
  Typography,
  message,
  Space,
  Tooltip,
} from "antd";
import { HeaderMenu } from "../../components/HeaderMenu";
import { SideMenu } from "../../components/SideMenu";
import { Link } from "react-router-dom";
import { EditTicketModal } from "../../components/Modals/Tickets/EditModal";
import { otrsGet, otrsPost } from "../../service/apiOtrs";
import { differenceInCalendarDays, format } from "date-fns";
import { dynamoGet, dynamoPost, dynamoPut } from "../../service/apiDsmDynamo";
import moment from "moment";

const { Content } = Layout;

export const Tickets = () => {
  const { Title } = Typography;
  const [collapsed, setCollapsed] = useState(false);
  const [loading, setLoading] = useState(false);
  const [loadingValidation, setLoadingValidation] = useState(false);
  const [dateErrorStatus, setDateErrorStatus] = useState(false);
  const [search, setSearch] = useState("");
  const [ticketEdited, setTicketEdited] = useState(false);
  let ticketsForAudit = [];
  const [queueItems, setQueueItems] = useState([]);
  const [auditedTickets, setAuditedTickets] = useState([]);
  const [dataTickets, setDataTickets] = useState([]);
  const [formatMoment, setFormatMoment] = useState({
    startMoment: "",
    endMoment: "",
  });

  const getTickets = async (data) => {
    setLoading(true);
    otrsPost("read/tickets/time", {
      params: {
        start: data.start,
        end: data.end,
      },
    })
      .then((res) => {
        setDataTickets(res.data);
        setTimeout(() => {
          setLoading(false)}, 8000
        )
      })
      .catch((err) => {
        console.log(err);
        setLoading(false);
        message.error("Erro ao buscar tickets, verifique a data inserida.");
      });
  };

  const handleTicketAnalysisAudit = async (ticket) => {
    try {
      JSON.parse(localStorage.getItem("@otrs/ticketsForAudit")).find(
        (item) =>
          item.search_id === ticket.ticket_id.toString() &&
          item.user === localStorage.getItem("@dsm/username")
      )
        ? await dynamoPut(
            `${process.env.REACT_APP_STAGE}-tickets-audits`,
            ticket.ticket_id.toString(),
            {
              updated_at: moment().format("YYYY-MM-DD HH:mm:ss"),
            }
          )
        : await dynamoPost(`${process.env.REACT_APP_STAGE}-tickets-audits`, {
            id: ticket.ticket_id.toString(),
            search_id: ticket.ticket_id.toString(),
            updated_at: moment().format("YYYY-MM-DD HH:mm:ss"),
            user: localStorage.getItem("@dsm/username"),
          });
    } catch (error) {
      console.log(error);
    }
  };

  const getTicketByParameter = async (ticketParam) => {
    setLoading(true);
    let res = await otrsPost(`read/tickets/number`, {
      params: ticketParam,
    }).then((res) => {
      if (res.data.length === 0) {
        return message.error("Ticket não encontrado."), setLoading(false);
      } else {
        setLoading(false);
        setDataTickets(res.data);
        JSON.parse(localStorage.getItem("@otrs/queues")).find(
          (i) => i.name === res.data[0].queue
        )
          ? message.success("Ticket encontrado.")
          : message.error("Você não tem permissão para acessar este ticket.");
      }
    });
  };
  console.log(dataTickets);
  const [columns, setColumns] = useState([
    {
      code: "ticket_number",
      title: "Número do ticket",
      dataIndex: "ticket_number",
      key: "ticket_number",
    },
    {
      code: "name",
      title: "Nome do cliente",
      dataIndex: "client_name",
      key: "client_name",
      sorter: (a, b) => a.client_name.localeCompare(b.client_name),
    },
    {
      code: "queue",
      title: "Fila",
      dataIndex: "queue",
      key: "queue",
      sorter: (a, b) => a.queue.localeCompare(b.queue),
      render: (id, item) => {
        return item?.queue?.split("::").pop();
      },
    },
    {
      code: "owner_ticket",
      title: "Proprietário",
      dataIndex: "owner_ticket",
      key: "owner_ticket",
      align: "center",
      sorter: (a, b) => a.owner_ticket.localeCompare(b.owner_ticket),
      render: (id, item) => {
        return item?.owner_ticket?.split("@").shift();
      },
    },
    {
      code: "priority",
      title: "Prioridade",
      dataIndex: "priority",
      key: "priority",
      sorter: (a, b) =>
        a.ticket_priority_name.localeCompare(b.ticket_priority_name),
      render: (id, item) => {
        let arr = [];
        item?.ticket_priority_name?.split(" ").map((name, nameId) => {
          if (nameId !== 0) {
            return arr.push(name);
          }
        });
        return arr?.join(" ");
      },
    },
    {
      code: "last_update",
      title: "Última atualização",
      dataIndex: "ticket_last_update",
      key: "ticket_last_update",
      align: "center",
      render: (id, item) => {
        setLoadingValidation(true);
        return ticketsForAudit?.find(
          (ticket) =>
            ticket?.search_id === item?.ticket_id?.toString() &&
            ticket?.user === localStorage.getItem("@dsm/username")
        ) ? (
          ticketsForAudit?.map((ticket) => {
            if (
              ticket.updated_at <
              moment(item.ticket_change_time).format("YYYY-MM-DD HH:mm:ss")
            ) {
              if (ticket.search_id === item.ticket_id.toString()) {
                return <Tag color="warning">Atualizado</Tag>;
              }
            } else {
              if (ticket.search_id === item.ticket_id.toString()) {
                return <Tag color="success">Nenhuma atualização</Tag>;
              }
            }
          })
        ) : (
          <Tag>Não visualizado</Tag>
        );
      },
    },
    {
      code: "date",
      title: "Data de criação",
      dataIndex: "ticket_create_time",
      key: "ticket_create_time",
      align: "center",
      sorter: (a, b) =>
        a.ticket_create_time.localeCompare(b.ticket_create_time),
      render: (date) => format(new Date(date), "dd/MM/yyyy HH:mm:ss"),
    },
    {
      code: "analize",
      title: "Analisar tickets",
      dataIndex: "analize",
      key: "analize",
      align: "center",
      render: (id, item) => (
        <Link to="/articles" state={item}>
          <Button type="text" onClick={() => handleTicketAnalysisAudit(item)}>
            Entrar
          </Button>
        </Link>
      ),
    },
    {
      code: "edit",
      title: "Editar",
      dataIndex: "id",
      key: "id",
      width: "1%",
      render: (id, item) => {
        return (
          <EditTicketModal
            userQueues={queueItems}
            ticketData={item}
            ticketEdited={ticketEdited}
            setTicketEdited={(ticketEdited) => setTicketEdited(ticketEdited)}
          />
        );
      },
    },
    {
      code: "active",
      title: "Status",
      dataIndex: "active",
      key: "active",
      align: "center",
      render: (id, item) => (
        <>
          <Tag
            color={
              item?.ticket_state === "open"
                ? "yellow"
                : item?.ticket_state === "closed successful"
                ? "green"
                : "black"
            }
          >
            {item?.ticket_state}
          </Tag>
        </>
      ),
    },
  ]);

  useEffect(() => {
    async function getTicketsForAudit() {
      const res = await dynamoGet(
        `${process.env.REACT_APP_STAGE}-tickets-audits`
      );
      res.map((tkt) => ticketsForAudit.push(tkt));
      setAuditedTickets(res);
      localStorage.setItem("@otrs/ticketsForAudit", JSON.stringify(res));
    }
    if (
      localStorage.getItem("@tickets/start") &&
      localStorage.getItem("@tickets/end")
    ) {
      setFormatMoment({
        startMoment: moment(localStorage.getItem("@tickets/start")),
        endMoment: moment(localStorage.getItem("@tickets/end")),
      });
      let start = format(
        new Date(localStorage.getItem("@tickets/start")),
        "yyyy-MM-dd"
      );
      let end = format(
        new Date(localStorage.getItem("@tickets/end")),
        "yyyy-MM-dd"
      );
      let formatStart = start.split("-");
      let formatEnd = end.split("-");
      getTickets({
        start: new Date(
          Date.UTC(formatStart[0], formatStart[1] - 1, formatStart[2], 0, 0, 0)
        ),
        end: new Date(
          Date.UTC(formatEnd[0], formatEnd[1] - 1, formatEnd[2], 0, 0, 0)
        ),
      });
    }
    getTicketsForAudit();
  }, [ticketEdited]);

  useEffect(() => {
    async function getQueue() {
      let queue = await otrsGet(
        `read/ticket/queues/allByUser/${localStorage.getItem("@otrs/userId")}`
      );
      setQueueItems(queue.data);
      localStorage.setItem("@otrs/queues", JSON.stringify(queue.data));
    }
    async function getUserId() {
      setLoadingValidation(false);
      const res = await otrsGet(
        `read/user/email/${localStorage.getItem("@dsm/mail")}`
      )
        .then((res) => {
          localStorage.setItem("@otrs/userId", res?.data[0]?.id);
        })
        .finally(() => {
          getQueue();
        });
    }
    if(localStorage.getItem("@tickets/search")){
      getTicketByParameter(localStorage.getItem("@tickets/search"));
    }

    getUserId();
  }, []);

  return (
    <Layout style={{ minHeight: "100vh" }}>
      <HeaderMenu collapsed={collapsed} setCollapsed={setCollapsed} />
      <Layout>
        <SideMenu collapsed={collapsed} />
        <Content style={{ padding: "2em" }}>
          <Card
            style={{
              boxShadow: "0 0 10px rgba(0,0,0,0.1)",
              borderRadius: "20px",
            }}
          >
            <Row justify="space-between" gutter={[24, 16]}>
              <Tooltip title="Pressione enter para pesquisar um ticket">
                <Col span={7}>
                  <Input
                    onKeyDown={(e) => {
                      if (e.key === "Enter") {
                        localStorage.setItem("@tickets/search", e.target.value);
                        setDataTickets([]);
                        getTicketByParameter(e.target.value);
                        setSearch("");
                      }
                    }}
                    onChange={(e) => setSearch(e.target.value)}
                    placeholder="Busque um ticket ou filtre os trazidos pela data"
                    style={{
                      borderRadius: "7px",
                    }}
                  />
                </Col>
              </Tooltip>

              <Space>
                <Col>
                  <Title
                    level={5}
                    style={{ fontWeight: "400", fontSize: "14px", margin: "0" }}
                  >
                    Insira o período para a busca:
                  </Title>
                </Col>
                <Col>
                  <DatePicker.RangePicker
                    style={{ width: "100%" }}
                    status={dateErrorStatus === true ? "error" : ""}
                    placeholder={
                      dateErrorStatus === true
                        ? ["Período inválido", "Período inválido"]
                        : ["Data inicial", "Data final"]
                    }
                    value={
                      dateErrorStatus === true
                        ? ["", ""]
                        : [formatMoment.startMoment, formatMoment.endMoment]
                    }
                    onChange={(e) => {
                      setDataTickets([]);
                      if (e !== null) {
                        let start = format(new Date(e[0]._d), "yyyy-MM-dd");
                        let end = format(new Date(e[1]._d), "yyyy-MM-dd");
                        let formatStart = start.split("-");
                        let formatEnd = end.split("-");

                        if (
                          differenceInCalendarDays(e[1]._d, e[0]._d) <= 5 &&
                          e.length === 2
                        ) {
                          setDateErrorStatus(false);
                          setFormatMoment({
                            startMoment: e[0],
                            endMoment: e[1],
                          });
                          getTickets({
                            start: new Date(
                              Date.UTC(
                                formatStart[0],
                                formatStart[1] - 1,
                                formatStart[2],
                                0,
                                0,
                                0
                              )
                            ),
                            end: new Date(
                              Date.UTC(
                                formatEnd[0],
                                formatEnd[1] - 1,
                                formatEnd[2],
                                0,
                                0,
                                0
                              )
                            ),
                          });
                          localStorage.setItem("@tickets/start", e[0]);
                          localStorage.setItem("@tickets/end", e[1]);
                        } else {
                          message.error("Período inválido, apenas até 5 dias");
                          setDateErrorStatus(true);
                          setFormatMoment({
                            startMoment: "",
                            endMoment: "",
                          });
                        }
                      }
                    }}
                  />
                </Col>
              </Space>
            </Row>

            {(formatMoment.startMoment !== "" &&
            formatMoment.endMoment !== "") ||
            localStorage.getItem("@tickets/search") !== ''
            ? (
              <Table
                loading={
                  (dataTickets.length === 0 || loadingValidation === false) &&
                  loading === true
                    ? true
                    : false
                }
                scroll={{ x: "100%" }}
                style={{ minWidth: "100%", marginTop: "20px" }}
                dataSource={dataTickets
                  .map((curr) => {
                    return queueItems?.find(
                      (item) => item?.name === curr?.queue
                    )
                      ? curr
                      : null;
                  })
                  .filter((e) => {
                    const data = [
                      e?.client_name
                        ? e?.client_name
                            ?.toString()
                            .toLocaleLowerCase()
                            .startsWith(search?.toLowerCase()) ||
                          e?.client_name
                            ?.toString()
                            .toLocaleLowerCase()
                            .includes(search?.toLowerCase())
                        : false,
                      e?.queue
                        ? e?.queue
                            ?.toString()
                            .toLocaleLowerCase()
                            .startsWith(search?.toLocaleLowerCase()) ||
                          e?.queue
                            ?.toString()
                            .toLocaleLowerCase()
                            .includes(search?.toLocaleLowerCase())
                        : false,
                      e?.ticket_priority_name
                        ? e?.ticket_priority_name
                            ?.toString()
                            .includes(search?.toLocaleLowerCase()) ||
                          e?.ticket_priority_name
                            ?.toString()
                            .toLocaleLowerCase()
                            .includes(search?.toLowerCase())
                        : false,
                      e?.owner_ticket
                        ? e?.owner_ticket?.toString().includes(search) ||
                          e?.owner_ticket
                            ?.toString()
                            .includes(search?.toLocaleLowerCase)
                        : false,
                    ];
                    for (let i = 0; i < data.length; i++) {
                      if (data[i] === true) {
                        return e;
                      }
                    }
                    if (search === "") return e;
                    return null;
                  })}
                columns={columns}
              />
            ) : (
              <Content
                style={{
                  margin: "50px 0",
                  padding: "60px 38%",
                  display: "flex",
                  height: "60px",
                  flexDirection: "column",
                  alignContent: "center",
                  justifyContent: "center",
                }}
              >
                <Row justify="center">
                  <CalendarOutlined style={{ fontSize: "48px" }} />
                </Row>
                <Row justify="center">
                  <Col>
                    <Title
                      level={4}
                      style={{
                        textAlign: "center",
                        fontWeight: "400",
                        fontSize: "14px",
                      }}
                    >
                      Selecione um período
                      <br />
                      para busca.
                    </Title>
                  </Col>
                </Row>
              </Content>
            )}
          </Card>
        </Content>
      </Layout>
    </Layout>
  );
};
