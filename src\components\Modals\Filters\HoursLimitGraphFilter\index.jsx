import { useEffect } from "react";
import { React, useState } from "react";
import { otrsGet } from "../../../../service/apiOtrs";
import { Select, Button, Form, Row, Typography } from "antd";
import { CloseCircleOutlined } from "@ant-design/icons";
import "./HoursLimitGraph.css";

function HoursLimitGraphFilter(props) {
  const { Title, Text } = Typography;
  const { Option } = Select;
  const [form] = Form.useForm();
  const [allProfessional, setAllProfessional] = useState([]);

  function onFilterFinish(values) {
    props.filter(values);
    props.handleVisibleChange();
  }

  function onFilterFailed() {}

  function clear() {
    form.resetFields();
  }

  const sortAlpha = (array) => {
    // Verificar se array é válido e é realmente um array
    if (!array || !Array.isArray(array)) {
      return [];
    }

    const sortedArray = array.sort((a, b) => {
      if (a.name > b.name) {
        return 1;
      }
      if (a.name < b.name) {
        return -1;
      }
      return 0;
    });

    return sortedArray;
  };

  return (
    <Form
      form={form}
      style={{ display: "flex", flexDirection: "column" }}
      className="flex flex-col"
      name="basic"
      initialValues={{
        remember: true,
      }}
      onFinish={onFilterFinish}
      onFinishFailed={onFilterFailed}
      autoComplete="off"
    >
      <div className="main-div-hoursLimited">
        <Row justify="space-between">
          <Title level={4} style={{ fontWeight: 400 }}>
            Limite diário de horas
          </Title>
          <Button type="text" onClick={() => props.handleVisibleChange()}>
            <CloseCircleOutlined style={{ color: "#868383" }} />
          </Button>
        </Row>
        <Form.Item name="professional">
          <Select
            showSearch
            className="w-full"
            placeholder="Profissional"
            optionFilterProp="children"
            filterOption={(input, option) =>
              option.value.toLowerCase().indexOf(input.toLowerCase()) >= 0 ||
              option.value.toLowerCase().indexOf(input.toLowerCase()) >= 0
            }
          >
            {sortAlpha(props.professionals).map((professional) => {
              return (
                <Option key={professional.id} value={professional.login}>
                  {professional.first_name} {professional.last_name}
                </Option>
              );
            })}
          </Select>
        </Form.Item>

        <Form.Item name="billable">
          <Select
            showSearch
            className="w-full"
            placeholder="Billable"
            optionFilterProp="children"
            filterOption={(input, option) =>
              option.props.children
                .toLowerCase()
                .indexOf(input.toLowerCase()) >= 0 ||
              option.props.value.toLowerCase().indexOf(input.toLowerCase()) >= 0
            }
          >
            <Option value="all">Todos</Option>
            <Option value="billable">Billable</Option>
            <Option value="notBillable">Não billable</Option>
          </Select>
        </Form.Item>
        <Form.Item name="buttons">
          <Row justify="space-between">
            <Button onClick={clear} type="default">
              Limpar
            </Button>
            <Button htmlType="submit" type="primary">
              Aplicar
            </Button>
          </Row>
        </Form.Item>
      </div>
    </Form>
  );
}

export default HoursLimitGraphFilter;
