import { apiDsm, getHeader } from "../utils/api";

/**
 * Função para fazer upload de objetos para um determinado bucket
 * @param bucketName nome do bucket
 * @param obj objeto que será salvo no bucket
 * @param fileName nome do objeto que será salvo no bucket
 * @param contentType tipo do objeto que será salvo no bucket
 */

export const s3UploadObj = async ({
  bucketName,
  obj,
  fileName,
  contentType,
}) => {
  const encriptyFileName = Buffer.from(fileName).toString("base64");
  const encriptyBucketName = Buffer.from(bucketName).toString("base64");
  const encriptyContentType = Buffer.from(contentType).toString("base64");

  let formData = new FormData();

  formData.append("obj", obj);
  formData.append("fileName", encriptyFileName);
  formData.append("bucketName", encriptyBucketName);
  formData.append("contentType", encriptyContentType);

  const headers = getHeader();
  const { data } = await apiDsm.post(`upload/object`, formData, { headers });

  return data.data;
};

export const s3CopyObjects = async ({
  sourceBucketName,
  destinationBucketName,
  fileKey,
  fileName,
}) => {
  let body = {
    sourceBucketName: sourceBucketName,
    destinationBucketName: destinationBucketName,
    fileKey: fileKey,
    fileName: fileName,
  };

  const headers = getHeader();
  const { data } = await apiDsm.post(`copy/object`, body, { headers });

  return data;
};

export const s3DeleteMultipleObjects = async ({ bucketName, fileKeys }) => {
  const body = {
    bucketName: bucketName,
    fileKeys: fileKeys,
  };

  const headers = getHeader();
  const { data } = await apiDsm.post(`delete/objects`, body, { headers });

  return data;
};

export const s3ListObjects = async (bucketName, prefix = null) => {
  let body = {
    bucketName: bucketName,
  };

  if (prefix) {
    body = { ...body, prefix: prefix };
  }

  const headers = getHeader();
  const { data } = await apiDsm.post(`list/objects`, body, { headers });

  return data;
};
