import { React } from 'react'
import { Select, DatePicker, Button, Form } from 'antd'
import { CloseSquareFilled } from '@ant-design/icons'

function FilterTicketPerProfissional(props) {
    const { Option } = Select
    const [form] = Form.useForm()
    const clients = ['Todos', 'Logikee', 'Darede']

    function onFilterFinish(values) {
        props.handlePopoverVisibility()
    }

    function onFilterFailed() {}

    function clear() {
        form.resetFields()
    }

    return (
        <Form
            form={form}
            style={{ display: 'flex', flexDirection: 'column' }}
            name="basic"
            onFinish={onFilterFinish}
            onFinishFailed={onFilterFailed}
            autoComplete="off">
            <div style={{ display: 'flex',  justifyContent: 'space-between' }}>
                <h2>Tickets por profissional</h2>
                <button className='filter-close-btn'>X</button>
            </div>
            <Form.Item name="profissional">
                <Select
                    showSearch
                    style={{ width: '100%' }}
                    placeholder="Profissional"
                    optionFilterProp="children"
                    filterOption={(input, option) =>
                        option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0 ||
                        option.props.value.toLowerCase().indexOf(input.toLowerCase()) >= 0
                    }>
                    {clients.map((val) => (
                        <Option value={val}>{val}</Option>
                    ))}
                </Select>
            </Form.Item>

            <Form.Item name="wallet">
                <Select
                    showSearch
                    style={{ width: '100%' }}
                    placeholder="Carteira"
                    optionFilterProp="children"
                    filterOption={(input, option) =>
                        option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0 ||
                        option.props.value.toLowerCase().indexOf(input.toLowerCase()) >= 0
                    }>
                    {clients.map((val) => (
                        <Option value={val}>{val}</Option>
                    ))}
                </Select>
            </Form.Item>

            <Form.Item name="row">
                <Select
                    showSearch
                    style={{ width: '100%' }}
                    placeholder="Fila"
                    optionFilterProp="children"
                    filterOption={(input, option) =>
                        option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0 ||
                        option.props.value.toLowerCase().indexOf(input.toLowerCase()) >= 0
                    }>
                    {clients.map((val) => (
                        <Option value={val}>{val}</Option>
                    ))}
                </Select>
            </Form.Item>

            <div className="buttons" style={{ display: 'flex', justifyContent: 'space-around' }}>
                <Button className="btnClear" onClick={clear}>Limpar</Button>
                <Button
                    htmlType="submit"
                    className="bg-[#43914F] w-36 h-11 text-white font-bold rounded">
                    Aplicar
                </Button>
            </div>
        </Form>
    )
}

export default FilterTicketPerProfissional
