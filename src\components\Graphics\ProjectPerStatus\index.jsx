import {ArrowLeftOutlined} from "@ant-design/icons";
import {Button, Card, Col, Progress, Row, Typography} from "antd";
import {useState} from "react";
import {ProjectHoursDetails} from "../ProjectHoursDetails";
import './ProjectPerStatusStyles.css';

export const ProjectPerStatus = ({data, toggleVision}) => {
    const {Title, Text } = Typography;
    const [openChart, setOpenChart] = useState(false);

    function toggleToChart() {
        setOpenChart(!openChart);
    }

    const projectsInfo = [
        {
            name: 'Novo',
            progress: 20,
            customer: 'Darede',
            strokeColor: '#43914F'
        },
        {
            name: 'Encer<PERSON>ent<PERSON>',
            progress: 40,
            customer: 'Darede',
            strokeColor: '#EA2C2C'
        },
        {
            name: 'Parali<PERSON><PERSON>',
            progress: 60,
            customer: 'Darede',
            strokeColor: '#EA2C2C'
        },
        {
            name: 'Planejamento e inicialização',
            progress: 80,
            customer: 'Darede',
            strokeColor: '#43914F'
        }
    ]

    return (
        <div className="card project-card">
            {!openChart ? (<>
                    <Row>
                        <Button type='text' onClick={toggleVision} style={{ marginBottom: '15px', padding: '2px 5px 2px 0' }}>
                            <ArrowLeftOutlined/>
                            {data.status}
                        </Button>
                    </Row>
                    <Row>
                        <Title level={3} style={{fontWeight: 400}}>Projetos</Title>
                    </Row>
                    <Row>
                        {projectsInfo.map((projects) => {
                            return (
                                <Col span={12}>
                                    <Card onClick={toggleToChart} style={{borderRadius: '8px', margin: '5px 3px'}} hoverable>
                                        <Row>
                                            <Col>
                                                <Title level={5}>{projects.name}</Title>
                                                <Text>{projects.customer}</Text>
                                            </Col>
                                            <Progress 
                                                percent={projects.progress} 
                                                showInfo={false} 
                                                strokeWidth={15} 
                                                success={{percent: projects.progress, 
                                                strokeColor: projects.strokeColor
                                                }}
                                            />
                                        </Row>
                                    </Card>
                                </Col>
                            )
                        })}
                    </Row>
                </>
            ) : (
                <ProjectHoursDetails data={data} toggleToChart={toggleToChart}/>
            )}
        </div>
    )
}
