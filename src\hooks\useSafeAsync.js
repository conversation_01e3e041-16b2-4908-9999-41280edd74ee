import { useEffect, useRef, useCallback, useState } from 'react';

/**
 * Hook to safely handle async operations in useEffect
 * Prevents memory leaks and race conditions
 */
export const useSafeAsync = () => {
  const isMountedRef = useRef(true);

  useEffect(() => {
    isMountedRef.current = true;
    
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  const safeAsync = useCallback((asyncFunction) => {
    return async (...args) => {
      if (isMountedRef.current) {
        try {
          return await asyncFunction(...args);
        } catch (error) {
          if (isMountedRef.current) {
            throw error;
          }
        }
      }
    };
  }, []);

  const isMounted = useCallback(() => isMountedRef.current, []);

  return { safeAsync, isMounted };
};

/**
 * Hook for safe async effects with cleanup
 */
export const useSafeAsyncEffect = (asyncEffect, dependencies = []) => {
  const { safeAsync, isMounted } = useSafeAsync();

  useEffect(() => {
    let cleanup;

    const runEffect = async () => {
      const safeAsyncEffect = safeAsync(asyncEffect);
      cleanup = await safeAsyncEffect();
    };

    runEffect();

    return () => {
      if (cleanup && typeof cleanup === 'function') {
        cleanup();
      }
    };
  }, dependencies); // eslint-disable-line react-hooks/exhaustive-deps

  return { isMounted };
};

/**
 * Hook for safe state updates
 */
export const useSafeState = (initialState) => {
  const { isMounted } = useSafeAsync();
  const [state, setState] = useState(initialState);

  const safeSetState = useCallback((newState) => {
    if (isMounted()) {
      setState(newState);
    }
  }, [isMounted]);

  return [state, safeSetState];
};

/**
 * Hook for abort controller management
 */
export const useAbortController = () => {
  const abortControllerRef = useRef(null);

  const createController = useCallback(() => {
    // Abort previous controller if exists
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    
    // Create new controller
    abortControllerRef.current = new AbortController();
    return abortControllerRef.current;
  }, []);

  const abort = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }
  }, []);

  const getSignal = useCallback(() => {
    return abortControllerRef.current?.signal;
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      abort();
    };
  }, [abort]);

  return {
    createController,
    abort,
    getSignal,
    controller: abortControllerRef.current,
  };
};

/**
 * Hook for timeout management with cleanup
 */
export const useSafeTimeout = () => {
  const timeoutRef = useRef(null);
  const { isMounted } = useSafeAsync();

  const setSafeTimeout = useCallback((callback, delay) => {
    // Clear existing timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    timeoutRef.current = setTimeout(() => {
      if (isMounted()) {
        callback();
      }
    }, delay);

    return timeoutRef.current;
  }, [isMounted]);

  const clearSafeTimeout = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      clearSafeTimeout();
    };
  }, [clearSafeTimeout]);

  return { setSafeTimeout, clearSafeTimeout };
};

/**
 * Hook for interval management with cleanup
 */
export const useSafeInterval = () => {
  const intervalRef = useRef(null);
  const { isMounted } = useSafeAsync();

  const setSafeInterval = useCallback((callback, delay) => {
    // Clear existing interval
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }

    intervalRef.current = setInterval(() => {
      if (isMounted()) {
        callback();
      }
    }, delay);

    return intervalRef.current;
  }, [isMounted]);

  const clearSafeInterval = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      clearSafeInterval();
    };
  }, [clearSafeInterval]);

  return { setSafeInterval, clearSafeInterval };
};

/**
 * Hook for promise management with cancellation
 */
export const useCancellablePromise = () => {
  const pendingPromises = useRef(new Set());

  const cancellablePromise = useCallback((promise) => {
    let isCancelled = false;
    
    const wrappedPromise = new Promise((resolve, reject) => {
      promise
        .then(result => {
          if (!isCancelled) {
            resolve(result);
          }
        })
        .catch(error => {
          if (!isCancelled) {
            reject(error);
          }
        })
        .finally(() => {
          pendingPromises.current.delete(wrappedPromise);
        });
    });

    wrappedPromise.cancel = () => {
      isCancelled = true;
      pendingPromises.current.delete(wrappedPromise);
    };

    pendingPromises.current.add(wrappedPromise);
    return wrappedPromise;
  }, []);

  const cancelAllPromises = useCallback(() => {
    pendingPromises.current.forEach(promise => {
      if (promise.cancel) {
        promise.cancel();
      }
    });
    pendingPromises.current.clear();
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      cancelAllPromises();
    };
  }, [cancelAllPromises]);

  return { cancellablePromise, cancelAllPromises };
};

export default useSafeAsync;
