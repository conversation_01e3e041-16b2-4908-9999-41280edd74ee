import { Col, Row, Typography } from "antd";
import { CheckSquareOutlined } from "@ant-design/icons";
import React from "react";
import { <PERSON><PERSON><PERSON>, Pie, Sector, Legend } from "recharts";

export const MainDonutChart = ({
  data,
  showModalFunction,
  swapVisible,
  dataKey,
  modalState,
  onPieEnter,
  activeIndex,
  renderActiveShape,
  pieGraphCurrentData,
  setActiveIndex,
}) => {
  const { Title, Text } = Typography;
  const [width, setWidth] = React.useState(window.innerWidth);
  let secondaryProportion = window.innerWidth;

  const updateWidth = () => {
    setWidth(window.innerWidth);
  };

  React.useEffect(() => {
    window.addEventListener("resize", updateWidth);

    return () => window.removeEventListener("resize", updateWidth);
  });

  const secondaryGraphWidth = React.useMemo(() => {
    if (width > 1800) {
      const proportion = 1850 / 800;
      return width / proportion;
    } else if (width > 1200 && width < 1800) {
      const proportion = 1850 / 1000;
      return width / proportion;
    } else if (width < 1200) {
      const proportion = 1850 / 1550;
      return width / proportion;
    }
  }, [width]);

  function handleOpenModal(e, index) {
    setActiveIndex(index);
    showModalFunction();
  }

  return (
    <Row align="middle" justify="center">
      <Col xl={16} md={16} sm={24}>
        <PieChart
          onClick={() => (modalState ? showModalFunction() : swapVisible())}
          width={500}
          height={250}
        >
          <Pie
            activeIndex={activeIndex}
            activeShape={renderActiveShape}
            data={data}
            startAngle={360}
            endAngle={0}
            cx={155}
            cy={120}
            innerRadius={70}
            outerRadius={120}
            fill="#43914F"
            dataKey={dataKey}
            onMouseEnter={onPieEnter}
          ></Pie>
        </PieChart>
      </Col>
      <Col>
        {data?.map((entry, index) => {
          return (
            <Col
              onClick={(e) => handleOpenModal(e, index)}
              onMouseEnter={(e) => onPieEnter(e, index)}
            >
              <Text style={{ color: entry?.fill }}>
                <CheckSquareOutlined style={{ backgroundColor: entry?.fill }} />{" "}
                {entry?.name}
              </Text>
            </Col>
          );
        })}
      </Col>
    </Row>
  );
};
