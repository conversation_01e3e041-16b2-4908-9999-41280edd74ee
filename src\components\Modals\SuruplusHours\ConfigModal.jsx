import React, { useState } from "react";
import { useSelector, shallowEqual } from "react-redux";

import { Form, Modal, Button, Checkbox, Row, Col } from "antd";

import { setGlobalPersistState } from "../../../store/actions/global-persist-action";
import { setConsumptionState } from "../../../store/actions/consumption-hours-action";

export const ConfigModal = ({ isOpen, toggle }) => {
  const collumnsToShow = useSelector(
    (state) => state.globalPersist.consumption.collumnsToShow,
    shallowEqual
  );
  const collumnsConsumptionHours = useSelector(
    (state) => state.consumption.collumnsConsumptionHours,
    shallowEqual
  );

  const [form] = Form.useForm();
  const [selectedFields, setSelectedFields] = useState(collumnsToShow);

  function handleSelectField(field, isChecked) {
    const includes = selectedFields.includes(field);

    let newSelectedFields = [];
    if (isChecked && !includes) newSelectedFields = [...selectedFields, field];

    if (!isChecked && includes)
      newSelectedFields = selectedFields.filter((f) => f !== field);

    setSelectedFields(newSelectedFields);
  }

  function handleSave() {
    const payload = { collumnsToShow: selectedFields };
    setGlobalPersistState({ field: "consumption", value: payload });
    toggle();
  }

  return (
    <Modal
      title="Configurações da Página"
      open={isOpen}
      onCancel={toggle}
      footer={[
        <Button onClick={toggle}>Fechar</Button>,
        <Button type="primary" onClick={handleSave}>
          Salvar
        </Button>,
      ]}
    >
      <Form
        layout="vertical"
        onFinish={toggle}
        requiredMark={false}
        form={form}
      >
        <h3>Exibir as colunas: </h3>

        <Row>
          {collumnsConsumptionHours.map((field, index) => (
            <Col md={12}>
              <Checkbox
                key={index}
                checked={selectedFields.includes(field)}
                onChange={(event) =>
                  handleSelectField(field, event.target.checked)
                }
              >
                {field}
              </Checkbox>
            </Col>
          ))}
        </Row>
      </Form>
    </Modal>
  );
};
