import { Row, Form, Select, Input, Col, Button } from "antd";
import { useState } from "react";
import { v4 } from "uuid";

export const UploadFilesForm = ({ form }) => {
  const { Option } = Select;
  const [allFormItems, setAllFormItems] = useState([
    {
      label: "Cliente",
      type: "select",
      mode: "default",
      rules: [
        {
          required: true,
          message: "Por favor, selecione um cliente",
        },
      ],
      placeholder: "Selecione um cliente",
    },
    {
      label: "Categoria",
      type: "select",
      mode: "default",
      rules: [
        {
          required: true,
          message: "Por favor, selecione uma categoria",
        },
      ],
      placeholder: "Selecione uma categoria",
    },
    {
      label: "Tags",
      type: "select",
      mode: "multiple",
      rules: [
        {
          required: true,
          message: "Por favor, selecione uma tag",
        },
      ],
      placeholder: "Adicione tags",
      options: [],
    },
    {
      label: "Email",
      type: "input",
      rules: [
        {
          required: false,
        },
      ],
      placeholder: "Email do cliente",
    },
  ]);

  const handleInsertTags = (value) => {
    setAllFormItems((prevState) => {
      const newState = [...prevState];
      newState[2].options.push({
        id: v4(),
        value: value,
      });
      return newState;
    });
  };

  return (
    <Form form={form} layout="vertical" name="filesForm">
      <Row>
        {allFormItems?.map((item, key) => (
          <Col span={6} key={key}>
            <Form.Item
              label={item.label}
              name={item.label}
              rules={item.rules}
              placeholder={item.placeholder}
            >
              {item.type === "select" ? (
                item.label === "Tags" ? (
                  <Select
                    style={{ width: "80%" }}
                    mode={item.mode}
                    allowClear
                    showSearch
                    placeholder={item.placeholder}
                    optionFilterProp="children"
                    filterOption={(input, option) =>
                      option.children
                        .toLowerCase()
                        .indexOf(input.toLowerCase()) >= 0
                    }
                    onKeyDown={(e) => {
                      if (e.key === "Enter") {
                        handleInsertTags(e.target.value);
                      }
                    }}
                  >
                    {item?.options?.map((option, key) => (
                      <Option key={key} value={option?.id}>
                        {option.value}
                      </Option>
                    ))}
                  </Select>
                ) : (
                  <Select
                    style={{ width: "80%" }}
                    mode={item.mode}
                    allowClear
                    showSearch
                    placeholder={item.placeholder}
                    optionFilterProp="children"
                    filterOption={(input, option) =>
                      option.children
                        .toLowerCase()
                        .indexOf(input.toLowerCase()) >= 0
                    }
                  >
                    {item?.options?.map((option, key) => (
                      <Option key={key} value={option}>
                        {option}
                      </Option>
                    ))}
                  </Select>
                )
              ) : (
                <Input
                  placeholder={item.placeholder}
                  style={{ borderRadius: "7px" }}
                />
              )}
            </Form.Item>
          </Col>
        ))}
      </Row>
    </Form>
  );
};
