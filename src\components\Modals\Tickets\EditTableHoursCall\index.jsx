import { Button, Modal, Form, Checkbox, Row, Col } from "antd";
import { MoreOutlined } from "@ant-design/icons";
import { useState } from "react";

export const EditTableHoursCall = (props) => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const { hours_call, setHoursCall } = props;
  const [form] = Form.useForm();

  const showModal = () => {
    setIsModalVisible(true);
  };

  const handleOk = () => {
    form.submit();
  };

  const handleCancel = () => {
    setIsModalVisible(false);
  };

  const handleSubmit = async (data) => {
    setHoursCall(
      hours_call.filter((a) => {
        if (data.lista.includes(a.code)) {
          return a.code;
        }
      })
    );
    setIsModalVisible(false);
  };

  return (
    <>
      <Button
        type="primary"
        icon={<MoreOutlined />}
        onClick={showModal}
        style={{ boxShadow:'none', height: 'auto' }}
      ></Button>
      <Modal
        title="Configuração"
        open={isModalVisible}
        onOk={handleOk}
        onCancel={handleCancel}
      >
        <Form layout="vertical" onFinish={handleSubmit} form={form}>
          <Form.Item name="lista" label="Fila selecionada:">
            <Checkbox.Group
              style={{
                width: "100%",
              }}
            >
              <Row>
                <Col span={8}>
                  <Checkbox value="contract">Contrato</Checkbox>
                </Col>
                <Col span={8}>
                  <Checkbox value="consumption">Consumo</Checkbox>
                </Col>
              </Row>
            </Checkbox.Group>
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};
