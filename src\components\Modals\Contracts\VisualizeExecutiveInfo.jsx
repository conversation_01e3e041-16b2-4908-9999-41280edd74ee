import { But<PERSON>, <PERSON>, <PERSON>, Modal, <PERSON>, Tag } from "antd";
import { useState } from "react";

export const VisualizeExecutiveInfo = ({ executive }) => {
  const [showModal, setShowModal] = useState(false);

  const formatInfo = (info) => {
    let res = {};
    for (const key of info) {
      switch (key[0]) {
        case "active":
          res["Ativo"] = key[1] === 1 ? "Sim" : "Não";
          break;
        case "user":
          let formatedName = key[1].replaceAll("undefined", "");
          if (key[1] != "" && formatedName.length > 1) {
            res["Nome"] = formatedName;
          }
          break;
        case "type":
          res["Tipo de Cargo"] = key[1];
          break;
        case "phone":
          if (key[1] != "" && key[1] != undefined) {
            res["Telefone"] = key[1];
          }
          break;
        case "email":
          res["Email"] = key[1];
          break;

        default:
          break;
      }
    }

    return Object.entries(res).filter((item) => item[1] !== null);
  };

  return (
    <>
      <Button
        style={{ padding: "0" }}
        type="text"
        onClick={async () => {
          setShowModal(true);
        }}
      >
        <Tag color="#0f9347">Visualizar</Tag>
      </Button>
      <Modal
        title="Informações do Executivo"
        centered
        open={showModal}
        footer={false}
        onCancel={() => setShowModal(false)}
      >
        <Row align="middle" justify="center">
          <Col span={24}>
            <List
              itemLayout="horizontal"
              pagination={{ pageSize: 10 }}
              dataSource={formatInfo(Object.entries(executive))}
              renderItem={(item) =>
                item[0] === "Nome" ? (
                  executive.type?.includes("Externo") ? (
                    <List.Item>
                      <List.Item.Meta title={item[0]} description={item[1]} />
                    </List.Item>
                  ) : null
                ) : (
                  <List.Item>
                    <List.Item.Meta title={item[0]} description={item[1]} />
                  </List.Item>
                )
              }
            />
          </Col>
        </Row>
      </Modal>
    </>
  );
};
