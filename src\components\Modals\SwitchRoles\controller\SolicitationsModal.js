export const formatSwitchRoleData = (data, clients) => {
  let formattedData = [];
  for (let i = 0; i < data.length; i++) {
    for (let j = 0; j < clients.length; j++) {
      let client = clients[j];
      let accounts = client.accounts;
      if (accounts === undefined || accounts.length) continue;
      if (
        accounts.find((account) => account.account_id === data[i].account_id)
      ) {
        const clientName = client.names.name;
        data[i].client_name = clientName.toUpperCase();
        data[i].key = i;
      }
    }
    formattedData.push(data[i]);
  }
  return formattedData;
};
