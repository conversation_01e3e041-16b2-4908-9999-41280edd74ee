import { useEffect, useState } from "react";
import {
  PlusOutlined,
  DeleteOutlined,
  ContactsOutlined,
  CheckOutlined,
} from "@ant-design/icons";
import {
  Button,
  Modal,
  Row,
  Col,
  Table,
  Typography,
  Input,
  Form,
  message,
  Popconfirm,
} from "antd";
import { v4 } from "uuid";
import { maskPhone } from "../../../utils/masks";
import { validateEmail, isOnlyLetter } from "../../../utils/validators";
import { setTechnicalProposalState } from "../../../store/actions/technical-proposal-action";
import { store } from "../../../store/store";
import { useRef } from "react";
import { shallowEqual, useSelector } from "react-redux";

import { useToggle } from "../../../hooks/useToggle";

const { Title } = Typography;

export const AddNewContact = () => {
  const selectedCustomer = useSelector(
    (state) => state.technicalProposal.selectedCustomer,
    shallowEqual
  );
  const clientName = useSelector(
    (state) => state.technicalProposal.clientName,
    shallowEqual
  );

  const [contacts, setContacts] = useState([]);
  const [name, setName] = useState("");
  const [lastName, setLastName] = useState("");
  const [email, setEmail] = useState("");
  const [phone, setPhone] = useState("");

  const nameInputRef = useRef(null);
  const lastNameInputRef = useRef(null);
  const emailInputRef = useRef(null);
  const phoneInputRef = useRef(null);

  const [toggleValue, toggleModal] = useToggle();
  const [removeSelectedCustomer, setRemoveSelectedCustomer] = useState(false);

  useEffect(() => {
    if (selectedCustomer && selectedCustomer.contacts)
      setContacts(selectedCustomer.contacts);
  }, [selectedCustomer]);

  function handleAddContact() {
    const name = nameInputRef.current.input.value.trim();
    const lastName = lastNameInputRef.current.input.value.trim();
    const email = emailInputRef.current.input.value.trim();
    const phone = phoneInputRef.current.input.value.trim();

    if (!name) return message.error("Nome inválido");
    if (!isOnlyLetter(name))
      return message.error(
        "O nome não pode conter numeros ou caracteres especiais"
      );

    if (!lastName) return message.error("Sobrenome inválido");
    if (!isOnlyLetter(lastName))
      return message.error(
        "O sobrenome não pode conter numeros ou caracteres especiais"
      );

    if (!email || !validateEmail(email))
      return message.error("E-mail inválido");

    if (!phone || phone.length < 14) return message.error("Telefone inválido");

    nameInputRef.current.input.value = "";
    lastNameInputRef.current.input.value = "";
    emailInputRef.current.input.value = "";
    phoneInputRef.current.input.value = "";

    setName("");
    setLastName("");
    setEmail("");
    setPhone("");

    const newContact = {
      contact_id: v4(),
      customer_id: selectedCustomer.id,
      identifications: selectedCustomer.identifications,
      responsability: null,
      contact_type: null,
      active: 1,
      names: {
        first_name: name,
        last_name: lastName,
      },
      email: email,
      phone: phone.replace(/\D/g, ""),
    };

    setContacts([...contacts, newContact]);
  }

  function handleSave() {
    setTechnicalProposalState({
      field: "selectedCustomer",
      value: {
        ...selectedCustomer,
        contacts: contacts,
      },
    });

    if (removeSelectedCustomer) {
      setTechnicalProposalState({
        field: "selectedContact",
        value: "",
      });

      setRemoveSelectedCustomer(false);
    }

    message.success("Informações salvas com sucesso");

    toggleModal();
  }

  function handleCancel() {
    nameInputRef.current.input.value = "";
    lastNameInputRef.current.input.value = "";
    emailInputRef.current.input.value = "";
    phoneInputRef.current.input.value = "";

    setName("");
    setLastName("");
    setEmail("");
    setPhone("");

    setContacts(selectedCustomer.contacts);

    toggleModal();
    setRemoveSelectedCustomer(false);
  }

  function handleRemoveContact(index) {
    const { selectedContact } = store.getState().technicalProposal;

    const selectedContactObj = contacts[index];
    if (selectedContact === selectedContactObj.names.first_name) {
      setRemoveSelectedCustomer(true);
    }

    const newContacts = [...contacts];
    newContacts.splice(index, 1);

    setContacts(newContacts);
  }

  return (
    <>
      <Button
        type="text"
        onClick={() => {
          if (!clientName && !selectedCustomer)
            return message.warning("Nenhum cliente selecionado");

          toggleModal();
        }}
      >
        <PlusOutlined />
      </Button>
      <Modal
        title="Adicionar Contato"
        visible={toggleValue}
        width={"80%"}
        closable={false}
        destroyOnClose={true}
        footer={null}
        onCancel={() => {}}
      >
        <Form layout="horizontal" onFinish={() => {}}>
          <Row justify="center" gutter={[16, 16]}>
            <Col span={6}>
              <LabelTitle label="Nome" />
              <Input
                value={name}
                ref={nameInputRef}
                onChange={(event) => setName(event.currentTarget.value)}
              />
            </Col>
            <Col span={6}>
              <LabelTitle label="Sobrenome" />
              <Input
                value={lastName}
                ref={lastNameInputRef}
                onChange={(event) => setLastName(event.currentTarget.value)}
              />
            </Col>
            <Col span={6}>
              <LabelTitle label="Email" />
              <Input
                value={email}
                ref={emailInputRef}
                onChange={(event) => setEmail(event.currentTarget.value)}
              />
            </Col>
            <Col span={6}>
              <LabelTitle label="Telefone" />
              <Input
                value={phone}
                ref={phoneInputRef}
                onChange={(event) =>
                  setPhone(maskPhone(event.currentTarget.value))
                }
                maxLength={15}
              />
            </Col>
          </Row>

          <br />

          <Row justify="center">
            <Form.Item>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={handleAddContact}
              >
                Adicionar
              </Button>
            </Form.Item>
          </Row>
        </Form>

        <Row style={{ marginBottom: "1rem" }}>
          <Col span={24}>
            {contacts?.length > 0 ? (
              <TableContact
                data={contacts?.map((contact, index) => {
                  return {
                    id: index,
                    name: `${contact?.names?.first_name} ${contact.names?.last_name}`,
                    email: contact?.email,
                    phone: contact?.phone ? contact?.phone : "",
                  };
                })}
                onRemove={handleRemoveContact}
              />
            ) : (
              <Row justify="center" gutter={[24, 16]}>
                <Col span={24}>
                  <Row justify="center">
                    <ContactsOutlined style={{ fontSize: "5rem" }} />
                  </Row>
                  <Row justify="center">
                    <Title level={5} style={{ fontWeight: "400" }}>
                      Não existem outros contatos inseridos selecionados
                    </Title>
                  </Row>
                </Col>
              </Row>
            )}
          </Col>
        </Row>

        <Row gutter={[8, 8]} justify={"space-between"}>
          {selectedCustomer &&
          selectedCustomer?.contacts?.length !== contacts?.length ? (
            <Popconfirm
              title="Ao cancelar você perderá suas modificações, tem certeza?"
              onConfirm={handleCancel}
              okText="Sim"
              cancelText="Não"
            >
              <Button type="text-color">
                Cancelar
                <DeleteOutlined />
              </Button>
            </Popconfirm>
          ) : (
            <Button type="text-color" onClick={handleCancel}>
              Cancelar
              <DeleteOutlined />
            </Button>
          )}
          <Button type="primary" onClick={handleSave}>
            Salvar
            <CheckOutlined />
          </Button>
        </Row>
      </Modal>
    </>
  );
};

const TableContact = ({ data, onRemove }) => {
  const columns = [
    {
      title: "Nome",
      dataIndex: "name",
      key: "name",
    },
    {
      title: "Email",
      dataIndex: "email",
      key: "email",
    },
    {
      title: "Telefone",
      dataIndex: "phone",
      key: "phone",
      align: "center",
      render: (phone) => {
        return <span>{maskPhone(phone)}</span>;
      },
    },
    {
      title: "Remover",
      dataIndex: "id",
      key: "id",
      align: "center",
      render: (id, props) => {
        return (
          <Popconfirm
            title={"Tem certeza que deseja remover este contato?"}
            okText={"Sim"}
            cancelText={"Não"}
            onConfirm={(e) => onRemove(id)}
          >
            <Button danger type="text" icon={<DeleteOutlined />}></Button>
          </Popconfirm>
        );
      },
    },
  ];

  return (
    <Table
      dataSource={data}
      columns={columns}
      scroll={{ x: 100 }}
      pagination={true}
    />
  );
};

const LabelTitle = ({ label }) => {
  return (
    <Title level={5} style={{ fontWeight: "400", fontSize: "14px" }}>
      {label}
    </Title>
  );
};
