import ExcelJS from "exceljs";
import {
  addFieldInSelectedFields,
  generateExcel,
  removeFieldInSelectedFields,
} from "../../../../controllers/clients/clientsGenerateExcelController";
import { data, fields } from "../../../../__mocks__/clients/data";

describe("removeFieldInSelectedFields", () => {
  it("should remove a specific field", () => {
    const selectedFields = [
      {
        label: "Clientes",
        subItems: [
          "DSM ID",
          "Data de Criação",
          "CRM do cliente",
          "ITSM do cliente",
          "Nome do cliente",
          "Nome fantasia",
          "CNPJ",
        ],
      },
    ];
    const expected = [
      {
        label: "Clientes",
        subItems: [
          "DSM ID",
          "Data de Criação",
          "CRM do cliente",
          "ITSM do cliente",
          "Nome do cliente",
          "Nome fantasia",
        ],
      },
    ];
    const field = "CNPJ";

    const response = removeFieldInSelectedFields(field, selectedFields);

    expect(response).toEqual(expected);
  });

  it("should remove a subItem field and its corresponding label", () => {
    const selectedFields = [
      {
        label: "Clientes",
        subItems: [
          "DSM ID",
          "Data de Criação",
          "CRM do cliente",
          "ITSM do cliente",
          "Nome do cliente",
          "Nome fantasia",
          "CNPJ",
        ],
      },
      {
        label: "Contatos",
        subItems: ["CRM do contato"],
      },
    ];
    const expected = [
      {
        label: "Clientes",
        subItems: [
          "DSM ID",
          "Data de Criação",
          "CRM do cliente",
          "ITSM do cliente",
          "Nome do cliente",
          "Nome fantasia",
          "CNPJ",
        ],
      },
    ];
    const field = "CRM do contato";

    const response = removeFieldInSelectedFields(field, selectedFields);

    expect(response).toEqual(expected);
  });

  it("should remove a label field and its corresponding subItems", () => {
    const selectedFields = [
      {
        label: "Clientes",
        subItems: [
          "DSM ID",
          "Data de Criação",
          "CRM do cliente",
          "ITSM do cliente",
          "Nome do cliente",
          "Nome fantasia",
          "CNPJ",
        ],
      },
      {
        label: "Contatos",
        subItems: [
          "DSM ID",
          "CRM do contato",
          "ITSM do contato",
          "Primeiro nome",
          "Último nome",
          "Telefone",
          "Email",
        ],
      },
    ];
    const expected = [
      {
        label: "Clientes",
        subItems: [
          "DSM ID",
          "Data de Criação",
          "CRM do cliente",
          "ITSM do cliente",
          "Nome do cliente",
          "Nome fantasia",
          "CNPJ",
        ],
      },
    ];

    const field = "Todos-Contatos";
    const response = removeFieldInSelectedFields(field, selectedFields);

    expect(response).toEqual(expected);
  });
});

describe("addFieldInSelectedFields", () => {
  it("should add a specific field", () => {
    const selectedFields = [
      {
        label: "Clientes",
        subItems: [
          "DSM ID",
          "Data de Criação",
          "CRM do cliente",
          "ITSM do cliente",
          "Nome do cliente",
          "Nome fantasia",
        ],
      },
    ];
    const expected = [
      {
        label: "Clientes",
        subItems: [
          "DSM ID",
          "Data de Criação",
          "CRM do cliente",
          "ITSM do cliente",
          "Nome do cliente",
          "Nome fantasia",
          "CNPJ",
        ],
      },
    ];
    const field = "CNPJ";

    const response = addFieldInSelectedFields(field, fields, selectedFields);
    expect(response).toEqual(expected);
  });

  it("should add a subItem field and its corresponding label", () => {
    const selectedFields = [
      {
        label: "Clientes",
        subItems: [
          "DSM ID",
          "Data de Criação",
          "CRM do cliente",
          "ITSM do cliente",
          "Nome do cliente",
          "Nome fantasia",
          "CNPJ",
        ],
      },
    ];
    const expected = [
      {
        label: "Clientes",
        subItems: [
          "DSM ID",
          "Data de Criação",
          "CRM do cliente",
          "ITSM do cliente",
          "Nome do cliente",
          "Nome fantasia",
          "CNPJ",
        ],
      },
      {
        label: "Contatos",
        subItems: ["CRM do contato"],
      },
    ];

    const field = "CRM do contato";

    const response = addFieldInSelectedFields(field, fields, selectedFields);

    expect(response).toEqual(expected);
  });

  it("should add a label field and its corresponding subItems", () => {
    const selectedFields = [
      {
        label: "Clientes",
        subItems: [
          "DSM ID",
          "Data de Criação",
          "CRM do cliente",
          "ITSM do cliente",
          "Nome do cliente",
          "Nome fantasia",
          "CNPJ",
        ],
      },
    ];
    const expected = [
      {
        label: "Clientes",
        subItems: [
          "DSM ID",
          "Data de Criação",
          "CRM do cliente",
          "ITSM do cliente",
          "Nome do cliente",
          "Nome fantasia",
          "CNPJ",
        ],
      },
      {
        label: "Contatos",
        subItems: [
          "CRM do contato",
          "ITSM do contato",
          "Primeiro nome",
          "Último nome",
          "Telefone",
          "Email",
          "Todos-Contatos",
        ],
      },
    ];

    const field = "Todos-Contatos";

    const response = addFieldInSelectedFields(field, fields, selectedFields);

    expect(response).toEqual(expected);
  });
});

describe("generateExcel", () => {
  const selectedFields = [
    {
      label: "Clientes",
      subItems: [
        "DSM ID",
        "Data de Criação",
        "CRM do cliente",
        "ITSM do cliente",
        "Nome do cliente",
        "CNPJ",
      ],
    },
    {
      label: "Contatos",
      subItems: [
        "CRM do contato",
        "ITSM do contato",
        "Primeiro nome",
        "Último nome",
        "Telefone",
        "Email",
      ],
    },
  ];
  it("should return a Blob with the correct 'xlsx' type", async () => {
    const blob = await generateExcel(data, selectedFields);
    expect(blob instanceof Blob).toBeTruthy();
    expect(blob.type).toBe(
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    );
  });

  it("should verify the presence of 'Clientes' and 'Contatos' tabs in the generated Excel file", async () => {
    const blob = await generateExcel(data, selectedFields);
    const workbook = new ExcelJS.Workbook();
    await workbook.xlsx.load(blob);
    const clientsSheet = workbook.getWorksheet("Clientes");
    const constactsSheet = workbook.getWorksheet("Contatos");

    expect(clientsSheet).toBeDefined();
    expect(constactsSheet).toBeDefined();
  });

  it("should verify the correctness of cell values in the generated Excel file", async () => {
    const blob = await generateExcel(data, selectedFields);
    const workbook = new ExcelJS.Workbook();
    await workbook.xlsx.load(blob);
    const clientesSheet = workbook.getWorksheet("Clientes");

    const expectedData = [
      [
        "DSM ID",
        "Data de Criação",
        "CRM do cliente",
        "ITSM do cliente",
        "Nome do cliente",
        "CNPJ",
      ],
    ];

    data.map((item) => {
      expectedData.push([
        item.created_at,
        item.identifications.crm_id,
        item.identifications.itsm_id,
        item.names.name,
        item.cnpj,
      ]);
    });

    clientesSheet.eachRow((row, rowNumber) => {
      const actualRowData = [];
      row.eachCell((cell) => {
        actualRowData.push(cell.value);
      });
      expect(actualRowData).toEqual(expectedData[rowNumber - 1]);
    });
  });
});
