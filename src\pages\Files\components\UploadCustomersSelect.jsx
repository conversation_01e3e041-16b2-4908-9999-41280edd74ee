import { Select, Row } from "antd";
import { setUploadFileFilesState } from "../../../store/actions/files-action";
import { useSelector, shallowEqual } from "react-redux";

export const UploadCustomersSelect = ({ allCustomers }) => {
  const { Option } = Select;

  const uploadFile = useSelector(
    (state) => state.files.uploadFile,
    shallowEqual
  );

  const handleSelectCustomer = (value) => {
    const customer = allCustomers?.find(
      (c) => c?.names?.fantasy_name === value
    );

    if (customer === undefined) {
      const customerFantasyName = allCustomers?.find(
        (c) => c?.names?.name === value
      );
      const fileCustomer = {
        id: customerFantasyName.id,
        name: customerFantasyName.names.name,
        cnpj: customerFantasyName.cnpj,
      };
      setUploadFileFilesState({
        field: "uploadFile",
        value: { ...uploadFile, customer: fileCustomer },
      });
    } else {
      const fileCustomer = {
        id: customer.id,
        name: customer.names.fantasy_name,
        cnpj: customer.cnpj,
      };
      setUploadFileFilesState({
        field: "uploadFile",
        value: { ...uploadFile, customer: fileCustomer },
      });
    }
  };

  return (
    <>
      <Row>Cliente*</Row>
      <Row>
        <Select
          showSearch
          placeholder="Selecione um cliente..."
          optionFilterProp="children"
          value={
            uploadFile?.customer?.id !== "" ? uploadFile?.customer?.name : null
          }
          filterOption={(input, option) =>
            option?.children?.toLowerCase().startsWith(input?.toLowerCase()) ||
            option?.children?.toLowerCase().includes(input?.toLowerCase())
          }
          filterSort={(optionA, optionB) =>
            optionA?.children
              ?.toLowerCase()
              .localeCompare(optionB?.children?.toLowerCase())
          }
          onSelect={handleSelectCustomer}
          style={{ width: "100%" }}
        >
          {allCustomers?.map((c) => {
            if (
              c?.names?.fantasy_name !== undefined ||
              c?.names?.fantasy_name !== ""
            ) {
              return (
                <Option
                  value={
                    c?.names?.fantasy_name ? c?.names?.fantasy_name : "N/A"
                  }
                >
                  {c?.names?.fantasy_name}
                </Option>
              );
            } else {
              return (
                <Option value={c?.names?.name ? c?.names?.name : "N/A"}>
                  {c?.names?.name}
                </Option>
              );
            }
          })}
        </Select>
      </Row>
    </>
  );
};
