import { useState, useEffect } from "react";
import { Row, Col, Typography, Input, Form } from "antd";

export const HourValueTypeCard = ({
  title,
  formRef,
  initialValues,
  hourValueData,
  hourTypeTitle,
  mainKey,
  handleChangeHourValue,
}) => {
  const { Title, Text } = Typography;
  const [sortedHourValueData, setSortedHourValueData] = useState([]);

  function compareSetup(a, b) {
    if (a.name.includes("setup") && !b.name.includes("setup")) {
      return -1;
    }
    if (!a.name.includes("setup") && b.name.includes("setup")) {
      return 1;
    }
    return 0;
  }
  function compareSust(a, b) {
    if (a.name.includes("sust") && !b.name.includes("sust")) {
      return -1;
    }
    if (!a.name.includes("sust") && b.name.includes("sust")) {
      return 1;
    }
    return 0;
  }

  useEffect(() => {
    setSortedHourValueData(hourValueData ? hourValueData : []);
  }, [hourValueData]);

  return (
    <Col lg={10} sm={20}>
      <Row justify="center">
        <Title level={5}>{title}</Title>
      </Row>
      <Form
        layout=""
        form={formRef}
        // onFinish={e => handleSubmit(e)}
        initialValues={{
          daredeHourValue: initialValues,
        }}
        // style={{ marginInline: '.5rem' }}
      >
        <Row
          justify="center"
          style={{
            backgroundColor: title?.includes("adicional")
              ? "#0F9347"
              : "#BEBFC1",
            padding: "1.5rem 0",
            borderRadius: ".7rem",
          }}
          gutter={[10, 0]}
        >
          {hourValueData
            ?.sort(compareSetup)
            ?.sort(compareSust)
            ?.slice(0, hourValueData.length / 2)
            ?.map((hourType, hourTypeKey) => {
              let temp = hourType.name.split("");
              let fieldName = "";
              if (hourType.name.includes("8x5")) {
                fieldName =
                  hourType.name.slice(0, temp.length - 3).toUpperCase() +
                  " " +
                  hourType.name.slice(temp.length - 3, temp.length);
              } else {
                fieldName =
                  hourType.name.slice(0, temp.length - 4).toUpperCase() +
                  " " +
                  hourType.name.slice(temp.length - 4, temp.length);
              }
              return (
                <Col span={10} key={hourTypeKey}>
                  <Row justify="center">
                    <Text strong>{fieldName}</Text>
                  </Row>
                  <Form.Item
                    name={"hourValue" + hourType.name + "_first_" + mainKey}
                    initialValue={hourType.value}
                  >
                    <Input
                      defaultValue={hourType.value}
                      type="text"
                      onChange={(e) =>
                        handleChangeHourValue(
                          e.target.value,
                          hourType.name,
                          mainKey,
                          hourTypeTitle
                        )
                      }
                      onInput={(event) =>
                        (event.target.value = event.target.value
                          .toString()
                          .replace(/\D/g, ""))
                      }
                    />
                  </Form.Item>
                </Col>
              );
            })}
          {hourValueData
            ?.sort(compareSetup)
            ?.sort(compareSust)
            ?.slice(hourValueData.length / 2, hourValueData.length)
            ?.map((hourType, hourTypeKey) => {
              let temp = hourType.name.split("");
              let fieldName = "";
              if (hourType.name.includes("8x5")) {
                fieldName =
                  hourType.name.slice(0, temp.length - 3).toUpperCase() +
                  " " +
                  hourType.name.slice(temp.length - 3, temp.length);
              } else {
                fieldName =
                  hourType.name.slice(0, temp.length - 4).toUpperCase() +
                  " " +
                  hourType.name.slice(temp.length - 4, temp.length);
              }
              return (
                <Col span={10} key={`second_${hourTypeKey}`}>
                  <Row justify="center">
                    <Text strong>{fieldName}</Text>
                  </Row>
                  <Form.Item
                    name={"hourValue" + hourType.name + "_second_" + mainKey}
                    initialValue={hourType.value}
                  >
                    <Input
                      defaultValue={hourType.value}
                      type="text"
                      onChange={(e) =>
                        handleChangeHourValue(
                          e.target.value,
                          hourType.name,
                          mainKey,
                          hourTypeTitle
                        )
                      }
                      onInput={(event) =>
                        (event.target.value = event.target.value
                          .toString()
                          .replace(/\D/g, ""))
                      }
                    />
                  </Form.Item>
                </Col>
              );
            })}
        </Row>
      </Form>
    </Col>
  );
};
