import moment from "moment";

export function filterByMonth(data, selectedMonth) {
  let filteredData = data;

  if (selectedMonth !== "") {
    const startOfMonth = moment(selectedMonth).startOf("month");
    const endOfMonth = moment(selectedMonth).endOf("month");

    filteredData = filteredData.filter((item) => {
      return (
        moment(item.createdAt).isBetween(
          startOfMonth,
          endOfMonth,
          "day",
          "[]"
        ) ||
        moment(item.createdAt).isSame(startOfMonth, "day") ||
        moment(item.createdAt).isSame(endOfMonth, "day")
      );
    });
  }

  return filteredData || [];
}
