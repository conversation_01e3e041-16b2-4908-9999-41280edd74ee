import { useState } from "react";
import { <PERSON><PERSON>, Button, Row, Col, Select, Form, Tag, message } from "antd";
import { cognitoPutRole } from "../../../service/apiCognito";
import { logNewAuditAction } from "../../../controllers/audit/logNewAuditAction";

const { Option } = Select;

export const AddPermissionUser = (props) => {
  const [form] = Form.useForm();
  const [show, setShow] = useState(false);
  const [loading, setLoading] = useState(false);
  const { user, users, permissions, mutate } = props;

  const handleOk = ({ role }) => {
    setLoading(true);
    try {
      cognitoPutRole({ user, role, stage: process.env.REACT_APP_STAGE });

      mutate(
        users.map((u) => {
          if (u.user === user) {
            return { ...u, permission: role };
          }

          return u;
        }),
        false
      );
      const permissionName = permissions.find((p) => p.id === role);
      const username = localStorage.getItem("@dsm/username");
      const title = "Alteração de Permissão de Usuário";
      const description = `${username} atribuiu a permissão '${permissionName.name_permission}' ao usuário ${user}`;
      logNewAuditAction(username, title, description);

      message.success("Permissão atualizada com sucesso!");
      setLoading(false);
      handleCancel();
    } catch (error) {
      setLoading(true);
      console.log(error);
      return message.error(
        "Ocorreu um erro ao tentar trocar a permissão do usuário :("
      );
    }
  };

  const handleCancel = () => {
    setShow(false);
  };

  return (
    <>
      <Button
        style={{ padding: "0" }}
        onClick={() => setShow(true)}
        type="text"
      >
        <Tag color="mediumseagreen">Atribuir role</Tag>
      </Button>
      <Modal
        title="Atribuir role"
        open={show}
        onOk={form.submit}
        confirmLoading={loading}
        onCancel={handleCancel}
        okText="Atribuir role"
      >
        <Row>
          <Col span="24">
            <Form layout="vertical" onFinish={handleOk} form={form}>
              <Form.Item label="Role" name="role">
                <Select
                  placeholder="Selecione uma permissão"
                  style={{ width: "100%" }}
                >
                  {permissions?.map((item, itemKey) => {
                    return (
                      <Option key={itemKey} value={item.id}>
                        {item.name_permission}
                      </Option>
                    );
                  })}
                </Select>
              </Form.Item>
            </Form>
          </Col>
        </Row>
      </Modal>
    </>
  );
};
