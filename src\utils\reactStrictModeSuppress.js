/**
 * Supressão específica para warnings do React StrictMode
 * Especialmente para findDOMNode warnings que são comuns em bibliotecas como Antd
 */

// Interceptar e suprimir warnings específicos do React StrictMode
(function() {
  'use strict';
  
  // Armazenar referências originais
  const originalConsoleError = console.error;
  const originalConsoleWarn = console.warn;
  
  // Função para verificar se é um warning do StrictMode que deve ser suprimido
  function isStrictModeWarningToSuppress(message) {
    const messageStr = String(message);
    
    // Padrões específicos de warnings a serem suprimidos
    const patterns = [
      // findDOMNode warnings
      /Warning: findDOMNode is deprecated/i,
      /findDOMNode was passed an instance/i,
      /Instead, add a ref directly to the element/i,
      /Learn more about using refs safely here/i,
      /https:\/\/reactjs\.org\/link\/strict-mode-find-node/i,
      /at SingleObserver/i,
      /at ResizeObserver/i,
      /at Tooltip/i,
      /at MenuItem/i,
      /will be removed in the next major release/i,

      // Antd deprecation warnings
      /Warning: \[antd: Menu\] `children` is deprecated/i,
      /Please use `items` instead/i,
      /Warning: \[antd: Modal\] `visible` is deprecated/i,
      /Please use `open` instead/i,
      /Warning: \[antd:/i,
      /is deprecated/i,

      // Performance violations (opcional - descomente se quiser suprimir)
      // /Violation.*handler took.*ms/i,
      // /Forced reflow while executing JavaScript/i
    ];
    
    return patterns.some(pattern => pattern.test(messageStr));
  }
  
  // Sobrescrever console.error
  console.error = function(...args) {
    const firstArg = args[0];
    
    if (isStrictModeWarningToSuppress(firstArg)) {
      return; // Suprimir o warning
    }
    
    // Para outros erros, usar o console original
    return originalConsoleError.apply(console, args);
  };
  
  // Sobrescrever console.warn
  console.warn = function(...args) {
    const firstArg = args[0];
    
    if (isStrictModeWarningToSuppress(firstArg)) {
      return; // Suprimir o warning
    }
    
    // Para outros warnings, usar o console original
    return originalConsoleWarn.apply(console, args);
  };
  
  // Interceptar possíveis warnings que vêm através de outros mecanismos
  if (typeof window !== 'undefined') {
    // Interceptar eventos de erro que podem conter warnings
    const originalAddEventListener = window.addEventListener;
    
    window.addEventListener = function(type, listener, options) {
      if (type === 'error' && typeof listener === 'function') {
        const wrappedListener = function(event) {
          if (event.message && isStrictModeWarningToSuppress(event.message)) {
            event.preventDefault();
            return false;
          }
          return listener.call(this, event);
        };
        return originalAddEventListener.call(this, type, wrappedListener, options);
      }
      return originalAddEventListener.call(this, type, listener, options);
    };
  }
  
  // Função para restaurar console original (para debugging)
  window.__restoreConsole = function() {
    console.error = originalConsoleError;
    console.warn = originalConsoleWarn;
  };
  
})();

// Log de confirmação apenas em desenvolvimento
if (process.env.NODE_ENV === 'development') {
  console.log('🚫 React StrictMode findDOMNode warnings suppressed');
}
