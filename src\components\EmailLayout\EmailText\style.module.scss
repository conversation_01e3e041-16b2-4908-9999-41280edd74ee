.text-container {
    border: 2px solid transparent;
    border-radius: 3px;
    min-height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    margin-bottom: 5px;

    p {
        margin: 0px;
        padding-left: 30px;
        padding-right: 30px;

    }

    .text-options {
        display: flex!important;
        position: absolute;
        right: -20px;
        width: 35px;
        height: 40px;
        color: white;
        border-radius: 3px;
        justify-content: center;
        align-items: center;
        transition: 0.7s;
        opacity: 0;
        z-index: -1;
        cursor: pointer;
    }

    &:hover {
        .text-options{
            display: flex;
            opacity: 1;
            right: -45px;
            z-index: 1;
        }
    }
}

.text-container:hover {
    border: 2px solid #0092cc8e;
}