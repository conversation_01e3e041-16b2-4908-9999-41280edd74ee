import { Button, Form, Input, message, Tooltip } from "antd";
import { useState } from "react";
import Modal from "antd/lib/modal/Modal";
import { UndoOutlined } from "@ant-design/icons";
import { dsmPost } from "../../../service/apiDsm";
import { otrsPost, otrsPut } from "../../../service/apiOtrs";
import Axios from "axios";

export const ResetSecret = (props) => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();
  const { secret, getSecrets } = props;

  const resetSecret = async (data) => {
    setLoading(true);

    if (!data.ticket_id) {
      message.error("Insira um ticket para resetar este MFA.");
      return setLoading(false);
    }

    let ticket = await otrsPost("read/tickets/number", {
      params: data.ticket_id,
    });

    ticket = ticket.data[0];

    if (!ticket) {
      message.error("Ticket inexistente, insira um número correto.");
      return setLoading(false);
    } else if (ticket.ticket_state.includes("closed")) {
      message.error("Este ticket já foi fechado.");
      return setLoading(false);
    }

    if (secret.origin === "dsm") {
      // ticketPut("close/ticket", {
      //   title: "MFA Resetado com sucesso",
      //   from: localStorage.getItem("@dsm/mail"),
      //   description:
      //     "MFA do usuário " +
      //     secret.user +
      //     " foi resetado com sucesso, pelo analista " +
      //     localStorage.getItem("@dsm/username") +
      //     ".",
      //   closed: "closed successful",
      //   tktNumber: parseInt(data.ticket_id),
      // })
      //   .then((resT) => {
      //     dsmPost("/mfa", {
      //       only_read: 2,
      //       user: secret.user,
      //     })
      //       .then((res) => {
      //         message.success("Secret do usuário resetado com sucesso.");
      //         getSecrets();
      //         form.resetFields();
      //         setLoading(false);
      //       })
      //       .catch((err) => {
      //         message.error("Erro ao tentar desativar ticket");
      //         setLoading(false);
      //       });
      //   })
      //   .catch((err) => {
      //     message.error("Erro ao tentar resetar secret de usuário");
      //     setLoading(false);
      //   });
      dsmPost("/mfa", {
        only_read: 2,
        user: secret.user,
      })
        .then((res) => {
          message.success("Secret do usuário resetado com sucesso.");
          getSecrets();
          form.resetFields();
          setLoading(false);
        })
        .catch((err) => {
          message.error("Erro ao tentar desativar ticket");
          setLoading(false);
        });
    } else {
      // ticketPut("close/ticket", {
      //   title: "MFA Resetado com sucesso",
      //   from: localStorage.getItem("@dsm/mail"),
      //   description:
      //     "MFA do usuário " +
      //     secret.user +
      //     " foi resetado com sucesso, pelo analista " +
      //     localStorage.getItem("@dsm/username") +
      //     ".",
      //   closed: "closed successful",
      //   tktNumber: parseInt(data.ticket_id),
      // })
      //   .then((resT) => {
      //     Axios.post(process.env.REACT_APP_API_CUSTOMER_PORTAL + "mfa", {
      //       only_read: 2,
      //       user: secret.user,
      //     })
      //       .then((res) => {
      //         message.success("Secret do usuário resetado com sucesso.");
      //         getSecrets();
      //         form.resetFields();
      //         setLoading(false);
      //       })
      //       .catch((err) => {
      //         message.error("Erro ao tentar desativar ticket");
      //         setLoading(false);
      //       });
      //   })
      //   .catch((err) => {
      //     message.error("Erro ao tentar resetar secret de usuário");
      //     setLoading(false);
      //   });
      Axios.post(process.env.REACT_APP_API_CUSTOMER_PORTAL + "mfa", {
        only_read: 2,
        user: secret.user,
      })
        .then((res) => {
          message.success("Secret do usuário resetado com sucesso.");
          getSecrets();
          form.resetFields();
          setLoading(false);
        })
        .catch((err) => {
          message.error("Erro ao tentar desativar ticket");
          setLoading(false);
        });
    }
  };

  const showModal = () => {
    setIsModalVisible(true);
  };

  const handleOk = () => {
    form.submit();
    // setIsModalVisible(false);
  };

  const handleCancel = () => {
    setIsModalVisible(false);
    form.resetFields();
  };

  return (
    <>
      <Tooltip
        title="Remova as informações do MFA deste usuário"
        placement="left"
      >
        <Button type="danger" onClick={showModal}>
          <UndoOutlined />
        </Button>
      </Tooltip>
      <Modal
        title="Resete uma chave de MFA"
        open={isModalVisible}
        confirmLoading={loading}
        onCancel={handleCancel}
        cancelText="Cancelar"
        onOk={handleOk}
        okText="Resetar"
      >
        <Form form={form} onFinish={resetSecret}>
          <Form.Item name="ticket_id" style={{ margin: "0" }}>
            <Input placeholder="Insira o número do Ticket" />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};
