.card {
  background-color: white;
  border-radius: 24px;
}

.headerTicket{
  display: flex;
  justify-content: space-between;
  padding: 20px 30px;
  align-items: center;
  margin-bottom: 5px;
}

.cardContainer{
  display: flex;
  flex-direction: column;
  border-radius: 24px;
  padding-bottom: 20px;
  padding: 0px 20px;
  height: 90%;
}

.cardContainer > div > .multipleChart {
  height: 250px ;
  width: 180px;
  margin-top: 3.5em;
  margin-right: 5px;
  overflow-y: auto;
  word-break:break-all;
}

.cardContainer > div > .test {
  margin-top: 3.5em;
  margin-right: 5px;
  word-break:break-all;
}

.cardContainer > div > div::-webkit-scrollbar{
  background-color: rgba(206, 206, 206, 0.5);    /* color of the scroll thumb */
  border-radius: 5px;       /* roundness of the scroll thumb */
  width: 10px;
}

.cardContainer > div > div::-webkit-scrollbar-thumb{
  background-color: rgba(137, 137, 137, 0.6);    /* color of the scroll thumb */
  height: 20px;
  border-radius: 10px;       /* roundness of the scroll thumb */
}

.icon{
  color: #1EBD71;
}

.select{
  width: 150px;
  float: left;
  text-align: left;
}

.name{
  width: 200px;
  float: left;
  display: flex;
}
