.table-card {
  margin: 0 auto;
  padding: 16px;
  width: 100%;
  background-color: rgb(255, 255, 255);
  box-shadow: 0px 0px 10px 2px rgba(113, 113, 113, 0.25);
  border-radius: 24px;
}

.button-hidden {
  cursor: pointer;
  display: flex;
  align-items: center;
  background-color: #43914f;
  font-weight: bold;
  color: white;
  padding: 6px 20px;
  border-radius: 5px;
  height: 30px;
  border: none;
}

.table-card-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.button button {
  background-color: #43914f;
  border: none;
  color: white;
  border-radius: 5px;
  padding: 6px 24px;
  box-shadow: 0px 0px 10px 2px rgba(64, 64, 64, 0.25);
  font-weight: bold;
  font-size: 16px;
  line-height: 22px;
}

.table-card-top-left {
  display: flex;
  align-items: center;
  width: 150px;
  gap: 10px;
}

.table-filter p {
  padding-top: 16px;
}

.container-table {
  width: 100%;
  margin: 0 auto;
}
.priority-container {
  display: flex;
  align-items: center;
  gap: 16px;
}
.priority {
  height: 80px;
  width: 4px;
}

.state {
  padding: 5px;
  background-color: #0053f4;
  color: white;
  border-radius: 5px;
  text-align: center;
}

.tableCard {
  margin: 0 auto;
  padding: 16px;
  background-color: rgb(255, 255, 255);
  box-shadow: 0px 0px 10px 2px rgba(113, 113, 113, 0.25);
  border-radius: 24px;
  width: 1090px;
}
