import React, { useState, useEffect } from "react";
import { Row, Col, Select, Typography } from "antd";

import { dynamoGet } from "../../../service/apiDsmDynamo";
import { setTechnicalProposalState } from "../../../store/actions/technical-proposal-action";
import { shallowEqual, useSelector } from "react-redux";
import { setTechnicalProposalCustomerState } from "../../../store/actions/technical-proposal-action";
import { getCustomerNameOption } from "../../../utils/getCustomerNameOption";
import * as customerController from "../../../controllers/clients/clientsController";
const { Title } = Typography;
const { Option } = Select;

export const CustomerField = () => {
  const clientName = useSelector(
    (state) => state.technicalProposal.clientName,
    shallowEqual
  );

  const [allCustomers, setAllCustomers] = useState([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    getCustomers();
  }, []);

  const getCustomers = async () => {
    try {
      setLoading(true);
      const customerRoute = "customers/read/status";
      const tableCustomers = await customerController.getCustomerByParameter(
        customerRoute,
        { status: 1 }
      );

      setAllCustomers(tableCustomers);
      setLoading(false);
    } catch (error) {
      setLoading(false);
    }
  };

  const handleSelectCustomer = (value) => {
    const customer = allCustomers.find((c) => c?.names?.fantasy_name === value);
    const customerName = customer.names.fantasy_name;
    setTechnicalProposalCustomerState({
      clientName: customerName,
      selectedCustomer: customer,
    });
  };

  return (
    <Col sm={24} md={24} lg={12}>
      <Row>
        <Title level={5} style={{ fontWeight: "400" }}>
          Cliente
        </Title>
      </Row>
      <Row>
        <Col span={23}>
          <Select
            id="clientName"
            loading={loading}
            showSearch
            placeholder={"Nome fantasia do cliente"}
            optionFilterProp="children"
            value={clientName}
            filterOption={(input, option) =>
              option?.children
                ?.toLowerCase()
                .startsWith(input?.toLowerCase()) ||
              option?.children?.toLowerCase().includes(input?.toLowerCase())
            }
            filterSort={(optionA, optionB) =>
              optionA?.children
                ?.toLowerCase()
                .localeCompare(optionB?.children?.toLowerCase())
            }
            onSelect={(e) => {
              handleSelectCustomer(e);
              setTechnicalProposalState({
                field: "clientName",
                value: e,
              });
            }}
            style={{ width: "100%" }}
          >
            {allCustomers?.map((c, key) => {
              const customerName = getCustomerNameOption(c);
              if (customerName === "") {
                return (
                  <Option key={key} value={"N/A"}>
                    {"N/A"}
                  </Option>
                );
              } else {
                return (
                  <Option key={key} value={customerName}>
                    {customerName}
                  </Option>
                );
              }
            })}
          </Select>
        </Col>
      </Row>
    </Col>
  );
};
