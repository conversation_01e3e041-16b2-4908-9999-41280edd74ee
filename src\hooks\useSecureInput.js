import { useState, useCallback, useMemo } from 'react';
import { useSecurity } from '../components/SecurityProvider';

/**
 * Secure Input Hook
 * Provides input validation, sanitization, and security checks
 */
export const useSecureInput = (initialValue = '', options = {}) => {
  const {
    maxLength = 1000,
    minLength = 0,
    pattern = null,
    required = false,
    sanitize = true,
    validateOnChange = true,
    customValidators = [],
    allowedTags = [],
    blockedPatterns = [],
  } = options;

  const { sanitizeInput, reportSecurityViolation } = useSecurity();
  const [value, setValue] = useState(initialValue);
  const [errors, setErrors] = useState([]);
  const [warnings, setWarnings] = useState([]);
  const [isValid, setIsValid] = useState(true);

  /**
   * Validate input value
   */
  const validateValue = useCallback((inputValue) => {
    const validationErrors = [];
    const validationWarnings = [];

    // Required validation
    if (required && (!inputValue || inputValue.trim() === '')) {
      validationErrors.push('Este campo é obrigatório');
    }

    // Length validation
    if (inputValue && inputValue.length < minLength) {
      validationErrors.push(`Mínimo de ${minLength} caracteres`);
    }

    if (inputValue && inputValue.length > maxLength) {
      validationErrors.push(`Máximo de ${maxLength} caracteres`);
    }

    // Pattern validation
    if (pattern && inputValue && !pattern.test(inputValue)) {
      validationErrors.push('Formato inválido');
    }

    // Security validations
    const securityCheck = performSecurityCheck(inputValue);
    if (!securityCheck.isSecure) {
      validationErrors.push(...securityCheck.errors);
      validationWarnings.push(...securityCheck.warnings);
    }

    // Custom validators
    customValidators.forEach(validator => {
      const result = validator(inputValue);
      if (result !== true) {
        if (typeof result === 'string') {
          validationErrors.push(result);
        } else if (Array.isArray(result)) {
          validationErrors.push(...result);
        }
      }
    });

    return {
      errors: validationErrors,
      warnings: validationWarnings,
      isValid: validationErrors.length === 0,
    };
  }, [required, minLength, maxLength, pattern, customValidators]);

  /**
   * Perform security checks on input
   */
  const performSecurityCheck = useCallback((inputValue) => {
    const errors = [];
    const warnings = [];

    if (!inputValue) {
      return { isSecure: true, errors, warnings };
    }

    // Check for script injection attempts
    const scriptPatterns = [
      /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
      /javascript:/gi,
      /on\w+\s*=/gi,
      /eval\s*\(/gi,
      /document\.write/gi,
      /innerHTML/gi,
      /outerHTML/gi,
    ];

    scriptPatterns.forEach(pattern => {
      if (pattern.test(inputValue)) {
        errors.push('Conteúdo potencialmente perigoso detectado');
        reportSecurityViolation('suspicious_input', {
          pattern: pattern.toString(),
          value: inputValue.substring(0, 100), // Only log first 100 chars
          location: window.location.href,
        });
      }
    });

    // Check for SQL injection attempts
    const sqlPatterns = [
      /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)/gi,
      /(--|\/\*|\*\/|;)/g,
      /(\b(OR|AND)\b.*=.*)/gi,
    ];

    sqlPatterns.forEach(pattern => {
      if (pattern.test(inputValue)) {
        warnings.push('Possível tentativa de injeção SQL detectada');
      }
    });

    // Check for blocked patterns
    blockedPatterns.forEach(pattern => {
      if (pattern.test(inputValue)) {
        errors.push('Conteúdo não permitido');
      }
    });

    // Check for excessive HTML tags
    const htmlTagCount = (inputValue.match(/<[^>]*>/g) || []).length;
    if (htmlTagCount > 5) {
      warnings.push('Muitas tags HTML detectadas');
    }

    // Check for allowed tags only
    if (allowedTags.length > 0) {
      const htmlTags = inputValue.match(/<(\w+)[^>]*>/g) || [];
      const invalidTags = htmlTags.filter(tag => {
        const tagName = tag.match(/<(\w+)/)[1].toLowerCase();
        return !allowedTags.includes(tagName);
      });

      if (invalidTags.length > 0) {
        errors.push(`Tags não permitidas: ${invalidTags.join(', ')}`);
      }
    }

    return {
      isSecure: errors.length === 0,
      errors,
      warnings,
    };
  }, [blockedPatterns, allowedTags, reportSecurityViolation]);

  /**
   * Handle input change
   */
  const handleChange = useCallback((newValue) => {
    let processedValue = newValue;

    // Sanitize input if enabled
    if (sanitize) {
      processedValue = sanitizeInput(processedValue);
    }

    setValue(processedValue);

    // Validate on change if enabled
    if (validateOnChange) {
      const validation = validateValue(processedValue);
      setErrors(validation.errors);
      setWarnings(validation.warnings);
      setIsValid(validation.isValid);
    }
  }, [sanitize, sanitizeInput, validateOnChange, validateValue]);

  /**
   * Manual validation trigger
   */
  const validate = useCallback(() => {
    const validation = validateValue(value);
    setErrors(validation.errors);
    setWarnings(validation.warnings);
    setIsValid(validation.isValid);
    return validation;
  }, [value, validateValue]);

  /**
   * Reset input state
   */
  const reset = useCallback(() => {
    setValue(initialValue);
    setErrors([]);
    setWarnings([]);
    setIsValid(true);
  }, [initialValue]);

  /**
   * Clear errors and warnings
   */
  const clearValidation = useCallback(() => {
    setErrors([]);
    setWarnings([]);
    setIsValid(true);
  }, []);

  /**
   * Get sanitized value for submission
   */
  const getSanitizedValue = useCallback(() => {
    return sanitizeInput(value);
  }, [value, sanitizeInput]);

  /**
   * Input props for form components
   */
  const inputProps = useMemo(() => ({
    value,
    onChange: (e) => {
      const newValue = e.target ? e.target.value : e;
      handleChange(newValue);
    },
    onBlur: validate,
    status: errors.length > 0 ? 'error' : warnings.length > 0 ? 'warning' : '',
  }), [value, handleChange, validate, errors.length, warnings.length]);

  /**
   * Form item props for Ant Design
   */
  const formItemProps = useMemo(() => ({
    validateStatus: errors.length > 0 ? 'error' : warnings.length > 0 ? 'warning' : 'success',
    help: errors.length > 0 ? errors[0] : warnings.length > 0 ? warnings[0] : '',
    hasFeedback: true,
  }), [errors, warnings]);

  return {
    value,
    setValue: handleChange,
    errors,
    warnings,
    isValid,
    validate,
    reset,
    clearValidation,
    getSanitizedValue,
    inputProps,
    formItemProps,
  };
};

/**
 * Secure Form Hook
 * Manages multiple secure inputs in a form
 */
export const useSecureForm = (initialValues = {}, fieldOptions = {}) => {
  const [fields, setFields] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Initialize fields
  useState(() => {
    const initialFields = {};
    Object.keys(initialValues).forEach(key => {
      initialFields[key] = useSecureInput(
        initialValues[key],
        fieldOptions[key] || {}
      );
    });
    setFields(initialFields);
  }, []);

  /**
   * Validate all fields
   */
  const validateAll = useCallback(() => {
    const validations = {};
    let isFormValid = true;

    Object.keys(fields).forEach(key => {
      const validation = fields[key].validate();
      validations[key] = validation;
      if (!validation.isValid) {
        isFormValid = false;
      }
    });

    return {
      isValid: isFormValid,
      validations,
    };
  }, [fields]);

  /**
   * Get all field values
   */
  const getValues = useCallback(() => {
    const values = {};
    Object.keys(fields).forEach(key => {
      values[key] = fields[key].getSanitizedValue();
    });
    return values;
  }, [fields]);

  /**
   * Reset all fields
   */
  const resetForm = useCallback(() => {
    Object.keys(fields).forEach(key => {
      fields[key].reset();
    });
  }, [fields]);

  /**
   * Submit form with security checks
   */
  const submitForm = useCallback(async (onSubmit) => {
    setIsSubmitting(true);
    
    try {
      const validation = validateAll();
      
      if (!validation.isValid) {
        throw new Error('Formulário contém erros de validação');
      }

      const values = getValues();
      await onSubmit(values);
      
    } catch (error) {
      throw error;
    } finally {
      setIsSubmitting(false);
    }
  }, [validateAll, getValues]);

  return {
    fields,
    validateAll,
    getValues,
    resetForm,
    submitForm,
    isSubmitting,
  };
};

export default useSecureInput;
