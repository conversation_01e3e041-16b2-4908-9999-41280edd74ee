import { useEffect, useState } from "react";
import { Link, useNavigate, useLocation } from "react-router-dom";
import { SideMenu } from "../../components/SideMenu";
import { HeaderMenu } from "../../components/HeaderMenu";
import { ArrowLeftOutlined, CaretRightOutlined } from "@ant-design/icons";
import {
  Layout,
  Card,
  Row,
  Col,
  Button,
  Table,
  Space,
  Collapse,
  Typography,
  message,
} from "antd";
import { EditTable } from "../../components/Modals/Tickets/EditTable";
import { differenceInCalendarDays, format, set } from "date-fns";
import { dynamoGetById } from "../../service/apiDsmDynamo";
import { otrsGet, otrsPost } from "../../service/apiOtrs";
import { ViewBody } from "../../components/Modals/TicketsAnswer/ViewBody";

const { Content } = Layout;
const { Panel } = Collapse;

export const ArticlesTickets = () => {
  const Navigate = useNavigate();
  const { state } = useLocation();
  const { Title } = Typography;
  const [collapsed, setCollapsed] = useState(false);
  const [loadingContracts, setLoadingContracts] = useState(false);
  const [loadingArticles, setLoadingArticles] = useState(false);
  const [stateColumns, setStateColumns] = useState([]);
  const [articles, setArticles] = useState([]);
  const [ticketContracts, setTicketContracts] = useState([]);
  const [ticketDraftInfo, setTicketDraftInfo] = useState({});
  const allTablesInfo = [
    {
      title: "main_table",
      name: "main_table",
      columns: [
        {
          title: "Número",
          dataIndex: "number",
          key: "number",
          align: "center",
        },
        {
          title: "Remetente",
          dataIndex: "sender",
          key: "sender",
          align: "center",
        },
        {
          title: "Fila",
          dataIndex: "via",
          key: "via",
          align: "center",
        },
        {
          title: "Assunto",
          dataIndex: "subject",
          key: "subject",
        },
        {
          title: "Data de criação",
          dataIndex: "create_date",
          key: "create_date",
          align: "center",
        },
      ],
      data: [
        {
          number: state?.ticket_number,
          sender: state?.user_name,
          via: state?.queue.split("::").pop(),
          subject: state?.subject,
          create_date: format(
            new Date(state?.ticket_create_time),
            "dd/MM/yyyy HH:mm"
          ),
        },
      ],
    },
    {
      title: "Informações do chamado",
      name: "ticketInfo",
      columnCodes: [
        "type",
        "age",
        "create",
        "state",
        "priority",
        "queue",
        "service",
        "update_time",
        "owner",
      ],
      columns: [
        {
          code: "type",
          title: "Tipo",
          dataIndex: "type",
          key: "type",
          align: "center",
          render: (text) => {return <span>{text !== null ? text : "default" }</span>},
        },
        {
          code: "age",
          title: "Idade",
          dataIndex: "age",
          key: "age",
        },
        {
          code: "create",
          title: "Criado",
          dataIndex: "create",
          key: "create",
          align: "center",
        },
        {
          code: "state",
          title: "Estado",
          dataIndex: "state",
          key: "state",
          align: "center",
        },
        {
          code: "priority",
          title: "Prioridade",
          dataIndex: "priority",
          key: "priority",
          align: "center",
        },
        {
          code: "service",
          title: "Serviço",
          dataIndex: "service",
          key: "service",
          align: "center",
        },
        // {
        //   code: "update_time",
        //   title: "Prazo de atualização",
        //   dataIndex: "deadline",
        //   key: "deadline",
        // },
        {
          code: "owner",
          title: "Proprietário",
          dataIndex: "owner",
          key: "owner",
          align: "center",
        },
      ],
      data: [
        {
          type: state?.type,
          age:
            differenceInCalendarDays(
              new Date(),
              new Date(state?.ticket_create_time)
            ) >= 1
              ? differenceInCalendarDays(
                  new Date(),
                  new Date(state?.ticket_create_time)
                ) + " dias"
              : differenceInCalendarDays(
                  new Date(),
                  new Date(state?.ticket_create_time)
                ) + " horas",
          create: format(
            new Date(state?.ticket_create_time),
            "dd/MM/yyyy HH:mm"
          ),
          state: state?.ticket_state,
          priority: state?.ticket_priority_name,
          queue: state?.queue,
          service: state?.service,
          // deadline: "teste",
          owner: state?.user_name,
        },
      ],
    },
    {
      title: "Consumo de horas totais por contrato",
      name: "hourContract",
      columnCodes: ["contract", "porcentage"],
      columns: [
        {
          code: "contract",
          title: "Contrato",
          dataIndex: "contract",
          key: "contract",
        },
        {
          code: "porcentage",
          title: "Porcentagem utilizada",
          dataIndex: "porcentage",
          key: "porcentage",
          align: "center",
          width: "50%",
        },
      ],
      data: ticketContracts.map((contract) => {
        return {
          contract: contract.contract,
          porcentage: contract.porcentage,
        };
      }),
    },
    {
      title: "Consumo de horas no chamado",
      name: "hoursTicket",
      columnCodes: ["contract", "consumption"],
      columns: [
        {
          code: "contract",
          title: "Contrato",
          dataIndex: "contract",
          key: "contract",
        },

        {
          code: "consumption",
          title: "Consumo (em minutos)",
          dataIndex: "consumption",
          key: "consumption",
          align: "center",
          width: "50%",
        },
      ],
      data: ticketContracts.map((contract) => {
        return {
          contract: contract.contract,
          consumption: contract.consumption,
        };
      }),
    },
    {
      title: "Informações do cliente",
      name: "clientInfo",
      columnCodes: ["name", "contract_name", "user", "email"],
      columns: [
        {
          code: "name",
          title: "Nome",
          dataIndex: "name",
          key: "name",
        },
        {
          code: "contract_name",
          title: "Nome do contrato",
          dataIndex: "contract_name",
          key: "contract_name",
        },
        {
          code: "user",
          title: "Usuário",
          dataIndex: "user",
          key: "user",
        },
        {
          code: "email",
          title: "E-mail",
          dataIndex: "email",
          key: "email",
        },
      ],
      data: [
        {
          name: state?.client_name,
          contract_name: state?.contract_name,
          user: state?.client_name,
          email: state?.ticket_customer_user_id,
        },
      ],
    },
  ];
  useEffect(() => {
    let isMounted = true;

    const loadData = async () => {
      try {
        if (!isMounted) return;

        setLoadingArticles(true);

        // Load articles and ticket data in parallel
        const [articlesResponse, ticketData] = await Promise.allSettled([
          otrsGet(`read/articles/${state.ticket_number}`),
          dynamoGetById(`${process.env.REACT_APP_STAGE}-tickets`, state.ticket_id)
        ]);

        if (!isMounted) return;

        // Handle articles response
        if (articlesResponse.status === 'fulfilled') {
          setArticles(articlesResponse.value.data);
        } else {
          console.error('Error loading articles:', articlesResponse.reason);
        }

        // Handle ticket response
        if (ticketData.status === 'fulfilled') {
          setTicketDraftInfo(ticketData.value || null);
        } else {
          console.error('Error loading ticket:', ticketData.reason);
          message.error("Erro ao carregar informações do ticket");
        }

        setLoadingArticles(false);

        // Set localStorage items
        localStorage.setItem("@ticket-number", state.ticket_number);
        localStorage.setItem("@ticket-client", state.client_name);
        localStorage.setItem("@ticket-client-id", state.customer_id);

      } catch (error) {
        if (isMounted) {
          console.error('Error in loadData:', error);
          setLoadingArticles(false);
          message.error("Erro ao carregar dados do ticket");
        }
      }
    };

    loadData();

    return () => {
      isMounted = false;
    };
  }, [state.ticket_id, state.ticket_number, state.client_name, state.customer_id]);

  const articlesColumns = [
    {
      code: "from",
      title: "De",
      dataIndex: "from",
      key: "from",
    },
    {
      code: "create_time",
      title: "Criado em",
      dataIndex: "create_time",
      key: "create_time",
      align: "center",
      render: (id, item) =>
        format(new Date(item.create_time), "dd/MM/yyyy HH:mm"),
    },
    {
      code: "subject",
      title: "Assunto",
      dataIndex: "subject",
      key: "subject",
      align: "center",
    },
    {
      code: "body",
      title: "Corpo do artigo",
      dataIndex: "body",
      key: "body",
      align: "center",
      render: (id, item) => <ViewBody body={item.body} />,
    },
  ];

  useEffect(() => {
    let consumedByContract = [];
    let contractsData = [];

    localStorage.setItem("@ticket/data", JSON.stringify(state));
    async function getConsumedHours(contractIds) {
      const arrayReturn = [];

      const pagIndex = 5;

      for (let i = 0; i < contractIds.length / pagIndex; i++) {
        const items = contractIds.slice(i * pagIndex, (i + 1) * pagIndex);

        const { data } = await otrsPost(`read/hours/contract/new/rule`, {
          contractsIds: items,
          ticketId: state.ticket_id,
        });

        arrayReturn.push(...data);
      }
      arrayReturn.map((item) => {
      consumedByContract.push({
        id: Object.keys(item)[0],
        total: Object.values(item)[0].ConsumoTotal,
        consumedMin: Object.values(item)[0].ConsumoNoTicket
          ? Object.values(item)[0].ConsumoNoTicket
          : 0,
      })});
      contractsData.map((item, key) => {
        consumedByContract.map((consumed) => {
          if (
            item.id === Number(consumed.id) &&
            key < contractsData.length
          ) {
            setTicketContracts((prevState) => [
              ...prevState,
              {
                id: item.id,
                contract: item.name,
                consumption: consumed.consumedMin,
                porcentage: consumed.total,
              },
            ]);
          }
        });
      });
      setLoadingContracts(false);
      // .then((res) =>
      //   res.data.map((item) => {
      //   })
      // )
      // .finally(() => {
      // });
    }
    async function getContractsByCustomer() {
      let contractIds = [];
      setLoadingContracts(true);
      let res = await otrsGet(`read/contract/customer/${state?.customer_id}`)
        .then((res) => {
          Object.values(res.data).map((item) => {
            contractIds.push(item.id);
            console.log(item);
            if(item.total_hours > 0){
              return contractsData.push({
                id: item.id,
                name: item.name,
              });
            }
          });
        })
        .finally(() => getConsumedHours(contractIds));
    }
    getContractsByCustomer();
  }, []);

  return (
    <Layout style={{ minHeight: "100vh" }}>
      <HeaderMenu collapsed={collapsed} setCollapsed={setCollapsed} />
      <Layout>
        <SideMenu collapsed={collapsed} />
        <Content style={{ padding: "2em" }}>
          <Card
            style={{
              boxShadow: "0 0 10px rgba(0,0,0,0.1)",
              borderRadius: "20px",
              marginRight: "10px",
            }}
          >
            <Row justify="space-between">
              <Col
                style={{
                  marginBottom: "1em",
                }}
              >
                <Space>
                  <Button type="text" onClick={() => Navigate(-1)}>
                    <ArrowLeftOutlined />
                  </Button>
                </Space>
              </Col>
              <Col>
                <Link
                  to={`/ticket-answer`}
                  state={
                    ticketDraftInfo !== null ? ticketDraftInfo : state.ticket_id
                  }
                >
                  <Button type="primary">Responder Ticket</Button>
                </Link>
              </Col>
            </Row>
            {allTablesInfo.map((currentTable, currentTableKey) => {
              return currentTableKey === 0 ? (
                <Row style={{ marginBottom: "25px" }}>
                  <Col span={24}>
                    <Table
                      scroll={{ x: "100%" }}
                      pagination={false}
                      dataSource={currentTable.data}
                      columns={currentTable.columns}
                    />
                  </Col>
                </Row>
              ) : (
                <Row>
                  <Col span={24}>
                    <Collapse
                      expandIcon={({ isActive }) => (
                        <CaretRightOutlined
                          style={{ color: "#fff" }}
                          rotate={isActive ? 90 : 0}
                        />
                      )}
                      style={{
                        backgroundColor: "#0F9347",
                        color: "#fff",
                        borderRadius: "5px",
                        marginBottom: "20px",
                      }}
                      className="site-collapse-custom-collapse"
                    >
                      <Panel
                        color="#fff"
                        extra={
                          <EditTable
                            stateColumns={currentTable.columns}
                            setStateColumns={(stateColumnsParams) =>
                              setStateColumns(
                                (stateColumns[currentTable.name] =
                                  stateColumnsParams)
                              )
                            }
                          />
                        }
                        ghost
                        header={
                          <p
                            style={{
                              color: "#fff",
                              margin: "0px",
                              padding: "0px",
                            }}
                          >
                            {currentTable.title}
                          </p>
                        }
                        key={currentTableKey}
                      >
                        <Table
                          loading={
                            currentTable.title.includes("Consumo")
                              ? loadingContracts
                              : false
                          }
                          pagination={false}
                          scroll={{ x: "100%" }}
                          dataSource={currentTable.data}
                          columns={
                            stateColumns.length === 0 ||
                            !stateColumns
                              .map((column) =>
                                currentTable.columnCodes.find(
                                  (curr) => curr === column.code
                                )
                              )
                              .every((i) => i !== undefined)
                              ? currentTable.columns
                              : stateColumns
                          }
                        />
                      </Panel>
                    </Collapse>
                  </Col>
                </Row>
              );
            })}
            <Title level={5} style={{ fontWeight: "400" }}>
              Artigos
            </Title>
            <Table
              loading={loadingArticles}
              scroll={{ x: "100%" }}
              dataSource={Object.values(articles)}
              columns={articlesColumns}
            ></Table>
          </Card>
        </Content>
      </Layout>
    </Layout>
  );
};
