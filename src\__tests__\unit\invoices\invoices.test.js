import React from "react";
import {
  filterByState,
  filterBySearch,
  formatDateRange,
  calculateInvoiceTotals,
  calculateDiscounts,
  calculateTotals,
  calculateTaxPercentage,
  groupInvoiceByInvoiceId,
  groupInvoiceByAccount,
} from "../../../controllers/invoices/invoiceController";
import { render, fireEvent } from "@testing-library/react";
import { UploadModal } from "../../../components/Modals/Invoices/UploadModal";
import moment from "moment";
import { getMonth } from "date-fns";
import {
  inputInvoicesMockedData,
  outputGroupInvoiceByAccount,
  outputGroupInvoiceByInvoiceId,
} from "../../../__mocks__/invoices/data";

describe("filter by state", () => {
  let data = [
    { unblended_cost: 1 },
    { unblended_cost: 2 },
    { unblended_cost: -3 },
    { unblended_cost: -4 },
  ];
  it("should filter all positive values", () => {
    let filteredData = filterByState(data, "saldoPos");
    expect(filteredData).toStrictEqual([
      { unblended_cost: 1 },
      { unblended_cost: 2 },
    ]);
  });
  it("should filter all negative values", () => {
    let filteredData = filterByState(data, "saldoNeg");
    expect(filteredData).toStrictEqual([
      { unblended_cost: -3 },
      { unblended_cost: -4 },
    ]);
  });
  it("should filter all values", () => {
    let filteredData = filterByState(data, "todos");
    expect(filteredData).toStrictEqual([
      { unblended_cost: 1 },
      { unblended_cost: 2 },
      { unblended_cost: -3 },
      { unblended_cost: -4 },
    ]);
  });
});

describe("filterBySearch", () => {
  it("should filter the data based on the search term", () => {
    const data = [
      {
        customer: "John Doe",
        line_item_usage_account_id: "123456",
        bill_payer_account_id: "789012",
        unblended_cost: "100.00",
        month: "January",
        bill_invoice_id: "ABC123",
        bill_billing_entity: "Entity A",
        line_item_legal_entity: "Legal Entity A",
        conversion: "true",
      },
      {
        customer: "Jane Smith",
        line_item_usage_account_id: "654321",
        bill_payer_account_id: "210987",
        unblended_cost: "50.00",
        month: "February",
        bill_invoice_id: "DEF456",
        bill_billing_entity: "Entity B",
        line_item_legal_entity: "Legal Entity B",
        conversion: "false",
      },
    ];

    const search = "Doe";

    const expectedFilteredData = [
      {
        customer: "John Doe",
        line_item_usage_account_id: "123456",
        bill_payer_account_id: "789012",
        unblended_cost: "100.00",
        month: "January",
        bill_invoice_id: "ABC123",
        bill_billing_entity: "Entity A",
        line_item_legal_entity: "Legal Entity A",
        conversion: "true",
      },
    ];

    const filteredData = filterBySearch(data, search);

    expect(filteredData).toEqual(expectedFilteredData);
  });

  it("should return an empty array if the search term does not match any data", () => {
    const data = [
      {
        customer: "John Doe",
        line_item_usage_account_id: "123456",
        bill_payer_account_id: "789012",
        unblended_cost: "100.00",
        month: "January",
        bill_invoice_id: "ABC123",
        bill_billing_entity: "Entity A",
        line_item_legal_entity: "Legal Entity A",
        conversion: "true",
      },
    ];

    const search = "Smith";

    const filteredData = filterBySearch(data, search);

    expect(filteredData).toEqual([]);
  });

  it("should return an empty array if the data is empty", () => {
    const data = [];
    const search = "Doe";

    const filteredData = filterBySearch(data, search);

    expect(filteredData).toEqual([]);
  });
});

describe("Upload modal toggle", () => {
  it("should open the modal", () => {
    const { getByTestId } = render(<UploadModal />);
    const button = getByTestId("upload-button");
    fireEvent.click(button);
    expect(getByTestId("upload-modal")).toBeInTheDocument();
  });
});

describe("formatDateRange", () => {
  it("should return a formatted date range when given a valid date range object", () => {
    const dateRange = { year: 2022, month: 10 };
    const formattedDateRange = formatDateRange(dateRange);
    expect(formattedDateRange.format("YYYY-MM-DD")).toBe("2022-10-01");
  });

  it("should subtract one month from the formatted date range if the month is the current month or later", () => {
    const currentMonth = getMonth(new Date()) + 1;
    const dateRange = { year: new Date().getFullYear(), month: currentMonth };
    const formattedDateRange = formatDateRange(dateRange);
    const expectedDate = moment(
      new Date(new Date().getFullYear(), getMonth(new Date()), 1, 1)
    ).format("YYYY-MM-DD");
    expect(formattedDateRange.format("YYYY-MM-DD")).toBe(expectedDate);
  });

  it("should throw an error if no date range object is provided", () => {
    expect(() => {
      formatDateRange();
    }).toThrow("Date range is required");
  });

  it("should throw an error if null date range is provided", () => {
    expect(() => {
      formatDateRange(null);
    }).toThrow("Date range is required");
  });

  it("should handle edge case with December month", () => {
    const dateRange = { year: 2022, month: 12 };
    const formattedDateRange = formatDateRange(dateRange);
    expect(formattedDateRange.format("YYYY-MM")).toBe("2022-12");
  });

  it("should handle edge case with January month", () => {
    const dateRange = { year: 2023, month: 1 };
    const formattedDateRange = formatDateRange(dateRange);
    expect(formattedDateRange.format("YYYY-MM")).toBe("2023-01");
  });
});

describe("calculateInvoiceTotals", () => {
  it("should calculate totals correctly", () => {
    const newList = [
      {
        spp_discount: 10,
        total_credits: 100,
        total_discount: 5,
        total_edp_discount: 8,
        total_bundled_discount: 15,
        total_saving_plans_discount: 20,
        final_balance: 150,
        unblended_cost: 120,
        tax: 8.5,
        charges: 200,
      },
    ];
    const invoiceIndex = 0;
    const invoice = {
      spp_discount: 5,
      total_credits: 50,
      total_discount: 2.5,
      total_edp_discount: 4,
      total_bundled_discount: 7.5,
      total_saving_plans_discount: 10,
      final_balance: 75,
      unblended_cost: 60,
      tax: 4.25,
      charges: 100,
    };

    calculateInvoiceTotals(newList, invoiceIndex, invoice);

    expect(newList[invoiceIndex].spp_discount).toBe(15);
    expect(newList[invoiceIndex].total_credits).toBe(150);
    expect(newList[invoiceIndex].total_discount).toBe(7.5);
  });
});

describe("calculateDiscounts", () => {
  it("should calculate discounts correctly", () => {
    const existingDiscount = 10;
    const newDiscount = 5;

    const result = calculateDiscounts(existingDiscount, newDiscount);

    expect(result).toBe(15);
  });

  it("should handle zero values", () => {
    const result = calculateDiscounts(0, 0);
    expect(result).toBe(0);
  });

  it("should handle negative values", () => {
    const result = calculateDiscounts(-10, 5);
    expect(result).toBe(-5);
  });

  it("should handle string numbers", () => {
    const result = calculateDiscounts("10.5", "5.25");
    expect(result).toBe(15.75);
  });

  it("should handle decimal precision", () => {
    const result = calculateDiscounts(10.123, 5.456);
    expect(result).toBeCloseTo(15.579, 3);
  });
});

describe("calculateTotals", () => {
  it("should calculate totals correctly", () => {
    const existingTotal = 100;
    const newAmount = 50;

    const result = calculateTotals(existingTotal, newAmount);

    expect(result).toBe(150);
  });

  it("should handle zero values", () => {
    const result = calculateTotals(0, 0);
    expect(result).toBe(0);
  });

  it("should handle negative amounts", () => {
    const result = calculateTotals(100, -30);
    expect(result).toBe(70);
  });

  it("should handle string numbers", () => {
    const result = calculateTotals("100.50", "25.25");
    expect(result).toBe(125.75);
  });

  it("should handle large numbers", () => {
    const result = calculateTotals(999999.99, 0.01);
    expect(result).toBe(1000000);
  });
});

describe("calculateTaxPercentage", () => {
  it("should calculate the tax percentage correctly", () => {
    const tax = 8.5;
    const charges = 200;
    const discountsTotal = 15;

    const result = calculateTaxPercentage(tax, charges, discountsTotal);

    expect(result).toBe("3.95%");
  });

  it("should return 0.00% when tax is zero", () => {
    const tax = 0;
    const charges = 200;
    const discountsTotal = 15;

    const result = calculateTaxPercentage(tax, charges, discountsTotal);

    expect(result).toBe("0.00%");
  });

  it("should handle zero charges", () => {
    const tax = 10;
    const charges = 0;
    const discountsTotal = 0;

    const result = calculateTaxPercentage(tax, charges, discountsTotal);

    expect(result).toBe("Infinity%");
  });

  it("should handle high discount values", () => {
    const tax = 10;
    const charges = 100;
    const discountsTotal = 50;

    const result = calculateTaxPercentage(tax, charges, discountsTotal);

    expect(result).toBe("6.67%");
  });

  it("should format percentage with two decimal places", () => {
    const tax = 7.333;
    const charges = 150;
    const discountsTotal = 10;

    const result = calculateTaxPercentage(tax, charges, discountsTotal);

    expect(result).toMatch(/^\d+\.\d{2}%$/);
  });
});

describe("groupInvoiceByInvoiceId", () => {
  it("should group invoices correctly by invoice ID", () => {
    const invoices = inputInvoicesMockedData;
    const dolar = 5.3;

    const result = groupInvoiceByInvoiceId(invoices, dolar);

    expect(result).toEqual(outputGroupInvoiceByInvoiceId);
  });
});

describe("groupInvoiceByAccount", () => {
  it("should group invoices correctly by account", () => {
    const invoices = inputInvoicesMockedData;
    const dolar = 5.3;

    const result = groupInvoiceByAccount(invoices, dolar);

    expect(result).toEqual(outputGroupInvoiceByAccount);
  });
});
