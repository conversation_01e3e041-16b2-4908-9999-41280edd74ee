import styleSheet from './style.module.scss'
import { useContext } from 'react'
import { DeleteOutlined, PlusOutlined } from '@ant-design/icons'
import { Tag, Popconfirm } from 'antd'

import { EmailAutomationContext } from '../../../contexts/email-automation'
import { EmailAutomationReducerType } from '../../../contexts/reducers/email-automation-reducer'

import { generateEmptyCollumn, RowItem } from './row-item'

export const EmailTable = ({ items, index }) => {
    const { emailAutomationState, setEmailAutomationState } = useContext(EmailAutomationContext)
    const { templateList } = emailAutomationState

    function addRowTable() {
        const newTemplateListStr = JSON.stringify(templateList)
        const newTemplateList = JSON.parse(newTemplateListStr)
        const table = newTemplateList[index]
        const newRow = generateEmptyRow()

        table.value.push(newRow)
        newTemplateList[index] = table

        setEmailAutomationState({
            type: EmailAutomationReducerType.SET_TEMPLATE_LIST,
            value: newTemplateList
        })

    }

    function generateEmptyRow() {
        const newCollumn = generateEmptyCollumn()

        let newRow = {
            collumns: [newCollumn]
        }

        return newRow
    }

    async function removeLastRowTable() {
        const newTemplateListStr = JSON.stringify(templateList)
        const newTemplateList = JSON.parse(newTemplateListStr)

        const table = newTemplateList[index]
        if (table.value.length === 1) {
            deleteTable()
            return
        }

        table.value.pop()

        newTemplateList[index] = table
        setEmailAutomationState({
            type: EmailAutomationReducerType.SET_TEMPLATE_LIST,
            value: newTemplateList
        })
    }

    function deleteTable() {
        const newTemplateListStr = JSON.stringify(templateList)
        let newTemplateList = JSON.parse(newTemplateListStr)

        newTemplateList = newTemplateList.filter((template, indexPosition) => indexPosition !== index)

        setEmailAutomationState({
            type: EmailAutomationReducerType.SET_TEMPLATE_LIST,
            value: newTemplateList
        })
    }

    return (
        <>
            <div className={styleSheet['table-container']}>
                <table
                    border={0}
                    cellPadding="0"
                    cellSpacing="0"
                    width="100%"
                    style={{
                        backgroundClip: '#FFF',
                        marginTop: 20
                    }}
                >
                    <tbody>
                        {
                            items.length > 0 && items.map((row, key) =>
                                <RowItem
                                    row={row}
                                    key={key}
                                    indexTable={index}
                                    indexRow={key}
                                />
                            )
                        }
                    </tbody>
                </table>

                <div className={styleSheet['table-row-options-container']} style={{ display: 'none' }}>
                    <Tag color="green" onClick={addRowTable}>
                        <PlusOutlined />
                    </Tag>

                    {
                        templateList[index].value.length === 1 ?
                            <Popconfirm
                                placement="leftBottom"
                                title="Tem certeza que deseja remover este item?"
                                style={{
                                    backgroundColor: 'transparent',
                                    border: 'none',
                                    cursor: 'pointer',
                                    color: 'black'
                                }}
                                onConfirm={removeLastRowTable}
                            >
                                <Tag color="red">
                                    <DeleteOutlined />
                                </Tag>
                            </Popconfirm>
                            :
                            <Tag color="red" onClick={removeLastRowTable}>
                                <DeleteOutlined />
                            </Tag>
                    }
                    
                </div>
            </div>
        </>
    )
}