import { setConsumptionStateReduce } from "../reducers/consumption-hours-reducer";
import {
  setConsumptionSearchStateReduce,
  setUpdatedHoursContractsReduce,
} from "../reducers/consumption-hours-reducer";
import { store } from "../store";

export const setConsumptionState = ({ field, value }) => {
  store.dispatch(setConsumptionStateReduce({ field, value }));
};

export const setConsumptionSearchState = ({ contracts, pageSelected }) => {
  store.dispatch(
    setConsumptionSearchStateReduce({
      contracts,
      pageSelected,
    })
  );
};

export const setUpdatedHoursContractsState = ({ updatedHoursContracts }) => {
  store.dispatch(setUpdatedHoursContractsReduce({ updatedHoursContracts }));
};
