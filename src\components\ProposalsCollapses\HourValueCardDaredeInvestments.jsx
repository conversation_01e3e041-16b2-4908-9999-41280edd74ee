import {
  Row,
  Col,
  Select,
  Typography,
  Input,
  Tooltip,
  Card,
  Space,
  Form,
} from "antd";
import { useEffect, useState, useContext } from "react";
import { invalidInputNumberChars } from "../../constants/invalidCharsInputNumber";
import { formatHourName } from "../../controllers/Proposals/formatHourName";
import { ProposalContext } from "../../contexts/totalValuesProposals";
import { TotalValuesProposalType } from "../../contexts/reducers/totalValuesProposal";
import { totalValuesController } from "../../controllers/Proposals/totalValuesTechnical";

async function updateProperty(
  prop,
  value,
  totalValuesState,
  hourClass,
  month,
  formName
) {
  let newObjTotalValues = [...totalValuesState.totalValues];

  let newFilteredTotalValues = newObjTotalValues?.find(
    (v) => v.hourClass === hourClass
  );

  let newFilteredTotalValuesByMonth = newFilteredTotalValues?.values?.find(
    (v) => v.month === month
  );

  newFilteredTotalValuesByMonth?.values?.map((v) => {
    if (v.formName === formName) {
      if (value === "reais" || value === "percentage") {
        v["discountUnit"] = value;

        if (value === "percentage") {
          const newHourWithDiscount =
            v["hourValue"] - (v["hourValue"] * v["discountValue"]) / 100;
          v["totalValue"] = v["totalHours"] * newHourWithDiscount;
        } else {
          v["totalValue"] =
            v["totalHours"] * (v["hourValue"] - v["discountValue"]);
        }
      } else {
        if (v["discountUnit"] === "percentage") {
          if (Number(value) >= 100) {
            v["discountValue"] = 100;
            v["totalValue"] = 0;
            return newObjTotalValues;
          } else {
            v["discountValue"] = Number(value);
          }

          const newHourWithDiscount =
            v["hourValue"] * ((100 - Number(value)) / 100);

          v["totalValue"] = v["totalHours"] * newHourWithDiscount;
        } else {
          if (Number(value) >= v["hourValue"]) {
            v["discountValue"] = v["hourValue"];
            v["totalValue"] = 0;
            return newObjTotalValues;
          } else {
            v["discountValue"] = Number(value);
          }

          v["totalValue"] = v["totalHours"] * (v["hourValue"] - Number(value));
        }
      }
    }
  });

  return newObjTotalValues;
}

export const HourValueCardDaredeInvestments = ({
  title, // 8x5 SETUP,
  hourClass,
  costManagementData,
  services,
  month,
}) => {
  const { Text } = Typography;
  const { Option } = Select;
  const { proposalState, setProposalState } = useContext(ProposalContext);

  const filteredTotalValues = proposalState?.totalValues?.find(
    (v) => v.hourClass === hourClass
  );

  const filteredTotalValuesByMonth = filteredTotalValues?.values?.find(
    (v) => v.month === month
  );

  const handleInputValue = async (value, hourClass, hourType) => {
    if (value < 0) value = 0;
    if (value === "reais" || value === "percentage") {
      const newState = await updateProperty(
        "discountUnit",
        value,
        proposalState,
        hourClass,
        month,
        hourType.formName
      );
      setProposalState({
        type: TotalValuesProposalType.SET_TOTAL_VALUES,
        value: newState,
      });
    } else {
      const newState = await updateProperty(
        "discountValue",
        value,
        proposalState,
        hourClass,
        month,
        hourType.formName
      );
      setProposalState({
        type: TotalValuesProposalType.SET_TOTAL_VALUES,
        value: newState,
      });
    }
  };

  const getDiscountDefaultValue = (t) => {
    return t.discountValue;
  };

  const getDiscountDefaultUnit = (t) => {
    return t.discountUnit;
  };

  return (
    <Col
      xxl={8}
      xl={24}
      sm={24}
      style={{ textAlign: "center", marginTop: "1rem" }}
    >
      <Text>{title}</Text>
      <Card
        style={{
          width: "100%",
          backgroundColor:
            hourClass === "additionalHourValue" ? "#fff" : "#0F9347",
          borderColor: hourClass === "additionalHourValue" ? "#000" : "none",
          borderRadius: "5px",
          marginTop: "1rem",
        }}
      >
        <Row justify="space-between" gutter={[4, 16]}>
          {filteredTotalValuesByMonth
            ? filteredTotalValuesByMonth?.values?.map((t, i) => {
                return (
                  <Col span={12} style={{ textAlign: "center" }}>
                    <Text
                      style={{
                        color: title.includes("Adicional") ? "#000" : "#fff",
                      }}
                    >
                      {formatHourName(t.formName)}
                    </Text>
                    <Row>
                      <Col span={8}>
                        <Tooltip title="Valor hora">
                          <Input
                            disabled
                            placeholder={`Valor em Gerenciamento de Custos`}
                            style={{ textAlign: "center", color: "#000" }}
                            type="number"
                            value={t.hourValue}
                          />
                        </Tooltip>
                      </Col>
                      <Col span={16}>
                        <Tooltip title="Valor do desconto">
                          <Input
                            style={{ textAlign: "center" }}
                            placeholder="Valor do desconto"
                            type="number"
                            defaultValue={getDiscountDefaultValue(t)}
                            onKeyDown={(e) => {
                              invalidInputNumberChars.find(
                                (keyPressed) => keyPressed === e.key
                              ) && e.preventDefault();
                            }}
                            onChange={(e) =>
                              handleInputValue(
                                e.target.value,
                                hourClass === "additionalHourValue"
                                  ? "additionalHourValue"
                                  : "hourValue", // since it is not discount values
                                t
                              )
                            }
                            addonAfter={
                              <Select
                                style={{ padding: 0 }}
                                defaultValue={getDiscountDefaultUnit(t)}
                                onChange={(value) => {
                                  handleInputValue(
                                    value,
                                    hourClass === "additionalHourValue"
                                      ? "additionalHourValue"
                                      : "hourValue", // since it is not discount values
                                    t
                                  );
                                }}
                              >
                                <Option value="reais">R$</Option>
                                <Option value="percentage">%</Option>
                              </Select>
                            }
                          />
                        </Tooltip>
                      </Col>
                    </Row>
                  </Col>
                );
              })
            : null}
        </Row>
      </Card>
    </Col>
  );
};
