import { message } from "antd";
import { logNewAuditAction } from "../../../../controllers/audit/logNewAuditAction";
import { portalPost } from "../../../../service/apiPortal";
import { otrsPost, otrsPut } from "../../../../service/apiOtrs";
import {
  dynamoGetById,
  dynamoPost,
  dynamoPut,
} from "../../../../service/apiDsmDynamo";
import { format } from "date-fns";
import { otrsWebServicePost } from "../../../../service/otrsWebService";

export const formatGetTicketBody = (ticketNumber) => {
  if (!ticketNumber || ticketNumber === null)
    throw new Error("Ticket não encontrado");
  return {
    params: parseInt(ticketNumber),
  };
};

export const getTicketId = async (ticketNumber) => {
  const params = formatGetTicketBody(ticketNumber);
  try {
    const { data } = await otrsPost("read/tickets/number", params);
    return data[0].ticket_id;
  } catch (err) {
    message.error("Ocorreu um erro ao buscar o ticket");
    new Error("Ticket não encontrado");
  }
};

export const formatMFAResetBody = async (username, ticket) => {
  if (!ticket || ticket === null) throw new Error("Ticket não encontrado");
  let ticketId = await getTicketId(ticket);
  return {
    tktNumber: parseInt(ticket),
    title: "MFA Resetado com sucesso",
    from: localStorage.getItem("@dsm/mail"),
    description:
      "MFA do usuário " +
      username +
      " foi resetado com sucesso, pelo analista " +
      localStorage.getItem("@dsm/username") +
      ".",
    tktId: ticketId,
    contract: 0,
    closed: "closed successful",
    sender: localStorage.getItem("@dsm/mail"),
    communicationType: "Email",
  };
};

const ticketUpdate = async (username, ticket) => {
  const body = await formatMFAResetBody(username, ticket);
  try {
    await otrsWebServicePost("tickets/close", body);
    message.success("MFA resetado com sucesso!");
  } catch (err) {
    message.error("Ops! Ocorreu um erro inesperado...");
  }
};

export const resetMfaController = async (value, ticket) => {
  try {
    await portalPost("mfa", {
      user: value,
    });
    try {
      await ticketUpdate(value, ticket);
      const username = localStorage.getItem("@dsm/username");
      const title = "Reset de MFA";
      const description = `${username} resetou o MFA do usuário ${value}`;
      logNewAuditAction(username, title, description);
    } catch (err) {
      console.log(err);
      message.error("Ocorreu um erro ao atualizar o ticket");
    }
  } catch (err) {
    message.error("Ocorreu um erro ao resetar o MFA");
  }
};
export const getCurrentContacts = async (client) => {
  try {
    if (!client || !client.id) {
      throw new Error('Cliente ou ID não encontrado');
    }

    const contacts = await dynamoGetById(
      `${process.env.REACT_APP_STAGE}-customers`,
      client.id
    );

    return contacts;
  } catch (error) {
    throw error;
  }
};
export const formatOtrsUpdateBody = (client, data) => {
  if (!client || client === null) throw new Error("Cliente não encontrado");
  if (!data || data === null) throw new Error("Dados não encontrados");
  return {
    customer_id: client?.identifications?.itsm_id,
    first_name: data.names.first_name,
    last_name: data.names.last_name,
    login: data.email,
    email: data.email,
    phone: data.phone,
    active: data.active === 0 ? 1 : 2,
  };
};

export const otrsUpdate = async (id, body) => {
  try {
    await otrsPut("update/person/" + id, body);
  } catch (err) {
    message.error("Ocorreu um erro inesperado, tente novamente mais tarde.");
  }
};

export const checksIfEmailAlreadyExists = (clients, newContact, customerId) => {
  const customers = JSON.parse(JSON.stringify(clients));

  const foundEmail = customers.some((customer) => {
    if (customer.id !== customerId) {
      return customer?.contacts.some((contact) => {
        return contact.email === newContact.email;
      });
    } else {
      return customer?.contacts.some(
        (contact) =>
          contact?.email === newContact.email &&
          contact.identifications.itsm_id !==
            newContact?.identifications?.itsm_id
      );
    }
  });

  return foundEmail;
};

export const formatUpdateBody = (client, newContact, isActive) => {
  if (!client || client === null) throw new Error("Cliente não encontrado");
  let copyClient = JSON.parse(JSON.stringify(client));
  newContact = JSON.parse(JSON.stringify(newContact));
  let newContacts = client.contacts.filter((contact) => {
    return (
      contact.identifications.itsm_id !== newContact.identifications.itsm_id
    );
  });
  newContact["updated_at"] = format(new Date(), "yyyy-MM-dd HH:mm:ss");
  if (isActive) newContact["active"] = newContact.active === 1 ? 0 : 1;
  newContacts.push(newContact);
  if (client.cnpj === "") copyClient.cnpj = "-";
  return {
    ...copyClient,
    contacts: newContacts,
  };
};

export const updateDynamo = async (id, body, data, isActive) => {
  try {
    await dynamoPut(`${process.env.REACT_APP_STAGE}-customers`, id, body);
    if (isActive)
      message.success(
        `Usuário ${data.active === 1 ? "desativado" : "ativado"} com sucesso!`
      );
    else message.success("Contato editado com sucesso!");
  } catch (error) {
    console.log(error);
    message.error(
      "Ocorreu um erro ao tentar atualizar o contato no OTRS, tente novamente"
    );
  }
};

export const formatOTRSInsertBody = (client, data) => {
  if (!client || client === null) throw new Error("Cliente não encontrado");
  if (!data || data === null) throw new Error("Dados não encontrados");
  return {
    customer_id: client?.identifications?.itsm_id,
    first_name: data.first_name,
    last_name: data.last_name,
    login: data.email,
    email: data.email,
    phone: data.phone,
    comments: null,
    country: null,
    mobile: null,
    street: null,
    city: null,
    fax: null,
    zip: null,
  };
};

export const insertOTRSPerson = async (client, data) => {
  let body = formatOTRSInsertBody(client, data);
  let otrs = await otrsPost("create/person", body);
  return otrs;
};

export const formatInsertContactArray = (client, data, itsm_id) => {
  if (!client || client === null) throw new Error("Cliente não encontrado");
  if (!data || data === null) throw new Error("Dados não encontrados");
  if (!itsm_id || itsm_id === null)
    throw new Error("Dados do OTRS não encontrados");
  data["created_at"] = format(new Date(), "yyyy-MM-dd HH:mm:ss");
  data["updated_at"] = format(new Date(), "yyyy-MM-dd HH:mm:ss");
  data["names"] = {
    first_name: data.first_name,
    last_name: data.last_name,
  };
  data["identifications"] = {
    itsm_id: itsm_id,
    crm_id: data.crm_id || null,
  };
  data["customer_id"] = client.id;
  data["responsability"] = null;
  data["active"] = 1;

  delete data.first_name;
  delete data.last_name;
  delete data.itsm_id;
  delete data.crm_id;

  return data;
};

export const contactCreationAuditLog = (data, client) => {
  const username = localStorage.getItem("@dsm/username");

  const title = "Contato adicionado";
  const description = `${username} criou o contato ${data.names.first_name} ${
    data.names.last_name
  }, no cliente ${client?.names?.fantasy_name || client?.names?.name}.`;

  dynamoPost(`${process.env.REACT_APP_STAGE}-audits`, {
    username: username,
    name: title,
    description: description,
    created_at: new Date(),
    updated_at: new Date(),
  });
};
