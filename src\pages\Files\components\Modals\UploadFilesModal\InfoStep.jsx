import { useMemo } from "react";
import { useSelector, shallowEqual } from "react-redux";

import { Typography, Spin } from "antd";

import { UPLOAD_STATUS } from "../../../../../store/reducers/files-reducer";

import success from "../../../../../assets/images/emailSent.png";
import warning from "../../../../../assets/images/warning.png";

const { Text } = Typography;

export const InfoStep = () => {
  const uploadStatus = useSelector(
    (state) => state.files.uploadStatus,
    shallowEqual
  );

  const emails = useSelector((state) => state.files.emails, shallowEqual);

  const content = useMemo(() => {
    switch (uploadStatus) {
      case UPLOAD_STATUS.SUCCESS:
        return (
          <>
            <img src={success} style={{ paddingBottom: 30, paddingTop: 30 }} />
            {emails.length > 0 ? (
              <Text>E-mail enviado com sucesso</Text>
            ) : (
              <Text>Upload realizado com sucesso</Text>
            )}
          </>
        );
      case UPLOAD_STATUS.ERROR:
        return (
          <>
            <img src={warning} style={{ paddingBottom: 30, paddingTop: 30 }} />
            <Text>Ops... seu arquivo não foi enviado</Text>
            <Text>por um erro, tente novamente</Text>
          </>
        );
      default:
        return (
          <>
            <Spin size="large" style={{ paddingBottom: 50, paddingTop: 50 }} />
            <Text>Aguarde, ainda estamos subindo</Text>
            <Text>o arquivo em nosso servidor</Text>
          </>
        );
    }
  }, [uploadStatus, emails]);

  return (
    <div
      style={{
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        flexDirection: "column",
      }}
    >
      {content}
    </div>
  );
};
