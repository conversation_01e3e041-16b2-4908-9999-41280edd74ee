import { useEffect, useState } from "react";
import {
  Card,
  Col,
  Input,
  Layout,
  Row,
  Select,
  Space,
  Table,
  Typography,
} from "antd";
import { SideMenu } from "../../components/SideMenu";
import { HeaderMenu } from "../../components/HeaderMenu";
import { dsmPost } from "../../service/apiDsm";
import { ResetSecret } from "../../components/Modals/MFA/ResetSecret";
import Axios from "axios";
import { dynamoGetById } from "../../service/apiDsmDynamo";
import useSWR from "swr";

export const SecurityMFA = () => {
  const [collapsed, setCollapsed] = useState(false);
  const [loading, setLoading] = useState(false);
  const [secrets, setSecrets] = useState([]);
  const [search, setSearch] = useState("");
  const [state, setState] = useState("");
  const { Text } = Typography;
  const { Content } = Layout;
  const { Option } = Select;

  const permissions = useSWR("clients", async () => {
    try {
      let data = await dynamoGetById(
        `${process.env.REACT_APP_STAGE}-permissions`,
        localStorage.getItem("@dsm/permission")
      );

      return [...data.permissions.find((x) => x.page === "MFA").actions];
    } catch (error) {
      console.log('⚠️ SecurityMFA: Erro ao buscar permissões, usando fallback:', error.message);
      // Fallback com TODOS os códigos de colunas necessários
      return [
        { code: "view_usuario" },
        { code: "view_portal" },
        { code: "view_reset" },
        { code: "create_mfa_secret" },
        { code: "edit_mfa_secret" },
        { code: "delete_mfa_secret" },
        { code: "view_mfa_users" }
      ];
    }
  });

  const getSecrets = async () => {
    setLoading(true);
    let req = await dsmPost("/mfa/secrets", {
      only_read: 1,
    });

    let arr = [];

    await Axios.post(process.env.REACT_APP_API_CUSTOMER_PORTAL + "mfa/secrets", {
      only_read: 1,
    }).then((res) => {
      Object.entries(res.data.data).forEach((e) => {
        let obj = {};
        obj["user"] = e[0];
        obj["secret"] = e[1];
        obj["origin"] = "area-logada";

        arr.push(obj);
      });
    });

    Object.entries(req.data).forEach((e) => {
      let obj = {};
      obj["user"] = e[0];
      obj["secret"] = e[1];
      obj["origin"] = "dsm";

      arr.push(obj);
    });

    setLoading(false);
    setSecrets(arr);
  };

  const columns = [
    {
      code: "view_usuario",
      dataIndex: "user",
      title: "Usuário",
    },
    {
      code: "view_portal",
      dataIndex: "origin",
      title: "Portal",
    },
    {
      code: "view_reset",
      dataIndex: "user",
      title: "Resetar",
      width: "1%",
      render: (user, item) => (
        <ResetSecret getSecrets={() => getSecrets()} secret={item} />
      ),
    },
  ];

  useEffect(() => {
    getSecrets();
  }, []);

  return (
    <Layout style={{ minHeight: "100vh" }}>
      <HeaderMenu collapsed={collapsed} setCollapsed={setCollapsed} />
      <Layout>
        <SideMenu collapsed={collapsed} />
        <Content style={{ padding: "2em" }}>
          <Card
            style={{
              boxShadow: "0 0 10px rgba(0,0,0,0.1)",
              borderRadius: "20px",
              marginRight: "10px",
            }}
          >
            <Row justify="space-between">
              <Col
                span={8}
                style={{ marginBottom: "1em", borderRadius: "15px" }}
              >
                <Input
                  placeholder="Busque um usuário"
                  style={{
                    height: "35px",
                    borderRadius: "7px",
                  }}
                  onChange={(e) => setSearch(e.target.value)}
                />
              </Col>
              <Col>
                <Space>
                  <Text>Filtrar por: </Text>
                  <Select onChange={setState} defaultValue="dsm">
                    <Option value="todos">Todos</Option>
                    <Option value="dsm">DSM</Option>
                    <Option value="area-logada">Area Logada</Option>
                  </Select>
                </Space>
              </Col>
            </Row>
            <Table
              scroll={{ x: "100%" }}
              dataSource={secrets.filter((e) => {
                const data = [
                  e?.user
                    ? e?.user
                        ?.toLowerCase()
                        .toString()
                        .includes(search.toLowerCase())
                    : false,
                  e?.origin
                    ? e?.origin
                        ?.toLowerCase()
                        .toString()
                        .includes(search.toLowerCase())
                    : false,
                ];

                if (state === "dsm" || state === "") {
                  for (let i = 0; i < data.length; i++) {
                    if (data[i] === true) {
                      return e.origin === "dsm";
                    }
                  }
                } else if (state === "area-logada") {
                  for (let i = 0; i < data.length; i++) {
                    if (data[i] === true) {
                      return e.origin === "area-logada";
                    }
                  }
                } else if (state === "todos") {
                  for (let i = 0; i < data.length; i++) {
                    if (data[i] === true) {
                      return e;
                    }
                  }
                }

                if (search === "") return e;

                return null;
              })}
              columns={columns.filter((e) =>
                permissions?.data
                  ?.map((permission) => {
                    return permission.code;
                  })
                  .includes(e.code)
              )}
              loading={loading}
            />
          </Card>
        </Content>
      </Layout>
    </Layout>
  );
};
