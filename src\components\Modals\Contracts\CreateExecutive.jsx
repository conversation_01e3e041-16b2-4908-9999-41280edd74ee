import { Mo<PERSON>, Button, Form, message, Row, Col, Input } from "antd";
import { format } from "date-fns";
import React, { useState } from "react";
import { dynamoPost } from "../../../service/apiDsmDynamo";

export const CreateExecutive = (props) => {
  const { executives, form, show, setShow } = props;
  const [loading, setLoading] = useState(false);
  const [createForm] = Form.useForm();

  const handleSubmit = async (data) => {
    if (executives.data.map((e) => e.name).includes(data.name)) {
      return message.error("Este tipo de executivo já existe.");
    }

    setLoading(true);

    try {
      await dynamoPost(`${process.env.REACT_APP_STAGE}-executives`, {
        ...data,
        created_at: format(new Date(), "yyyy-MM-dd HH:mm:ss"),
        updated_at: format(new Date(), "yyyy-MM-dd HH:mm:ss"),
      });

      form.setFieldsValue({
        type: data.name,
      });
      createForm.resetFields();

      message.success("Tipo de Executivo adicionado com sucesso!");

      setShow(false);
    } catch (error) {
      console.log(error);
      message.error(
        "Ocorreu um erro ao tentar adicionar este tipo executivo..."
      );
    }

    setLoading(false);
  };

  return (
    <Modal
      title="Inserir um novo tipo executivo"
      onCancel={() => {
        createForm.resetFields();
        form.setFieldsValue({
          type: undefined,
        });
        setShow(false);
      }}
      closable={false}
      open={show}
      footer={[
        <Button
          onClick={() => {
            createForm.resetFields();
            setShow(false);
          }}
        >
          Cancelar
        </Button>,
        <Button
          type="primary"
          onClick={() => createForm.submit()}
          loading={loading}
        >
          Adicionar
        </Button>,
      ]}
    >
      <Row align="middle" justify="center">
        <Col span={24}>
          <Form
            onFinish={handleSubmit}
            name="control-hooks"
            form={createForm}
            layout="vertical"
          >
            <Form.Item name="name">
              <Input placeholder="Nome do Tipo de Executivo" />
            </Form.Item>
          </Form>
        </Col>
      </Row>
    </Modal>
  );
};
