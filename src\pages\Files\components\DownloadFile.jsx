import { useState } from "react";
import { Button, message } from "antd";
import { CloudDownloadOutlined } from "@ant-design/icons";

import * as controller from "../../../controllers/files/filesController";
import { logNewAuditAction } from "../../../controllers/audit/logNewAuditAction";

export const DownloadFile = ({ item }) => {
  const [loading, setLoading] = useState(false);
  const handleDownload = async () => {
    setLoading(true);
    try {
      await controller.downloadFileS3(item);

      const username = localStorage.getItem("@dsm/username");
      const title = "Download de Arquivo";
      const description = `${username} realizou o download do arquivo: ${item.name}`;
      logNewAuditAction(username, title, description);
    } catch (error) {
      message.error("Erro ao fazer o download do arquivo!");
    }
    setLoading(false);
  };
  return (
    <>
      <Button
        type="text"
        loading={loading}
        icon={<CloudDownloadOutlined />}
        onClick={handleDownload}
      />
    </>
  );
};
