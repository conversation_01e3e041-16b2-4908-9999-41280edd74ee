.table-card {
  margin: 0 auto;
  padding: 16px;
  width: 100%;
  background-color: rgb(255, 255, 255);
  box-shadow: 0px 0px 10px 2px rgba(113, 113, 113, 0.25);
  border-radius: 24px;
}
.table-card-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}
.btn-hidden {
  cursor: pointer;
}
.button button {
  background-color: #43914f;
  border: none;
  color: white;
  border-radius: 5px;
  padding: 6px 24px;
  box-shadow: 0px 0px 10px 2px rgba(64, 64, 64, 0.25);
  font-weight: bold;
  font-size: 16px;
  line-height: 22px;
}
.table-filter p {
  padding-top: 16px;
}
.table-filter-content {
  height: 20px;
  width: 20px;
  border-radius: 3px;
  border: 1px solid #1ebd71;
  margin-right: 37px;
}
.table-filter-icon {
  font-size: 12px;
  line-height: 16px;
  color: #1ebd71;
  width: 10px;
  height: 10px;
  margin-top: 4px;
  cursor: pointer;
}
.container-table {
  width: 100%;
  margin: 0 auto;
}
.priority-container {
  display: flex;
  align-items: center;
  gap: 16px;
}
.priority {
  height: 80px;
  width: 4px;
}
.expiration-of-contracts-table {
  width: 100%;
  border-radius: 20px;
}
.ant-modal{
  max-height: 80vh;
}
