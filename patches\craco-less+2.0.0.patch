diff --git a/node_modules/craco-less/lib/utils.js b/node_modules/craco-less/lib/utils.js
index 1234567..abcdefg 100644
--- a/node_modules/craco-less/lib/utils.js
+++ b/node_modules/craco-less/lib/utils.js
@@ -1,12 +1,16 @@
 const mapValues = (obj, fn) => {
-    return Object.keys(obj).reduce((acc, key) => {
+    return Object.keys(obj || {}).reduce((acc, key) => {
         acc[key] = fn(obj[key]);
         return acc;
     }, {});
 };
 
 const deepClone = value => {
+    // Verificar se o valor é nulo ou indefinido
+    if (value === null || value === undefined) {
+        return value;
+    }
+
     switch (value.constructor) {
         case Array:
             return value.map(deepClone);