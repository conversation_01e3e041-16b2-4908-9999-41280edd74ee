import { Typo<PERSON>, Select, Row, Col, message } from "antd";
import { useSelector, shallowEqual } from "react-redux";
import {
  setUploadFilesState,
  setUploadReadyFilesState,
} from "../../../../../store/actions/files-action";
import { validateEmail } from "../../../../../utils/validators";

export const MailStep = () => {
  const { Option } = Select;
  const { Text } = Typography;
  const emails = useSelector((state) => state.files.emails, shallowEqual);

  return (
    <>
      <Row style={{ paddingBottom: "8px" }}>
        <Text>
          Digite os <strong>Emails</strong> (opcional)
        </Text>
      </Row>
      <Row align={"middle"} style={{ paddingBottom: "40px" }}>
        <Col span={24}>
          <Select
            onChange={(e, i) => {
              let allValid = true;
              for (let i = 0; i < e.length; i++) {
                if (!validateEmail(e[i])) {
                  message.warning("Insira um email válido", 3);
                  allValid = false;
                  break;
                }
              }
              if (allValid) {
                setUploadFilesState({ field: "emails", value: e });
                setUploadReadyFilesState(true);
              }
            }}
            placeholder={"Digite os e-mails e pressione ENTER"}
            value={emails}
            style={{ width: "100%" }}
            mode={"tags"}
          >
            {emails?.map((email, key) => (
              <Option value={email} key={key}>
                {email}
              </Option>
            ))}
          </Select>
        </Col>
      </Row>

      <Row style={{ justifyContent: "center", alignItems: "center" }}>
        <Text>
          Ao informar um e-mail, será enviado uma notificação ao destinatario,
        </Text>
        <Text>informando sobre a disponibilidade dos arquivos no portal.</Text>
      </Row>
    </>
  );
};
