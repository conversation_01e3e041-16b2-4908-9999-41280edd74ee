import {React, useEffect, useState} from 'react'
import {BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ReferenceLine} from 'recharts'
import {ArrowLeftOutlined} from '@ant-design/icons'
import moment from 'moment'
import './forecastGraphics.css'


const ForeCast = ({professional, showModal}) => {

    const monthsName = ['Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho', 'Jul<PERSON>', 'Agosto', 'Setembro', 'Outubro', 'Novembro', 'Dezembro']
    const LIMIT = 140;
    const showCurrentMonth = monthsName[moment().month()]
    const showCurrentYear = moment().year()

    const previousMonth = monthsName[moment().month() - 1]
    const monthBeforePrevious = monthsName[moment().month() - 2]
    const firstMonth = monthsName[moment().month() - 3]

    const data = [
        {
            name: previousMonth,
            registradas: 40,
            alocadas: 24,
        },
        {
            name: monthBeforePrevious,
            registradas: 30,
            alocadas: 13,
        },
        {
            name: firstMonth,
            registradas: 20,
            alocadas: 98,
        },
    ]


    useEffect(() => {
    }, [])

    console.log(professional)

    return (
        <>
            <div className="card-header">
                <div className='card-header-left header-test'>
                    <div className="button">
                        <button className="filter-clear-btn back-button" onClick={() => showModal()}>
                            <ArrowLeftOutlined/>
                        </button>
                    </div>
                    <div className="card-sub-header">
                        <h2 className="card-title title-name">{professional.professional}</h2>
                        <div className="card-icons header-icon">
                            {`${showCurrentMonth}/${showCurrentYear}`}
                        </div>
                    </div>
                </div>
            </div>

            <div className="tes">
                <BarChart
                    width={550}
                    height={230}
                    data={data}
                    margin={{
                        top: 10,
                        right: 50,
                        left: 20,
                        bottom: 0,
                    }}
                >
                    <CartesianGrid vertical={false} strokeDasharray="3 3"/>
                    <XAxis fontSize={16} dataKey="name"/>
                    <YAxis fontSize={16}/>
                    <Tooltip cursor={false}/>
                    <ReferenceLine
                        y={LIMIT}
                        label={{position: 'right', value: '140h', fill: 'red', fontSize: 16}}
                        stroke="red"
                        strokeDasharray="3 3"
                    />
                    <Bar dataKey="alocadas" stackId="a" fill={`#43914F`}/>
                    <Bar dataKey="registradas" stackId="a" fill={`#435E91`}/>
                    <Bar dataKey="total" stackId="a" fill="#ff0000"/>
                </BarChart>
                <div className="infosContainer1">
                    <div className="infoSingle">
                        <div className="captionColorBillable"></div>
                        <p>Alocadas</p>
                    </div>
                    <div className="infoSingle">
                        <div className="captionColorNonBillable"></div>
                        <p>Registradas</p>
                    </div>
                </div>
            </div>
        </>
    )
}

export {ForeCast}
