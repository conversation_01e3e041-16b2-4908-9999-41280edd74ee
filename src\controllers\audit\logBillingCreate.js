import { getContractById } from "../../utils/getContractById";
import { getCustomerById } from "../../utils/getCustomerById";
import { logNewAuditAction } from "./logNewAuditAction";

export const logBillingCreate = async (contractId, customerId) => {
  if (!contractId || contractId === "") {
    const returnMessage = {
      statusCode: 501,
      message: `Error adding edit billing audit action log. Using wrong parameter when calling the function. Wrong parameter considered: 'contractId'`,
    };
    return returnMessage;
  }

  if (!customerId || customerId === "") {
    const returnMessage = {
      statusCode: 502,
      message: `Error adding edit billing audit action log. Using wrong parameter when calling the function. Wrong parameter considered: 'customerId'`,
    };
    return returnMessage;
  }

  try {
    const res = getContractById(contractId).then(async (contractData) => {
      if (contractData) {
        const username = localStorage.getItem("@dsm/username");
        const title = "Cadastro de Billing";
        const description = `${username} cadastrou o billing para o contrato: ${
          contractData.name
        } de DSM ID: ${contractData.dsm_id || ""}`;

        await logNewAuditAction(username, title, description);

        const returnMessage = {
          statusCode: 200,
          message: `Success adding create billing audit action log: ${title}`,
        };

        return returnMessage;
      } else {
        const res = getCustomerById(customerId).then(async (customerData) => {
          const username = localStorage.getItem("@dsm/username");
          const title = "Cadastro de Billing - Erro ao Cadastrar";
          const description = `${username} cadastrou um billing onde os dados do contrato não foram encontrados. Customer ITSM ID: ${
            customerData.identifications.itsm_id || ""
          } Contract ID: ${contractId}`;

          await logNewAuditAction(username, title, description);

          const returnMessage = {
            statusCode: 201,
            message: `Success adding create billing audit action log with no contract data: ${title}`,
          };

          return returnMessage;
        });

        return res;
      }
    });

    return res;
  } catch (error) {
    const returnMessage = {
      statusCode: 500,
      message: `Error adding create billing audit action log. Error message: ${error}`,
    };
    return returnMessage;
  }
};
