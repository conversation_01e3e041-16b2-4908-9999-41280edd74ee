import { But<PERSON>, <PERSON><PERSON>, <PERSON> } from "antd";
import { PlusOutlined } from "@ant-design/icons";
import { EmailsTable } from "./components/EmailsTable";
import { useToggle } from "../../../../../hooks/useToggle";

export const AddEmailsModal = () => {
  const [toggleModalValue, toggle] = useToggle();

  return (
    <>
      <Button type="primary" onClick={toggle} icon={<PlusOutlined />}></Button>
      <Modal
        title="Emails"
        open={toggleModalValue}
        width={"50%"}
        closable={false}
        centered
        footer={
          <Row justify={"end"}>
            <Button type="primary" onClick={toggle}>
              Ok
            </Button>
          </Row>
        }
      >
        <EmailsTable />
      </Modal>
    </>
  );
};
