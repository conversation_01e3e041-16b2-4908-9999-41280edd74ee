import { dsmProvider } from "../../../../../provider/dsm-provider";
export const getEmailTemplate = (username, account) => {
  return `
  <p>Prezado(a) ${username},</p>

  <p>Identific<PERSON>s que as roles <strong>darede-full</strong> e <strong>darede-switch-role</strong> ainda não foram criadas para a conta <strong>"${account}"</strong> que você acabou de cadastrar.</p>

  <p>Para evitar possíveis problemas de acesso dos usuários, solicitamos que realize a criação dessas roles o quanto antes.</p>

  <p>Voc<PERSON> pode criar as roles necessárias clicando no botão abaixo:<br>
  <a href="${process.env.REACT_APP_CREATE_ROLE_LINK}" style="
           display: inline-block;
           padding: 10px 20px;
           font-size: 12px;
           color: white;
           background-color: #1ebd71;
           text-align: center;
           text-decoration: none;
           border-radius: 5px;
           margin-top: 10px;
         ">Criar Roles</a></p>

  <p>Em caso de dúvidas, nossa equipe de suporte está à disposição.</p>

  <p>Atenciosamente,<br>
  Equipe Dosystems</p>
  `;
};

export const sendEmail = async (emailTemplate, emailSubject, emailAddress) => {
  try {
    const provider = dsmProvider();
    let formData = new FormData();

    const encriptyBody = Buffer.from(emailTemplate).toString("base64");
    const encriptySubject = Buffer.from(emailSubject).toString("base64");

    formData.append("htmlTemplate", encriptyBody);
    formData.append("emails", emailAddress);
    formData.append("subject", encriptySubject);

    await provider.post("email/send-email", formData);
  } catch (error) {
    console.log(error);
  }
};

const normalizeName = (username) => {
  let parts = username.split(".");
  let formattedName = parts
    .map((part) => part.charAt(0).toUpperCase() + part.slice(1))
    .join(" ");

  return formattedName;
};

export const sendCreateSwitchRoleFailEmail = async (account) => {
  const email = localStorage.getItem("@dsm/mail");
  const username = normalizeName(localStorage.getItem("@dsm/name"));
  const emailTemplate = getEmailTemplate(username, account);

  const emailSubject = `Ação necessária: Criação das roles darede-full e darede-switch-role`;

  return sendEmail(emailTemplate, emailSubject, email);
};
