import { useEffect, useState, useContext } from "react";
import axios from "axios";
import {
  Button,
  Modal,
  Row,
  Col,
  Table,
  message,
  Popconfirm,
  Tooltip,
  Typography,
  Alert,
} from "antd";
import {
  PaperClipOutlined,
  DeleteOutlined,
  CheckOutlined,
  DownloadOutlined,
} from "@ant-design/icons";
import { v4 } from "uuid";
import {
  s3DeleteMultipleObjects,
  s3ListObjects,
  s3UploadObj,
} from "../../../service/apiDsmS3";
import { ProposalContext } from "../../../contexts/totalValuesProposals";
import { TotalValuesProposalType } from "../../../contexts/reducers/totalValuesProposal";
const FileDownload = require("js-file-download");

async function getBase64(file) {
  const base64string = await new Promise((resolve) => {
    let reader = new FileReader();
    if (file) {
      reader.readAsDataURL(file);
    }
    reader.onload = function () {
      resolve(reader.result);
    };
    reader.onerror = function (error) {
      resolve("");
    };
  });
  return base64string;
}

async function getFilesToBeDeleted(idFixed, files) {
  let filesToBeDeleted = [];

  const bucketFilesResponse = await s3ListObjects(
    `${process.env.REACT_APP_STAGE}-proposals-documents`,
    `${idFixed}/documents/architecture`
  );

  for (let i = 0; i < bucketFilesResponse.Contents.length; i++) {
    const keySplitted = bucketFilesResponse.Contents[i].Key.split("/");
    const fileNameExtractedFromKey = decodeURI(
      keySplitted[keySplitted.length - 1]
    );

    let found = false;

    for (let j = 0; j < files.length && !found; j++) {
      if (files[j].name === fileNameExtractedFromKey) {
        found = true;
      }
    }

    if (!found) {
      filesToBeDeleted.push(bucketFilesResponse.Contents[i].Key);
    }
  }

  return filesToBeDeleted;
}

export const AddArchitectureDocument = () => {
  const [showModal, setShowModal] = useState();
  const [loading, setLoading] = useState(false);
  const { proposalState, setProposalState } = useContext(ProposalContext);
  const [supportArchitectArray, setSupportArchitectArray] = useState([]);
  const [firstOpenVerify, setFirstOpenVerify] = useState(true);
  const { Text } = Typography;
  const [fileId, setFileId] = useState(
    proposalState.architectureFileNames.length || 0
  );
  const [files, setFiles] = useState([]);
  const [newFiles, setNewFiles] = useState([]);

  const idFixed = proposalState.idFixed === "" ? v4() : proposalState.idFixed;

  useEffect(() => {
    setSupportArchitectArray(proposalState.architectureFileNames);
  }, [proposalState]);

  const handleCloseModal = (file) => {
    setShowModal(false);
  };

  const handleRemoveItem = (deleteIndex, item) => {
    const prevListStr = JSON.stringify(proposalState.architectureFileNames);
    const prevList = JSON.parse(prevListStr);

    const newList = prevList.filter((prev, index) => index !== deleteIndex);

    setProposalState({
      type: TotalValuesProposalType.SET_ARCHITECTURE_FILE_NAMES,
      value: newList,
    });
  };

  const handleFileSelect = () => {
    const architectureFile =
      document.getElementById("fileArchitecture").files[0];

    const ids = [0];

    proposalState.architectureFileNames.map((f) => {
      const id = f.id;
      ids.push(id);
    });

    if (architectureFile) {
      setFiles((state) => [...state, architectureFile]);

      let exists = false;
      const addedFile = {
        id: Math.max.apply(null, ids) + 1,
        lastModified: architectureFile.lastModified,
        name: architectureFile.name,
        size: architectureFile.size,
        type: architectureFile.type,
        isSaved: true,
      };

      const newId = fileId + 1;
      setFileId(newId);

      for (let i = 0; i < proposalState.architectureFileNames.length; i++) {
        if (proposalState.architectureFileNames[i].name === addedFile.name) {
          exists = true;
        }
      }

      if (exists === false) {
        let arr = supportArchitectArray;
        arr.push(addedFile);
        setProposalState({
          type: TotalValuesProposalType.SET_ARCHITECTURE_FILE_NAMES,
          value: arr,
        });
      }
    } else {
      message.error("Selecione um arquivo");
    }
  };

  const handleOk = async (file) => {
    setLoading(true);
    setFirstOpenVerify(false);

    setProposalState({
      type: TotalValuesProposalType.SET_ID_FIXED,
      value: idFixed,
    });

    let filesToBeDeleted = await getFilesToBeDeleted(
      idFixed,
      proposalState.architectureFileNames
    );

    for (let i = 0; i < files.length; i++) {
      let name = `${idFixed}/documents/architecture/${files[i].name}`;

      const fileToTransform = files.find((file) => file.name === files[i].name);

      if (fileToTransform) {
        const base64obj = await getBase64(fileToTransform);
        const payload = {
          bucketName: `${process.env.REACT_APP_STAGE}-proposals-documents`,
          obj: base64obj,
          fileName: name,
          contentType: `${files[i].type}`,
        };

        try {
          await s3UploadObj(payload);
        } catch (error) {
          console.log({ error });
          message.error("Houve um erro ao fazer o upload dos arquivos!");
        }
      }
    }

    console.log("Deleting Remaining Objects: ", { filesToBeDeleted });

    const payloadDelete = {
      bucketName: `${process.env.REACT_APP_STAGE}-proposals-documents`,
      fileKeys: filesToBeDeleted,
    };

    try {
      await s3DeleteMultipleObjects(payloadDelete);
    } catch (error) {
      message.error(
        "Houve um erro ao fazer a deleção dos arquivos de arquitetura!"
      );
    }
    setLoading(false);
    setShowModal(false);
  };

  const handleGet = async (file) => {
    setLoading(true);
    setShowModal(true);
    let name = `${proposalState.idFixed}/documents/architecture/${file}`;

    const {
      data: { data },
    } = await axios.post(
      `${process.env.REACT_APP_API_PERMISSION}s3/link`,
      {
        operation: "get",
        key: encodeURI(name),
        bucket: `${process.env.REACT_APP_STAGE}-proposals-documents`,
      },
      {
        headers: {
          authorization: localStorage.getItem("jwt"),
        },
      }
    );

    axios
      .get(data, {
        responseType: "blob",
      })
      .then((response) => {
        FileDownload(response.data, `${file}`);
        setLoading(false);
      })
      .catch((err) => {
        setLoading(false);
        message.error("Erro ao tentar fazer download! ", { err });
      });

    setShowModal(false);
  };

  const handleOpen = () => {
    setShowModal(true);
  };

  const columns = [
    {
      title: "ID",
      dataIndex: "id",
      key: "id",
      render: (id, item) => {
        return <Text>{item.id}</Text>;
      },
    },
    {
      title: "Nome do arquivo",
      dataIndex: "file_name",
      key: "file_name",
      render: (id, item) => {
        return <Text>{item.name}</Text>;
      },
    },
    {
      title: "Tipo do Arquivo",
      dataIndex: "file_type",
      key: "file_type",
      align: "center",
      render: (id, item) => {
        return <Text>{item.type}</Text>;
      },
    },
    {
      title: "Download",
      dataIndex: "download",
      key: "download",
      align: "center",
      render: (id, item) => {
        return (
          <Button
            type="text"
            onClick={() => {
              handleGet(item.name);
            }}
          >
            <DownloadOutlined />
          </Button>
        );
      },
    },
    {
      title: "Remover",
      dataIndex: "remove",
      key: "remove",
      render: (id, item, index) => {
        return (
          <Button
            danger
            type="text"
            onClick={() => {
              handleRemoveItem(index, item);
            }}
          >
            <DeleteOutlined />
          </Button>
        );
      },
    },
  ];

  return (
    <>
      <Button type="text-color" onClick={handleOpen}>
        Anexos
        <PaperClipOutlined />
      </Button>
      <Modal
        title="Anexos"
        open={showModal}
        closable={false}
        width={"60%"}
        footer={[
          <Row justify="space-between">
            <Popconfirm
              title="Ao cancelar, você perderá os arquivos não salvos, tem certeza?"
              okText="Sim"
              cancelText="Não"
              onConfirm={() => handleCloseModal(supportArchitectArray)}
            >
              <Button type="text-color">
                Cancelar <DeleteOutlined />
              </Button>
            </Popconfirm>
            <Tooltip
              title={
                proposalState.architectureFileNames.length > 0
                  ? ""
                  : "Adicione arquivos para habilitar"
              }
            >
              <Button
                // disabled={
                //   proposalState.architectureFileNames.length > 0 ? false : true
                // }
                type="primary"
                onClick={() => handleOk(proposalState.architectureFileNames)}
              >
                Salvar <CheckOutlined />
              </Button>
            </Tooltip>
          </Row>,
        ]}
      >
        <Row
          style={{ display: "flex", flexDirection: "column" }}
          justify="center"
        >
          <Table
            dataSource={proposalState.architectureFileNames.map((e) => e)}
            columns={columns}
            scroll={{ x: 100 }}
            pagination={true}
            loading={loading}
          ></Table>

          <Row justify="center">
            <Button type="dashed" style={{ margin: "20px 0 20px 0" }}>
              <label htmlFor="fileArchitecture">Selecionar anexo</label>
            </Button>
          </Row>
          <Row
            justify="center"
            align="bottom"
            style={{ marginBottom: "-20px" }}
          >
            <Alert
              type="warning"
              showIcon
              message={`São aceitos apenas arquivos do tipo imagem(.png, .jpeg, .jpg)`}
              description={`O tamanho máximo permitido para cada arquivo é de 2MB`}
            />
          </Row>
          <Col>
            <input
              type="file"
              name="fileArchitecture"
              id="fileArchitecture"
              accept="image/png, image/jpeg"
              onChange={handleFileSelect}
              style={{ display: "none" }}
            />
          </Col>
        </Row>
      </Modal>
    </>
  );
};
