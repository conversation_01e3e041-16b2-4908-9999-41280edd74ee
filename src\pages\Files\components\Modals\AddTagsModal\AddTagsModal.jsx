import { But<PERSON>, <PERSON><PERSON>, <PERSON> } from "antd";
import { PlusOutlined, EditOutlined } from "@ant-design/icons";
import { TagsTable } from "./components/TagsTable";
import { useToggle } from "../../../../../hooks/useToggle";

export const AddTagsModal = () => {
  const [toggleModalValue, toggle] = useToggle();

  return (
    <>
      <Button type="default" onClick={toggle} icon={<EditOutlined />}>
        Tags
      </Button>
      {toggleModalValue && (
        <Modal
          title="Tags"
          open={toggleModalValue}
          width={"50%"}
          closable={false}
          centered
          footer={
            <Row justify={"end"}>
              <Button
                type="primary"
                onClick={() => {
                  toggle();
                }}
              >
                Ok
              </Button>
            </Row>
          }
        >
          <TagsTable />
        </Modal>
      )}
    </>
  );
};
