import { <PERSON><PERSON>, <PERSON>confirm, Row, Tag, Tooltip } from "antd";
import { differenceInMinutes } from "date-fns";
import { AllowAccess } from "..";
import { LikeOutlined } from "@ant-design/icons";
import * as switchRoleController from "../../../../pages/SwitchRoles/controllers/index";

export const SolicitationTimeTag = (props) => {
  const { active, item } = props;

  return (
    <>
      {active &&
      item.allowed &&
      item.time - differenceInMinutes(new Date(), new Date(item.updated_at)) >
        0 ? (
        <Row justify="center">
          <Tag color="green">
            Restam{" "}
            {item.time -
              differenceInMinutes(new Date(), new Date(item.updated_at))}
            min.
          </Tag>
        </Row>
      ) : active && item.allowed ? (
        <Row justify="center">
          <Tag color="red">Tempo Expirado</Tag>
        </Row>
      ) : !active && item.allowed ? (
        <Row justify="center">
          <Tag color="red">Tempo Expirado</Tag>
        </Row>
      ) : (
        <Row justify="center">
          <Tag color="red">Inativo</Tag>
        </Row>
      )}
    </>
  );
};

export const ActionsSwitchRoleCell = (props) => {
  const { item, confirm, setCurrentSolicitation, permissionSets, origin } =
    props;
  return (
    <>
      {!item.allowed && item.active === "Solicitado" ? (
        <AllowAccess
          getSolicitations={async () =>
            await switchRoleController.getAllSwitchRoleData(permissionSets)
          }
          data={item}
          origin={origin}
          permissionSets={permissionSets}
        />
      ) : (
        <Tooltip
          title="Remova o acesso deste usuário a esta conta"
          placement="left"
        >
          <Popconfirm
            placement="leftBottom"
            title="Tem certeza que deseja remover este acesso?"
            onConfirm={() => confirm(item, "revoke")}
            cancelText="Não"
            okText="Sim"
          >
            <Button type="danger" onClick={() => setCurrentSolicitation(item)}>
              <LikeOutlined />
            </Button>
          </Popconfirm>
        </Tooltip>
      )}
    </>
  );
};
