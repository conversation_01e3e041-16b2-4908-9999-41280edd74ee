/**
 * Dependency Injection Container
 * Implements Dependency Injection Pattern and Inversion of Control
 * Follows SOLID principles, especially Dependency Inversion Principle
 */

/**
 * Service Lifetime Enum
 */
export const ServiceLifetime = {
  SINGLETON: 'singleton',
  TRANSIENT: 'transient',
  SCOPED: 'scoped'
};

/**
 * Service Registration
 */
class ServiceRegistration {
  constructor(name, factory, lifetime = ServiceLifetime.SINGLETON, dependencies = []) {
    this.name = name;
    this.factory = factory;
    this.lifetime = lifetime;
    this.dependencies = dependencies;
    this.instance = null;
  }
}

/**
 * Dependency Injection Container
 */
class DIContainer {
  constructor() {
    this.services = new Map();
    this.scopedInstances = new Map();
    this.resolving = new Set(); // For circular dependency detection
  }

  /**
   * Register a service
   */
  register(name, factory, lifetime = ServiceLifetime.SINGLETON, dependencies = []) {
    if (typeof factory !== 'function') {
      throw new Error('Factory must be a function');
    }

    const registration = new ServiceRegistration(name, factory, lifetime, dependencies);
    this.services.set(name, registration);
    return this;
  }

  /**
   * Register a singleton service
   */
  registerSingleton(name, factory, dependencies = []) {
    return this.register(name, factory, ServiceLifetime.SINGLETON, dependencies);
  }

  /**
   * Register a transient service
   */
  registerTransient(name, factory, dependencies = []) {
    return this.register(name, factory, ServiceLifetime.TRANSIENT, dependencies);
  }

  /**
   * Register a scoped service
   */
  registerScoped(name, factory, dependencies = []) {
    return this.register(name, factory, ServiceLifetime.SCOPED, dependencies);
  }

  /**
   * Register an instance directly
   */
  registerInstance(name, instance) {
    const factory = () => instance;
    return this.register(name, factory, ServiceLifetime.SINGLETON);
  }

  /**
   * Register a class
   */
  registerClass(name, ClassConstructor, lifetime = ServiceLifetime.SINGLETON, dependencies = []) {
    const factory = (...deps) => new ClassConstructor(...deps);
    return this.register(name, factory, lifetime, dependencies);
  }

  /**
   * Resolve a service
   */
  resolve(name, scope = null) {
    if (this.resolving.has(name)) {
      throw new Error(`Circular dependency detected: ${Array.from(this.resolving).join(' -> ')} -> ${name}`);
    }

    const registration = this.services.get(name);
    if (!registration) {
      throw new Error(`Service '${name}' is not registered`);
    }

    this.resolving.add(name);

    try {
      return this.createInstance(registration, scope);
    } finally {
      this.resolving.delete(name);
    }
  }

  /**
   * Create service instance based on lifetime
   */
  createInstance(registration, scope) {
    const { name, factory, lifetime, dependencies } = registration;

    switch (lifetime) {
      case ServiceLifetime.SINGLETON:
        if (!registration.instance) {
          registration.instance = this.instantiate(factory, dependencies, scope);
        }
        return registration.instance;

      case ServiceLifetime.SCOPED:
        if (!scope) {
          throw new Error(`Scoped service '${name}' requires a scope`);
        }
        
        const scopeKey = `${scope}_${name}`;
        if (!this.scopedInstances.has(scopeKey)) {
          this.scopedInstances.set(scopeKey, this.instantiate(factory, dependencies, scope));
        }
        return this.scopedInstances.get(scopeKey);

      case ServiceLifetime.TRANSIENT:
        return this.instantiate(factory, dependencies, scope);

      default:
        throw new Error(`Unknown service lifetime: ${lifetime}`);
    }
  }

  /**
   * Instantiate service with dependencies
   */
  instantiate(factory, dependencies, scope) {
    const resolvedDependencies = dependencies.map(dep => this.resolve(dep, scope));
    return factory(...resolvedDependencies);
  }

  /**
   * Check if service is registered
   */
  isRegistered(name) {
    return this.services.has(name);
  }

  /**
   * Get all registered service names
   */
  getRegisteredServices() {
    return Array.from(this.services.keys());
  }

  /**
   * Clear scoped instances for a specific scope
   */
  clearScope(scope) {
    const keysToDelete = [];
    for (const key of this.scopedInstances.keys()) {
      if (key.startsWith(`${scope}_`)) {
        keysToDelete.push(key);
      }
    }
    keysToDelete.forEach(key => this.scopedInstances.delete(key));
  }

  /**
   * Clear all instances (useful for testing)
   */
  clear() {
    this.services.clear();
    this.scopedInstances.clear();
    this.resolving.clear();
  }

  /**
   * Create a child container (for scoping)
   */
  createScope() {
    return new ScopedContainer(this);
  }
}

/**
 * Scoped Container
 * Inherits from parent container but maintains its own scoped instances
 */
class ScopedContainer {
  constructor(parentContainer) {
    this.parent = parentContainer;
    this.scopeId = Math.random().toString(36).substr(2, 9);
    this.scopedInstances = new Map();
  }

  resolve(name) {
    return this.parent.resolve(name, this.scopeId);
  }

  isRegistered(name) {
    return this.parent.isRegistered(name);
  }

  dispose() {
    this.parent.clearScope(this.scopeId);
    this.scopedInstances.clear();
  }
}

/**
 * Service Decorator
 * Decorator for automatic service registration
 */
export function Service(name, dependencies = [], lifetime = ServiceLifetime.SINGLETON) {
  return function(target) {
    // Store metadata on the class
    target._serviceName = name;
    target._serviceDependencies = dependencies;
    target._serviceLifetime = lifetime;
    
    return target;
  };
}

/**
 * Injectable Decorator
 * Marks a class as injectable
 */
export function Injectable(dependencies = []) {
  return function(target) {
    target._injectable = true;
    target._dependencies = dependencies;
    return target;
  };
}

/**
 * Auto-registration helper
 */
export function autoRegister(container, classes) {
  classes.forEach(ClassConstructor => {
    if (ClassConstructor._serviceName) {
      container.registerClass(
        ClassConstructor._serviceName,
        ClassConstructor,
        ClassConstructor._serviceLifetime,
        ClassConstructor._serviceDependencies
      );
    } else if (ClassConstructor._injectable) {
      const name = ClassConstructor.name;
      container.registerClass(
        name,
        ClassConstructor,
        ServiceLifetime.SINGLETON,
        ClassConstructor._dependencies
      );
    }
  });
}

/**
 * React Hook for Dependency Injection
 */
import { useContext, createContext } from 'react';

const DIContext = createContext(null);

export const DIProvider = ({ container, children }) => {
  return (
    <DIContext.Provider value={container}>
      {children}
    </DIContext.Provider>
  );
};

export const useDI = () => {
  const container = useContext(DIContext);
  if (!container) {
    throw new Error('useDI must be used within a DIProvider');
  }
  return container;
};

export const useService = (serviceName) => {
  const container = useDI();
  return container.resolve(serviceName);
};

/**
 * Higher-Order Component for Dependency Injection
 */
export const withDI = (serviceNames = []) => {
  return function(WrappedComponent) {
    return function DIComponent(props) {
      const container = useDI();
      const services = {};
      
      serviceNames.forEach(serviceName => {
        services[serviceName] = container.resolve(serviceName);
      });

      return <WrappedComponent {...props} services={services} />;
    };
  };
};

/**
 * Singleton DI Container
 */
class DIManager {
  constructor() {
    if (DIManager.instance) {
      return DIManager.instance;
    }
    
    this.container = new DIContainer();
    DIManager.instance = this;
  }

  static getInstance() {
    if (!DIManager.instance) {
      DIManager.instance = new DIManager();
    }
    return DIManager.instance;
  }

  getContainer() {
    return this.container;
  }
}

// Export singleton instance
export const diManager = DIManager.getInstance();
export const container = diManager.getContainer();

export default DIContainer;
