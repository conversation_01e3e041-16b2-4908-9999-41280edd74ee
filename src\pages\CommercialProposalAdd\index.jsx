import React, { useEffect, useState, useContext } from "react";
import { SideMenu } from "../../components/SideMenu";
import { HeaderMenu } from "../../components/HeaderMenu";
import {
  Layout,
  Card,
  Row,
  Col,
  Input,
  Form,
  Select,
  Button,
  message,
  Popconfirm,
} from "antd";
import {
  PlusOutlined,
  DeleteOutlined,
  ArrowLeftOutlined,
  LoadingOutlined,
  SaveOutlined,
} from "@ant-design/icons";
import { MainCollapse } from "../../components/Collapse/CommercialProposal";
import { TableProposals } from "../../components/Table/Proposals/TableProposals";
import { ProposalHeader } from "../../components/ProposalHeader";
import RichTextEditor from "../../components/RichTextEditor/RichTextEditor";
import { AdditionalCostsContent } from "../../components/Proposals/AdditionalCostsContent";
import { useLocation, useNavigate } from "react-router-dom";
import { AddDocument } from "../../components/Modals/TechnicalProposals/AddDocument";
import { v4 } from "uuid";
import Cookies from "js-cookie";
import { DisableIncrementScroll } from "../../hooks/DisableIncrementScroll";
import { dynamoGet } from "../../service/apiDsmDynamo";
import { invalidInputNumberChars } from "../../constants/invalidCharsInputNumber";
import { ProposalsCards } from "../../components/Proposals/ProposalsCards";
import { handleSubmitClick } from "../../hooks/ProposalSubmit";
import { realMask } from "../../utils/masks";
import { AddHourValueCardDaredeInvestments } from "../../components/ProposalsCollapses/AddHourValueCardDaredeInvestments";
import { AddHourBurstCard } from "../../components/ProposalsCollapses/AddHoursBurstCard";
import { contractTimeOptions } from "../../constants/contractTimeOptions";
import { TotalCalculatorCards } from "../../components/ProposalsCollapses/TotalCalculatorCards";
// import { totalValuesController } from "../../controllers/Proposals/totalValues";
import { totalValuesController } from "../../controllers/Proposals/newTotalValuesTechnical";
import { getProposalSettings } from "../../controllers/Proposals/getProposalSettings";
import { submitCommercialController } from "../../controllers/Proposals/sumbitCommercialAdd";
import {
  ProposalProvider,
  ProposalContext,
} from "../../contexts/totalValuesProposals";
import { TotalValuesProposalType } from "../../contexts/reducers/totalValuesProposal";
import {
  DataCookieContext,
  DataCookieProvider,
} from "../../contexts/data-cookie";
import { PriceInput } from "../../components/ProposalsMoneyInput/proposalMoneyInput";
import { AddArchitectureDocument } from "../../components/Modals/TechnicalProposals/AddArchitectureDocument";
import { AddScenarioDocument } from "../../components/Modals/TechnicalProposals/AddScenarioDocument";
import { AddArchitectureDocumentCommercial } from "../../components/Modals/TechnicalProposals/AddArchitectureDocumentCommercial";
import { AddScenarioDocumentCommercial } from "../../components/Modals/TechnicalProposals/AddScenarioDocumentCommercial";
import { awsRegionsArr } from "../../constants/awsRegionNames";

export const CommercialProposalRfs = () => {
  return (
    <DataCookieProvider>
      <CommercialProposalAdd />
    </DataCookieProvider>
  );
};

export const CommercialProposalAdd = () => {
  return (
    <ProposalProvider>
      <CommercialProposalAddPage />
    </ProposalProvider>
  );
};

export const CommercialProposalAddPage = () => {
  let mainSearchId = v4();
  const [add_proposal] = Form.useForm();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const { cookieState, setCookieState } = useContext(DataCookieContext);
  const { proposalState, setProposalState } = useContext(ProposalContext);
  const { Content } = Layout;
  const { Option } = Select;
  const [services, setServices] = useState([]);
  const [collapsed, setCollapsed] = useState(false);
  const [currentContacts, setCurrentContacts] = useState([]);
  const [recognizingFormList, setRecognizingFormList] = useState(false);
  const [paymentDarede, setPaymentDarede] = useState([]);
  const [costManagementData, setCostManagementData] = useState([]);
  const [isInvestmentList, setIsInvestmentList] = useState(false);
  const [initializedServicesState, setInitializedServicesState] =
    useState(false);

  const [draftItems, setDraftItems] = useState({
    challenges: "",
    scenarios: "",
    premisses: "",
    extra_points: "",
    architecture: "",
    main_factors: "",
    expected_results: "",
    observations: "",
    internal_notes: "",
  });

  const [customerBrand, setCustomerBrand] = useState("");
  const [regions, setRegions] = useState([]);

  const selectAfter = (
    <Select
      loading={paymentDarede?.length === 0 ? true : false}
      placeholder="Selecione uma forma de pagamento"
      defaultValue={paymentDarede[0]?.id}
      onSelect={(e) => {
        add_proposal.setFieldsValue({ payment_method: e });
        setProposalState({
          type: TotalValuesProposalType.SET_PAYMENT_METHOD,
          value: e,
        });
      }}
    >
      {paymentDarede?.map((item, index) => {
        return item.active === true ? (
          <Option value={item.id}>{item.name}</Option>
        ) : null;
      })}
    </Select>
  );

  const handleDraftItems = (itemName, value) => {
    const items = { ...draftItems };
    items[itemName] = value;
    setDraftItems(items);
  };

  const handleMonthSelect = (month) => {
    setProposalState({
      type: TotalValuesProposalType.SET_MONTH,
      value: month,
    });
  };

  const handleTotalValues = async () => {
    const newTotalValues = await totalValuesController(
      proposalState,
      costManagementData,
      services
    );

    setProposalState({
      type: TotalValuesProposalType.SET_TOTAL_VALUES,
      value: newTotalValues,
    });
  };

  const handleChangeAwsCostRegion = (e, index) => {
    let data = add_proposal.getFieldValue("main_aws_investments");
    data[index] = {
      ...data[index],
      regions: e,
    };
    add_proposal.setFieldsValue({ main_aws_investments: data });
    setRegions(data);
  };

  const collapseContent = [
    {
      name: "Desafios",
      itemName: "challenges",
      content: (
        <Form.Item
          name="proposal_challenge"
          initialValue={draftItems.challenges}
        >
          <RichTextEditor
            value={draftItems.challenges}
            handleDraftItems={handleDraftItems}
            itemName="challenges"
          />
        </Form.Item>
      ),
    },
    {
      name: "Cenário apresentado",
      itemName: "scenarios",
      content: (
        <>
          <Form.Item
            name="proposal_scenario"
            initialValue={draftItems.scenarios}
          >
            <RichTextEditor
              value={draftItems.scenarios}
              handleDraftItems={handleDraftItems}
              itemName="scenarios"
            />
          </Form.Item>
          <Row justify="center">
            <AddScenarioDocumentCommercial />
          </Row>
        </>
      ),
    },
    {
      name: "Premissas",
      itemName: "premisses",
      content: (
        <Form.Item
          name="proposal_premisses"
          initialValue={draftItems.premisses}
        >
          {!loading ? (
            <RichTextEditor
              value={draftItems.premisses}
              handleDraftItems={handleDraftItems}
              itemName="premisses"
            />
          ) : (
            <Row justify="center">
              <LoadingOutlined />
            </Row>
          )}
        </Form.Item>
      ),
    },
    {
      name: "Pontos não contemplados",
      itemName: "extra_points",
      content: (
        <Form.Item
          name="proposal_extra_points"
          initialValue={draftItems.extra_points}
        >
          <div className="preview-container" id="no-covered-container"></div>
          {!loading ? (
            <RichTextEditor
              value={draftItems.extra_points}
              handleDraftItems={handleDraftItems}
              itemName="extra_points"
            />
          ) : (
            <Row justify="center">
              <LoadingOutlined />
            </Row>
          )}
        </Form.Item>
      ),
    },
    {
      name: "Arquitetura",
      itemName: "architecture",
      content: (
        <>
          <Form.Item
            name="proposal_architecture"
            initialValue={draftItems.architecture}
          >
            <RichTextEditor
              value={draftItems.architecture}
              handleDraftItems={handleDraftItems}
              itemName="architecture"
            />
          </Form.Item>
          <Row justify="center">
            <AddArchitectureDocumentCommercial />
          </Row>
        </>
      ),
    },
    {
      name: "Calculadora",
      content: (
        <Form.List name="main_aws_investments">
          {(main_aws_investments, { add, remove }) => (
            <>
              {main_aws_investments.map((field, index) => (
                <Form.Item required={false} key={field.key}>
                  <Row gutter={24}>
                    <Col span={24}>
                      <Card
                        style={{
                          backgroundColor: "#F6F6F6",
                          boxShadow: "0 0 5px rgba(0,0,0,0.1)",
                          borderRadius: "5px",
                          marginRight: "10px",
                        }}
                      >
                        <Row gutter={24}>
                          <Col span={24}>
                            <Form.Item
                              label="Título"
                              {...field}
                              validateTrigger={["onChange", "onBlur"]}
                              name={[field.name, "title"]}
                              required={true}
                            >
                              <Input
                                onChange={() =>
                                  setRecognizingFormList(!recognizingFormList)
                                }
                              />
                            </Form.Item>
                          </Col>
                          <Col lg={8} md={24}>
                            <Form.Item
                              label="Valor mensal"
                              {...field}
                              validateTrigger={["onChange", "onBlur"]}
                              name={[field.name, "month_value"]}
                              required={true}
                            >
                              <PriceInput
                                index={index}
                                form={add_proposal}
                                formListName="main_aws_investments"
                                fieldName={"month_value"}
                              />
                            </Form.Item>
                          </Col>
                          <Col lg={8} md={24}>
                            <Form.Item
                              label="Valor anual"
                              {...field}
                              validateTrigger={["onChange", "onBlur"]}
                              name={[field.name, "year_value"]}
                              required={true}
                            >
                              <PriceInput
                                index={index}
                                form={add_proposal}
                                formListName="main_aws_investments"
                                fieldName={"year_value"}
                              />
                            </Form.Item>
                          </Col>
                          <Col lg={8} md={24}>
                            <Form.Item
                              label="Valor Upfront"
                              {...field}
                              validateTrigger={["onChange", "onBlur"]}
                              name={[field.name, "upfront_value"]}
                            >
                              <PriceInput
                                index={index}
                                form={add_proposal}
                                formListName="main_aws_investments"
                                fieldName={"upfront_value"}
                              />
                            </Form.Item>
                          </Col>
                        </Row>
                        <Row>
                          <Col span={24}>
                            <Form.Item
                              label="Link Público"
                              {...field}
                              validateTrigger={["onChange", "onBlur"]}
                              name={[field.name, "calculator_link"]}
                              required={true}
                              rules={[
                                {
                                  type: "url",
                                  message: "Insira um link válido",
                                },
                              ]}
                            >
                              <Input
                                onChange={() =>
                                  setRecognizingFormList(!recognizingFormList)
                                }
                              />
                            </Form.Item>
                          </Col>
                        </Row>
                        <Row>
                          <Col span={24}>
                            <Form.Item label="Regiões">
                              <Select
                                // loading={loading}
                                mode="multiple"
                                value={regions[index]?.regions}
                                showSearch
                                placeholder={
                                  "Regiões consideradas na estimativa..."
                                }
                                optionFilterProp="children"
                                filterOption={(input, option) =>
                                  option?.children
                                    ?.toLowerCase()
                                    .includes(input?.toLowerCase())
                                }
                                filterSort={(optionA, optionB) =>
                                  optionA?.children
                                    ?.toLowerCase()
                                    .localeCompare(
                                      optionB.children?.toLowerCase()
                                    )
                                }
                                onChange={(e) =>
                                  handleChangeAwsCostRegion(e, index)
                                }
                                style={{ width: "100%" }}
                              >
                                {awsRegionsArr.map((value, key) => (
                                  <Option value={value.code} key={key}>
                                    {`${value.name} / ${value.code}`}
                                  </Option>
                                ))}
                              </Select>
                            </Form.Item>
                          </Col>
                        </Row>
                        <Row justify="end">
                          <Col>
                            <Button
                              danger
                              type="text"
                              onClick={() => {
                                remove(field.name);
                                message.warning("Custo AWS removido");
                              }}
                            >
                              Remover item
                              <DeleteOutlined />
                            </Button>
                          </Col>
                        </Row>
                      </Card>
                    </Col>
                  </Row>
                </Form.Item>
              ))}
              <Button type="text" onClick={() => add()}>
                Adicionar Custo AWS
                <PlusOutlined />
              </Button>
            </>
          )}
        </Form.List>
      ),
    },
    {
      name: "Custos adicionais",
      content: <AdditionalCostsContent add_proposal={add_proposal} />,
    },
    {
      name: "Fatores influenciadores",
      itemName: "main_factors",
      content: (
        <Form.Item
          name="proposal_main_factors"
          initialValue={draftItems.main_factors}
        >
          <div className="preview-container" id="factor-container"></div>
          {!loading ? (
            <RichTextEditor
              value={draftItems.main_factors}
              handleDraftItems={handleDraftItems}
              itemName="main_factors"
            />
          ) : (
            <Row justify="center">
              <LoadingOutlined />
            </Row>
          )}
        </Form.Item>
      ),
    },
    {
      name: "Resultados esperados",
      itemName: "expected_results",
      content: (
        <Form.Item
          name="proposal_expected_results"
          initialValue={draftItems.expected_results}
        >
          <RichTextEditor
            value={draftItems.expected_results}
            handleDraftItems={handleDraftItems}
            itemName="expected_results"
          />
        </Form.Item>
      ),
    },
    {
      name: "Investimentos Darede",
      itemName: "darede_investment",
      content: (
        <>
          <Row>
            <Col span={24}>
              <Form.Item
                label="Tempo de contrato"
                name="contract_time"
                rules={[
                  {
                    required: true,
                    message: "Selecione o tempo de contrato!",
                  },
                ]}
              >
                <Select
                  mode="multiple"
                  placeholder="Selecione o tempo de contrato"
                  onChange={(e) => handleMonthSelect(e)}
                >
                  {contractTimeOptions.map((option) => {
                    return <Option value={option.value}>{option.label}</Option>;
                  })}
                </Select>
              </Form.Item>
              <Form.Item
                label="Pagamento"
                name="payment_method"
                initialValue={proposalState.paymentMethod}
              >
                {selectAfter}
              </Form.Item>
            </Col>
          </Row>
        </>
      ),
    },
    {
      name: "Observações",
      itemName: "observations",
      content: (
        <Form.Item name="proposal_observations">
          <RichTextEditor
            value={draftItems.observations}
            handleDraftItems={handleDraftItems}
            itemName="observations"
          />
        </Form.Item>
      ),
    },
    {
      name: "Notas Internas",
      itemName: "internal_notes",
      content: (
        <Form.Item name="proposal_internal_notes">
          <RichTextEditor
            value={draftItems.internal_notes}
            handleDraftItems={handleDraftItems}
            itemName="internal_notes"
          />
        </Form.Item>
      ),
      formName: "proposal_internal_notes",
    },
  ];

  const setInitialTextInCollapses = async () => {
    const res = await getProposalSettings();
    setTimeout(() => {}, 500);

    setDraftItems({
      ...draftItems,
      ...res,
    });
  };

  const Submit = async (data) => {
    setLoading(true);
    const user = {
      name: localStorage.getItem("@dsm/username"),
      mail: localStorage.getItem("@dsm/mail"),
    };

    const res = await submitCommercialController(
      add_proposal,
      draftItems,
      currentContacts,
      services,
      proposalState,
      customerBrand,
      proposalState.idFixed,
      user
    );

    if (res) {
      await cleanLocalStorage(collapseContent);
      setLoading(false);
      navigate(-1);
    } else {
      setLoading(false);
    }
  };

  async function cleanLocalStorage(collapseContent) {
    localStorage.setItem("technical-proposal/services", "");
    localStorage.setItem("technical-proposal/form", "");
    localStorage.setItem("technical-proposal/customer", "");
    localStorage.setItem("technical-proposal/fileList", "");
    localStorage.setItem("technical-proposal/architectureFileList", "");
    localStorage.setItem("CollapseValidator", "");

    collapseContent.map((item) => {
      return item.itemName ? localStorage.setItem(item.itemName, "") : null;
    });
  }

  const handleLocalStorageCollapseItems = () => {
    if (
      mainSearchId &&
      !localStorage.getItem("technical-proposal/mainSearchId")
    ) {
      localStorage.setItem("technical-proposal/mainSearchId", mainSearchId);
    }

    if (localStorage.getItem("technical-proposal/services")) {
      setServices(
        JSON.parse(localStorage.getItem("technical-proposal/services"))
      );
    }

    if (localStorage.getItem("technical-proposal/architectureFileList")) {
      add_proposal.setFieldsValue(
        JSON.parse(
          localStorage.getItem("technical-proposal/architectureFileList")
        )
      );
    }
  };

  useEffect(() => {
    setLoading(true);

    dynamoGet(`${process.env.REACT_APP_STAGE}-cost-management`).then((data) => {
      const res = data.find(
        (d) => d.id === `${process.env.REACT_APP_COST_MANAGEMENT_OBJ_ID}`
      );

      setCostManagementData(res);

      setPaymentDarede(res?.paymentMethods);
    });

    handleLocalStorageCollapseItems();

    handleTotalValues();

    setInitialTextInCollapses();

    DisableIncrementScroll();
    setLoading(false);
  }, []);

  return (
    <Layout style={{ minHeight: "100vh" }}>
      <HeaderMenu collapsed={collapsed} setCollapsed={setCollapsed} />
      <Layout>
        <SideMenu collapsed={collapsed} />
        <Content style={{ padding: "2em" }}>
          <Card
            style={{
              boxShadow: "0 0 10px rgba(0,0,0,0.1)",
              borderRadius: "20px",
              marginRight: "10px",
            }}
          >
            <Popconfirm
              title="Ao voltar você perderá os dados do formulário, tem certeza?"
              onConfirm={() => navigate(-1)}
            >
              <Button
                type="text"
                icon={<ArrowLeftOutlined />}
                style={{ marginBottom: "15px" }}
              >
                Criar proposta
              </Button>
            </Popconfirm>

            <Row>
              <Col span={24}>
                <Card
                  style={{
                    backgroundColor: "#f4f4f4",
                    boxShadow: "0px 4px 4px rgba(0, 0, 0, .5)",
                    borderRadius: "5px",
                  }}
                >
                  <Form
                    layout="vertical"
                    onFinish={(data) => Submit(data)}
                    form={add_proposal}
                    initialValues={{
                      paymentMethod: paymentDarede[0]?.id,
                      main_aws_investments: [],
                      aditional_costs: [],
                    }}
                  >
                    <Row gutter={16}>
                      <ProposalHeader
                        currentContacts={currentContacts}
                        setCurrentContacts={(e) => setCurrentContacts(e)}
                        setBrand={(image) => setCustomerBrand(image)}
                      />
                    </Row>

                    <Row gutter={24}>
                      {collapseContent.map((collapseItem, collapseItemKey) => {
                        return (
                          <Col span={24} key={collapseItemKey}>
                            <MainCollapse
                              title={collapseItem.name}
                              editor={collapseItem.content}
                            />
                          </Col>
                        );
                      })}
                    </Row>
                  </Form>
                  <TableProposals
                    state={[
                      services,
                      add_proposal.getFieldValue("client_name"),
                      "commercial",
                    ]}
                    setServices={(services) => setServices(services)}
                  />

                  <Row gutter={24} justify="space-between">
                    <Col sapn={6}>
                      <AddDocument />
                    </Col>
                    <Col sapn={6}>
                      <Button
                        type="primary"
                        onClick={() => handleSubmitClick(add_proposal)}
                        loading={loading}
                      >
                        Salvar
                        <SaveOutlined />
                      </Button>
                    </Col>
                  </Row>
                </Card>
              </Col>
            </Row>
          </Card>
        </Content>
      </Layout>
    </Layout>
  );
};
