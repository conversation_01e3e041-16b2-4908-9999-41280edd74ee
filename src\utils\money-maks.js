export function moneyMask(value) {
    let negativo = false;

    if (value) {

        if (value.includes("-"))
            negativo = true;

        value = value.toString().replace(/\D/g, "");

        if (value.length <= 2)
            value = value.replace(/(\d{2})/g, "$1");
        else if (value.length === 3)
            value = value.replace(/(\d{1})(\d{2})/g, "$1,$2");
        else if (value.length === 4)
            value = value.replace(/(\d{2})(\d{2})/g, "$1,$2");
        else if (value.length === 5)
            value = value.replace(/(\d{3})(\d{2})/g, "$1,$2");
        else if (value.length === 6)
            value = value.replace(/(\d{1})(\d{3})(\d{2})/g, "$1.$2,$3");
        else if (value.length === 7)
            value = value.replace(/(\d{2})(\d{3})(\d{2})/g, "$1.$2,$3");
        else if (value.length === 8)
            value = value.replace(/(\d{3})(\d{3})(\d{2})/g, "$1.$2,$3");
        else if (value.length === 9)
            value = value.replace(/(\d{1})(\d{3})(\d{3})(\d{2})/g, "$1.$2.$3,$4");
        else if (value.length === 10)
            value = value.replace(/(\d{2})(\d{3})(\d{3})(\d{2})/g, "$1.$2.$3,$4");
        else if (value.length === 11)
            value = value.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/g, "$1.$2.$3,$4");
    }

    if (negativo)
        value = "-" + value;

    return value
}