/**
 * Supressão específica para warnings do Antd relacionados ao findDOMNode
 * Esta é uma solução temporária até que o Antd seja atualizado para React 18
 */

// Interceptar warnings específicos do React relacionados ao Antd
const suppressAntdFindDOMNodeWarnings = () => {
  // Armazenar console original
  const originalError = console.error;
  
  console.error = function(...args) {
    // Converter argumentos para string
    const message = args.join(' ');
    
    // Lista de padrões específicos de warnings do Antd
    const antdWarningPatterns = [
      // findDOMNode warnings
      'Warning: findDOMNode is deprecated and will be removed in the next major release',
      'Instead, add a ref directly to the element you want to reference',
      'Learn more about using refs safely here: https://reactjs.org/link/strict-mode-find-node',
      'at SingleObserver',
      'at ResizeObserver',
      'at Tooltip',
      'at MenuItem',
      'findDOMNode is deprecated',
      'findDOMNode was passed an instance',
      'which is inside StrictMode',
      'add a ref directly to the element you want to reference',

      // Antd deprecation warnings
      'Warning: [antd: Menu] `children` is deprecated',
      'Please use `items` instead',
      'Warning: [antd: Modal] `visible` is deprecated',
      'Please use `open` instead',
      'Warning: [antd:',
      'is deprecated',
      '`children` is deprecated',
      '`visible` is deprecated',
      'Please use `items` instead',
      'Please use `open` instead'
    ];
    
    // Verificar se a mensagem contém algum dos padrões do Antd
    const shouldSuppress = antdWarningPatterns.some(pattern =>
      message.includes(pattern)
    );
    
    // Se não deve ser suprimido, chamar o console.error original
    if (!shouldSuppress) {
      originalError.apply(console, args);
    }
    // Caso contrário, suprimir silenciosamente
  };
};

// Aplicar supressão imediatamente
suppressAntdFindDOMNodeWarnings();

// Exportar para uso manual se necessário
export { suppressAntdFindDOMNodeWarnings };

// Log apenas em desenvolvimento
if (process.env.NODE_ENV === 'development') {
  console.log('🔇 Antd findDOMNode warnings suppressed');
}
