import { Button, Modal, Form, Checkbox, Row, Col } from "antd";
import { MoreOutlined } from "@ant-design/icons";
import { useState } from "react";

export const EditTableCall = (props) => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const { info_call, setInfoCall } = props;
  const [form] = Form.useForm();

  const showModal = () => {
    setIsModalVisible(true);
  };

  const handleOk = () => {
    form.submit();
  };

  const handleCancel = () => {
    setIsModalVisible(false);
  };

  const handleSubmit = async (data) => {
    setInfoCall(
      info_call.filter((a) => {
        if (data.lista.includes(a.code)) {
          return a.code;
        }
      })
    );
    setIsModalVisible(false);
  };

  return (
    <>
      <Button
        type="primary"
        icon={<MoreOutlined />}
        onClick={showModal}
        style={{ boxShadow:'none', height: 'auto' }}
      ></Button>
      <Modal
        title="Configuração"
        open={isModalVisible}
        onOk={handleOk}
        onCancel={handleCancel}
      >
        <Form layout="vertical" onFinish={handleSubmit} form={form}>
          <Form.Item name="lista" label="Fila selecionada:">
            <Checkbox.Group
              style={{
                width: "100%",
              }}
            >
              <Row>
                <Col span={8}>
                  <Checkbox value="type">Tipo</Checkbox>
                </Col>
                <Col span={8}>
                  <Checkbox value="age">Idade</Checkbox>
                </Col>
                <Col span={8}>
                  <Checkbox value="create">Criado</Checkbox>
                </Col>
                <Col span={8}>
                  <Checkbox value="state">Estado</Checkbox>
                </Col>
                <Col span={8}>
                  <Checkbox value="priority">Prioridade</Checkbox>
                </Col>
                <Col span={8}>
                  <Checkbox value="queue">Fila</Checkbox>
                </Col>
                <Col span={8}>
                  <Checkbox value="service">Serviço</Checkbox>
                </Col>
                <Col span={8}>
                  <Checkbox value="update_time">Prazo de atualização</Checkbox>
                </Col>
                <Col span={8}>
                  <Checkbox value="owner">Proprietario</Checkbox>
                </Col>
              </Row>
            </Checkbox.Group>
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};
