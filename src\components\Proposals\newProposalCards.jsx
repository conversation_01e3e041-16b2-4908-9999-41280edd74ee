import { Card, Col, Row, Typography } from "antd";
import { shallowEqual, useSelector } from "react-redux";
import { formatHourName } from "../../controllers/Proposals/formatHourName";

export const ProposalsCards = ({ hourClass }) => {
  const totalValues = useSelector(
    (state) => state.technicalProposal.totalValues,
    shallowEqual
  );

  const { Title, Text } = Typography;

  const filteredTotalValues = totalValues?.find(
    (v) => v.hourClass === hourClass
  );

  const filteredTotalValuesByMonth = filteredTotalValues?.values?.find(
    (v) => v.month === "12"
  );

  return (
    <Row justify="space-between">
      {filteredTotalValuesByMonth?.values?.map((t) => {
        if (t.type !== "SEC") {
          return (
            <Col lg={5} md={11} sm={22}>
              <Card
                style={{
                  textAlign: "center",
                  backgroundColor: t.type === "SETUP" ? "#BEBFC1" : "#bebfc10",
                  borderRadius: "5px",
                }}
              >
                <Text>{formatHourName(t.formName)}</Text>
                <Title level={3}>{t.totalHours}</Title>
              </Card>
            </Col>
          );
        }
      })}
    </Row>
  );
};
