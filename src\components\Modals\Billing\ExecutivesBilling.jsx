import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Tag, Toolt<PERSON>, Col, Row } from "antd";
import { UserOutlined } from "@ant-design/icons";
import React, { useState } from "react";

export const ExecutivesBilling = (props) => {
  const { executives } = props;
  const [showModal, setShowModal] = useState(false);

  const closeModal = () => {
    setShowModal(false);
  };

  const columns = [
    {
      dataIndex: "email",
      title: "Email",
    },
    {
      dataIndex: "type",
      title: "Tipo",
    },
    {
      title: "Ativo",
      dataIndex: "active",
      key: "active",
      width: "1%",
      render: (a) => {
        if (a === 1) {
          return <Tag color="green">Ativo</Tag>;
        } else {
          return <Tag color="red">Inativo</Tag>;
        }
      },
      sorter: (a, b) => a.active - b.active,
      sortDirections: ["descend", "ascend"],
    },
  ];

  return (
    <>
      <Row justify="center">
        <Col>
          <Tooltip title="Visualize os executivos deste contrato">
            <Button
              style={{
                color: executives?.length ? "#0f9347" : "#333",
              }}
              type="text"
              onClick={async () => {
                setShowModal(true);
              }}
            >
              <UserOutlined />
            </Button>
          </Tooltip>
        </Col>
      </Row>
      <Modal
        width="60vw"
        title="Executivos"
        onCancel={closeModal}
        open={showModal}
        closable={false}
        footer={[
          <Button type="primary" onClick={closeModal}>
            Fechar
          </Button>,
        ]}
      >
        <Table
          scroll={{ x: "100%" }}
          dataSource={executives}
          columns={columns}
        />
      </Modal>
    </>
  );
};
