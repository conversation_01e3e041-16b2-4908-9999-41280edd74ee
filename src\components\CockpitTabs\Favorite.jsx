import { Col, Row } from 'antd'

export default function FavoriteTab(props) {

 /* Checking if the props.favorites is not null and if the length is not 0. */
    if (props.favorites && props.favorites.length !== 0) {
        return (
            <Row justify="space-between" gutter={[48, 20]}>
                {
                props.favorites.map((val, key) => {
                    return (
                        <Col span={12} key={key}>
                            {props.components[val.component]?.object}
                        </Col>
                    )
                })}
            </Row>
        )
    } else {
        return ( 
            <div>
                <h1>Não há favoritos</h1>
            </div>
        )
    }
}
