async function cleanLocalStorage(collapseContent) {
  localStorage.setItem("technical-proposal/services", "");
  localStorage.setItem("technical-proposal/form", "");
  localStorage.setItem("technical-proposal/customer", "");
  localStorage.setItem("technical-proposal/fileList", "");
  localStorage.setItem("technical-proposal/architectureFileList", "");
  localStorage.setItem("CollapseValidator", "");

  collapseContent.map((item) => {
    return item.itemName ? localStorage.setItem(item.itemName, "") : null;
  });
}
