import { useState } from "react";
import { SideMenu } from "../../components/SideMenu";
import { HeaderMenu } from "../../components/HeaderMenu";
import { Card, Layout } from "antd";
import { SwitchRoleAuditHeader } from "./components/header";
import { SolicitationsTable } from "./components/tables/solicitationsTable";
import { ConceededTable } from "./components/tables/conceededTable";

export const SwitchRolesAudits = () => {
  const [collapsed, setCollapsed] = useState(false);
  const [searchDate, setSearchDate] = useState(false);
  const [search, setSearch] = useState("");
  const [selectedTable, setSelectedTable] = useState("requested");
  const [exportData, setExportData] = useState([]);
  const [exportColumns, setExportColumns] = useState([]);
  const [searchTrigger, setSearchTrigger] = useState(0);
  const { Content } = Layout;

  return (
    <Layout style={{ minHeight: "100vh" }}>
      <HeaderMenu collapsed={collapsed} setCollapsed={setCollapsed} />
      <Layout>
        <SideMenu collapsed={collapsed} />
        <Content style={{ padding: "2em" }}>
          <Card
            style={{
              boxShadow: "0 0 10px rgba(0,0,0,0.1)",
              borderRadius: "20px",
              marginRight: "10px",
            }}
          >
            <SwitchRoleAuditHeader
              setSelectedTable={(value) => setSelectedTable(value)}
              setSearch={(value) => setSearch(value)}
              setSearchDate={(value) => setSearchDate(value)}
              exportData={exportData}
              setSearchTrigger={() => setSearchTrigger(prev => prev + 1)}
              exportColumns={exportColumns}
            />
            {selectedTable === "requested" ? (
              <SolicitationsTable
                search={search}
                searchDate={searchDate}
                searchTrigger={searchTrigger}
                setExportData={(value) => setExportData(value)}
                setExportColumns={(value) => setExportColumns(value)}
              />
            ) : (
              <ConceededTable
                search={search}
                searchDate={searchDate}
                searchTrigger={searchTrigger}
                setExportData={(value) => setExportData(value)}
                setExportColumns={(value) => setExportColumns(value)}
              />
            )}
          </Card>
        </Content>
      </Layout>
    </Layout>
  );
};
