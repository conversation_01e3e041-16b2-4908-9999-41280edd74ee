import React, { useState } from "react";
import { useSelector, shallowEqual } from "react-redux";
import { Row, Col, Form } from "antd";

import RichTextEditor from "../../../components/RichTextEditor/RichTextEditor";
import { MainCollapse } from "../../../components/Collapse/CommercialProposal";

import { setTechnicalProposalState } from "../../../store/actions/technical-proposal-action";
import { useEffect } from "react";
import { getPremisesSettings } from "../../../controllers/Proposals/getProposalSettings";

export const PremisseCollapce = ({ fieldName, formName, title, children }) => {
    const value = useSelector(state => state.technicalProposal[fieldName], shallowEqual)

    const [fixedValue, setFixedValue] = useState("")

    useEffect(() => {
        initalPage()
    }, [])
    
    const handleDraftItems = (itemName, value) => {
        setTechnicalProposalState({ field: fieldName, value })
    };

    async function initalPage() {
        const premises = await getPremisesSettings()
        setFixedValue(premises)
    }

    function openCollapse(isOpen, collapseName) {
        if (isOpen && title === collapseName && !value) {
            setTechnicalProposalState({ field: fieldName, value: fixedValue })
        }
    }

    return (
        <Row>
            <Col span={24} key={0}>
                <MainCollapse
                    title={title}
                    onChange={openCollapse}
                    editor={
                        <Form.Item
                            name={formName}
                            initialValue={value}
                        >
                            <RichTextEditor
                                value={value}
                                handleDraftItems={handleDraftItems}
                                itemName={fieldName}
                            />

                            <br />

                            {children}
                        </Form.Item>
                    }
                >
                </MainCollapse>
            </Col>
        </Row>
    )
}