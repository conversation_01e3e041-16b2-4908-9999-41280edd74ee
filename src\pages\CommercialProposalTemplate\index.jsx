import React, { useEffect, useState, useContext } from "react";
import { SideMenu } from "../../components/SideMenu";
import { HeaderMenu } from "../../components/HeaderMenu";
import {
  Layout,
  Card,
  Row,
  Col,
  Typography,
  Input,
  Form,
  Select,
  Button,
  Popconfirm,
} from "antd";
import {
  ArrowLeftOutlined,
  PlusOutlined,
  DeleteOutlined,
  EditOutlined,
} from "@ant-design/icons";
import { MainCollapse } from "../../components/Collapse/CommercialProposal";
import { ProposalHeader } from "../../components/ProposalHeader";
import RichTextEditor from "../../components/RichTextEditor/RichTextEditor";
import { useLocation, useNavigate } from "react-router-dom";
import { AddArchitectureDocument } from "../../components/Modals/TechnicalProposals/AddArchitectureDocument";
import { TableProposals } from "../../components/Table/Proposals/TableProposals";
import { AddDocument } from "../../components/Modals/TechnicalProposals/AddDocument";
import { dynamoGet } from "../../service/apiDsmDynamo";
import { DisableIncrementScroll } from "../../hooks/DisableIncrementScroll";
import { PriceInput } from "../../components/ProposalsMoneyInput/proposalMoneyInput";
import { ProposalsCards } from "../../components/Proposals/ProposalsCards";
import { handleSubmitClick } from "../../hooks/ProposalSubmit";
import { contractTimeOptions } from "../../constants/contractTimeOptions";
import { HourValueCardDaredeInvestments } from "../../components/ProposalsCollapses/HourValueCardDaredeInvestments";
import { AdditionalHourValueCardDaredeInvestments } from "../../components/ProposalsCollapses/AdditionalHourValueCardDaredeInvestments";
import { HourBurstCard } from "../../components/ProposalsCollapses/HoursBurstCard";
import { TotalCalculatorCards } from "../../components/ProposalsCollapses/TotalCalculatorCards";
import { submitCommercialController } from "../../controllers/Proposals/sumbitCommercialAdd";
import { totalValuesController } from "../../controllers/Proposals/newTotalValuesTechnical";
import {
  ProposalProvider,
  ProposalContext,
} from "../../contexts/totalValuesProposals";
import { TotalValuesProposalType } from "../../contexts/reducers/totalValuesProposal";
import { awsRegionsArr } from "../../constants/awsRegionNames";

export const CommercialProposalTemplate = () => {
  return (
    <ProposalProvider>
      <CommercialProposalPage />
    </ProposalProvider>
  );
};

export const CommercialProposalPage = () => {
  const { Content } = Layout;
  const { Title, Text } = Typography;
  const { Option } = Select;
  const { state } = useLocation();
  const [collapsed, setCollapsed] = useState(false);
  const [edit_proposal] = Form.useForm();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);

  const { proposalState, setProposalState } = useContext(ProposalContext);
  const [currentContacts, setCurrentContacts] = useState([]);
  const [costManagementData, setCostManagementData] = useState([]);
  const [recognizingFormList, setRecognizingFormList] = useState(false);
  const allHeaderData = (state, currentContacts);
  const [services, setServices] = useState(state.services);
  const [paymentDarede, setPaymentDarede] = useState([]);
  const [draftItems, setDraftItems] = useState({
    challenges: state?.specialFields[0]?.value,
    scenarios: state?.specialFields[1]?.value,
    premisses: state?.specialFields[2]?.value,
    extra_points: state?.specialFields[3]?.value,
    architecture: state?.specialFields[4]?.value,
    main_factors: state?.specialFields[5]?.value,
    expected_results: state?.specialFields[7]?.value,
    observations: state?.specialFields[8]?.value,
    internal_notes: state?.specialFields[9]?.value,
  });
  const [customerBrand, setCustomerBrand] = useState("");
  const [regions, setRegions] = useState(state?.awsCosts);

  const selectAfter = (
    <Select
      loading={paymentDarede?.length === 0 ? true : false}
      placeholder="Selecione uma forma de pagamento"
      defaultValue={
        state?.paymentMethod?.id
          ? state?.paymentMethod?.id
          : paymentDarede[0]?.id
      }
      onSelect={(e) => {
        edit_proposal.setFieldsValue("payment_method", e);
        setProposalState({
          type: TotalValuesProposalType.SET_PAYMENT_METHOD,
          value: e,
        });
      }}
    >
      {paymentDarede?.map((item, index) => {
        return <Option value={item.id}>{item.name}</Option>;
      })}
    </Select>
  );

  const handleDraftItems = (itemName, value) => {
    const items = { ...draftItems };
    items[itemName] = value;
    setDraftItems(items);
  };

  async function cleanLocalStorage(collapseContent) {
    localStorage.setItem("technical-proposal/services", "");
    localStorage.setItem("technical-proposal/form", "");
    localStorage.setItem("technical-proposal/customer", "");
    localStorage.setItem("technical-proposal/fileList", "");
    localStorage.setItem("technical-proposal/architectureFileList", "");
    localStorage.setItem("CollapseValidator", "");

    collapseContent.map((item) => {
      return item.itemName ? localStorage.setItem(item.itemName, "") : null;
    });
  }

  const Submit = async () => {
    setLoading(true);
    const user = localStorage.getItem("@dsm/username");

    await submitCommercialController(
      edit_proposal,
      draftItems,
      currentContacts,
      services,
      proposalState,
      customerBrand,
      proposalState.idFixed,
      user
    );
    await cleanLocalStorage(collapseContent);
    setLoading(false);
    navigate(-1);
  };

  const handleLocalStorageManagement = () => {
    if (localStorage.getItem("technical-proposal/services")) {
      setServices(
        JSON.parse(localStorage.getItem("technical-proposal/services"))
      );
    }
    if (localStorage.getItem("technical-proposal/form")) {
      edit_proposal.setFieldsValue(
        JSON.parse(localStorage.getItem("technical-proposal/form"))
      );
    }
    localStorage.setItem("oportunities", JSON.stringify(state?.opportunity));

    DisableIncrementScroll();
  };

  const handleTotalValues = async () => {
    handleLocalStorageManagement();

    const newTotalValues = await totalValuesController(
      proposalState,
      costManagementData,
      services
    );

    setProposalState({
      type: TotalValuesProposalType.SET_TOTAL_VALUES,
      value: newTotalValues,
    });
  };

  const handleMonthSelect = (month) => {
    setProposalState({
      type: TotalValuesProposalType.SET_MONTH,
      value: month,
    });
  };

  const handleChangeAwsCostRegion = (e, index) => {
    let data = edit_proposal.getFieldValue("main_aws_investments");
    data[index] = {
      ...data[index],
      regions: e,
    };
    edit_proposal.setFieldsValue("main_aws_investments", data);
    setRegions(data);
  };

  const collapseContent = [
    {
      name: "Desafios",
      itemName: "challenges",
      content: (
        <Form.Item name="proposal_challenge">
          <RichTextEditor
            value={state.specialFields[0].value}
            handleDraftItems={handleDraftItems}
            itemName="challenges"
          />
        </Form.Item>
      ),
      formName: "proposal_challenge",
    },
    {
      name: "Cenário apresentado",
      itemName: "scenarios",
      content: (
        <Form.Item name="proposal_scenario">
          <RichTextEditor
            value={draftItems.scenarios}
            handleDraftItems={handleDraftItems}
            itemName="scenarios"
          />
        </Form.Item>
      ),
      formName: "proposal_scenario",
    },
    {
      name: "Premissas",
      itemName: "premisses",
      content: (
        <Form.Item
          name="proposal_premisses"
          initialValue={draftItems.premisses}
        >
          <div className="preview-container" id="premisses-container"></div>
          <RichTextEditor
            value={state.specialFields[2].value}
            handleDraftItems={handleDraftItems}
            itemName="premisses"
          />
        </Form.Item>
      ),
      formName: "proposal_premisses",
    },
    {
      name: "Pontos não contemplados",
      itemName: "extra_points",
      content: (
        <Form.Item
          name="proposal_extra_points"
          initialValue={draftItems.extra_points}
        >
          <div className="preview-container" id="no-covered-container"></div>
          <RichTextEditor
            value={draftItems.extra_points}
            handleDraftItems={handleDraftItems}
            itemName="extra_points"
          />
        </Form.Item>
      ),
      formName: "proposal_extra_points",
    },
    {
      name: "Arquitetura",
      itemName: "architecture",
      content: (
        <>
          <Form.Item
            name="proposal_architecture"
            initialValue={draftItems.architecture}
          >
            <RichTextEditor
              value={draftItems.architecture}
              handleDraftItems={handleDraftItems}
              itemName="architecture"
            />
          </Form.Item>
          <Row justify="center">
            <Row justify="space-between">
              <AddArchitectureDocument />
            </Row>
          </Row>
        </>
      ),
      formName: "proposal_architecture",
    },
    {
      name: "Fatores influenciadores",
      itemName: "main_factors",
      content: (
        <Form.Item
          name="proposal_main_factors"
          initialValue={draftItems.main_factors}
        >
          <div className="preview-container" id="factor-container"></div>
          <RichTextEditor
            value={draftItems.main_factors}
            handleDraftItems={handleDraftItems}
            itemName="main_factors"
          />
        </Form.Item>
      ),
      formName: "proposal_main_factors",
    },
    {
      name: "Calculadora",
      content: (
        <Form.List name="main_aws_investments">
          {(fields, { add, remove }) => (
            <>
              {fields.map((field, index) => (
                <Form.Item required={false} key={field.key}>
                  <Row gutter={24}>
                    <Col span={24}>
                      <Card
                        style={{
                          backgroundColor: "#F6F6F6",
                          boxShadow: "0 0 5px rgba(0,0,0,0.1)",
                          borderRadius: "5px",
                          marginRight: "10px",
                        }}
                      >
                        <Row gutter={24}>
                          <Col span={24}>
                            <Form.Item
                              label="Título"
                              {...field}
                              name={[field.name, "title"]}
                              required={true}
                            >
                              <Input
                                onChange={() =>
                                  setRecognizingFormList(!recognizingFormList)
                                }
                              />
                            </Form.Item>
                          </Col>
                          <Col lg={8} md={24}>
                            <Form.Item
                              label="Valor mensal"
                              {...field}
                              name={[field.name, "month_value"]}
                              required={true}
                            >
                              <PriceInput
                                index={index}
                                initialValue={
                                  state?.awsCosts[index]?.month_value
                                }
                                form={edit_proposal}
                                formListName="main_aws_investments"
                                fieldName={"month_value"}
                              />
                            </Form.Item>
                          </Col>
                          <Col lg={8} md={24}>
                            <Form.Item
                              label="Valor anual"
                              {...field}
                              name={[field.name, "year_value"]}
                              required={true}
                            >
                              <PriceInput
                                index={index}
                                initialValue={
                                  state?.awsCosts[index]?.year_value
                                }
                                form={edit_proposal}
                                formListName="main_aws_investments"
                                fieldName={"year_value"}
                              />
                            </Form.Item>
                          </Col>
                          <Col lg={8} md={24}>
                            <Form.Item
                              label="Valor Upfront"
                              {...field}
                              validateTrigger={["onChange", "onBlur"]}
                              name={[field.name, "upfront_value"]}
                            >
                              <PriceInput
                                index={index}
                                initialValue={
                                  state?.awsCosts[index]?.upfront_value
                                }
                                form={edit_proposal}
                                formListName="main_aws_investments"
                                fieldName={"upfront_value"}
                              />
                            </Form.Item>
                          </Col>
                        </Row>
                        <Row>
                          <Col span={24}>
                            <Form.Item
                              label="Link Público"
                              {...field}
                              name={[field.name, "calculator_link"]}
                              required={true}
                              rules={[
                                {
                                  type: "url",
                                  message: "Insira um link válido",
                                },
                              ]}
                            >
                              <Input />
                            </Form.Item>
                          </Col>
                        </Row>
                        <Row>
                          <Col span={24}>
                            <Form.Item label="Regiões">
                              <Select
                                mode="multiple"
                                value={regions[index]?.regions}
                                showSearch
                                placeholder={
                                  "Regiões consideradas na estimativa..."
                                }
                                optionFilterProp="children"
                                filterOption={(input, option) =>
                                  option?.children
                                    ?.toLowerCase()
                                    .includes(input?.toLowerCase())
                                }
                                filterSort={(optionA, optionB) =>
                                  optionA?.children
                                    ?.toLowerCase()
                                    .localeCompare(
                                      optionB.children?.toLowerCase()
                                    )
                                }
                                onChange={(e) =>
                                  handleChangeAwsCostRegion(e, index)
                                }
                                style={{ width: "100%" }}
                              >
                                {awsRegionsArr.map((value, key) => (
                                  <Option value={value.code} key={key}>
                                    {`${value.name} / ${value.code}`}
                                  </Option>
                                ))}
                              </Select>
                            </Form.Item>
                          </Col>
                        </Row>
                        <Row justify="end">
                          <Col>
                            {fields.length > 1 ? (
                              <Button
                                danger
                                type="text"
                                onClick={() => remove(field.name)}
                              >
                                Remover item
                                <DeleteOutlined />
                              </Button>
                            ) : null}
                          </Col>
                        </Row>
                      </Card>
                    </Col>
                  </Row>
                </Form.Item>
              ))}
              <Button
                type="text"
                onClick={() => add(state.main_aws_investments)}
              >
                Adicionar Custo AWS
                <PlusOutlined />
              </Button>
            </>
          )}
        </Form.List>
      ),
      formName: "main_aws_investments",
    },
    {
      name: "Custos adicionais",
      content: (
        <Form.List name="aditional_costs">
          {(fields, { add, remove }) => (
            <>
              {fields.map((field, index) => (
                <Form.Item required={false} key={field.key}>
                  <Row gutter={24}>
                    <Col span={24}>
                      <Card
                        style={{
                          backgroundColor: "#F6F6F6",
                          boxShadow: "0 0 5px rgba(0,0,0,0.1)",
                          borderRadius: "5px",
                          marginRight: "10px",
                        }}
                      >
                        <Row gutter={24}>
                          <Col span={24}>
                            <Form.Item
                              label="Título"
                              {...field}
                              name={[field.name, "title"]}
                              required={true}
                              validateTrigger={["onChange", "onBlur"]}
                            >
                              <Input
                                onChange={() =>
                                  setRecognizingFormList(!recognizingFormList)
                                }
                              />
                            </Form.Item>
                          </Col>
                          <Col lg={12} md={24}>
                            <Form.Item
                              label="Valor mensal"
                              {...field}
                              name={[field.name, "month_value"]}
                              required={true}
                              validateTrigger={["onChange", "onBlur"]}
                            >
                              <PriceInput
                                index={index}
                                initialValue={
                                  state?.specialFields[6].value
                                    ? state?.specialFields[6].value[index]
                                        ?.month_value
                                    : 0
                                }
                                form={edit_proposal}
                                formListName={"aditional_costs"}
                                fieldName={"month_value"}
                              />
                            </Form.Item>
                          </Col>
                          <Col lg={12} md={24}>
                            <Form.Item
                              label="Valor anual"
                              {...field}
                              name={[field.name, "year_value"]}
                              required={true}
                              validateTrigger={["onChange", "onBlur"]}
                            >
                              <PriceInput
                                index={index}
                                formListName={"aditional_costs"}
                                initialValue={
                                  state?.specialFields[6].value
                                    ? state?.specialFields[6].value[index]
                                        ?.year_value
                                    : 0
                                }
                                form={edit_proposal}
                                fieldName={"year_value"}
                              />
                            </Form.Item>
                          </Col>
                        </Row>
                        <Row justify="end">
                          <Col>
                            {fields.length > 1 ? (
                              <Button
                                danger
                                type="text"
                                onClick={() => remove(field.name)}
                              >
                                Remover item
                                <DeleteOutlined />
                              </Button>
                            ) : null}
                          </Col>
                        </Row>
                      </Card>
                    </Col>
                  </Row>
                </Form.Item>
              ))}
              <Button type="text" onClick={() => add()}>
                Adicionar Custo extra
                <PlusOutlined />
              </Button>
            </>
          )}
        </Form.List>
      ),
      formName: "aditional_costs",
    },
    {
      name: "Resultados esperados",
      itemName: "expected_results",
      content: (
        <Form.Item
          name="proposal_expected_results"
          initialValue={draftItems.expected_results}
        >
          <RichTextEditor
            value={draftItems.expected_results}
            handleDraftItems={handleDraftItems}
            itemName="expected_results"
          />
        </Form.Item>
      ),
      formName: "proposal_expected_results",
    },
    {
      name: "Investimentos Darede",
      itemName: "darede_investment",
      content: (
        <>
          <Row>
            <Col span={24}>
              <Form.Item
                label="Tempo de contrato"
                name="contract_time"
                initialValue={
                  state ? state.monthSelected : proposalState?.month
                }
                rules={[
                  {
                    required: true,
                    message: "Selecione o tempo de contrato!",
                  },
                ]}
              >
                <Select
                  mode="multiple"
                  placeholder="Selecione o tempo de contrato"
                  onChange={(e) => handleMonthSelect(e)}
                >
                  {contractTimeOptions.map((option) => {
                    return <Option value={option.value}>{option.label}</Option>;
                  })}
                </Select>
              </Form.Item>
              <Form.Item
                label="Pagamento"
                name="payment_method"
                initialValue={
                  state?.paymentSelected?.name
                    ? state?.paymentSelected?.name
                    : proposalState.paymentMethod
                }
              >
                {selectAfter}
              </Form.Item>
            </Col>
          </Row>
          {proposalState?.month !== "" && proposalState?.month !== [] ? (
            <>
              <Col span={24}>
                {proposalState?.month.map((m) => {
                  return (
                    <>
                      <Row gutter={24} style={{ marginBottom: "1rem" }}>
                        <HourValueCardDaredeInvestments
                          title={`Valor Hora (${m} meses)`}
                          hourClass="hourValue"
                          costManagementData={costManagementData}
                          services={services}
                          month={m}
                        />

                        <HourValueCardDaredeInvestments
                          title={`Valor Hora - Adicional (${m} meses)`}
                          hourClass="additionalHourValue"
                          costManagementData={costManagementData}
                          services={services}
                          month={m}
                        />

                        <HourBurstCard
                          hourClass="hourValue"
                          costManagementData={costManagementData}
                          services={services}
                          state={state}
                          month={m}
                        />

                        <TotalCalculatorCards month={m} />
                      </Row>
                    </>
                  );
                })}
              </Col>
            </>
          ) : null}
        </>
      ),
    },
    {
      name: "Observações",
      itemName: "observations",
      content: (
        <Form.Item
          name="proposal_observations"
          initialValue={draftItems.observations}
        >
          <RichTextEditor
            value={draftItems.observations}
            handleDraftItems={handleDraftItems}
            itemName="observations"
          />
        </Form.Item>
      ),
      formName: "proposal_observations",
    },
    {
      name: "Notas Internas",
      itemName: "internal_notes",
      content: (
        <Form.Item
          name="proposal_internal_notes"
          initialValue={draftItems.internal_notes}
        >
          <RichTextEditor
            value={draftItems.internal_notes}
            handleDraftItems={handleDraftItems}
            itemName="internal_notes"
          />
        </Form.Item>
      ),
      formName: "proposal_internal_notes",
    },
  ];

  useEffect(() => {
    dynamoGet(`${process.env.REACT_APP_STAGE}-cost-management`).then((data) => {
      const res = data.find(
        (d) => d.id === `${process.env.REACT_APP_COST_MANAGEMENT_OBJ_ID}`
      );

      setCostManagementData(res);

      setPaymentDarede(res?.paymentMethods);
    });
    if (proposalState?.month !== "") {
      handleTotalValues();
    }
  }, []);

  useEffect(() => {
    setProposalState({
      type: TotalValuesProposalType.SET_ID_FIXED,
      value: state.searchId,
    });
    setProposalState({
      type: TotalValuesProposalType.SET_TOTAL_VALUES,
      value: state.totalValues,
    });
    setProposalState({
      type: TotalValuesProposalType.SET_MONTH,
      value: state.monthSelected ? state.monthSelected : [],
    });
    setProposalState({
      type: TotalValuesProposalType.SET_ARCHITECTURE_FILE_NAMES,
      value: state.architectureFileNames,
    });
    setProposalState({
      type: TotalValuesProposalType.SET_FILE_NAMES,
      value: state.fileNames,
    });
    if (state.spreadSetup) {
      setProposalState({
        type: TotalValuesProposalType.SET_SPREAD_SETUP,
        value: state.spreadSetup,
      });
    }
    if (state.commercialStatus) {
      setProposalState({
        type: TotalValuesProposalType.SET_STATUS,
        value: state.commercialStatus,
      });
    }
    if (state.awsCosts) {
      edit_proposal.setFieldValue("main_aws_investments", state.awsCosts);
    }
    if (state.specialFields) {
      const stateValue = state.specialFields.find(
        (i) => i.name === "Custos adicionais"
      );
      edit_proposal.setFieldValue("aditional_costs", stateValue.value);
    }
  }, [state]);

  return (
    <Layout style={{ minHeight: "100vh" }}>
      <HeaderMenu collapsed={collapsed} setCollapsed={setCollapsed} />
      <Layout>
        <SideMenu collapsed={collapsed} />
        <Content style={{ padding: "2em" }}>
          <Card
            style={{
              boxShadow: "0 0 10px rgba(0,0,0,0.1)",
              borderRadius: "20px",
              marginRight: "10px",
            }}
          >
            <Popconfirm
              title="Ao voltar você perderá os dados do formulário, tem certeza?"
              onConfirm={() => {
                navigate(-1);
                localStorage.setItem("technical-proposal/services", "");
                localStorage.setItem("technical-proposal/form", "");
                localStorage.setItem("technical-proposal/customer", "");
                localStorage.setItem("technical-proposal/fileList", "");
                localStorage.setItem(
                  "technical-proposal/architectureFileList",
                  ""
                );
                localStorage.setItem("CollapseValidator", "");

                collapseContent.map((item) => {
                  return item.itemName
                    ? localStorage.setItem(item.itemName, "")
                    : null;
                });
              }}
              okText="Voltar"
              cancelText="Não"
            >
              <Button type="text" style={{ marginBottom: "15px" }}>
                <ArrowLeftOutlined />
                Editar proposta
              </Button>
            </Popconfirm>

            <Row>
              <Col span={24}>
                <Card
                  style={{
                    backgroundColor: "#f4f4f4",
                    boxShadow: "0px 4px 4px rgba(0, 0, 0, .5)",
                    borderRadius: "5px",
                  }}
                >
                  <Form
                    Layout="vertical"
                    style={{ marginBottom: "2rem" }}
                    form={edit_proposal}
                    onFinish={Submit}
                    initialValues={{
                      main_aws_investments: state?.awsCosts,
                      paymentMethod: state?.paymentMethod?.id
                        ? state?.paymentMethod?.id
                        : paymentDarede[0]?.id,
                      aditional_costs:
                        state?.specialFields[6]?.value !== undefined
                          ? state?.specialFields[6].value
                          : "",
                      proposal_challenge: state?.specialFields[0]?.value,
                      proposal_scenario: state?.specialFields[1]?.value,
                      proposal_premisses: state?.specialFields[2]?.value,
                      proposal_extra_points: state?.specialFields[3]?.value,
                      proposal_architecture: state?.specialFields[4]?.value,
                      proposal_main_factors: state?.specialFields[5]?.value,
                      proposal_expected_results: state?.specialFields[7]?.value,
                      proposal_observations: state?.specialFields[8]?.value,
                      proposal_internal_notes: state?.specialFields[9]?.value,
                    }}
                  >
                    <Row gutter={16}>
                      <ProposalHeader
                        currentContacts={allHeaderData}
                        setCurrentContacts={(e) => setCurrentContacts(e)}
                        setBrand={(image) => setCustomerBrand(image)}
                      />
                    </Row>
                    <Row gutter={24}>
                      {collapseContent.map((collapseItem, collapseItemKey) => {
                        return (
                          <Col span={24} key={collapseItemKey}>
                            <MainCollapse
                              title={collapseItem.name}
                              editor={collapseItem.content}
                            />
                          </Col>
                        );
                      })}
                    </Row>
                    <Row
                      justify="space-between"
                      style={{ marginBottom: "1rem" }}
                    >
                      <ProposalsCards hourClass={"hourValue"} />
                    </Row>
                  </Form>
                  <TableProposals
                    state={[
                      services,
                      edit_proposal.getFieldValue("client_name"),
                    ]}
                    setServices={(services) => setServices(services)}
                  />

                  <Row gutter={[16, 16]} justify="space-between">
                    <Col>
                      <AddDocument />
                    </Col>
                    <Col>
                      <Button
                        type="primary"
                        onClick={() => handleSubmitClick(edit_proposal)}
                        loading={loading}
                      >
                        Editar proposta
                        <EditOutlined />
                      </Button>
                    </Col>
                  </Row>
                </Card>
              </Col>
            </Row>
          </Card>
        </Content>
      </Layout>
    </Layout>
  );
};
