export const permissions = [
  {
    id: 1,
    page: "Home",
    permissions: [
      {
        id: 1,
        action: "Listar o tempo na tabela",
      },
      {
        id: 2,
        action: "Listar os nomes dos clientes na tabela",
      },
      {
        id: 3,
        action: "Listar os usuários na tabela",
      },
      {
        id: 4,
        action: "Listar se está ativo na tabela",
      },
      {
        id: 5,
        action: "Listar se está permitido na tabela",
      },
      {
        id: 6,
        action: "Listar ticket na tabela",
      },
      {
        id: 7,
        action: "Listar contas na tabela",
      },
    ],
  },
  {
    id: 2,
    page: "Clientes",
    permissions: [
      {
        id: 1,
        action: "Listar CRM na tabela",
      },
      {
        id: 2,
        action: "Listar CRM na tabela de funcionalidade de contratos",
      },
      {
        id: 3,
        action: "Listar ITSM na tabela",
      },
      {
        id: 5,
        action: "Listar ITSM na tabela da funcionalidade de usuários",
      },
      {
        id: 6,
        action: "Listar ITSM na tabela de funcionalidade de contratos",
      },
      {
        id: 7,
        action: "Listar ativo na tabela de funcionalidade de contratos",
      },
      {
        id: 8,
        action: "Listar clientes na tabela de funcionalidade de contratos",
      },
      {
        id: 9,
        action: "Listar conexão na tabela de funcionalidade de contas",
      },
      {
        id: 10,
        action: "Listar conta na tabela de funcionalidade de contas",
      },
      {
        id: 11,
        action: "Listar email na tabela de funcionalidade de usuários",
      },
      {
        id: 12,
        action: "Listar horas na tabela de funcionalidade de contratos",
      },
      {
        id: 13,
        action: "Listar nomes na tabela",
      },
      {
        id: 14,
        action: "Listar nomes na tabela de funcionalidade de contratos",
      },
      {
        id: 15,
        action: "Listar o CNPJ dos usuários na tabela",
      },
      {
        id: 16,
        action: "Listar perfil na tabela de funcionalidade de contas",
      },
      {
        id: 17,
        action: "Listar primeiro nome na tabela de funcionalidade de usuários",
      },
      {
        id: 18,
        action: "Listar razão social na tabela",
      },
      {
        id: 19,
        action: "Listar role na tabela de funcionalidade de contas",
      },
      {
        id: 20,
        action: "Listar se ele está ativo na tabela",
      },
      {
        id: 21,
        action: "Listar se ele é Ativo na tabela de funcionalidade de usuários",
      },
      {
        id: 22,
        action: "Listar telefone na tabela de funcionalidade de usuários",
      },
      {
        id: 23,
        action: "Listar tipo na tabela de funcionalidade de contratos",
      },
      {
        id: 24,
        action: "Listar usuários ativos",
      },
      {
        id: 25,
        action: "Listar usuários inativos",
      },
      {
        id: 26,
        action: "Listar último nome na tabela de funcionalidade de usuários",
      },
      {
        id: 27,
        action: "Permitir a funcionalidade de ver contrato na tabela",
      },
      {
        id: 28,
        action: "Permitir a funcionalidade de ver usuário na tabela",
      },
      {
        id: 29,
        action: "Permitir adicionar conta aws na funcionalidade de contas aws",
      },
      {
        id: 30,
        action: "Permitir ativar ou desativar clientes",
      },
      {
        id: 31,
        action:
          "Permitir ativar/desativar dados na tabela de funcionalidade de contratos",
      },
      {
        id: 32,
        action:
          "Permitir ativar/desativar dados na tabela de funcionalidade de usuários",
      },
      {
        id: 33,
        action: "Permitir editar contrato no modal de contratos",
      },
      {
        id: 34,
        action: "Permitir editar dados na tabela",
      },
      {
        id: 35,
        action: "Permitir editar dados na tabela de funcionalidade de contas",
      },
      {
        id: 36,
        action:
          "Permitir editar dados na tabela de funcionalidade de contratos",
      },
      {
        id: 37,
        action: "Permitir editar dados na tabela de funcionalidade de usuários",
      },
      {
        id: 38,
        action: "Permitir excluir dados na tabela de funcionalidade de contas",
      },
      {
        id: 39,
        action:
          "Permitir solicitar acesso a todas as contas aws na funcionalidade de contas",
      },
      {
        id: 40,
        action: "Permitir ver a funcionalidade de ver contas na tabela",
      },
      {
        id: 41,
        action: "Permitir ver a funcionalidade de ver contas na tabela",
      },
    ],
  },
  {
    id: 3,
    page: "Billing",
    permissions: [
      {
        id: 1,
        action: "Listar ITSM na tabela",
      },
      {
        id: 2,
        action: "Listar cliente na tabela",
      },
      {
        id: 3,
        action: "Listar completo na tabela",
      },
      {
        id: 4,
        action: "Listar contrato na tabela",
      },
      {
        id: 5,
        action: "Listar criação na tabela",
      },
      {
        id: 6,
        action: "Permitir funcionalidade Cadastro de Billing",
      },
      {
        id: 7,
        action: "Permitir funcionalidade de detalhes na tabela",
      },
    ],
  },
  {
    id: 4,
    page: "Invoices",
    permissions: [
      {
        id: 1,
        action: "Listar account ID na tabela",
      },
      {
        id: 2,
        action: "Listar bill payer account na tabela",
      },
      {
        id: 3,
        action: "Listar billing entity ID na tabela",
      },
      {
        id: 4,
        action: "Listar conversion na tabela",
      },
      {
        id: 5,
        action: "Listar cost na tabela",
      },
      {
        id: 6,
        action: "Listar customer na tabela",
      },
      {
        id: 7,
        action: "Listar invoice ID na tabela",
      },
      {
        id: 8,
        action: "Listar line item legal entity na tabela",
      },
      {
        id: 9,
        action: "Listar line item legal entity na tabela",
      },
      {
        id: 10,
        action: "Listar month na tabela",
      },
      {
        id: 11,
        action: "Permitir funcionalidade de exportar",
      },
      {
        id: 12,
        action: "Permitir funcionalidade de upload",
      },
    ],
  },
  {
    id: 5,
    page: "Gerenciar Usuários",
    permissions: [
      {
        id: 1,
        action: "Listar cargo na tabela",
      },
      {
        id: 2,
        action: "Listar email na tabela",
      },
      {
        id: 3,
        action: "Listar funcionalidade de ações na tabela",
      },
      {
        id: 4,
        action: "Listar status na tabela",
      },
      {
        id: 5,
        action: "Listar usuários na tabela",
      },
      {
        id: 6,
        action: "Permitir funcionalidade de adicionar novo usuário",
      },
    ],
  },
  {
    id: 6,
    page: "Gerenciar Cargos",
    permissions: [
      {
        id: 1,
        action: "Listar cargos na tabela",
      },
      {
        id: 2,
        action: "Listar status na tabela",
      },
      {
        id: 3,
        action: "Permitir funcionalidades de ações na tabela",
      },
      {
        id: 4,
        action: "Permitir funcionalidades de criar um novo cargo",
      },
    ],
  },
  {
    id: 7,
    page: "Auditoria",
    permissions: [
      {
        id: 1,
        action: "Listar data na tabela",
      },
      {
        id: 2,
        action: "Listar descrição na tabela",
      },
      {
        id: 3,
        action: "Listar nomes na tabela",
      },
    ],
  },
  {
    id: 8,
    page: "MFA",
    permissions: [
      {
        id: 1,
        action: "Listar funcionalidade resetar na tabela",
      },
      {
        id: 2,
        action: "Listar portal na tabela",
      },
      {
        id: 3,
        action: "Listar usuário na tabela",
      },
      {
        id: 4,
        action: "Permitir listar area-logada na tabela",
      },
      {
        id: 5,
        action: "Permitir listar dsm na tabela",
      },
    ],
  },
  {
    id: 9,
    page: "Permission Sets",
    permissions: [
      {
        id: 1,
        action: "Listar ARN na tabela",
      },
      {
        id: 2,
        action: "Listar funcionalidade ações na tabela",
      },
      {
        id: 3,
        action: "Listar usuário na tabela",
      },
      {
        id: 4,
        action: "Permitir listar ativos na tabela",
      },
      {
        id: 5,
        action: "Permitir listar inativos na tabela",
      },
    ],
  },
  {
    id: 10,
    page: "Switch Roles",
    permissions: [
      {
        id: 1,
        action: "Listar funcionalidade ações na tabela",
      },
      {
        id: 2,
        action: "Listar nome do cliente na tabela",
      },
      {
        id: 3,
        action: "Listar ticket na tabela",
      },
      {
        id: 4,
        action: "Listar usuário na tabela",
      },
      {
        id: 5,
        action: "Listar ticket soc na funcionalidade Ações",
      },
      {
        id: 6,
        action: "Listar contas na funcionalidade Ações",
      },
      {
        id: 7,
        action: "Listar usuários na funcionalidade Ações",
      },
      {
        id: 8,
        action: "Listar tempo na funcionalidade Ações",
      },
      {
        id: 9,
        action: "Listar permitido na funcionalidade Ações",
      },
      {
        id: 10,
        action: "Listar ativo na funcionalidade Ações",
      },
    ],
  },
  {
    id: 11,
    page: "Contatos",
    permissions: [
      {
        id: 1,
        action: "Listar CRM na tabela",
      },
      {
        id: 2,
        action: "Listar ITSM na tabela",
      },
      {
        id: 3,
        action: "Listar e-mail na tabela",
      },
      {
        id: 4,
        action: "Listar editar na tabela",
      },
      {
        id: 5,
        action: "Listar funcionalidade area logada na tabela",
      },
      {
        id: 6,
        action: "Listar funcionalidade ativar na tabela",
      },
      {
        id: 7,
        action: "Listar nome na tabela",
      },
      {
        id: 8,
        action: "Listar status na tabela",
      },
    ],
  },
  {
    id: 12,
    page: "Contratos",
    permissions: [
      {
        id: 1,
        action: "Listar CRM na tabela",
      },
      {
        id: 2,
        action: "Listar ITSM na tabela",
      },
      {
        id: 3,
        action: "Listar ativar na tabela",
      },
      {
        id: 4,
        action: "Listar ativo na tabela",
      },
      {
        id: 5,
        action: "Listar ativo na tabela de funcionalidades de executivo",
      },
      {
        id: 6,
        action: "Listar ativo na tabela na funcionalidade de executivo",
      },
      {
        id: 7,
        action: "Listar cliente na tabela",
      },
      {
        id: 8,
        action: "Listar editar na tabela",
      },
      {
        id: 9,
        action: "Listar email na tabela de funcionalidades de executivo",
      },
      {
        id: 10,
        action: "Listar executivos na tabela",
      },
      {
        id: 11,
        action: "Listar horas na tabela",
      },
      {
        id: 12,
        action: "Listar nome na tabela",
      },
      {
        id: 13,
        action: "Listar remover na tabela de funcionalidades de executivo",
      },
      {
        id: 14,
        action: "Listar remover na tabela na funcionalidade de executivo",
      },
      {
        id: 15,
        action: "Listar tipo na tabela",
      },
      {
        id: 16,
        action: "Listar tipo na tabela de funcionalidades de executivo",
      },
      {
        id: 17,
        action: "Listar tipo na tabela na funcionalidade de executivo",
      },
      {
        id: 18,
        action:
          "Permitir adicionar executivo na funcionalidade de executivo na tabela",
      },
    ],
  },
];
