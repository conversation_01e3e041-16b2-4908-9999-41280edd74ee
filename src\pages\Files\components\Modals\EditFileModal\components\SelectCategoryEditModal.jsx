import { Row, Col, Select, Typography } from "antd";
import { useSelector, shallowEqual } from "react-redux";
import { setSelectedFileFieldState } from "../../../../../../store/actions/files-action";

const { Option } = Select;
const { Text } = Typography;

export const SelectCategoryEditModal = () => {
  const category = useSelector(
    (state) => state.files.selectedFile.category,
    shallowEqual
  );

  return (
    <Col span={24}>
      <Row>
        <Text>Categoria: </Text>
        <Select
          onChange={(e) => {
            setSelectedFileFieldState({ field: "category", value: e });
          }}
          value={category}
          style={{ width: "100%" }}
        >
          <Option value="Incident Management">Incident Management</Option>
          <Option value="Non-service affecting incidents">
            Non-service affecting incidents
          </Option>
          <Option value="Performance analysis">Performance analysis</Option>
          <Option value="Assets/resources">Assets/resources</Option>
          <Option value="Exceptions">Exceptions</Option>
        </Select>
      </Row>
    </Col>
  );
};
