import React, { useContext, useEffect, useState, useRef } from "react";
import {
  Layout,
  Card,
  Row,
  Col,
  Input,
  Popconfirm,
  Button,
  Select,
  Form,
  message,
} from "antd";
import {
  DeleteOutlined,
  PlusOutlined,
  ArrowLeftOutlined,
  SaveOutlined,
  LoadingOutlined,
} from "@ant-design/icons";
import { HeaderMenu } from "../../components/HeaderMenu";
import { SideMenu } from "../../components/SideMenu";
import { useLocation, useNavigate } from "react-router-dom";
import { AddDocument } from "../../components/Modals/TechnicalProposals/AddDocument";
import { ProposalHeader } from "../../components/ProposalHeader";
import { AddArchitectureDocument } from "../../components/Modals/TechnicalProposals/AddArchitectureDocument";
import { MainCollapse } from "../../components/Collapse/CommercialProposal";
import { v4 } from "uuid";
import Cookies from "js-cookie";
import RichTextEditor from "../../components/RichTextEditor/RichTextEditor";
import { dynamoPost, dynamoGet } from "../../service/apiDsmDynamo";
import { TableProposals } from "../../components/Table/Proposals/TableProposals";
import { DisableIncrementScroll } from "../../hooks/DisableIncrementScroll";
import { otrsPost } from "../../service/apiOtrs";
import { apiProposals, getHeader } from "../../utils/api";
import { invalidInputNumberChars } from "../../constants/invalidCharsInputNumber";
import { ProposalsCards } from "../../components/Proposals/ProposalsCards";
import { AdditionalCostsContent } from "../../components/Proposals/AdditionalCostsContent";
import { add } from "date-fns";
import { handleSubmitClick } from "../../hooks/ProposalSubmit";
import { realMask } from "../../utils/masks";
import { clearLocalStorageItems } from "../../constants/clearLocalStorageItems";
import { proposalsHeaderItems } from "../../constants/proposalsHeaderItems";
import { submitAddTechnicalController } from "../../controllers/Proposals/sumbitTechnicalAdd";
import { totalValuesController } from "../../controllers/Proposals/totalValuesTechnical";
import { getProposalSettings } from "../../controllers/Proposals/getProposalSettings";
import {
  ProposalProvider,
  ProposalContext,
} from "../../contexts/totalValuesProposals";
import { TotalValuesProposalType } from "../../contexts/reducers/totalValuesProposal";
import {
  DataCookieContext,
  DataCookieProvider,
} from "../../contexts/data-cookie";
import { EditorState } from "draft-js";

const { Content } = Layout;

export const TechnicalProposalRfs2 = () => {
  return (
    <DataCookieProvider>
      <TechnicalProposalAdd />
    </DataCookieProvider>
  );
};

export const TechnicalProposalAdd = () => {
  return (
    <ProposalProvider>
      <TechnicalProposalAddPage />
    </ProposalProvider>
  );
};

export const TechnicalProposalAddPage = () => {
  let mainSearchId = v4();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const { cookieState, setCookieState } = useContext(DataCookieContext);
  const { proposalState, setProposalState } = useContext(ProposalContext);
  const { Option } = Select;
  const [add_proposal] = Form.useForm();
  const [collapsed, setCollapsed] = useState(false);
  const [services, setServices] = useState([]);
  const [currentContacts, setCurrentContacts] = useState([]);
  const [recognizingFormList, setRecognizingFormList] = useState(false);
  const [recongnizingHeaderChange, setRecongnizingHeaderChange] =
    useState(false);
  const [initializedServicesState, setInitializedServicesState] =
    useState(false);
  const [costManagementData, setCostManagementData] = useState([]);

  const [draftItems, setDraftItems] = useState({
    challenges: Cookies.get("challenges_cookie")
      ? Cookies.get("challenges_cookie")
      : "",
    scenarios: Cookies.get("scenarios_cookie")
      ? Cookies.get("scenarios_cookie")
      : "",
    premisses: Cookies.get("premisses_cookie")
      ? Cookies.get("premisses_cookie")
      : "",
    extra_points: Cookies.get("extra_points_cookie")
      ? Cookies.get("extra_points_cookie")
      : "",
    architecture: Cookies.get("architecture_cookie")
      ? Cookies.get("architecture_cookie")
      : "",
    main_factors: Cookies.get("main_factors_cookie")
      ? Cookies.get("main_factors_cookie")
      : "",
    expected_results: Cookies.get("expected_results_cookie")
      ? Cookies.get("expected_results_cookie")
      : "",
    observations: Cookies.get("observations_cookie")
      ? Cookies.get("observations_cookie")
      : "",
    internal_notes: Cookies.get("internal_notes_cookie")
      ? Cookies.get("internal_notes_cookie")
      : "",
  });

  const [customerBrand, setCustomerBrand] = useState("");

  const selectAfter = (
    <Select defaultValue="dolar">
      <Option value="dolar">$</Option>
      <Option value="reais">R$</Option>
    </Select>
  );

  const handleDraftItems = (itemName, value) => {
    const items = { ...draftItems };
    items[itemName] = value;
    setDraftItems(items);
  };

  const handleLocalStorageCollapseItems = () => {
    if (
      mainSearchId &&
      !localStorage.getItem("technical-proposal/mainSearchId")
    ) {
      localStorage.setItem("technical-proposal/mainSearchId", mainSearchId);
    }

    if (localStorage.getItem("technical-proposal/services")) {
      setServices(
        JSON.parse(localStorage.getItem("technical-proposal/services"))
      );
    }

    // if (localStorage.getItem("technical-proposal/form")) {
    //   add_proposal.setFieldsValue(
    //     JSON.parse(localStorage.getItem("technical-proposal/form"))
    //   );
    // }

    if (localStorage.getItem("technical-proposal/architectureFileList")) {
      console.log(
        "setou",
        localStorage.getItem("technical-proposal/architectureFileList"),
        JSON.parse(
          localStorage.getItem("technical-proposal/architectureFileList")
        )
      );
      add_proposal.setFieldsValue(
        JSON.parse(
          localStorage.getItem("technical-proposal/architectureFileList")
        )
      );
    }
  };

  const handleTotalValues = async () => {
    const newTotalValues = await totalValuesController(
      proposalState,
      costManagementData,
      services
    );

    setProposalState({
      type: TotalValuesProposalType.SET_TOTAL_VALUES,
      value: newTotalValues,
    });
  };

  const challengeRef = useRef(null);
  const scenariosRef = useRef(null);
  const premissesRef = useRef(null);
  const extraPointsRef = useRef(null);
  const architectureRef = useRef(null);
  const mainFactorsRef = useRef(null);
  const expectedResultsRef = useRef(null);
  const observationsRef = useRef(null);
  const internalNotesRef = useRef(null);

  const collapseContent = [
    {
      name: "Desafios",
      itemName: "challenges",
      content: (
        <Form.Item
          name="proposal_challenge"
          initialValue={
            Cookies.get("challenges_cookie")
              ? Cookies.get("challenges_cookie")
              : draftItems.challenges
          }
        >
          <RichTextEditor
            value={
              Cookies.get("challenges_cookie")
                ? Cookies.get("challenges_cookie")
                : draftItems.challenges
            }
            handleDraftItems={handleDraftItems}
            itemName="challenges"
            ref={challengeRef}
          />
        </Form.Item>
      ),
    },
    {
      name: "Cenário apresentado",
      itemName: "scenarios",
      content: (
        <Form.Item
          name="proposal_scenario"
          initialValue={
            Cookies.get("scenarios_cookie")
              ? Cookies.get("scenarios_cookie")
              : draftItems.scenarios
          }
        >
          <RichTextEditor
            value={
              Cookies.get("scenarios_cookie")
                ? Cookies.get("scenarios_cookie")
                : draftItems.scenarios
            }
            handleDraftItems={handleDraftItems}
            itemName="scenarios"
            ref={scenariosRef}
          />
        </Form.Item>
      ),
    },
    {
      name: "Premissas",
      itemName: "premisses",
      content: (
        <Form.Item
          name="proposal_premisses"
          initialValue={
            Cookies.get("premisses_cookie")
              ? Cookies.get("premisses_cookie")
              : draftItems.premisses
          }
        >
          <div className="preview-container" id="premisses-container"></div>
          {!loading ? (
            <RichTextEditor
              value={
                Cookies.get("premisses_cookie")
                  ? Cookies.get("premisses_cookie")
                  : draftItems.premisses
              }
              handleDraftItems={handleDraftItems}
              itemName="premisses"
              ref={premissesRef}
            />
          ) : (
            <Row justify="center">
              <LoadingOutlined />
            </Row>
          )}
        </Form.Item>
      ),
    },
    {
      name: "Pontos não contemplados",
      itemName: "extra_points",
      content: (
        <Form.Item
          name="proposal_extra_points"
          initialValue={
            Cookies.get("extra_points_cookie")
              ? Cookies.get("extra_points_cookie")
              : draftItems.extra_points
          }
        >
          <div className="preview-container" id="no-covered-container"></div>
          {!loading ? (
            <RichTextEditor
              value={
                Cookies.get("extra_points_cookie")
                  ? Cookies.get("extra_points_cookie")
                  : draftItems.extra_points
              }
              handleDraftItems={handleDraftItems}
              itemName="extra_points"
              ref={extraPointsRef}
            />
          ) : (
            <Row justify="center">
              <LoadingOutlined />
            </Row>
          )}
        </Form.Item>
      ),
    },
    {
      name: "Arquitetura",
      itemName: "architecture",
      content: (
        <>
          <Form.Item
            name="proposal_architecture"
            initialValue={
              Cookies.get("architecture_cookie")
                ? Cookies.get("architecture_cookie")
                : draftItems.architecture
            }
          >
            <RichTextEditor
              value={
                Cookies.get("architecture_cookie")
                  ? Cookies.get("architecture_cookie")
                  : draftItems.architecture
              }
              handleDraftItems={handleDraftItems}
              itemName="architecture"
              ref={architectureRef}
            />
          </Form.Item>
          <Row justify="center">
            <AddArchitectureDocument />
          </Row>
        </>
      ),
    },
    {
      name: "Calculadora",
      content: (
        <Form.List name="main_aws_investments">
          {(main_aws_investments, { add, remove }) => (
            <>
              {main_aws_investments.map((field, index) => (
                <Form.Item required={false} key={field.key}>
                  <Row gutter={24}>
                    <Col span={24}>
                      <Card
                        style={{
                          backgroundColor: "#F6F6F6",
                          boxShadow: "0 0 5px rgba(0,0,0,0.1)",
                          borderRadius: "5px",
                          marginRight: "10px",
                        }}
                      >
                        <Row gutter={24}>
                          <Col span={24}>
                            <Form.Item
                              label="Título"
                              {...field}
                              validateTrigger={["onChange", "onBlur"]}
                              name={[field.name, "title"]}
                              required={true}
                            >
                              <Input
                                onChange={() =>
                                  setRecognizingFormList(!recognizingFormList)
                                }
                              />
                            </Form.Item>
                          </Col>
                          <Col lg={8} md={24}>
                            <Form.Item
                              label="Valor mensal"
                              {...field}
                              validateTrigger={["onChange", "onBlur"]}
                              name={[field.name, "month_value"]}
                              required={true}
                            >
                              <Input
                                onKeyDown={(e) => {
                                  invalidInputNumberChars.find(
                                    (keyPressed) => keyPressed === e.key
                                  ) && e.preventDefault();
                                }}
                                addonAfter={selectAfter}
                                type="text"
                                maxLength={14}
                                onChange={(e) => {
                                  const newValue = realMask(
                                    e.currentTarget.value
                                  );

                                  const formValue = add_proposal.getFieldValue(
                                    "main_aws_investments"
                                  );
                                  formValue[field.key].month_value = newValue;
                                  add_proposal.getFieldValue(
                                    "main_aws_investments",
                                    formValue
                                  );

                                  setRecognizingFormList(!recognizingFormList);
                                }}
                              />
                            </Form.Item>
                          </Col>
                          <Col lg={8} md={24}>
                            <Form.Item
                              label="Valor anual"
                              {...field}
                              validateTrigger={["onChange", "onBlur"]}
                              name={[field.name, "year_value"]}
                              required={true}
                            >
                              <Input
                                onKeyDown={(e) => {
                                  invalidInputNumberChars.find(
                                    (keyPressed) => keyPressed === e.key
                                  ) && e.preventDefault();
                                }}
                                addonAfter={selectAfter}
                                type="text"
                                maxLength={14}
                                onChange={(e) => {
                                  const newValue = realMask(
                                    e.currentTarget.value
                                  );

                                  const formValue = add_proposal.getFieldValue(
                                    "main_aws_investments"
                                  );
                                  formValue[field.key].year_value = newValue;
                                  add_proposal.getFieldValue(
                                    "main_aws_investments",
                                    formValue
                                  );

                                  setRecognizingFormList(!recognizingFormList);
                                }}
                              />
                            </Form.Item>
                          </Col>
                          <Col lg={8} md={24}>
                            <Form.Item
                              label="Valor Upfront"
                              {...field}
                              validateTrigger={["onChange", "onBlur"]}
                              name={[field.name, "upfront_value"]}
                            >
                              <Input
                                onKeyDown={(e) => {
                                  invalidInputNumberChars.find(
                                    (keyPressed) => keyPressed === e.key
                                  ) && e.preventDefault();
                                }}
                                addonAfter={selectAfter}
                                type="text"
                                maxLength={14}
                                onChange={(e) => {
                                  const newValue = realMask(
                                    e.currentTarget.value
                                  );

                                  const formValue = add_proposal.getFieldValue(
                                    "main_aws_investments"
                                  );
                                  formValue[field.key].upfront_value = newValue;
                                  add_proposal.getFieldValue(
                                    "main_aws_investments",
                                    formValue
                                  );

                                  setRecognizingFormList(!recognizingFormList);
                                }}
                              />
                            </Form.Item>
                          </Col>
                        </Row>
                        <Row>
                          <Col span={24}>
                            <Form.Item
                              label="Link Público"
                              {...field}
                              validateTrigger={["onChange", "onBlur"]}
                              name={[field.name, "calculator_link"]}
                              required={true}
                              rules={[
                                {
                                  type: "url",
                                  message: "Insira um link válido",
                                },
                              ]}
                            >
                              <Input
                                type="url"
                                onChange={() =>
                                  setRecognizingFormList(!recognizingFormList)
                                }
                              />
                            </Form.Item>
                          </Col>
                        </Row>
                        <Row justify="end">
                          <Col>
                            <Button
                              danger
                              type="text"
                              onClick={() => {
                                remove(field.name);
                                message.warning("Custo AWS removido");
                              }}
                            >
                              Remover item
                              <DeleteOutlined />
                            </Button>
                          </Col>
                        </Row>
                      </Card>
                    </Col>
                  </Row>
                </Form.Item>
              ))}
              <Button type="text" onClick={() => add()}>
                Adicionar Custo AWS
                <PlusOutlined />
              </Button>
            </>
          )}
        </Form.List>
      ),
    },
    {
      name: "Fatores influenciadores",
      itemName: "main_factors",
      content: (
        <Form.Item
          name="proposal_main_factors"
          initialValue={
            Cookies.get("main_factors_cookie")
              ? Cookies.get("main_factors_cookie")
              : draftItems.main_factors
          }
        >
          <div className="preview-container" id="factor-container"></div>
          {!loading ? (
            <RichTextEditor
              value={
                Cookies.get("main_factors_cookie")
                  ? Cookies.get("main_factors_cookie")
                  : draftItems.main_factors
              }
              handleDraftItems={handleDraftItems}
              itemName="main_factors"
              ref={mainFactorsRef}
            />
          ) : (
            <Row justify="center">
              <LoadingOutlined />
            </Row>
          )}
        </Form.Item>
      ),
    },
    {
      name: "Custos adicionais",
      content: <AdditionalCostsContent add_proposal={add_proposal} />,
    },
    {
      name: "Resultados esperados",
      itemName: "expected_results",
      content: (
        <Form.Item
          name="proposal_expected_results"
          initialValue={
            Cookies.get("expected_results_cookie")
              ? Cookies.get("expected_results_cookie")
              : draftItems.expected_results
          }
        >
          <RichTextEditor
            value={
              Cookies.get("expected_results_cookie")
                ? Cookies.get("expected_results_cookie")
                : draftItems.expected_results
            }
            handleDraftItems={handleDraftItems}
            itemName="expected_results"
            ref={expectedResultsRef}
          />
        </Form.Item>
      ),
    },
    {
      name: "Observações",
      itemName: "observations",
      content: (
        <Form.Item
          name="proposal_observations"
          initialValue={
            Cookies.get("observations_cookie")
              ? Cookies.get("observations_cookie")
              : draftItems.observations
          }
        >
          <RichTextEditor
            value={
              Cookies.get("observations_cookie")
                ? Cookies.get("observations_cookie")
                : draftItems.observations
            }
            handleDraftItems={handleDraftItems}
            itemName="observations"
            ref={observationsRef}
          />
        </Form.Item>
      ),
    },
    {
      name: "Notas Internas",
      itemName: "internal_notes",
      content: (
        <Form.Item
          name="proposal_internal_notes"
          initialValue={
            Cookies.get("internal_notes_cookie")
              ? Cookies.get("internal_notes_cookie")
              : draftItems.internal_notes
          }
        >
          <RichTextEditor
            value={
              Cookies.get("internal_notes_cookie")
                ? Cookies.get("internal_notes_cookie")
                : draftItems.internal_notes
            }
            handleDraftItems={handleDraftItems}
            itemName="internal_notes"
            ref={internalNotesRef}
          />
        </Form.Item>
      ),
    },
  ];

  const Submit = async () => {
    setLoading(true);
    const user = localStorage.getItem("@dsm/username");
    await submitAddTechnicalController(
      add_proposal,
      collapseContent,
      draftItems,
      currentContacts,
      services,
      proposalState,
      customerBrand,
      proposalState.idFixed,
      user
    );
    setLoading(false);
    navigate(-1);
  };

  const updateCookies = ({ premisses, main_factors, extra_points }) => {
    setInterval(() => {
      let allInputFieldName = [
        "project_name",
        "client_name",
        "project_type",
        "project_status",
        "project_contacts",
        "project_architects",
        "project_bu",
        "project_opportunity",
      ];

      let challengesValue =
        challengeRef.current?.refs?.editor?.editor?.innerText;
      let scenariosValue =
        scenariosRef.current?.refs?.editor?.editor?.innerText;

      let premissesValue = premissesRef.current?.props?.value || premisses;
      let extraPointsValue =
        extraPointsRef.current?.props?.value || extra_points;
      let architectureValue =
        architectureRef.current?.refs?.editor?.editor?.innerText;
      let mainFactorsValue =
        mainFactorsRef.current?.props?.value || main_factors;
      let expectedResultsValue =
        expectedResultsRef.current?.refs?.editor?.editor?.innerText;
      let observationsValue =
        observationsRef.current?.refs?.editor?.editor?.innerText;
      let internalNotesValue =
        internalNotesRef.current?.refs?.editor?.editor?.innerText;

      Cookies.set("challenges_cookie", challengesValue, {
        expires: 1,
      });
      Cookies.set("scenarios_cookie", scenariosValue, {
        expires: 1,
      });

      Cookies.set("premisses_cookie", premissesValue, {
        expires: 1,
      });

      Cookies.set("extra_points_cookie", extraPointsValue, {
        expires: 1,
      });

      Cookies.set("architecture_cookie", architectureValue, {
        expires: 1,
      });
      Cookies.set("main_factors_cookie", mainFactorsValue, {
        expires: 1,
      });
      Cookies.set("expected_results_cookie", expectedResultsValue, {
        expires: 1,
      });
      Cookies.set("observations_cookie", observationsValue, {
        expires: 1,
      });
      Cookies.set("internal_notes_cookie", internalNotesValue, {
        expires: 1,
      });

      allInputFieldName.map((item) => {
        let newValue = add_proposal.getFieldValue(item);
        setCookieState({
          type: item,
          value: newValue,
        });
      });

      console.log(mainFactorsRef.current?.props?.value);
      console.log(extraPointsRef.current?.props?.value);
      console.log(premissesRef.current?.props?.value);
    }, 15000);
  };

  useEffect(() => {
    setLoading(true);

    dynamoGet(`${process.env.REACT_APP_STAGE}-cost-management`).then((data) => {
      const res = data.find(
        (d) => d.id === `${process.env.REACT_APP_COST_MANAGEMENT_OBJ_ID}`
      );

      setCostManagementData(res);
    });

    handleLocalStorageCollapseItems();

    DisableIncrementScroll();
    setLoading(false);
    initialPage();
  }, []);

  async function initialPage() {
    const { premisses, main_factors, extra_points } =
      await getProposalSettings();
    setDraftItems({
      ...draftItems,
      premisses,
      main_factors,
      extra_points,
    });

    updateCookies({ premisses, main_factors, extra_points });
  }

  // useEffect(() => {
  //   const setHeaderCookie = () => {
  //     let allHeaderInputs = {
  //       projectName: cookieState.project_name,
  //       projectClient: cookieState.client_name,
  //       projectType: cookieState.project_type,
  //       projectStatus: cookieState.project_status,
  //       projectContacts: cookieState.project_contacts,
  //       projectArchitects: cookieState.project_architects,
  //       projectBUs: cookieState.project_bu,
  //       projectOpportunity: cookieState.project_opportunity,
  //     };

  //     Cookies.set("header_cookie", allHeaderInputs, { expires: 1 });
  //   };

  //   setHeaderCookie();
  // }, [cookieState]);

  useEffect(() => {
    if (initializedServicesState === false) {
      setInitializedServicesState(true);
    } else {
      handleTotalValues();
    }
  }, [services]);

  return (
    <Layout style={{ minHeight: "100vh" }}>
      <HeaderMenu collapsed={collapsed} setCollapsed={setCollapsed} />
      <Layout>
        <SideMenu collapsed={collapsed} />
        <Content style={{ padding: "2em" }}>
          <Card
            style={{
              boxShadow: "0 0 10px rgba(0,0,0,0.1)",
              borderRadius: "20px",
            }}
          >
            <Popconfirm
              title="Ao voltar você perderá os dados do formulário, tem certeza?"
              onConfirm={() => {
                navigate(-1);
                localStorage.setItem("technical-proposal/services", "");
                // localStorage.setItem("technical-proposal/form", "");
                localStorage.setItem("technical-proposal/customer", "");
                localStorage.setItem(
                  "technical-proposal/architectureFileList",
                  ""
                );
                localStorage.setItem("technical-proposal/fileList", "");
                localStorage.setItem("CollapseValidator", "");
                localStorage.setItem("technical-proposal/mainSearchId", "");
              }}
              okText="Sim"
              cancelText="Não"
            >
              <Button
                type="text"
                style={{ marginBottom: "15px" }}
                icon={<ArrowLeftOutlined />}
              >
                Criar proposta
              </Button>
            </Popconfirm>
            <Row justify="center">
              <Col span={24}>
                <Card
                  style={{
                    backgroundColor: "#f4f4f4",
                    boxShadow: "0px 4px 4px rgba(0, 0, 0, .5)",
                    borderRadius: "5px",
                  }}
                >
                  <Form
                    Layout="vertical"
                    style={{ marginBottom: "2rem" }}
                    form={add_proposal}
                    initialValues={{
                      main_aws_investments: /*state ? state?.awsCosts :*/ [],
                      aditional_costs:
                        /*state?.specialFields[6]?.value !== undefined
                          ? state?.specialFields[6].value
                          :*/ [],
                    }}
                    onFinish={(data) => {
                      Submit(data);
                    }}
                  >
                    <ProposalHeader
                      currentContacts={currentContacts}
                      setCurrentContacts={(e) => setCurrentContacts(e)}
                      mainForm={add_proposal}
                      setBrand={(image) => setCustomerBrand(image)}
                      recongnizingHeaderChange={recongnizingHeaderChange}
                      setRecongnizingHeaderChange={(e) =>
                        setRecongnizingHeaderChange(e)
                      }
                    />

                    {collapseContent.map((collapseItem, collapseItemKey) => (
                      <Row>
                        <Col span={24} key={collapseItemKey}>
                          <MainCollapse
                            title={collapseItem.name}
                            editor={collapseItem.content}
                          ></MainCollapse>
                        </Col>
                      </Row>
                    ))}
                    <ProposalsCards hourClass="hourValue" />
                  </Form>
                </Card>
              </Col>
            </Row>
            <TableProposals
              state={[
                services,
                add_proposal.getFieldValue("client_name"),
                "technical",
              ]}
              setServices={(services) => setServices(services)}
            />
            <Row justify="space-between">
              <Col>
                <AddDocument />
              </Col>
              <Col>
                <Button
                  type="primary"
                  onClick={() => handleSubmitClick(add_proposal)}
                  loading={loading}
                >
                  Salvar proposta
                  <SaveOutlined />
                </Button>
              </Col>
            </Row>
          </Card>
        </Content>
      </Layout>
    </Layout>
  );
};
