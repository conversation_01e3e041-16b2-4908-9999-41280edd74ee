import React, { useState, useEffect } from "react";
import { Card, Col, Row, Tag, Typography } from "antd";
import moment from "moment";
import { FieldTimeOutlined } from "@ant-design/icons";
import "./OpenTickets.css";
import { otrsGet } from "../../../service/apiOtrs";

export const options = {
  responsive: true,
  plugins: {
    legend: {
      position: "top",
      boxWidth: 0,
      display: false,
    },
    title: {
      display: true,
    },
  },
};

function getPriorityColor(priorityId) {
  if (priorityId === 5) {
    return "#FF3C3C";
  } else if (priorityId === 4) {
    return "#F1F43F";
  } else if (priorityId === 3 || priorityId === 1) {
    return "#74CF49";
  } else {
    return "#fff";
  }
}

function formatConsumedMin(ticketMinutes) {
  const consumedMinutes = parseInt(ticketMinutes);
  const milliseconds = moment.duration(consumedMinutes * 60000, "milliseconds");

  const hours = Math.floor(milliseconds.asHours());
  const minutes = Math.floor(milliseconds.asMinutes() - hours * 60);

  return `${("00" + hours).slice(-2)}:${("00" + minutes).slice(-2)}`;
}

export function KanbanPopover(props) {
  const [ticketChartData, setTicketChartData] = useState([]);
  const { infos, closePopover } = props;

  async function getTicketChartData() {
    const { data } = await otrsGet(`/read/ticket/usage/${infos.ticket_id}`);
    const payload = [
      { table: moment().format("DD/MM"), time: 0 },
      { table: moment().subtract(1, "days").format("DD/MM"), time: 0 },
      { table: moment().subtract(2, "days").format("DD/MM"), time: 0 },
      { table: moment().subtract(3, "days").format("DD/MM"), time: 0 },
      { table: moment().subtract(4, "days").format("DD/MM"), time: 0 },
      { table: moment().subtract(5, "days").format("DD/MM"), time: 0 },
      { table: moment().subtract(6, "days").format("DD/MM"), time: 0 },
    ];

    for (let worklog of data) {
      const worklogDate = moment(worklog.change_time).format("DD/MM");
      const weekDay = payload.find((item) => item.table === worklogDate);

      if (weekDay) {
        weekDay.time += parseInt(worklog.time_unit);
      }
    }
    setTicketChartData(payload);
  }

  useEffect(() => {
    getTicketChartData();
  }, []);
}

let first = false;
const KANBAN_PAGE = 0;
const TABLE_PAGE = 1;

export function KanbanPageComponent(props) {
  const { showModal, setSelectedPage, data } = props;
  const { Title, Text } = Typography;
  const [showPopover, setShowPopover] = useState(false);
  const [popoverInfos, setPopoverInfos] = useState({});
  const [columns, setColumns] = useState([
    {
      name: "Backlog",
      tickets: [],
    },
    {
      name: "Abertos",
      tickets: [],
    },
    {
      name: "Pendente Cliente",
      tickets: [],
    },
    {
      name: "Pendente Darede",
      tickets: [],
    },
    {
      name: "Reabertos",
      tickets: [],
    },
  ]);

  // const [filteredColumns, setFilteredColumns] = useState(structuredClone(columns));

  function populateColumns() {
    if (data) {
      setColumns(data);
    }
  }

  useEffect(() => {
    populateColumns();
  }, [props.data]);

  return (
    <Row>
      <div>
        {/* <div className="kanban-container">
          <div className="kanban-items">
            <PlusSquareOutlined
              onClick={() => setSelectedPage(KANBAN_PAGE)}
              className="star-icon"
            />
            <AppstoreFilled
              onClick={() => setSelectedPage(TABLE_PAGE)}
              className="star-icon"
            />
          </div>
        </div> */}

        <Row
          style={{
            borderRadius: "20px",
            flexWrap: "nowrap",
          }}
        >
          {columns.map((column, index) => {
            return (
              <Col
                style={{
                  borderRadius: "8px",
                  height: "600px",
                  margin: "0 10px",
                  width: "180px",
                  backgroundColor: "#f0f2f5",
                }}
              >
                <div className="kanban-tickets-column-container">
                  <Text style={{ fontSize: "16px" }}>{column.name}</Text>
                </div>
                <div className="kanban-single-column">
                  {column.tickets.length > 0
                    ? column.tickets.map((ticket) => {
                        ticket.column = column.name;
                        return (
                          <a
                            style={{
                              display: "flex",
                              justifyContent: "center",
                            }}
                            target="_blank"
                            href={`https://${
                              process.env.REACT_APP_STAGE !== "prod"
                                ? "hml."
                                : ""
                            }tickets.darede.com.br/otrs/index.pl?Action=AgentTicketZoom;TicketNumber=${
                              ticket.ticket_number
                            }`}
                          >
                            <Card
                              style={{ width: "91.6%", borderRadius: "10px" }}
                              hoverable
                            >
                              <Row justify="center">
                                <Col span={24}>
                                  <Text>{ticket.subject}</Text>
                                </Col>
                                <Tag
                                  color={getPriorityColor(
                                    ticket.ticket_priority_id
                                  )}
                                >
                                  <Text>{ticket.ticket_priority_name}</Text>
                                </Tag>
                              </Row>
                              <div
                                style={{
                                  display: "flex",
                                  textAlign: "start",
                                  flexDirection: "column",
                                }}
                              >
                                <Text>Cliente: {ticket.client_name}</Text>
                                <Text>Proprietario: {ticket.owner_ticket}</Text>
                              </div>
                              <Text>
                                <p>Data de criação: </p>
                                {moment(ticket.ticket_create_time).format(
                                  "DD/MM/YYYY"
                                )}
                              </Text>
                              <Row>
                                <Text style={{ color: "#d1d2d4" }}>
                                  <FieldTimeOutlined />
                                  {formatConsumedMin(ticket.consumed_min)}
                                </Text>
                              </Row>
                            </Card>
                          </a>
                        );
                      })
                    : null}
                </div>
              </Col>
            );
          })}
        </Row>
      </div>
      <div>
        {showPopover ? (
          <KanbanPopover
            closePopover={closePopover}
            infos={popoverInfos}
          ></KanbanPopover>
        ) : null}
      </div>
    </Row>
  );

  // function ascFilter(index) {
  //     let newItems = structuredClone(columns)
  //
  //     if (columns[index].filtered === undefined || columns[index].filtered === true) {
  //         newItems[index].items = newItems[index].items.sort((valueOne, valueTwo) =>
  //             valueOne.title.localeCompare(valueTwo.title)
  //         )
  //         columns[index].filtered = false
  //     } else {
  //         // }else{
  //         newItems[index].items = newItems[index].items
  //             .sort((valueOne, valueTwo) => valueOne.title.localeCompare(valueTwo.title))
  //             .reverse()
  //         columns[index].filtered = true
  //     }
  //     // }
  //     setFilteredColumns(newItems)
  // }

  function openPopover(infos) {
    // setPopoverInfos(infos)
    // setShowPopover(!showPopover)
    if (!first || (showPopover && infos.subject === popoverInfos.subject)) {
      setShowPopover(!showPopover);
      setPopoverInfos(infos);
      first = !first;
    } else {
      setPopoverInfos(infos);
    }
  }

  function closePopover() {
    first = false;
    setShowPopover(!showPopover);
  }
}

function OpenTicketsKanban(props) {
  const [selectedPage, setSelectedPage] = useState(KANBAN_PAGE);
  const { showModal, data } = props;

  return (
    // <div
    //   style={{
    //     overflow: "auto",
    //     scrollbarWidth: "thin",
    //     marginTop: "20px",
    //     width: "85vw",
    //     maxWidth: selectedPage === KANBAN_PAGE ? "1032px" : "700px",
    //   }}
    // >
    <div
      style={{
        background: "none",
        height: "65vh",
        overflow: "auto",
        maxHeight: "630px",
      }}
    >
      {/* {selectedPage === KANBAN_PAGE ? ( */}
      <KanbanPageComponent
        showModal={showModal}
        setSelectedPage={setSelectedPage}
        data={data}
      />
      {/* ) : (
        <TableTicketKanban
          showModal={showModal}
          setSelectedPage={setSelectedPage}
          data={data}
        />
      )} */}
    </div>
    // </div>
  );
}

export default OpenTicketsKanban;
