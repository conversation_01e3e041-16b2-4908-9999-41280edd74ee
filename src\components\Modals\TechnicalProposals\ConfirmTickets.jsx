import { DeleteOutlined, CheckOutlined, EditOutlined } from "@ant-design/icons";
import { useState } from "react";
import { Button, Modal, Row, Input, Form, Select } from "antd";
import { getTicketByNumber } from "../../../service/getTicketByNumber";
import { shallowEqual, useSelector } from "react-redux";
import { useToggle } from "../../../hooks/useToggle";
import { setTechnicalProposalState } from "../../../store/actions/technical-proposal-action";
import { useEffect } from "react";
import { useCallback } from "react";
import { useNavigate } from "react-router-dom";

const { Option } = Select;

export const ConfirmTicket = ({ submitFunction }) => {
  const navigate = useNavigate();
  const ticketData = useSelector(
    (state) => state.technicalProposal.ticketData,
    shallowEqual
  );

  const [toggleModalValue, toggle] = useToggle();
  const [ticketName, setTicketName] = useState("");
  const [ticketSelected, setTicketSelected] = useState(false);
  const [loading, setLoading] = useState(false);

  // const initPage = useCallback(async () => {
  //   if (ticketData && ticketData.TicketNumber) {
  //     const ticket = await getTicketByNumber(ticketData.TicketNumber);

  //     if (ticket.length >= 0) setTicketName(ticket[0].subject);
  //   }
  // }, [ticketData]);

  // useEffect(() => {
  //   initPage();
  // }, [initPage]);

  async function handleSubmit() {
    try {
      setLoading(true);
      await submitFunction();
      setLoading(false);
    } catch (error) {
      setLoading(false);
    }
  }

  return (
    <>
      <EditProposalButton toggle={toggle} />
      <Modal
        title="Registrar horas em ticket"
        open={toggleModalValue}
        width={"50%"}
        closable={false}
        destroyOnClose={true}
        footer={
          <Row justify={"space-between"}>
            <Button type="text-color" onClick={toggle}>
              Cancelar <DeleteOutlined />
            </Button>
            <Button
              type="primary"
              onClick={handleSubmit}
              // disabled={!ticketSelected || loading}
              loading={loading}
            >
              Salvar <CheckOutlined />
            </Button>
          </Row>
        }
        onCancel={toggle}
      >
        {/* <Form layout="horizontal" onFinish={toggle}>
          <Row justify="center" style={{ marginBottom: "1rem" }}>
            <Select
              style={{ width: "100%" }}
              showSearch
              placeholder="Número do ticket"
              onChange={() => setTicketSelected(true)}
            >
              {ticketData && (
                <Option value={ticketData.TicketNumber}>
                  {ticketData ? ticketData.TicketNumber : ""} - {ticketName}
                </Option>
              )}
            </Select>
          </Row>
          <Row>
            <Input
              type="number"
              placeholder="Tempo (em minutos)"
              onChange={(e) =>
                setTechnicalProposalState({
                  field: "ticketData",
                  value: {
                    ...ticketData,
                    time_unit: Number(e.target.value),
                  },
                })
              }
            />
          </Row>
        </Form> */}
      </Modal>
    </>
  );
};

const EditProposalButton = ({ toggle }) => {
  const loadingPersist = useSelector(
    (state) => state.globalPersist.technicalProposal.loadingPersist,
    shallowEqual
  );

  return (
    <Button
      type="primary"
      onClick={toggle}
      loading={loadingPersist}
      disabled={loadingPersist}
    >
      Editar proposta <EditOutlined />
    </Button>
  );
};
