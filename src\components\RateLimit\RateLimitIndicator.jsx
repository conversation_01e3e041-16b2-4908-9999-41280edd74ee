import React from 'react';
import { Progress, Alert, Tooltip, Space, Typography } from 'antd';
import { ExclamationCircleOutlined, ClockCircleOutlined } from '@ant-design/icons';

const { Text } = Typography;

/**
 * Componente para exibir informações de rate limiting
 * Fornece feedback visual sobre o uso de limites de requisições
 */
const RateLimitIndicator = ({
  requestCount = 0,
  maxRequests = 100,
  isLimited = false,
  timeRemaining = 0,
  formatTimeRemaining,
  usagePercentage = 0,
  showProgress = true,
  showAlert = true,
  showTooltip = true,
  size = 'default',
  type = 'line', // 'line' | 'circle' | 'dashboard'
  className = '',
  style = {}
}) => {
  /**
   * Determina a cor baseada na porcentagem de uso
   */
  const getProgressColor = () => {
    if (usagePercentage >= 90) return '#ff4d4f'; // Vermelho
    if (usagePercentage >= 70) return '#faad14'; // <PERSON><PERSON>
    return '#52c41a'; // Verde
  };

  /**
   * Determina o status do progresso
   */
  const getProgressStatus = () => {
    if (isLimited) return 'exception';
    if (usagePercentage >= 90) return 'exception';
    if (usagePercentage >= 70) return 'active';
    return 'success';
  };

  /**
   * Renderiza o componente de progresso
   */
  const renderProgress = () => {
    if (!showProgress) return null;

    const progressProps = {
      percent: Math.round(usagePercentage),
      status: getProgressStatus(),
      strokeColor: getProgressColor(),
      size: size,
      format: (percent) => `${requestCount}/${maxRequests}`
    };

    if (type === 'circle') {
      return (
        <Progress
          {...progressProps}
          type="circle"
          width={size === 'small' ? 60 : 80}
        />
      );
    }

    if (type === 'dashboard') {
      return (
        <Progress
          {...progressProps}
          type="dashboard"
          width={size === 'small' ? 80 : 120}
        />
      );
    }

    return <Progress {...progressProps} />;
  };

  /**
   * Renderiza o alerta quando limitado
   */
  const renderAlert = () => {
    if (!showAlert || !isLimited) return null;

    return (
      <Alert
        message="Rate Limit Exceeded"
        description={
          <Space direction="vertical" size="small">
            <Text>
              You have exceeded the maximum number of requests ({maxRequests}) 
              allowed in this time window.
            </Text>
            {timeRemaining > 0 && (
              <Text type="secondary">
                <ClockCircleOutlined /> Try again in {formatTimeRemaining?.() || `${Math.ceil(timeRemaining / 1000)}s`}
              </Text>
            )}
          </Space>
        }
        type="warning"
        icon={<ExclamationCircleOutlined />}
        showIcon
        closable={false}
        style={{ marginTop: 8 }}
      />
    );
  };

  /**
   * Renderiza informações detalhadas
   */
  const renderDetails = () => {
    return (
      <Space direction="vertical" size="small" style={{ width: '100%' }}>
        <Space>
          <Text strong>Requests:</Text>
          <Text>{requestCount} / {maxRequests}</Text>
        </Space>
        
        <Space>
          <Text strong>Usage:</Text>
          <Text>{Math.round(usagePercentage)}%</Text>
        </Space>
        
        {isLimited && timeRemaining > 0 && (
          <Space>
            <Text strong>Reset in:</Text>
            <Text type="danger">
              {formatTimeRemaining?.() || `${Math.ceil(timeRemaining / 1000)}s`}
            </Text>
          </Space>
        )}
        
        <Space>
          <Text strong>Remaining:</Text>
          <Text type={isLimited ? 'danger' : 'success'}>
            {Math.max(0, maxRequests - requestCount)}
          </Text>
        </Space>
      </Space>
    );
  };

  /**
   * Conteúdo do tooltip
   */
  const tooltipContent = (
    <div style={{ minWidth: 200 }}>
      {renderDetails()}
    </div>
  );

  /**
   * Renderiza o componente principal
   */
  const mainContent = (
    <div className={className} style={style}>
      {renderProgress()}
      {renderAlert()}
    </div>
  );

  if (showTooltip) {
    return (
      <Tooltip 
        title={tooltipContent} 
        placement="topLeft"
        overlayStyle={{ maxWidth: 300 }}
      >
        {mainContent}
      </Tooltip>
    );
  }

  return mainContent;
};

/**
 * Componente compacto para exibição em headers/toolbars
 */
export const RateLimitBadge = ({
  requestCount = 0,
  maxRequests = 100,
  isLimited = false,
  usagePercentage = 0,
  ...props
}) => {
  const getColor = () => {
    if (isLimited) return '#ff4d4f';
    if (usagePercentage >= 90) return '#faad14';
    if (usagePercentage >= 70) return '#fa8c16';
    return '#52c41a';
  };

  return (
    <Tooltip title={`API Requests: ${requestCount}/${maxRequests}`}>
      <div
        style={{
          display: 'inline-flex',
          alignItems: 'center',
          padding: '2px 8px',
          borderRadius: '12px',
          backgroundColor: getColor(),
          color: 'white',
          fontSize: '12px',
          fontWeight: 'bold',
          minWidth: '50px',
          justifyContent: 'center',
          ...props.style
        }}
      >
        {requestCount}/{maxRequests}
      </div>
    </Tooltip>
  );
};

/**
 * Componente para exibição em formulários
 */
export const FormRateLimitWarning = ({
  isLimited,
  timeRemaining,
  formatTimeRemaining,
  maxRequests,
  requestCount,
  ...props
}) => {
  if (!isLimited) return null;

  return (
    <Alert
      message="Submission Limit Reached"
      description={
        <div>
          <p>
            You have reached the maximum number of submissions ({maxRequests}) 
            allowed in this time period.
          </p>
          {timeRemaining > 0 && (
            <p>
              <ClockCircleOutlined /> Please wait {formatTimeRemaining?.() || `${Math.ceil(timeRemaining / 1000)} seconds`} before trying again.
            </p>
          )}
        </div>
      }
      type="warning"
      showIcon
      style={{ marginBottom: 16, ...props.style }}
    />
  );
};

export default RateLimitIndicator;
