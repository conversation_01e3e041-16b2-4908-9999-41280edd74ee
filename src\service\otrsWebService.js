import Axios from "axios";

const token = process.env.REACT_APP_TOKEN_NEW;

export const otrsWebServiceGet = async (route) => {
  const { data } = await Axios.get(
    process.env.REACT_APP_WEBSERVICE_NEW + route,
    {
      headers: { Authorization: token },
    }
  );

  return data;
};

export const otrsWebServicePost = async (route, body) => {
  const { data } = await Axios.post(
    process.env.REACT_APP_WEBSERVICE_NEW + route,
    { ...body },
    {
      headers: {
        "Content-Type": "application/json",
        Authorization: token,
      },
    }
  );
  return data;
};

export const otrsWebServicePut = async (route, body) => {
  const { data } = await Axios.put(
    process.env.REACT_APP_WEBSERVICE_NEW + route,
    { ...body },
    {
      headers: { Authorization: token },
    }
  );

  return data;
};

export const otrsWebServiceDelete = async (route, body) => {
  const { data } = await Axios.delete(
    process.env.REACT_APP_API_BASE_OTRS_URL + route,
    { ...body },
    {
      headers: { Authorization: token },
    }
  );

  return data;
};
