import moment from "moment";
import { getItemsByDynamicIndex } from "../../service/apiDsmDynamo";
import { Spin, Row, Typography } from "antd";

const { Text } = Typography;
export function formatContracts(dsmContracts) {
  let formatedContracts = [];
  if (dsmContracts) {
    dsmContracts.forEach((contract) => {
      formatedContracts.push({
        id: contract?.id,
        contract_id: contract?.identifications?.itsm_id
          ? contract?.identifications?.itsm_id
          : contract?.identifications?.crm_id,
        customer_id: contract?.customer?.id,
        create_time: contract?.created_at,
        document_id: contract?.squad,
        end_date: contract?.target_dates?.expected_close_date,
        freeze_percentage: contract?.percentage?.freezing,
        name: contract?.name,
        notification_flag: 0,
        notify_email: contract?.emails,
        hasChangedHours: contract?.hasChangedHours || 0,
        notify_percentage: contract?.percentage,
        start_date: contract?.target_dates?.expected_start_date,
        total_hours: parseInt(contract?.total_hours) || 0,
        type_hours: contract?.type_hours,
        valid_id: contract?.active,
      });
    });
    return formatedContracts.filter((contract) => contract.valid_id === 1);
  } else {
    return [];
  }
}

export const categorizeContractsByMonths = (contracts) => {
  const values = {
    oneMonth: [],
    twoMonth: [],
    threeMonth: [],
    fourMonth: [],
    fiveMonth: [],
  };

  for (const contract of contracts) {
    const contractDate = moment(contract.created_at);
    const contractMonth = contractDate.month();
    if (
      contractDate > moment().subtract("5", "months") &&
      contractDate < moment()
    ) {
      if (contractMonth === moment().subtract("4", "months").month()) {
        values.oneMonth.push(contract);
      } else if (contractMonth === moment().subtract("3", "months").month()) {
        values.twoMonth.push(contract);
      } else if (contractMonth === moment().subtract("2", "months").month()) {
        values.threeMonth.push(contract);
      } else if (contractMonth === moment().subtract("1", "months").month()) {
        values.fourMonth.push(contract);
      }
      if (contractMonth === moment().month()) {
        values.fiveMonth.push(contract);
      }
    }
  }

  return values;
};

export const calculateGraphValues = (activeContracts, inactiveContracts) => {
  const contracts = [...activeContracts, ...inactiveContracts];
  return categorizeContractsByMonths(contracts);
};

export const formatNewContractsTableData = (tableData, customers) => {
  const tempData = [];

  for (let i = 0; i < tableData.length; i++) {
    const customer = customers.find(
      (custom) => custom.id === tableData[i].customer_id
    );

    tempData.push({
      customer_id: tableData[i].customer_id,
      client: customer?.names.name || customer?.names.fantasy_name,
      name: tableData[i].name,
      squad: tableData[i].squad,
      end_date: moment(tableData[i].target_dates.expected_close_date).format(
        "DD/MM/YYYY"
      ),
    });
  }

  return tempData;
};

export const getUpdatedContractHours = async (
  contracts,
  indexName = "contract_id",
  indexParam = "id"
) => {
  let onlyMostRecentHoursUpdated = [];
  const consumptionsUpdatedPromises = contracts.map(async (contract) => {
    const response = await getItemsByDynamicIndex(
      `${process.env.REACT_APP_STAGE}-consumption-hours`,
      indexName,
      contract[indexParam]
    );
    return response;
  });

  const updatedContracts = await Promise.all(consumptionsUpdatedPromises);

  updatedContracts.forEach((contract) => {
    if (contract.length > 0) {
      onlyMostRecentHoursUpdated.push(
        contract.sort(
          (a, b) =>
            new Date(b.updated_consumption_hours) -
            new Date(a.updated_consumption_hours)
        )[0]
      );
    }
  });

  return onlyMostRecentHoursUpdated;
};

export const renderTotalHours = (total, data, contractsWithChangedHours) => {
  if (data?.hasChangedHours) {
    return contractsWithChangedHours ? (
      <Text>
        {
          contractsWithChangedHours.find(
            (contract) => contract.contract_id === data.id
          )?.new_hours
        }
      </Text>
    ) : (
      <Row justify="center">
        <Spin />
      </Row>
    );
  }
  return <Text>{total}</Text>;
};

export const renderTotalHoursByITSM = (
  total,
  data,
  contractsWithChangedHours
) => {
  if (data?.hasChangedHours) {
    return contractsWithChangedHours ? (
      <Text>
        {
          contractsWithChangedHours.find(
            (contract) => contract.itsm_id == data.id
          )?.new_hours
        }
      </Text>
    ) : (
      <Row justify="center">
        <Spin />
      </Row>
    );
  }
  return <Text>{total}</Text>;
};

export const groupBy = (array, key) => {
  return array.reduce((result, currentValue) => {
    (result[currentValue[key]] = result[currentValue[key]] || []).push(
      currentValue
    );
    return result;
  }, {});
};
