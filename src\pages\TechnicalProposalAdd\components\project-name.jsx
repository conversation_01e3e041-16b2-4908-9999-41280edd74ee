import React from "react";
import { Row, Col, Input, Typography } from "antd";
import { shallowEqual, useSelector } from "react-redux";
import { setTechnicalProposalState } from "../../../store/actions/technical-proposal-action";

const { Title } = Typography;

export const ProjectName = () => {
  const value = useSelector(state => state.technicalProposal.projectName, shallowEqual)

  return (
    <Col sm={12} md={12} lg={12}>
      <Row>
        <Title level={5} style={{ fontWeight: "400" }}>
          Nome do Projeto
        </Title>
      </Row>
      <Row>
        <Col span={23}>
          <Input 
          id="projectName"
          placeholder={"Título da proposta"} 
          style={{ width: "100%" }} 
          value={value}
          onChange={(e) => setTechnicalProposalState({ field: 'projectName', value: e.currentTarget.value })}
          />
        </Col>
      </Row>
    </Col>
  );
};