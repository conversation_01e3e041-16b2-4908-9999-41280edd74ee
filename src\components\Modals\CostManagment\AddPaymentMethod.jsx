import {
  DeleteOutlined,
  PlusOutlined,
  CreditCardOutlined,
  DollarCircleOutlined,
  CheckOutlined,
} from "@ant-design/icons";
import { useEffect, useState } from "react";
import {
  Button,
  Modal,
  Row,
  Col,
  Table,
  Input,
  Typography,
  Form,
  message,
} from "antd";
import { v4 } from "uuid";
import { dynamoGet, dynamoPut } from "../../../service/apiDsmDynamo";
import { logNewAuditAction } from "../../../controllers/audit/logNewAuditAction";

let count = 1;

export const AddPaymentMethod = (paymentMethodsParent) => {
  const { Title } = Typography;
  const [paymentMethodForm] = Form.useForm();
  const [showModal, setShowModal] = useState();
  const { paymentMethods, setPaymentMethods } = paymentMethodsParent;
  const [childPaymentMethods, setChildPaymentMethods] = useState([]);
  const [currentPaymentMethod, setCurrentPaymentMethod] = useState({
    id: "",
    name: "",
    description: "",
  });

  useEffect(() => {
    setChildPaymentMethods(paymentMethodsParent.paymentMethodsParent);
  }, [paymentMethodsParent.paymentMethodsParent]);

  const handleSubmit = (e) => {
    e.preventDefault();
    setPaymentMethods(childPaymentMethods);
    setShowModal(false);

    try {
      dynamoGet(`${process.env.REACT_APP_STAGE}-cost-management`)
        .then(async (data) => {
          console.log(data[0]?.id);
          try {
            dynamoPut(
              `${process.env.REACT_APP_STAGE}-cost-management`,
              data[0].id,
              {
                paymentMethods: childPaymentMethods,
              }
            );
          } catch (err) {
            console.log("err: ", err);
            message.error("Erro ao alterar Gerenciamento de Custos!");
          }
        })
        .catch((err) => {
          console.log("Error on getting paymentMethods from dynamoDB: ", err);
        });
    } catch (err) {
      console.log("err: ", err);
    }
  };

  const handleAddItem = async (e) => {
    if (
      childPaymentMethods?.find((p) => p?.name === currentPaymentMethod?.name)
    ) {
      message.error(
        `Forma de pagamento "${currentPaymentMethod?.name}" já existe!`
      );
    } else {
      let index = 0;
      const addedItem = {
        id: v4(),
        active: true,
        name: currentPaymentMethod?.name,
        description: currentPaymentMethod?.description,
      };

      setChildPaymentMethods((prev) => [
        ...prev.slice(0, index + 1),
        addedItem,
        ...prev.slice(index + 1),
      ]);
      paymentMethodForm.resetFields(["name", "description"]);

      const username = localStorage.getItem("@dsm/username");
      const title = "Adição de Forma de Pagamento";
      const description = `${username} adicionou a forma de pagamento: ${addedItem.name}`;
      logNewAuditAction(username, title, description);

      message.success(
        `Forma de pagamento "${currentPaymentMethod.name}" adicionada com sucesso!`
      );
      setCurrentPaymentMethod({
        id: "",
        name: "",
        description: "",
      });
    }
  };

  const handleCloseModal = () => {
    setShowModal(false);
  };

  const columns = [
    {
      title: "Nome",
      dataIndex: "name",
      key: "name",
    },
    {
      title: "Descrição",
      dataIndex: "description",
      key: "description",
    },
  ];

  return (
    <>
      <Button
        type="primary"
        onClick={() => {
          setChildPaymentMethods(paymentMethodsParent.paymentMethodsParent);
          setShowModal(true);
        }}
      >
        Adicionar Forma de Pagamento <PlusOutlined />
      </Button>
      <Modal
        title="Adicionar Forma de Pagamento"
        visible={showModal}
        width={"80%"}
        closable={false}
        destroyOnClose={true}
        footer={null}
        onCancel={() => {
          setShowModal(false);
          setPaymentMethods(childPaymentMethods);
        }}
      >
        <Form
          layout="horizontal"
          form={paymentMethodForm}
          onFinish={handleSubmit}
        >
          <Row gutter={[16, 16]} justify="center">
            <Col span={24}>
              <Title level={5} style={{ fontWeight: "400", fontSize: "14px" }}>
                Forma de pagamento
              </Title>
              <Form.Item name={"name"}>
                <Input
                  required
                  onChange={(e) => {
                    setCurrentPaymentMethod({
                      ...currentPaymentMethod,
                      name: e.target.value,
                    });
                  }}
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={[16, 16]} justify="center">
            <Col span={24}>
              <Title level={5} style={{ fontWeight: "400", fontSize: "14px" }}>
                Descrição
              </Title>
              <Form.Item name={"description"}>
                <Input
                  required
                  onChange={(e) => {
                    setCurrentPaymentMethod({
                      ...currentPaymentMethod,
                      description: e.target.value,
                    });
                  }}
                />
              </Form.Item>
            </Col>
          </Row>

          <Row justify="center">
            <Form.Item>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={handleAddItem}
                disabled={
                  !currentPaymentMethod.name ||
                  !currentPaymentMethod.description
                }
              >
                Adicionar
              </Button>
            </Form.Item>
          </Row>
        </Form>

        <Row style={{ marginBottom: "1rem" }}>
          <Col span={24}>
            {childPaymentMethods?.length > 0 ? (
              <Table
                dataSource={childPaymentMethods.map((item, idex) => {
                  return {
                    name: `${item?.name}`,
                    description: item?.description,
                  };
                })}
                columns={columns}
                scroll={{ x: 100 }}
                pagination={true}
              ></Table>
            ) : (
              <Row justify="center" gutter={[24, 16]}>
                <Col span={24}>
                  <Row justify="center">
                    <DollarCircleOutlined style={{ fontSize: "5rem" }} />
                  </Row>
                  <Row justify="center">
                    <Title level={5} style={{ fontWeight: "400" }}>
                      Não existem formas de pagamento selecionadas
                    </Title>
                  </Row>
                </Col>
              </Row>
            )}
          </Col>
        </Row>

        <Row gutter={[16, 16]} justify={"space-between"}>
          <Button type="text-color" onClick={handleCloseModal}>
            Cancelar
          </Button>
          <Button type="primary" onClick={handleSubmit}>
            Salvar <CheckOutlined />
          </Button>
        </Row>
      </Modal>
    </>
  );
};
