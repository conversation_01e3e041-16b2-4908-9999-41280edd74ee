import React, {useState, useEffect} from 'react'
import {Modal, Spin, Table} from "antd";
import {ArrowLeftOutlined} from "@ant-design/icons";
import {jiraGet} from "../../../service/apiJira";
import "./projectDetailsStyles.css";

export const ProjectDetails = ({data, toggleTable}) => {
    const columns = [
        {
            title: 'Profissional',
            dataIndex: 'user',
            key: 'user',
            render: (user) => <p className="table-font">{user}</p>,
        },
        {
            title: 'Consumidas',
            dataIndex: 'registeredTime',
            key: 'registeredTime',
            render: (registeredTime) => <p className="table-font">{(registeredTime / 3600).toFixed(2)} horas</p>,
        },
        {
            title: 'Contratadas',
            dataIndex: 'engagedTime',
            key: 'engagedTime',
            render: (engagedTime) => <p className="table-font">{(engagedTime / 3600).toFixed(2)} horas</p>,
        },
    ];

    const [loading, setLoading] = useState(false);
    const [tableData, setTableData] = useState([]);
    const [modalVisible, setModalVisible] = useState(false);

    const {availableTime} = data;
    const {description} = data;

    async function getTableData() {
        const {issues, engagedTime} = data;

        let payloadArray = [];

        setLoading(true);

        for (let issue of issues) {
            const {key, fields} = issue;
            const {data} = await jiraGet(`read/worklogs/${key}`);
            const {worklogs} = data;

            for (let worklog of worklogs) {
                const {updateAuthor, timeSpentSeconds} = worklog;
                const {displayName} = updateAuthor;

                const payloadItem = payloadArray.find((item) => item.user === displayName);

                if (!payloadItem) {
                    payloadArray.push({
                        user: displayName,
                        project: fields.project.name,
                        registeredTime: timeSpentSeconds,
                        engagedTime: engagedTime
                    });
                } else {
                    payloadItem.registeredTime += timeSpentSeconds;
                }
            }
        }

        setTableData(payloadArray);
        setLoading(false);
    }

    function showModal() {
        setModalVisible(!modalVisible)
    }

    function handleOk() {
        setModalVisible(false)
    }

    function handleCancel() {
        setModalVisible(false)
    }

    useEffect(() => {
        getTableData()
    }, []);

    return (
        <div className="card">
            {loading && (
                <div className="loading">
                    <Spin/>
                </div>
            )}
            <div className="card-header details-main-header">
                <div className="button">
                    <button className="filter-clear-btn button-back" onClick={toggleTable}>
                        <ArrowLeftOutlined/>
                    </button>
                </div>
                <div className="header-infos">
                    <h3 className="card-title title-details">Cockpit</h3>
                </div>
                <div className='button'>
                    <button className="button-details" onClick={showModal}>Descrição do Projeto</button>
                </div>
            </div>
            <div className="subtitle-header">
                <h3 className="">Informações do Contrato - {availableTime / 3600}h disponíveis</h3>
            </div>
            <div className="table-padding">
                <Table scroll={{y: 130}} columns={columns} dataSource={tableData}/>
            </div>
            <Modal
                closable={false}
                style={{display: 'flex', justifyContent: 'center'}}
                bodyStyle={{backgroundColor: 'transparent'}}
                cancelButtonProps={{style: {display: 'none'}}}
                okButtonProps={{
                    style: {
                        display: 'none',
                        backgroundColor: '#43914F',
                        color: 'white',
                        fontWeight: 'bold',
                        borderRadius: '5px',
                    },
                }}
                okText={'Esconder'}
                open={modalVisible}
                onOk={handleOk}
                onCancel={handleCancel}
            >
                <div>{description}</div>
            </Modal>
        </div>
    )
}