import { Select } from 'antd';


export const UsersSelect = (props) => {
    const { Option } = Select;
    return(
        <Select
            loading={props?.receiver?.length <= 0 ? true : false}
            showSearch
            placeholder={props?.placeholder}
            value={props?.currentValue === "" ? props?.value : props?.currentValue}
            optionFilterProp="children"
            onSelect={(e) => props.name === 'queue' ? props.setQueue(e) : props.name === 'receiver' ? props.setReceiver(e) : props.setSender(e)}
            filterOption={(input, option) =>
            option?.children
                ?.toLowerCase()
                .includes(input?.toLowerCase())
            }
            filterSort={(optionA, optionB) =>
            optionA.children
                ?.toLowerCase()
                .localeCompare(optionB.children?.toLowerCase())
            }
        >
        {
            props.name === 'queue' 
            ?
            props?.queueItems?.map((item, index) => {
                return(
                    <Option key={index} value={item?.id}>{item?.name}</Option>
                )
            })
            :
            props.name === 'receiver' ?
                props?.receiver.map(t => (
                    <Option value={t.email}>{t.email}</Option>
                )) 
                :
                props?.sender.map(t => (
                    <Option value={t.email}>{t.email}</Option>
            )) 
        }
        </Select>
    )
}