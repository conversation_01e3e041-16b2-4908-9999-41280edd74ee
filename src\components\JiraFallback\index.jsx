/**
 * Componente de fallback para quando a API Jira não está disponível
 */

import React from 'react';
import { Card, Alert, Typography, Space } from 'antd';
import { ExclamationCircleOutlined } from '@ant-design/icons';

const { Title, Text } = Typography;

export const JiraFallback = ({ 
  title = "Funcionalidade Indisponível", 
  message = "A integração com Jira não está configurada.",
  showDetails = false 
}) => {
  return (
    <Card 
      bordered={false} 
      style={{ 
        borderRadius: "20px", 
        height: "100%",
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}
    >
      <Space direction="vertical" align="center" style={{ textAlign: 'center' }}>
        <ExclamationCircleOutlined 
          style={{ 
            fontSize: '48px', 
            color: '#faad14',
            marginBottom: '16px'
          }} 
        />
        
        <Title level={4} style={{ fontWeight: 400, margin: 0 }}>
          {title}
        </Title>
        
        <Text type="secondary" style={{ marginBottom: '16px' }}>
          {message}
        </Text>
        
        {showDetails && (
          <Alert
            message="Configuração Necessária"
            description="Para habilitar esta funcionalidade, configure a variável de ambiente REACT_APP_API_JIRA_BASE_URL."
            type="info"
            showIcon
            style={{ maxWidth: '400px' }}
          />
        )}
      </Space>
    </Card>
  );
};

export const JiraTableFallback = ({ columns = [] }) => {
  return (
    <Card 
      bordered={false} 
      style={{ borderRadius: "20px", height: "100%" }}
    >
      <Space direction="vertical" style={{ width: '100%' }}>
        <Alert
          message="Dados do Jira Indisponíveis"
          description="A integração com Jira não está configurada. Esta tabela ficará vazia até que a configuração seja realizada."
          type="warning"
          showIcon
          style={{ marginBottom: '16px' }}
        />
        
        {/* Tabela vazia para manter layout */}
        <div style={{ 
          border: '1px solid #f0f0f0',
          borderRadius: '6px',
          padding: '16px',
          textAlign: 'center',
          backgroundColor: '#fafafa'
        }}>
          <Text type="secondary">Nenhum dado disponível</Text>
        </div>
      </Space>
    </Card>
  );
};

export default JiraFallback;
