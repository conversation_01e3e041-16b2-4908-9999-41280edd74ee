/**
 * Validadores melhorados e corrigidos
 * Substituem as validações problemáticas anteriores
 */

// Regex melhorada para email
const EMAIL_REGEX = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

// Regex para diferentes tipos de validação
const VALIDATION_REGEX = {
  email: EMAIL_REGEX,
  phone: /^[\d\s\-+()]+$/,
  onlyNumbers: /^\d+$/,
  onlyLetters: /^[a-zA-ZÀ-ÿ\s]+$/,
  alphanumeric: /^[a-zA-Z0-9\s\-_.]+$/,
  cnpj: /^\d{2}\.\d{3}\.\d{3}\/\d{4}-\d{2}$/,
  cpf: /^\d{3}\.\d{3}\.\d{3}-\d{2}$/,
  cep: /^\d{5}-?\d{3}$/,
  currency: /^\d+([.,]\d{1,2})?$/,
  percentage: /^(100|[1-9]?\d)(\.\d{1,2})?$/,
  daredeEmail: /^[a-zA-Z]+\.[a-zA-Z]+@darede\.com\.br$/
};

/**
 * Valida email com regex melhorada
 */
export function validateEmail(email) {
  if (!email || typeof email !== 'string') {
    return false;
  }
  return VALIDATION_REGEX.email.test(email.trim());
}

/**
 * Remove todos os caracteres não numéricos
 */
export function onlyNumber(value) {
  if (!value) return '';
  return value.toString().replace(/\D/g, "");
}

/**
 * Remove números e caracteres especiais, mantém apenas letras e espaços
 */
export function onlyLetters(value) {
  if (!value) return '';
  return value
    .toString()
    .replace(/[^a-zA-ZÀ-ÿ\s]/g, "")
    .trim();
}

/**
 * Verifica se o valor contém apenas letras (sem números ou caracteres especiais)
 */
export function isOnlyLetter(value) {
  if (!value || typeof value !== 'string') {
    return false;
  }
  return VALIDATION_REGEX.onlyLetters.test(value.trim());
}

/**
 * Valida CNPJ
 */
export function validateCNPJ(cnpj) {
  if (!cnpj) return false;
  return VALIDATION_REGEX.cnpj.test(cnpj);
}

/**
 * Valida CPF
 */
export function validateCPF(cpf) {
  if (!cpf) return false;
  return VALIDATION_REGEX.cpf.test(cpf);
}

/**
 * Valida telefone
 */
export function validatePhone(phone) {
  if (!phone) return false;
  const cleanPhone = phone.replace(/\D/g, '');
  return cleanPhone.length >= 10 && cleanPhone.length <= 11;
}

/**
 * Valida CEP
 */
export function validateCEP(cep) {
  if (!cep) return false;
  return VALIDATION_REGEX.cep.test(cep);
}

/**
 * Valida valor monetário
 */
export function validateCurrency(value) {
  if (!value) return false;
  return VALIDATION_REGEX.currency.test(value.toString());
}

/**
 * Valida porcentagem (0-100)
 */
export function validatePercentage(value) {
  if (!value) return false;
  return VALIDATION_REGEX.percentage.test(value.toString());
}

/**
 * Valida email da Darede
 */
export function validateDaredeEmail(email) {
  if (!email) return false;
  return VALIDATION_REGEX.daredeEmail.test(email);
}

/**
 * Validador genérico baseado em tipo
 */
export function validate(value, type) {
  const validators = {
    email: validateEmail,
    phone: validatePhone,
    cnpj: validateCNPJ,
    cpf: validateCPF,
    cep: validateCEP,
    currency: validateCurrency,
    percentage: validatePercentage,
    daredeEmail: validateDaredeEmail,
    onlyLetters: isOnlyLetter
  };

  const validator = validators[type];
  return validator ? validator(value) : true;
}

// Exportar regex para uso direto se necessário
export { VALIDATION_REGEX };
