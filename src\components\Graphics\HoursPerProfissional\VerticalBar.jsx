import React, { useEffect, useState } from "react";
import moment from "moment";
import HoursPerProfissionalFilter from "../../Modals/Filters/HoursPerProfissionalFilter";
import "./HoursPerProfissional.css";
import {
  AlignLeftOutlined,
  Bar<PERSON>hartOutlined,
  BgColorsOutlined,
  FilterFilled,
  StarFilled,
  StarOutlined,
} from "@ant-design/icons";
import { Col, Popover, Row, Spin, Typography } from "antd";
import ColorPicker from "../../ColorPicker";
import { otrsGet, otrsPost } from "../../../service/apiOtrs";
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip } from "recharts";
import { faListSquares } from "@fortawesome/free-solid-svg-icons";

moment.updateLocale("pt", {
  months: [
    "Janeiro",
    "Fevereiro",
    "Março",
    "<PERSON>bri<PERSON>",
    "<PERSON><PERSON>",
    "<PERSON><PERSON>",
    "<PERSON><PERSON>",
    "<PERSON><PERSON><PERSON>",
    "<PERSON><PERSON><PERSON>",
    "<PERSON><PERSON><PERSON>",
    "<PERSON><PERSON><PERSON>",
    "Dezembro",
  ],
});

export default function VerticalBar(props) {
  const { Title, Text } = Typography;
  const [isFavorite, setIsFavorite] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [colors, setColors] = useState();
  const [width, setWidth] = React.useState(window.innerWidth);
  let secondaryProportion = window.innerWidth;
  const [data, setData] = useState([]);
  const GRAPH_NAME = "BarGrapich";

  const notBillableContracts = [
    "Darede Apoio Técnico",
    "Darede Labs",
    "Darede - Atendimento 8x5",
    "Darede - Atendimento 24x7",
    "Darede SI",
  ];

  function transformMinutesInHours(ticketsInMinutes) {
    ticketsInMinutes.map((item) => {
      const totalMinutes = item.billable;
      const hours = Math.floor(totalMinutes / 60);
      const minutes = (totalMinutes % 60) / 100;
      const result = hours + minutes;
      item.billable = result;
    });
    ticketsInMinutes.map((item) => {
      const totalMinutes = item.notBillable;
      const hours = Math.floor(totalMinutes / 60);
      const minutes = (totalMinutes % 60) / 100;
      const result = hours + minutes;
      item.notBillable = result;
    });
    return ticketsInMinutes;
  }

  // useEffect(() => {
  //   getFormerMonthsHours(props?.filterParameter);
  // }, [props.filterParameter]);

  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      return (
        <div
          style={{
            backgroundColor: "white",
            padding: "10px",
            borderRadius: "20px",
            boxShadow: "2px 2px 10px black",
          }}
          className="tooltip"
        >
          <p className="tooltipLabel">{`Não billada: ${
            Math.floor(payload[0].payload?.notBillable) +
            "h" +
            Math.floor(
              (payload[0].payload?.notBillable -
                Math.floor(payload[0].payload?.notBillable)) *
                60
            ) +
            "m"
          }`}</p>
          <p className="tooltipDesc">{`Billada: ${
            Math.floor(payload[0].payload?.billable) +
            "h" +
            Math.floor(
              (payload[0].payload?.billable -
                Math.floor(payload[0].payload?.billable)) *
                60
            ) +
            "m"
          }`}</p>
        </div>
      );
    }

    return null;
  };

  const updateWidth = () => {
    setWidth(window.innerWidth);
  };
  React.useEffect(() => {
    window.addEventListener("resize", updateWidth);

    return () => window.removeEventListener("resize", updateWidth);
  });

  const graphWidth = React.useMemo(() => {
    if (width > 1800) {
      const proportion = 1850 / 800;
      return width / proportion;
    } else if (width > 1200 && width < 1800) {
      const proportion = 1850 / 500;
      return width / proportion;
    } else if (width < 1200) {
      const proportion = 1850 / 500;
      return width / proportion;
    }
  }, [width]);

  const secondaryGraphWidth = React.useMemo(() => {
    if (width > 1800) {
      const proportion = 1850 / 800;
      return width / proportion;
    } else if (width > 1200 && width < 1800) {
      const proportion = 1850 / 850;
      return width / proportion;
    } else if (width < 1200) {
      const proportion = 1850 / 1550;
      return width / proportion;
    }
  }, [width]);

  // console.log("Data: ", data, props.filterParameter, props.loading);

  return (
    <>
      {props.loading === true ? (
        <Row style={{ height: "250px" }} justify="center" align="middle">
          <Spin />
        </Row>
      ) : !props?.filterParameter ? (
        <Row style={{ height: "250px" }} align="middle" justify="center">
          <Col>
            <Text>Selecione os parâmetros de pequisa*</Text>
          </Col>
        </Row>
      ) : (
        <Row>
          <Col span={24}>
          <BarChart
            width={
              secondaryProportion
                ? width < 1600
                  ? secondaryGraphWidth - 200
                  : secondaryGraphWidth - 150
                : width < 1600
                ? graphWidth - 350
                : graphWidth - 150
            }
            height={300}
            margin={{right: 30}}
            data={props.barChartData}
          >
            <CartesianGrid vertical={false} strokeDasharray="3 3" />
            <XAxis fontSize={16} dataKey="name" />
            <YAxis fontSize={16} />
            <Tooltip content={<CustomTooltip />} cursor={false} />
            <Bar
              dataKey="notBillable"
              stackId="a"
              fill={colors ? colors[1].color : `#435E91`}
            />
            <Bar
              dataKey="billable"
              stackId="b"
              fill={colors ? colors[2].color : `#43914F`}
            />
          </BarChart>
          {/* <div style={{ justifyContent: "center" }} className="infosContainer">
              <div className="infoSingle">
                <div
                  className="captionColorBillable"
                  style={{
                    backgroundColor: colors
                      ? colors[1].color
                      : `#43914F`,
                  }}
                ></div>
                <Text>Billada</Text>
              </div>
              <div className="infoSingle">
                <div
                  className="captionColorBillable"
                  style={{
                    backgroundColor: colors
                      ? colors[2].color
                      : `#435E91`,
                  }}
                ></div>
                <Text>Não Billada</Text>
              </div>
            </div> */}
          </Col>
        </Row>
      )}
    </>
  );
}
