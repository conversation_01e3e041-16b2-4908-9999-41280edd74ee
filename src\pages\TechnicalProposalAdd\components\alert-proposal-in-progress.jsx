import {
    Row,
    Col,
    Space,
    Button,
    Alert
} from "antd";
import { useEffect, useState } from "react";
import { shallowEqual, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { deletePersistData, existPersistData } from "../../../controllers/Proposals/proposal-controller";

export const AlertProposalInProgress = () => {
    const persistID = useSelector(state => state.technicalProposal.persistID, shallowEqual)
    const navigate = useNavigate()
    const [persistData, setPersistData] = useState(null)

    const [show, setShow] = useState('')

    useEffect(() => {
        initPage()
    }, [])

    async function initPage() {
        const localStoragePersistID = localStorage.getItem('persist-proposal-id')
        if (localStoragePersistID || persistID) {
            let id = localStoragePersistID

            if (persistID && localStoragePersistID !== persistID) {
                deletePersistData(localStoragePersistID)
                id = persistID
                localStorage.setItem('persist-proposal-id', id)
            }

            const response = await existPersistData(id)
            if (response) {
                setPersistData(response)
                setShow(true)
            }
            else {
                setShow(false)
                localStorage.removeItem('persist-proposal-id')
            }

        }
    }

    function onRemovePersist() {
        const localStoragePersistID = localStorage.getItem('persist-proposal-id')
        deletePersistData(localStoragePersistID)
    }

    function onRestorePersist() {
        if (persistData) {
            const { proposalID } = persistData
            if (proposalID)
                navigate(`/technical-proposals/edit/${proposalID}`)
            else
                navigate(`/technical-proposals/add`)
        }
    }

    return (
        show ?
            <Row>
                <Col>
                    <Alert
                        message="Você possui uma proposta em progresso, deseja restaura-la?"
                        type="warning"
                        action={
                            <Space>
                                <Button
                                    size="small"
                                    type="ghost"
                                    onClick={onRestorePersist}
                                >
                                    Restaurar
                                </Button>
                            </Space>
                        }
                        onClose={onRemovePersist}
                        closable
                    />
                </Col>
            </Row>
            :
            null
    )
}