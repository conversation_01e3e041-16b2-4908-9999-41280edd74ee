/**
 * Serviço de Cache Inteligente
 * Implementa estratégias de cache com TTL, invalidação e persistência
 */

class CacheService {
  constructor(options = {}) {
    this.defaultTTL = options.defaultTTL || 300000; // 5 minutos
    this.maxSize = options.maxSize || 100; // Máximo de 100 entradas
    this.storage = options.storage || 'memory'; // 'memory' | 'localStorage' | 'sessionStorage'
    this.prefix = options.prefix || 'cache_';
    
    // Cache em memória
    this.memoryCache = new Map();
    
    // Configurar limpeza automática
    this.setupCleanup();
  }

  /**
   * Gera chave de cache
   */
  generateKey(key, params = {}) {
    const paramString = Object.keys(params)
      .sort()
      .map(k => `${k}:${JSON.stringify(params[k])}`)
      .join('|');
    
    return paramString ? `${key}:${paramString}` : key;
  }

  /**
   * Obtém item do cache
   */
  get(key, params = {}) {
    const cacheKey = this.generateKey(key, params);
    
    try {
      let item;
      
      if (this.storage === 'memory') {
        item = this.memoryCache.get(cacheKey);
      } else {
        const stored = this.getFromStorage(cacheKey);
        item = stored ? JSON.parse(stored) : null;
      }
      
      if (!item) {
        return null;
      }
      
      // Verificar TTL
      if (Date.now() > item.expiry) {
        this.delete(key, params);
        return null;
      }
      
      // Atualizar último acesso
      item.lastAccess = Date.now();
      
      if (this.storage === 'memory') {
        this.memoryCache.set(cacheKey, item);
      } else {
        this.setToStorage(cacheKey, JSON.stringify(item));
      }
      
      return item.data;
    } catch (error) {
      console.warn('Erro ao obter do cache:', error);
      return null;
    }
  }

  /**
   * Define item no cache
   */
  set(key, data, ttl = this.defaultTTL, params = {}) {
    const cacheKey = this.generateKey(key, params);
    
    try {
      const item = {
        data,
        expiry: Date.now() + ttl,
        created: Date.now(),
        lastAccess: Date.now(),
        ttl,
        key: cacheKey
      };
      
      if (this.storage === 'memory') {
        // Verificar limite de tamanho
        if (this.memoryCache.size >= this.maxSize) {
          this.evictLRU();
        }
        
        this.memoryCache.set(cacheKey, item);
      } else {
        this.setToStorage(cacheKey, JSON.stringify(item));
      }
      
      return true;
    } catch (error) {
      console.warn('Erro ao definir no cache:', error);
      return false;
    }
  }

  /**
   * Remove item do cache
   */
  delete(key, params = {}) {
    const cacheKey = this.generateKey(key, params);
    
    try {
      if (this.storage === 'memory') {
        return this.memoryCache.delete(cacheKey);
      } else {
        this.removeFromStorage(cacheKey);
        return true;
      }
    } catch (error) {
      console.warn('Erro ao remover do cache:', error);
      return false;
    }
  }

  /**
   * Verifica se item existe no cache
   */
  has(key, params = {}) {
    return this.get(key, params) !== null;
  }

  /**
   * Limpa todo o cache
   */
  clear() {
    try {
      if (this.storage === 'memory') {
        this.memoryCache.clear();
      } else {
        this.clearStorage();
      }
      return true;
    } catch (error) {
      console.warn('Erro ao limpar cache:', error);
      return false;
    }
  }

  /**
   * Obtém estatísticas do cache
   */
  getStats() {
    const stats = {
      size: 0,
      hits: 0,
      misses: 0,
      entries: []
    };
    
    if (this.storage === 'memory') {
      stats.size = this.memoryCache.size;
      
      for (const [key, item] of this.memoryCache.entries()) {
        stats.entries.push({
          key,
          size: JSON.stringify(item.data).length,
          created: new Date(item.created),
          lastAccess: new Date(item.lastAccess),
          expiry: new Date(item.expiry),
          ttl: item.ttl
        });
      }
    } else {
      // Para storage persistente, contar chaves com prefixo
      const storage = this.getStorageObject();
      for (const key in storage) {
        if (key.startsWith(this.prefix)) {
          stats.size++;
          try {
            const item = JSON.parse(storage[key]);
            stats.entries.push({
              key: key.replace(this.prefix, ''),
              size: storage[key].length,
              created: new Date(item.created),
              lastAccess: new Date(item.lastAccess),
              expiry: new Date(item.expiry),
              ttl: item.ttl
            });
          } catch (error) {
            // Ignorar entradas corrompidas
          }
        }
      }
    }
    
    return stats;
  }

  /**
   * Remove entradas expiradas
   */
  cleanup() {
    const now = Date.now();
    let removed = 0;
    
    if (this.storage === 'memory') {
      for (const [key, item] of this.memoryCache.entries()) {
        if (now > item.expiry) {
          this.memoryCache.delete(key);
          removed++;
        }
      }
    } else {
      const storage = this.getStorageObject();
      for (const key in storage) {
        if (key.startsWith(this.prefix)) {
          try {
            const item = JSON.parse(storage[key]);
            if (now > item.expiry) {
              this.removeFromStorage(key.replace(this.prefix, ''));
              removed++;
            }
          } catch (error) {
            // Remover entradas corrompidas
            this.removeFromStorage(key.replace(this.prefix, ''));
            removed++;
          }
        }
      }
    }
    
    return removed;
  }

  /**
   * Remove item menos recentemente usado (LRU)
   */
  evictLRU() {
    if (this.storage !== 'memory') return;
    
    let oldestKey = null;
    let oldestTime = Date.now();
    
    for (const [key, item] of this.memoryCache.entries()) {
      if (item.lastAccess < oldestTime) {
        oldestTime = item.lastAccess;
        oldestKey = key;
      }
    }
    
    if (oldestKey) {
      this.memoryCache.delete(oldestKey);
    }
  }

  /**
   * Configura limpeza automática
   */
  setupCleanup() {
    // Limpeza a cada 5 minutos
    this.cleanupInterval = setInterval(() => {
      this.cleanup();
    }, 300000);
  }

  /**
   * Para a limpeza automática
   */
  stopCleanup() {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }
  }

  /**
   * Obtém do storage persistente
   */
  getFromStorage(key) {
    const storage = this.getStorageObject();
    return storage.getItem(this.prefix + key);
  }

  /**
   * Define no storage persistente
   */
  setToStorage(key, value) {
    const storage = this.getStorageObject();
    storage.setItem(this.prefix + key, value);
  }

  /**
   * Remove do storage persistente
   */
  removeFromStorage(key) {
    const storage = this.getStorageObject();
    storage.removeItem(this.prefix + key);
  }

  /**
   * Limpa storage persistente
   */
  clearStorage() {
    const storage = this.getStorageObject();
    const keys = [];
    
    for (let i = 0; i < storage.length; i++) {
      const key = storage.key(i);
      if (key && key.startsWith(this.prefix)) {
        keys.push(key);
      }
    }
    
    keys.forEach(key => storage.removeItem(key));
  }

  /**
   * Obtém objeto de storage
   */
  getStorageObject() {
    switch (this.storage) {
      case 'localStorage':
        return localStorage;
      case 'sessionStorage':
        return sessionStorage;
      default:
        throw new Error('Storage inválido para operação persistente');
    }
  }

  /**
   * Destrói o serviço de cache
   */
  destroy() {
    this.stopCleanup();
    this.clear();
  }
}

/**
 * Instância padrão do cache
 */
export const defaultCache = new CacheService({
  storage: 'memory',
  defaultTTL: 300000, // 5 minutos
  maxSize: 100
});

/**
 * Cache persistente para dados importantes
 */
export const persistentCache = new CacheService({
  storage: 'localStorage',
  defaultTTL: 3600000, // 1 hora
  prefix: 'dsm_cache_'
});

/**
 * Cache de sessão para dados temporários
 */
export const sessionCache = new CacheService({
  storage: 'sessionStorage',
  defaultTTL: 1800000, // 30 minutos
  prefix: 'dsm_session_'
});

/**
 * Wrapper para cache com invalidação automática
 */
export class SmartCache extends CacheService {
  constructor(options = {}) {
    super(options);
    this.dependencies = new Map(); // key -> Set of dependent keys
    this.subscribers = new Map(); // key -> Set of callback functions
  }

  /**
   * Define dependências entre chaves de cache
   */
  setDependency(key, dependsOn) {
    if (!this.dependencies.has(dependsOn)) {
      this.dependencies.set(dependsOn, new Set());
    }
    this.dependencies.get(dependsOn).add(key);
  }

  /**
   * Invalida cache e suas dependências
   */
  invalidate(key, params = {}) {
    const cacheKey = this.generateKey(key, params);

    // Remover da cache
    this.delete(key, params);

    // Invalidar dependências
    if (this.dependencies.has(cacheKey)) {
      for (const dependentKey of this.dependencies.get(cacheKey)) {
        this.invalidate(dependentKey);
      }
    }

    // Notificar subscribers
    this.notifySubscribers(cacheKey);
  }

  /**
   * Subscreve a mudanças em uma chave
   */
  subscribe(key, callback, params = {}) {
    const cacheKey = this.generateKey(key, params);

    if (!this.subscribers.has(cacheKey)) {
      this.subscribers.set(cacheKey, new Set());
    }

    this.subscribers.get(cacheKey).add(callback);

    // Retorna função para cancelar subscription
    return () => {
      const callbacks = this.subscribers.get(cacheKey);
      if (callbacks) {
        callbacks.delete(callback);
        if (callbacks.size === 0) {
          this.subscribers.delete(cacheKey);
        }
      }
    };
  }

  /**
   * Notifica subscribers sobre mudanças
   */
  notifySubscribers(cacheKey) {
    const callbacks = this.subscribers.get(cacheKey);
    if (callbacks) {
      callbacks.forEach(callback => {
        try {
          callback(cacheKey);
        } catch (error) {
          console.warn('Erro ao notificar subscriber:', error);
        }
      });
    }
  }

  /**
   * Override do set para notificar subscribers
   */
  set(key, data, ttl = this.defaultTTL, params = {}) {
    const result = super.set(key, data, ttl, params);

    if (result) {
      const cacheKey = this.generateKey(key, params);
      this.notifySubscribers(cacheKey);
    }

    return result;
  }
}

/**
 * Cache inteligente padrão
 */
export const smartCache = new SmartCache({
  storage: 'memory',
  defaultTTL: 300000,
  maxSize: 100
});

export default CacheService;
