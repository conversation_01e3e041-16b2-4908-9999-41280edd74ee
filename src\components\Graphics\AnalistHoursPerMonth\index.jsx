import { Table, Select, Spin, Row, Col, Card, Typography } from "antd";
import { React, useState, useEffect } from "react";
import { jiraGet } from "../../../service/apiJira";
export const AnalistHoursPerMonth = (props) => {
  const { Text } = Typography;
  const [data, setData] = useState([]);
  const [allProjects, setAllProjects] = useState([]);
  const [allUsers, setAllUsers] = useState([]);
  const [selectedAnalist, setSelectedAnalist] = useState([]);
  const [selectedProject, setSelectedProject] = useState([]);
  const [searchAnalist, setSearchAnalist] = useState(false);
  const [searchProject, setSearchProject] = useState(false);
  const [loadingUsers, setLoadingUsers] = useState(false);
  const [loadingAnalist, setLoadingAnalist] = useState(false);
  const [loadingProject, setLoadingProject] = useState(false);

  const { Option } = Select;
  const columns = [
    {
      title: "Analista",
      dataIndex: "user",
      key: "user",
      render: (user) => <Text>{user}</Text>,
    },
    {
      title: "Projeto",
      dataIndex: "project",
      key: "project",
      render: (project) => <Text>{project}</Text>,
    },
    {
      title: "Horas registradas",
      dataIndex: "registeredTime",
      key: "registeredTime",
      render: (registeredTime) => (
        <Text>{(registeredTime / 3600).toFixed(2)} horas</Text>
      ),
    },
  ];
  useEffect(() => {
    getAllProjects();
    getAllUsers();
  }, []);

  async function getAllProjects() {
    setLoadingProject(true);
    const { data } = await jiraGet("read/projects").then((resp) => {
      setLoadingProject(false);
    });

    setAllProjects(data.values);
  }

  async function getAllUsers() {
    setLoadingUsers(true);
    const { data } = await jiraGet("read/users").then(() => {
      setLoadingUsers(false);
    });
    setAllUsers(data);
  }

  async function getAnalistData(professional) {
    let payloadAnalist = [];

    if (searchProject) {
      if (professional === "Todos") {
        setData(selectedProject);
      } else {
        let filteredAnalist = [];
        selectedProject.map((contract) => {
          if (contract.user === professional) {
            filteredAnalist.push(contract);
          }
        });

        setData(filteredAnalist);
      }
    } else {
      if (professional === "Limpar") {
        setData([]);
        setSearchAnalist(false);
      } else {
        setLoadingAnalist(true);
        setSearchAnalist(true);

        const { data } = await jiraGet(`read/user/issues/${professional}`);
        const { issues } = data;

        for (let issue of issues) {
          const { key, fields } = issue;

          const { data } = await jiraGet(`read/worklogs/${key}`);
          const { worklogs } = data;

          for (let worklog of worklogs) {
            const { updateAuthor, timeSpentSeconds } = worklog;

            if (professional === updateAuthor.displayName) {
              const payloadItem = payloadAnalist.find(
                (item) =>
                  item.user === updateAuthor.displayName &&
                  item.project === fields.project.name
              );

              if (!payloadItem) {
                payloadAnalist.push({
                  user: updateAuthor.displayName,
                  project: fields.project.name,
                  registeredTime: timeSpentSeconds,
                });
              } else {
                payloadItem.registeredTime += timeSpentSeconds;
              }
            }
          }
        }

        for (let item of payloadAnalist) {
          const { project } = item;
          const { data } = await jiraGet(`read/project/issues/${project}`);
          const { issues } = data;
        }

        setSelectedAnalist(payloadAnalist);
        setData(payloadAnalist);
        setLoadingAnalist(false);
      }
    }
  }

  async function getProjectData(project) {
    if (searchAnalist) {
      if (project === "Todos") {
        setData(selectedAnalist);
      } else {
        let filteredProjects = [];
        selectedAnalist.map((contract) => {
          if (contract.project === project) {
            filteredProjects.push(contract);
          }
        });

        setData(filteredProjects);
      }
    } else {
      let payloadArray = [];

      setData([]);
      setLoadingAnalist(true);

      if (project === "Limpar" || project === "Todos") {
        setData([]);
        setSearchProject(false);
        setLoadingAnalist(false);
      } else {
        setSearchProject(true);

        allUsers.map(async (user) => {
          const { data } = await jiraGet(
            `read/user/issues/${user.displayName}`
          );
          const { issues } = data;

          for (let issue of issues) {
            const { key } = issue;

            const { data } = await jiraGet(`read/worklogs/${key}`);
            const { worklogs } = data;

            for (let worklog of worklogs) {
              const { updateAuthor, timeSpentSeconds } = worklog;

              if (user.displayName === updateAuthor.displayName) {
                const payloadItem = payloadArray.find(
                  (item) =>
                    item.user === updateAuthor.displayName &&
                    item.project === project
                );

                if (!payloadItem) {
                  payloadArray.push({
                    user: updateAuthor.displayName,
                    project: project,
                    registeredTime: timeSpentSeconds,
                  });
                } else {
                  payloadItem.registeredTime += timeSpentSeconds;
                }
              }
            }
          }

          setTimeout(() => {
            setSelectedProject(payloadArray);
            setData(payloadArray);
            setLoadingAnalist(false);
          }, 20000);
        });
      }
    }
  }

  return (
    <Card
      bordered="false"
      style={{
        borderRadius: "20px",
        height: "100%",
        boxShadow: "0 0 10px rgba(0,0,0,0.1)",
      }}
    >
      <Row style={{ marginBottom: "15px" }} gutter={8}>
        <Col lg={7} sm={24}>
          <Select
            showSearch
            mode="default"
            placeholder="Analista"
            style={{ width: "100%", margin: "5px" }}
            loading={loadingAnalist}
            onChange={(name) => getAnalistData(name)}
            optionFilterProp="children"
            filterOption={(input, option) =>
              option.props.children
                .toString()
                .toLowerCase()
                .indexOf(input.toLowerCase()) >= 0 ||
              option.props.value
                .toString()
                .toLowerCase()
                .indexOf(input.toLowerCase()) >= 0
            }
          >
            <Option value={"Todos"}>{"Todos"}</Option>
            <Option value={"Limpar"}>{"Limpar"}</Option>
            {allUsers.map((user) => (
              <Option key={user.accountId} value={user.displayName}>
                {user.displayName}
              </Option>
            ))}
          </Select>
        </Col>
        <Col lg={7} sm={24}>
          <Select
            showSearch
            mode="default"
            placeholder="Projeto"
            style={{ width: "100%", margin: "5px" }}
            loading={loadingProject}
            onChange={(project) => getProjectData(project)}
            optionFilterProp="children"
            filterOption={(input, option) =>
              option.props.children
                .toString()
                .toLowerCase()
                .indexOf(input.toLowerCase()) >= 0 ||
              option.props.value
                .toString()
                .toLowerCase()
                .indexOf(input.toLowerCase()) >= 0
            }
          >
            <Option value={"Todos"}>{"Todos"}</Option>
            <Option value={"Limpar"}>{"Limpar"}</Option>
            {allProjects.map((project) => (
              <Option key={project.name} value={project.name}>
                {project.name}
              </Option>
            ))}
          </Select>
        </Col>
        <Col lg={7} sm={24}>
          <Select
            mode="default"
            placeholder="Billada"
            style={{ width: "100%", margin: "5px" }}
          >
            <option value={"Todos"}></option>
            <option value={"Billable"}></option>
            <option value={"Not Billable"}></option>
          </Select>
        </Col>
      </Row>
      <div>
        {loadingAnalist ? (
          <Row justify="center" align="middle">
            <Spin />
          </Row>
        ) : (
          ""
        )}
      </div>
      <Table scroll={{ x: "100%" }} columns={columns} dataSource={data}></Table>
    </Card>
  );
};
