import { useState } from "react";
import {
  Modal,
  Button,
  Input,
  Row,
  Col,
  Select,
  Form,
  Checkbox,
  Table,
} from "antd";
import { EditOutlined } from "@ant-design/icons";

const { Option } = Select;

export const AddModalPosition = () => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [active, setActive] = useState(false);
  const showModal = () => {
    setIsModalVisible(true);
  };

  const handleOk = () => {
    setIsModalVisible(false);
  };

  const handleCancel = () => {
    setIsModalVisible(false);
  };

  const CheckInput = () => {
    if (active == true) {
      setActive(false);
    } else {
      setActive(true);
    }
  };

  const dataSource = [
    {
      key: "1",
      position: "admin",
      status: "",
      action: "",
    },
  ];

  const columns = [
    {
      title: "Cargos",
      dataIndex: "position",
      key: "position",
    },
    {
      title: "Ações",
      dataIndex: "action",
      key: "action",
    },
  ];

  return (
    <>
      <Button type="primary" onClick={showModal}>
        Novo cargo
      </Button>
      <Modal
        title="Novo cargo"
        open={isModalVisible}
        onOk={handleOk}
        onCancel={handleCancel}
        okText="Criar cargo"
      >
        <Row>
          <Col span="24">
            <p>Nome</p>
          </Col>
          <Col span="24">
            <Form.Item>
              <Input placeholder="Digite o nome do usuario" />
            </Form.Item>
          </Col>
        </Row>
        <Row>
          <Col span="24">
            <p>Permissões de contrato</p>
          </Col>
          <Col span="24">
            <Form.Item>
              <Input placeholder="Digite para pesquisar" disabled={active} />
            </Form.Item>
          </Col>
          <Col>
            <Form.Item>
              <Checkbox checked={active} onChange={CheckInput}>
                Todos os contratos
              </Checkbox>
            </Form.Item>
          </Col>
        </Row>
        <Row>
          <Col span={24}>
            <Table
              style={{ minWidth: "100%", marginTop: "20px" }}
              dataSource={dataSource}
              columns={columns}
            />
          </Col>
        </Row>
        <Row>
          <Col span={24}>
            <p>Permissões de acesso</p>
          </Col>
          <Col span={24}>
            <Checkbox>Tickets</Checkbox>
          </Col>
          <Col span={24}>
            <Checkbox>Consumo de Horas</Checkbox>
          </Col>
          <Col span={24}>
            <Checkbox>Painel de Tickets</Checkbox>
          </Col>
          <Col span={24}>
            <Checkbox>Gestão de Acessos</Checkbox>
          </Col>
        </Row>
      </Modal>
    </>
  );
};
