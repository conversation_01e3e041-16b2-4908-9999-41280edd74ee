// const { CognitoJwtVerifier } = require('aws-jwt-verify')

const jose = require('jose')

export async function verifyExpTime(token) {
  try {
    const key = await jose.decodeJwt(token)

    const loginTime = new Date(key.auth_time * 1000)
    const expiringTime = new Date(key.exp * 1000)
    const currentTime = new Date(Date.now())

    if (currentTime > expiringTime) {
      return true
    } else {
      return false
    }
  } catch (err) {
    return
  }
}
