import { s3ListObjects } from "../../service/apiDsmS3";

export async function getFilesToBeDeleted(idFixed, files, keys = false, folder) {
    const bucketFilesResponse = await s3ListObjects(
      `${process.env.REACT_APP_STAGE}-proposals-documents`,
      `${idFixed}${folder}`
    );
  
    let filesToBeDeleted = [];
    let bucketFilesKeys = [];
  
  
    for (let i = 0; i < bucketFilesResponse.Contents.length; i++) {
      const keySplitted = bucketFilesResponse.Contents[i].Key.split("/");
      const fileNameExtractedFromKey = decodeURI(
        keySplitted[keySplitted.length - 1]
      );
  
      bucketFilesKeys.push(fileNameExtractedFromKey)
    }
  
    if (keys) {
      for (let i = 0; i < bucketFilesKeys.length; i++) {
        const foundKeyInFiles = files.find(f => f.name === bucketFilesKeys[i])
  
        if (foundKeyInFiles === undefined) {
          filesToBeDeleted.push(bucketFilesKeys[i])
        }
      }
    } else {
      filesToBeDeleted = files.filter(f => !bucketFilesKeys.includes(f.name))
    }
  
    return filesToBeDeleted;
  }