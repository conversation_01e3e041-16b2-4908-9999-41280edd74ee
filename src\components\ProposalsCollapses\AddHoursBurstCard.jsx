import { Row, Col, Form, Input, Card, Typography } from "antd";
import { invalidInputNumberChars } from "../../constants/invalidCharsInputNumber";
import { useContext } from "react";
import { formatHourName } from "../../controllers/Proposals/formatHourName";
import { ProposalContext } from "../../contexts/totalValuesProposals";
import { TotalValuesProposalType } from "../../contexts/reducers/totalValuesProposal";
import { totalValuesController } from "../../controllers/Proposals/totalValuesTechnical";

async function updateProperty(
  prop,
  value,
  totalValuesState,
  hourClass,
  month,
  formName
) {
  let newObj = totalValuesState;
  let newObjTotalValues = newObj.totalValues;

  let newFilteredTotalValues = totalValuesState?.totalValues?.find(
    (v) => v.hourClass === hourClass
  );

  let newFilteredTotalValuesByMonth = newFilteredTotalValues?.values?.find(
    (v) => v.month === month
  );

  newFilteredTotalValuesByMonth?.values?.map((v) => {
    if (v.formName === formName) {
      if (value === "reais" || value === "percentage") {
        v[prop] = value;
      } else {
        v[prop] = Number(value);
        // console.log("passou");
      }
    }
  });

  for (let i = 0; i < newObjTotalValues.length; i++) {
    if (newObjTotalValues[i].hourClass === hourClass) {
      newObjTotalValues[i] = newFilteredTotalValues;
    }
  }
  return newObjTotalValues;
}

export const AddHourBurstCard = ({
  hourClass,
  costManagementData,
  services,
}) => {
  const { Text } = Typography;
  const { proposalState, setProposalState } = useContext(ProposalContext);

  const filteredTotalValues = proposalState?.totalValues?.find(
    (v) => v.hourClass === hourClass
  );

  const filteredTotalValuesByMonth = filteredTotalValues?.values?.find(
    (v) => v.month === proposalState?.month
  );

  const handleInputValue = async (value, hourType) => {
    if (value < 0) value = 0;
    const calculatedValue = await totalValuesController(
      proposalState,
      costManagementData,
      services
    );
    console.log("burst", { calculatedValue });
    setProposalState({
      type: TotalValuesProposalType.SET_TOTAL_VALUES,
      value: calculatedValue,
    });
  };

  return (
    <Col xxl={8} xl={24} sm={24}>
      <Form.Item style={{ textAlign: "center" }}>
        <Text>Burst De Horas</Text>
        <Card
          style={{
            width: "100%",
            backgroundColor: "#D9D9D9",
            borderRadius: "5px",
          }}
        >
          <Row justify="space-between" gutter={[4, 16]}>
            {filteredTotalValuesByMonth?.values?.map((t) => {
              return (
                <Col span={12} style={{ textAlign: "center" }}>
                  <Text>{formatHourName(t.formName)}</Text>
                  <Input
                    style={{ textAlign: "center" }}
                    type="number"
                    // placeholder="0"
                    defaultValue={0}
                    onKeyDown={(e) => {
                      invalidInputNumberChars.find(
                        (keyPressed) => keyPressed === e.key
                      ) && e.preventDefault();
                    }}
                    onChange={(e) => handleInputValue(e.target.value, t)}
                  />
                </Col>
              );
            })}
          </Row>
        </Card>
      </Form.Item>
    </Col>
  );
};
