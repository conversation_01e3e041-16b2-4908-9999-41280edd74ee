import { <PERSON><PERSON>, Mo<PERSON> } from "antd";
import { EyeOutlined } from "@ant-design/icons";
import { useMemo, useState } from "react";

import * as controller from "../../../../controllers/reports/ticket-reports-controller";
import { DynamicTable } from "../../../../components/Table/DynamicTable";
import moment from "moment";
import { SearchInput } from "../../../../components/SearchInput";
import { filterTableData } from "../../../../utils/filterTableData";
import { Counter } from "../../../../components/Counter";

const columns = [
  {
    code: "view_article_id",
    title: "ID do Artigo",
    dataIndex: "article_id",
    key: "article_id",
    sorter: (a, b) => b.article_id - a.article_id,
    sortDirections: ["descend", "ascend"],
  },
  {
    code: "view_article_subject",
    title: "Assunto do Artigo",
    dataIndex: "article_subject",
    key: "article_subject",
    sorter: (a, b) => a.article_subject.localeCompare(b.article_subject),
    sortDirections: ["descend", "ascend"],
  },
  {
    code: "view_article_description",
    title: "Descrição do Artigo",
    dataIndex: "article_description",
    key: "article_description",
    sorter: (a, b) =>
      a.article_description.localeCompare(b.article_description),
    sortDirections: ["descend", "ascend"],
  },
  {
    code: "view_article_created_at",
    title: "Data de Criação do Artigo",
    dataIndex: "article_created_at",
    key: "article_created_at",
    sorter: (a, b) => a.article_created_at.localeCompare(b.article_created_at),
    sortDirections: ["descend", "ascend"],
    render: (text) => {
      return text ? moment(text).format("DD/MM/YYYY HH:mm:ss") : "";
    },
  },
  {
    code: "view_author",
    title: "Autor",
    dataIndex: "customer_representative",
    key: "customer_representative",
    sorter: (a, b) =>
      a.customer_representative.localeCompare(b.customer_representative),
    sortDirections: ["descend", "ascend"],
  },
  {
    code: "view_contract_name",
    title: "Contrato",
    dataIndex: "contract_name",
    key: "contract_name",
    sorter: (a, b) =>
      a.contract_name === null || b.contract_name === null
        ? -1
        : a.contract_name.localeCompare(b.contract_name),
    sortDirections: ["descend", "ascend"],
    align: "center",
    render: (text) => {
      return text ? <span>{text}</span> : <span>N/A</span>;
    },
  },
];
export const ViewArticlesModal = ({ ticketNumber }) => {
  const [open, setOpen] = useState(false);
  const [search, setSearch] = useState("");
  const [loading, setLoading] = useState(false);
  const [dataSource, setDataSource] = useState([]);

  const filteredArticles = useMemo(() => {
    let filteredData = dataSource;
    const searchFields = [
      "article_id",
      "article_subject",
      "article_description",
      "article_created_at",
      "customer_representative",
      "contract_name",
    ];
    filteredData = filterTableData({ searchFields, search, data: dataSource });
    return filteredData;
  }, [dataSource, search]);

  return (
    <>
      <Button
        type="link"
        onClick={async () => {
          await controller.handleViewDetailsModalOpen(
            ticketNumber.ticket_number,
            setLoading,
            setDataSource,
            setOpen
          );
        }}
        icon={<EyeOutlined />}
      />
      <Modal
        open={open}
        closable={true}
        width={window.innerWidth > 768 ? "80%" : "100%"}
        onCancel={() => setOpen(false)}
        footer={null}
        title={`Atividades do Ticket #${ticketNumber.ticket_number}`}
      >
        <SearchInput
          onChange={(e) => setSearch(e)}
          placeholder="Pesquisar"
          style={{ marginBottom: "10px" }}
        />
        <Counter tableData={filteredArticles} />
        <DynamicTable
          columns={columns}
          dataSource={filteredArticles}
          loading={loading}
        />
      </Modal>
    </>
  );
};
