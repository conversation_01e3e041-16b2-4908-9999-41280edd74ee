import { Select as SelectAntd } from "antd";

const { Option } = SelectAntd;
export const Select = ({
  onChange,
  options,
  value,
  style,
  className = "",
  disabled = false,
  loading = false,
  placeholder = "Selecione",
  defaultValue = null,
  ...rest
}) => {
  return (
    <SelectAntd
      defaultValue={defaultValue}
      value={value}
      style={style}
      onChange={onChange}
      placeholder={placeholder}
      disabled={disabled}
      className={className}
      loading={loading}
      {...rest}
    >
      {options?.map((option, key) => (
        <Option value={option.value} key={key}>
          {option.label}
        </Option>
      ))}
    </SelectAntd>
  );
};
