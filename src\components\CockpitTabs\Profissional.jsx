import { Col, Row } from "antd";

export default function ProfissionalTab(props) {
  /* Checking if the array is empty. */
  if (props.profissionalComponents.length === 0) {
    return <h1 className="font-bold shadow-lg">Nenhuma visão encontrada!</h1>;
  }

  return (
    <Row gutter={[48, 20]} justify="space-between">
      {/* Mapping the array of objects. */}
      {props.profissionalComponents
        .filter((item) => item.name !== "Ferias")
        .map((val, index) => {
          return (
            <Col xl={12} lg={24} key={index}>
              {props.components[val.component]?.object}
            </Col>
          );
        })}
    </Row>
  );
}
