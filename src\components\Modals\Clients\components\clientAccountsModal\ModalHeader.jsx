import {
  Row,
  Button,
  Col,
  Form,
  Input,
  Popover,
  Checkbox,
  Tooltip,
} from "antd";
import { SolicitationsModal } from "../../../SwitchRoles/SolicitationsModal";
import { useEffect, useState } from "react";
import { CreateTicket } from "./CreateTicket";
export const ModalHeader = ({
  hidden,
  setHidden,
  setSearch,
  permissions,
  isEditing,
  setIsEditing,
  handleSubmit,
  form,
  allAccessForm,
  loadingAccess,
  accounts,
  client,
  fieldEdit,
  getChange,
  loadingRequest,
  setAccountID,
  requestAccountAccess,
  setIsMultipleAccounts,
  isMultipleAccounts,
  solicitations,
  getSoliciations,
  permissionSets,
  origin,
  clients,
}) => {
  const [disabledSolicitationButton, setDisabledSolicitationButton] =
    useState(false);
  const [ticketNumber, setTicketNumber] = useState("");
  const [popoverZIndex, setPopoverZIndex] = useState(2000);

  const handleTicketNumberUpdate = (ticket) => {
    setTicketNumber(ticket);
    allAccessForm.setFieldsValue({
      ticket_id: ticket,
    });
  };

  const disableSolicitationButton = () => {
    let checkIfThereAreNewAccounts = accounts.map((account) => {
      return (
        solicitations.find(
          (solicitation) => solicitation.account_id === account
        ) || false
      );
    });

    if (accounts.length === 0 || checkIfThereAreNewAccounts.includes(true)) {
      setDisabledSolicitationButton(true);
    } else {
      setDisabledSolicitationButton(false);
    }
  };

  const accountIdSetter = () => {
    let allNewAccounts = accounts.filter((account) => {
      return (
        !solicitations.find(
          (solicitation) => solicitation.account_id === account
        ) || false
      );
    });
    setAccountID(allNewAccounts);
  };

  useEffect(() => {
    disableSolicitationButton();
  }, [accounts, solicitations]);

  return (
    <Row justify="space-around">
      {permissions
        ?.map((permission) => {
          return permission.code;
        })
        .includes("view_account_add") ? (
        <>
          <Col>
            <Button
              type="primary"
              onClick={() => {
                setHidden(!hidden);
                form.resetFields();
                setIsEditing(false);
                if (hidden) {
                  form.setFieldsValue({
                    con_status: null,
                    customer_id: client.id,
                  });
                }
              }}
            >
              {hidden === true ? "Adicionar Conta AWS" : "Cancelar"}
            </Button>
          </Col>
          <Col>
            <Button
              onClick={() => {
                setIsMultipleAccounts(!isMultipleAccounts);
              }}
              style={{ marginBottom: "1em" }}
            >
              Solicitar acesso a
              {isMultipleAccounts ? " uma conta" : " múltiplas contas"}
            </Button>
          </Col>
          <Col span={hidden ? 0 : 24}>
            <Form
              onValuesChange={(data) => {
                if (isEditing) {
                  fieldEdit.push(
                    `${Object.keys(data)[0]} alterado de: ${getChange(
                      Object.keys(data)[0]
                    )} para ${Object.values(data)[0]}`
                  );
                }
              }}
              form={form}
              onFinish={handleSubmit}
              name="control-hooks"
              layout="vertical"
              requiredMark={false}
              style={{ marginTop: "1em" }}
            >
              <Form.Item hidden name="id" />
              <Form.Item hidden name="customer_id" />
              <Form.Item hidden name="con_status" />
              <Row gutter={[12, 0]}>
                <Col span={8}>
                  <Form.Item
                    rules={[{ required: true, message: "Preencha este campo" }]}
                    label="Perfil"
                    name="profile"
                  >
                    <Input placeholder="Insira o Perfil" />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    rules={[{ required: true, message: "Preencha este campo" }]}
                    label="Conta AWS"
                    name="account_id"
                  >
                    <Input placeholder="Insira o AWS Account ID" />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    rules={[{ required: true, message: "Preencha este campo" }]}
                    label="Região"
                    name="region"
                  >
                    <Input placeholder="Insira a Região" />
                  </Form.Item>
                </Col>
              </Row>

              <Row>
                <Form.Item valuePropName="checked" name="payer">
                  <Checkbox>Payer Account</Checkbox>
                </Form.Item>
              </Row>
              <Form.Item>
                <Button
                  loading={loadingRequest}
                  type="primary"
                  htmlType="submit"
                >
                  Salvar
                </Button>
              </Form.Item>
            </Form>
          </Col>
        </>
      ) : (
        ""
      )}

      {permissions
        ?.map((permission) => {
          return permission.code;
        })
        .includes("view_account_ask_all") && isMultipleAccounts ? (
        <Popover
          trigger="click"
          zIndex={popoverZIndex}
          content={
            <Form onFinish={requestAccountAccess} form={allAccessForm}>
              <Row align="center" gutter={12} justify="center">
                <Col>
                  <Form.Item
                    rules={[
                      {
                        required: true,
                        message: "Preencha este campo para solicitar o acesso",
                      },
                    ]}
                    style={{ marginBottom: "1em" }}
                    name="ticket_id"
                  >
                    <Input
                      placeholder="Número do Ticket"
                      value={ticketNumber}
                    />
                  </Form.Item>
                </Col>
                <Col>
                  <Form.Item
                    rules={[
                      {
                        required: true,
                        message: "Preencha este campo para solicitar o acesso",
                      },
                    ]}
                    style={{ marginBottom: "1em" }}
                    name="time"
                  >
                    <Input
                      placeholder="Tempo (em horas)"
                      type="number"
                      min="1"
                    />
                  </Form.Item>
                </Col>
              </Row>
              <Row justify="space-between">
                <Col>
                  <CreateTicket
                    client={client}
                    setTicketNumber={handleTicketNumberUpdate}
                    accounts={accounts}
                    setPopoverZIndex={setPopoverZIndex}
                  />
                </Col>
                <Col>
                  <Tooltip
                    title={
                      disabledSolicitationButton
                        ? "Nenhuma conta selecionada"
                        : ""
                    }
                  >
                    <Button
                      loading={loadingAccess}
                      type="primary"
                      htmlType="submit"
                      disabled={disabledSolicitationButton}
                    >
                      Solicitar
                    </Button>
                  </Tooltip>
                </Col>
              </Row>
            </Form>
          }
        >
          <Button onClick={accountIdSetter}>Solicitar acesso</Button>
        </Popover>
      ) : (
        ""
      )}
      <Col>
        <SolicitationsModal
          clients={clients}
          permissions={permissions}
          client={client}
          solicitations={solicitations}
          getSolicitations={getSoliciations}
          permissionSets={permissionSets?.data}
          origin={origin}
        />
      </Col>
      <Col>
        <Input
          onChange={(e) => setSearch(e.target.value)}
          style={{
            width: "300px",
            height: "33px",
            borderRadius: "7px",
            marginBottom: "2em",
          }}
          placeholder="Buscar conta..."
        />
      </Col>
    </Row>
  );
};
