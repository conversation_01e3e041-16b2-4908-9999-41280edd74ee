import { expectedOutput, inputQueues } from "../../../__mocks__/tickets/data";
import { formatQueues } from "../../../components/Modals/Clients/controllers/clientAccountsModal";

describe("formatQueues", () => {
  it("should format queues into a hierarchical tree structure", () => {
    const formattedDate = formatQueues(inputQueues);
    expect(formattedDate).toEqual(expectedOutput);
  });

  it("should return an empty array when input is an empty array", () => {
    const inputQueues = [];
    const expectedOutput = [];
    const formattedDate = formatQueues(inputQueues);
    expect(formattedDate).toEqual(expectedOutput);
  });
});
