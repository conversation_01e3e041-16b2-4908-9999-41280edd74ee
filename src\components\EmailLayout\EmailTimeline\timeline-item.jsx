import styledSheet from './style.module.scss'
import { Tag, Tooltip, Popconfirm } from 'antd'

import {
    DeleteOutlined,
    EditOutlined
} from '@ant-design/icons'

export const TimelineItem = ({ data, onEditItem, onRemoveItem }) => {

    return (
        <div className={styledSheet['timeline-item']}>
            <div className={styledSheet['timeline-item-content']}>
                <span className={styledSheet['tag']} style={{ background: data.category.color }}
                >
                    {data.category.tag}
                </span>
                <time>{new Date(data.date).toLocaleDateString()}</time>
                <p>{data.text}</p>
                <span className={styledSheet['circle']} />
            </div>

            <div className={styledSheet['timeline-item-options']}>
                <div onClick={onEditItem} >
                    <Tooltip title="Editar">
                        <Tag color="green">
                            <EditOutlined />
                        </Tag>
                    </Tooltip>
                </div>
                <Popconfirm
                    placement="leftBottom"
                    title="Tem certeza que deseja remover este item?"
                    style={{
                        backgroundColor: 'transparent',
                        border: 'none',
                        cursor: 'pointer',
                        color: 'black'
                    }}
                    onConfirm={onRemoveItem}
                >
                    <Tooltip title="Remover">
                        <Tag color="red">
                            <DeleteOutlined />
                        </Tag>
                    </Tooltip>
                </Popconfirm>
            </div>
        </div>
    )
};